/**
 * 共享工具包 - 公共基础设施层
 *
 * @description 提供微前端项目的公共工具、类型定义、常量和错误处理机制
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * 资源类型
 */
export type ResourceType = 'script' | 'style' | 'link' | 'image';
/**
 * 资源加载选项
 */
export interface ResourceLoadOptions {
    /** 超时时间（毫秒） */
    timeout?: number;
    /** 重试次数 */
    retries?: number;
    /** 是否缓存 */
    cache?: boolean;
    /** 完整性校验 */
    integrity?: string;
    /** 跨域设置 */
    crossOrigin?: string;
    /** 自定义属性 */
    attributes?: Record<string, string>;
}
/**
 * 加载资源
 * @param url 资源URL
 * @param type 资源类型
 * @param options 加载选项
 */
export declare function loadResource(url: string, type: ResourceType, options?: ResourceLoadOptions): Promise<void>;
/**
 * 预加载资源
 * @param urls 资源URL数组
 * @param type 资源类型
 * @param options 加载选项
 */
export declare function preloadResources(urls: string[], type: ResourceType, options?: ResourceLoadOptions): Promise<void[]>;
/**
 * 批量加载资源
 * @param resources 资源配置数组
 */
export declare function loadResourcesBatch(resources: Array<{
    url: string;
    type: ResourceType;
    options?: ResourceLoadOptions;
}>): Promise<void[]>;
/**
 * 检查资源是否已加载
 * @param url 资源URL
 */
export declare function isResourceLoaded(url: string): boolean;
/**
 * 清理资源缓存
 * @param url 资源URL（可选，不传则清理所有）
 */
export declare function clearResourceCache(url?: string): void;
/**
 * 获取资源缓存统计
 */
export declare function getResourceCacheStats(): {
    total: number;
    urls: string[];
};
//# sourceMappingURL=resource-loading-utils.d.ts.map