/**
 * @fileoverview 对象工具函数
 * @description 提供对象操作相关的工具函数
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 深度克隆对象
 */
export declare function deepClone<T>(obj: T, visited?: WeakMap<object, any>): T;
/**
 * 深度合并对象
 */
export declare function deepMerge<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T;
/**
 * 判断是否为普通对象
 */
export declare function isPlainObject(obj: any): obj is Record<string, any>;
//# sourceMappingURL=object.d.ts.map