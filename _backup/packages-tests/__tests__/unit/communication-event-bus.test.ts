/**
 * @fileoverview EventBus 通信模块单元测试
 * @description 测试事件总线的完整功能，确保100%代码覆盖率
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { createEventBus, EventBus, globalEventBus, type EventBusConfig } from '../src/communication/event-bus';

describe('EventBus 通信模块', () => {
    let eventBus: EventBus;
    let mockLogger: any;

    beforeEach(() => {
        // 创建模拟日志器
        mockLogger = {
            debug: vi.fn(),
            info: vi.fn(),
            warn: vi.fn(),
            error: vi.fn()
        };

        // 模拟 createLogger 函数
        vi.doMock('../src/utils', () => ({
            createLogger: vi.fn(() => mockLogger)
        }));

        eventBus = new EventBus();
    });

    afterEach(() => {
        eventBus.destroy();
        vi.clearAllMocks();
        vi.resetModules();
    });

    describe('构造函数', () => {
        it('应该使用默认配置创建实例', () => {
            const bus = new EventBus();
            expect(bus).toBeInstanceOf(EventBus);
            expect(bus.eventNames()).toEqual([]);
        });

        it('应该使用自定义配置创建实例', () => {
            const config: EventBusConfig = {
                maxListeners: 50,
                enableDebug: true
            };
            const bus = new EventBus(config);
            expect(bus).toBeInstanceOf(EventBus);
        });

        it('应该合并默认配置和自定义配置', () => {
            const config: EventBusConfig = {
                enableDebug: true
            };
            const bus = new EventBus(config);
            expect(bus).toBeInstanceOf(EventBus);
        });

        it('应该处理空配置对象', () => {
            const bus = new EventBus({});
            expect(bus).toBeInstanceOf(EventBus);
        });
    });

    describe('事件监听 - on 方法', () => {
        it('应该能够监听事件', () => {
            const listener = vi.fn();
            const unsubscribe = eventBus.on('test-event', listener);

            expect(typeof unsubscribe).toBe('function');
            expect(eventBus.listenerCount('test-event')).toBe(1);
        });

        it('应该能够监听多个相同事件', () => {
            const listener1 = vi.fn();
            const listener2 = vi.fn();

            eventBus.on('test-event', listener1);
            eventBus.on('test-event', listener2);

            expect(eventBus.listenerCount('test-event')).toBe(2);
        });

        it('应该能够监听不同事件', () => {
            const listener1 = vi.fn();
            const listener2 = vi.fn();

            eventBus.on('event1', listener1);
            eventBus.on('event2', listener2);

            expect(eventBus.listenerCount('event1')).toBe(1);
            expect(eventBus.listenerCount('event2')).toBe(1);
        });

        it('应该支持一次性监听选项', () => {
            const listener = vi.fn();
            eventBus.on('test-event', listener, { once: true });

            eventBus.emit('test-event', 'data');
            eventBus.emit('test-event', 'data2');

            expect(listener).toHaveBeenCalledTimes(1);
            expect(listener).toHaveBeenCalledWith('data');
        });

        it('应该返回取消监听函数', () => {
            const listener = vi.fn();
            const unsubscribe = eventBus.on('test-event', listener);

            expect(eventBus.listenerCount('test-event')).toBe(1);

            unsubscribe();
            expect(eventBus.listenerCount('test-event')).toBe(0);
        });

        it('应该在调试模式下记录日志', () => {
            const debugBus = new EventBus({ enableDebug: true });
            const listener = vi.fn();

            debugBus.on('test-event', listener);

            expect(mockLogger.debug).toHaveBeenCalledWith('监听事件: test-event');
        });

        it('应该处理异步监听器', async () => {
            const asyncListener = vi.fn().mockResolvedValue(undefined);
            eventBus.on('async-event', asyncListener);

            eventBus.emit('async-event', 'data');

            expect(asyncListener).toHaveBeenCalledWith('data');
        });

        it('应该处理带类型的监听器', () => {
            interface TestData {
                id: number;
                name: string;
            }

            const listener = vi.fn();
            eventBus.on<TestData>('typed-event', listener);

            const testData: TestData = { id: 1, name: 'test' };
            eventBus.emit('typed-event', testData);

            expect(listener).toHaveBeenCalledWith(testData);
        });
    });

    describe('一次性监听 - once 方法', () => {
        it('应该只执行一次监听器', () => {
            const listener = vi.fn();
            eventBus.once('once-event', listener);

            eventBus.emit('once-event', 'data1');
            eventBus.emit('once-event', 'data2');

            expect(listener).toHaveBeenCalledTimes(1);
            expect(listener).toHaveBeenCalledWith('data1');
        });

        it('应该返回取消监听函数', () => {
            const listener = vi.fn();
            const unsubscribe = eventBus.once('once-event', listener);

            expect(eventBus.listenerCount('once-event')).toBe(1);

            unsubscribe();
            expect(eventBus.listenerCount('once-event')).toBe(0);
        });

        it('应该在调试模式下记录日志', () => {
            const debugBus = new EventBus({ enableDebug: true });
            const listener = vi.fn();

            debugBus.once('once-event', listener);

            expect(mockLogger.debug).toHaveBeenCalledWith('监听事件: once-event');
        });

        it('应该处理多个一次性监听器', () => {
            const listener1 = vi.fn();
            const listener2 = vi.fn();

            eventBus.once('once-event', listener1);
            eventBus.once('once-event', listener2);

            eventBus.emit('once-event', 'data');

            expect(listener1).toHaveBeenCalledTimes(1);
            expect(listener2).toHaveBeenCalledTimes(1);
            expect(eventBus.listenerCount('once-event')).toBe(0);
        });
    });

    describe('取消监听 - off 方法', () => {
        it('应该能够取消指定监听器', () => {
            const listener1 = vi.fn();
            const listener2 = vi.fn();

            eventBus.on('test-event', listener1);
            eventBus.on('test-event', listener2);

            expect(eventBus.listenerCount('test-event')).toBe(2);

            eventBus.off('test-event', listener1);
            expect(eventBus.listenerCount('test-event')).toBe(1);

            eventBus.emit('test-event', 'data');
            expect(listener1).not.toHaveBeenCalled();
            expect(listener2).toHaveBeenCalledWith('data');
        });

        it('应该能够取消所有监听器', () => {
            const listener1 = vi.fn();
            const listener2 = vi.fn();

            eventBus.on('test-event', listener1);
            eventBus.on('test-event', listener2);

            expect(eventBus.listenerCount('test-event')).toBe(2);

            eventBus.off('test-event');
            expect(eventBus.listenerCount('test-event')).toBe(0);
        });

        it('应该在调试模式下记录日志', () => {
            const debugBus = new EventBus({ enableDebug: true });
            const listener = vi.fn();

            debugBus.on('test-event', listener);
            debugBus.off('test-event', listener);

            expect(mockLogger.debug).toHaveBeenCalledWith('取消监听事件: test-event');
        });

        it('应该处理不存在的监听器', () => {
            const listener = vi.fn();

            expect(() => {
                eventBus.off('non-existent-event', listener);
            }).not.toThrow();
        });

        it('应该处理不存在的事件', () => {
            expect(() => {
                eventBus.off('non-existent-event');
            }).not.toThrow();
        });
    });

    describe('事件发布 - emit 方法', () => {
        it('应该能够发布事件', () => {
            const listener = vi.fn();
            eventBus.on('test-event', listener);

            const result = eventBus.emit('test-event', 'data');

            expect(result).toBe(true);
            expect(listener).toHaveBeenCalledWith('data');
        });

        it('应该能够发布带多个参数的事件', () => {
            const listener = vi.fn();
            eventBus.on('test-event', listener);

            eventBus.emit('test-event', 'arg1', 'arg2', 'arg3');

            expect(listener).toHaveBeenCalledWith('arg1', 'arg2', 'arg3');
        });

        it('应该能够发布无参数的事件', () => {
            const listener = vi.fn();
            eventBus.on('test-event', listener);

            eventBus.emit('test-event');

            expect(listener).toHaveBeenCalledWith();
        });

        it('应该在没有监听器时返回false', () => {
            const result = eventBus.emit('no-listeners-event', 'data');
            expect(result).toBe(false);
        });

        it('应该在调试模式下记录日志', () => {
            const debugBus = new EventBus({ enableDebug: true });
            const listener = vi.fn();

            debugBus.on('test-event', listener);
            debugBus.emit('test-event', 'data');

            expect(mockLogger.debug).toHaveBeenCalledWith('发布事件: test-event', ['data']);
        });

        it('应该触发所有监听器', () => {
            const listener1 = vi.fn();
            const listener2 = vi.fn();
            const listener3 = vi.fn();

            eventBus.on('test-event', listener1);
            eventBus.on('test-event', listener2);
            eventBus.on('test-event', listener3);

            eventBus.emit('test-event', 'data');

            expect(listener1).toHaveBeenCalledWith('data');
            expect(listener2).toHaveBeenCalledWith('data');
            expect(listener3).toHaveBeenCalledWith('data');
        });

        it('应该处理监听器中的错误', () => {
            const errorListener = vi.fn().mockImplementation(() => {
                throw new Error('监听器错误');
            });
            const normalListener = vi.fn();

            eventBus.on('error-event', errorListener);
            eventBus.on('error-event', normalListener);

            expect(() => {
                eventBus.emit('error-event', 'data');
            }).not.toThrow();

            expect(errorListener).toHaveBeenCalled();
            expect(normalListener).toHaveBeenCalled();
        });
    });

    describe('监听器计数 - listenerCount 方法', () => {
        it('应该返回正确的监听器数量', () => {
            expect(eventBus.listenerCount('test-event')).toBe(0);

            const listener1 = vi.fn();
            const listener2 = vi.fn();

            eventBus.on('test-event', listener1);
            expect(eventBus.listenerCount('test-event')).toBe(1);

            eventBus.on('test-event', listener2);
            expect(eventBus.listenerCount('test-event')).toBe(2);

            eventBus.off('test-event', listener1);
            expect(eventBus.listenerCount('test-event')).toBe(1);
        });

        it('应该处理不存在的事件', () => {
            expect(eventBus.listenerCount('non-existent-event')).toBe(0);
        });
    });

    describe('事件名称 - eventNames 方法', () => {
        it('应该返回所有事件名称', () => {
            expect(eventBus.eventNames()).toEqual([]);

            eventBus.on('event1', vi.fn());
            eventBus.on('event2', vi.fn());
            eventBus.on('event3', vi.fn());

            const eventNames = eventBus.eventNames();
            expect(eventNames).toContain('event1');
            expect(eventNames).toContain('event2');
            expect(eventNames).toContain('event3');
            expect(eventNames.length).toBe(3);
        });

        it('应该在移除所有监听器后返回空数组', () => {
            eventBus.on('event1', vi.fn());
            eventBus.on('event2', vi.fn());

            expect(eventBus.eventNames().length).toBe(2);

            eventBus.removeAllListeners();
            expect(eventBus.eventNames()).toEqual([]);
        });
    });

    describe('获取监听器 - listeners 方法', () => {
        it('应该返回指定事件的所有监听器', () => {
            const listener1 = vi.fn();
            const listener2 = vi.fn();

            eventBus.on('test-event', listener1);
            eventBus.on('test-event', listener2);

            const listeners = eventBus.listeners('test-event');
            expect(listeners).toContain(listener1);
            expect(listeners).toContain(listener2);
            expect(listeners.length).toBe(2);
        });

        it('应该处理不存在的事件', () => {
            const listeners = eventBus.listeners('non-existent-event');
            expect(listeners).toEqual([]);
        });

        it('应该返回正确类型的监听器', () => {
            interface TestData {
                value: string;
            }

            const listener = vi.fn();
            eventBus.on<TestData>('typed-event', listener);

            const listeners = eventBus.listeners<TestData>('typed-event');
            expect(listeners).toContain(listener);
        });
    });

    describe('移除所有监听器 - removeAllListeners 方法', () => {
        it('应该移除所有事件的所有监听器', () => {
            eventBus.on('event1', vi.fn());
            eventBus.on('event1', vi.fn());
            eventBus.on('event2', vi.fn());

            expect(eventBus.listenerCount('event1')).toBe(2);
            expect(eventBus.listenerCount('event2')).toBe(1);

            eventBus.removeAllListeners();

            expect(eventBus.listenerCount('event1')).toBe(0);
            expect(eventBus.listenerCount('event2')).toBe(0);
            expect(eventBus.eventNames()).toEqual([]);
        });

        it('应该移除指定事件的所有监听器', () => {
            eventBus.on('event1', vi.fn());
            eventBus.on('event1', vi.fn());
            eventBus.on('event2', vi.fn());

            expect(eventBus.listenerCount('event1')).toBe(2);
            expect(eventBus.listenerCount('event2')).toBe(1);

            eventBus.removeAllListeners('event1');

            expect(eventBus.listenerCount('event1')).toBe(0);
            expect(eventBus.listenerCount('event2')).toBe(1);
        });

        it('应该在调试模式下记录日志', () => {
            const debugBus = new EventBus({ enableDebug: true });

            debugBus.removeAllListeners();
            expect(mockLogger.debug).toHaveBeenCalledWith('移除所有监听器');

            debugBus.removeAllListeners('specific-event');
            expect(mockLogger.debug).toHaveBeenCalledWith('移除所有监听器: specific-event');
        });

        it('应该处理不存在的事件', () => {
            expect(() => {
                eventBus.removeAllListeners('non-existent-event');
            }).not.toThrow();
        });
    });

    describe('检查监听器 - hasListeners 方法', () => {
        it('应该正确检查是否有监听器', () => {
            expect(eventBus.hasListeners('test-event')).toBe(false);

            eventBus.on('test-event', vi.fn());
            expect(eventBus.hasListeners('test-event')).toBe(true);

            eventBus.removeAllListeners('test-event');
            expect(eventBus.hasListeners('test-event')).toBe(false);
        });

        it('应该处理不存在的事件', () => {
            expect(eventBus.hasListeners('non-existent-event')).toBe(false);
        });
    });

    describe('销毁 - destroy 方法', () => {
        it('应该移除所有监听器', () => {
            eventBus.on('event1', vi.fn());
            eventBus.on('event2', vi.fn());

            expect(eventBus.eventNames().length).toBe(2);

            eventBus.destroy();

            expect(eventBus.eventNames()).toEqual([]);
        });

        it('应该在调试模式下记录日志', () => {
            const debugBus = new EventBus({ enableDebug: true });

            debugBus.destroy();
            expect(mockLogger.debug).toHaveBeenCalledWith('事件总线已销毁');
        });

        it('应该能够多次调用而不出错', () => {
            eventBus.on('test-event', vi.fn());

            expect(() => {
                eventBus.destroy();
                eventBus.destroy();
            }).not.toThrow();
        });
    });

    describe('统计信息 - getStats 方法', () => {
        it('应该返回正确的统计信息', () => {
            const stats1 = eventBus.getStats();
            expect(stats1).toEqual({
                totalEvents: 0,
                totalListeners: 0,
                eventNames: []
            });

            eventBus.on('event1', vi.fn());
            eventBus.on('event1', vi.fn());
            eventBus.on('event2', vi.fn());

            const stats2 = eventBus.getStats();
            expect(stats2.totalEvents).toBe(2);
            expect(stats2.totalListeners).toBe(3);
            expect(stats2.eventNames).toContain('event1');
            expect(stats2.eventNames).toContain('event2');
        });

        it('应该在移除监听器后更新统计信息', () => {
            eventBus.on('event1', vi.fn());
            eventBus.on('event2', vi.fn());

            let stats = eventBus.getStats();
            expect(stats.totalEvents).toBe(2);
            expect(stats.totalListeners).toBe(2);

            eventBus.removeAllListeners('event1');

            stats = eventBus.getStats();
            expect(stats.totalEvents).toBe(1);
            expect(stats.totalListeners).toBe(1);
        });
    });

    describe('全局事件总线', () => {
        it('应该提供全局事件总线实例', () => {
            expect(globalEventBus).toBeInstanceOf(EventBus);
        });

        it('应该是单例模式', () => {
            const { globalEventBus: globalBus1 } = require('../src/communication/event-bus');
            const { globalEventBus: globalBus2 } = require('../src/communication/event-bus');

            expect(globalBus1).toBe(globalBus2);
        });

        it('应该能够正常使用', () => {
            const listener = vi.fn();
            globalEventBus.on('global-test', listener);

            globalEventBus.emit('global-test', 'data');

            expect(listener).toHaveBeenCalledWith('data');

            // 清理
            globalEventBus.removeAllListeners('global-test');
        });
    });

    describe('工厂函数', () => {
        it('应该创建新的事件总线实例', () => {
            const bus1 = createEventBus();
            const bus2 = createEventBus();

            expect(bus1).toBeInstanceOf(EventBus);
            expect(bus2).toBeInstanceOf(EventBus);
            expect(bus1).not.toBe(bus2);
        });

        it('应该使用提供的配置', () => {
            const config: EventBusConfig = {
                maxListeners: 200,
                enableDebug: true
            };

            const bus = createEventBus(config);
            expect(bus).toBeInstanceOf(EventBus);
        });

        it('应该处理空配置', () => {
            const bus = createEventBus();
            expect(bus).toBeInstanceOf(EventBus);
        });
    });

    describe('边界情况和错误处理', () => {
        it('应该处理空字符串事件名', () => {
            const listener = vi.fn();

            expect(() => {
                eventBus.on('', listener);
                eventBus.emit('', 'data');
            }).not.toThrow();

            expect(listener).toHaveBeenCalledWith('data');
        });

        it('应该处理特殊字符事件名', () => {
            const listener = vi.fn();
            const specialEventName = 'event:with:special@chars#123';

            eventBus.on(specialEventName, listener);
            eventBus.emit(specialEventName, 'data');

            expect(listener).toHaveBeenCalledWith('data');
        });

        it('应该处理大量监听器', () => {
            const listeners: Array<() => void> = [];

            for (let i = 0; i < 1000; i++) {
                const listener = vi.fn();
                listeners.push(listener);
                eventBus.on('mass-event', listener);
            }

            expect(eventBus.listenerCount('mass-event')).toBe(1000);

            eventBus.emit('mass-event', 'data');

            listeners.forEach(listener => {
                expect(listener).toHaveBeenCalledWith('data');
            });
        });

        it('应该处理循环事件发布', () => {
            let callCount = 0;
            const maxCalls = 5;

            const listener = vi.fn().mockImplementation(() => {
                callCount++;
                if (callCount < maxCalls) {
                    eventBus.emit('circular-event', callCount);
                }
            });

            eventBus.on('circular-event', listener);
            eventBus.emit('circular-event', 0);

            expect(listener).toHaveBeenCalledTimes(maxCalls);
        });

        it('应该处理监听器中的异步错误', async () => {
            const asyncErrorListener = vi.fn().mockRejectedValue(new Error('异步错误'));
            const normalListener = vi.fn();

            eventBus.on('async-error-event', asyncErrorListener);
            eventBus.on('async-error-event', normalListener);

            expect(() => {
                eventBus.emit('async-error-event', 'data');
            }).not.toThrow();

            expect(asyncErrorListener).toHaveBeenCalled();
            expect(normalListener).toHaveBeenCalled();
        });
    });

    describe('性能测试', () => {
        it('应该高效处理大量事件发布', () => {
            const listener = vi.fn();
            eventBus.on('performance-event', listener);

            const start = performance.now();

            for (let i = 0; i < 10000; i++) {
                eventBus.emit('performance-event', i);
            }

            const end = performance.now();

            expect(end - start).toBeLessThan(1000); // 应该在1秒内完成
            expect(listener).toHaveBeenCalledTimes(10000);
        });

        it('应该高效处理大量监听器注册和注销', () => {
            const start = performance.now();
            const unsubscribers: Array<() => void> = [];

            // 注册1000个监听器
            for (let i = 0; i < 1000; i++) {
                const unsubscribe = eventBus.on(`event-${i}`, vi.fn());
                unsubscribers.push(unsubscribe);
            }

            // 注销所有监听器
            unsubscribers.forEach(unsubscribe => unsubscribe());

            const end = performance.now();

            expect(end - start).toBeLessThan(500); // 应该在500ms内完成
            expect(eventBus.eventNames().length).toBe(0);
        });
    });

    describe('内存泄漏防护', () => {
        it('应该在销毁后清理所有引用', () => {
            const listener = vi.fn();
            eventBus.on('memory-test', listener);

            expect(eventBus.hasListeners('memory-test')).toBe(true);

            eventBus.destroy();

            expect(eventBus.hasListeners('memory-test')).toBe(false);
            expect(eventBus.eventNames()).toEqual([]);
        });

        it('应该正确处理监听器的移除', () => {
            const listeners = Array.from({ length: 100 }, () => vi.fn());

            listeners.forEach(listener => {
                eventBus.on('cleanup-test', listener);
            });

            expect(eventBus.listenerCount('cleanup-test')).toBe(100);

            // 移除一半监听器
            listeners.slice(0, 50).forEach(listener => {
                eventBus.off('cleanup-test', listener);
            });

            expect(eventBus.listenerCount('cleanup-test')).toBe(50);

            // 移除剩余监听器
            eventBus.removeAllListeners('cleanup-test');

            expect(eventBus.listenerCount('cleanup-test')).toBe(0);
        });
    });
});