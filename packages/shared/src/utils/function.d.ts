/**
 * @fileoverview 函数处理工具
 * @description 提供各种函数操作和转换功能
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 防抖函数
 */
export declare function debounce<T extends (...args: any[]) => any>(fn: T, delay: number): (...args: Parameters<T>) => void;
/**
 * 节流函数
 */
export declare function throttle<T extends (...args: any[]) => any>(fn: T, delay: number): (...args: Parameters<T>) => void;
/**
 * 记忆化函数
 */
export declare function memoize<T extends (...args: any[]) => any>(fn: T, keyGenerator?: (...args: Parameters<T>) => string): T & {
    cache: Map<string, ReturnType<T>>;
    clear: () => void;
};
/**
 * 只执行一次的函数
 */
export declare function once<T extends (...args: any[]) => any>(fn: T): T;
/**
 * 柯里化函数
 */
export declare function curry<T extends (...args: any[]) => any>(fn: T, arity?: number): (...args: any[]) => any;
/**
 * 函数组合
 */
export declare function compose<T>(...fns: Array<(arg: T) => T>): (arg: T) => T;
/**
 * 管道函数
 */
export declare function pipe<T>(...fns: Array<(arg: T) => T>): (arg: T) => T;
/**
 * 偏函数应用
 */
export declare function partial<T extends (...args: any[]) => any>(fn: T, ...partialArgs: any[]): (...args: any[]) => ReturnType<T>;
/**
 * 取反函数
 */
export declare function negate<T extends (...args: any[]) => boolean>(fn: T): (...args: Parameters<T>) => boolean;
/**
 * 恒等函数
 */
export declare function identity<T>(value: T): T;
//# sourceMappingURL=function.d.ts.map