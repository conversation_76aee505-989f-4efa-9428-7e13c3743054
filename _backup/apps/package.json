{"name": "main-app-basic", "version": "0.1.0", "description": "Micro-Core 示例主应用 - 基础版本", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "serve": "vite preview --port 3000", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.vue", "lint:fix": "eslint src --ext .ts,.vue --fix", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/sidecar": "workspace:*", "@micro-core/plugin-router": "workspace:*", "@micro-core/plugin-communication": "workspace:*", "@micro-core/plugin-sandbox-proxy": "workspace:*", "@micro-core/adapter-react": "workspace:*", "@micro-core/adapter-vue3": "workspace:*", "vue": "^3.4.0"}, "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-vue": "^5.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "typescript": "^5.3.0", "vite": "^5.4.0", "vitest": "^3.2.4", "@vitest/coverage-v8": "^3.2.4"}, "keywords": ["micro-frontend", "main-app", "basic", "vue", "vite", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}