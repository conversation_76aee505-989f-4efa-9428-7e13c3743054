# @micro-core/plugin-sandbox-webcomponent

基于 Web Components 和 Shadow DOM 实现的微前端沙箱插件，提供强大的样式和 DOM 隔离能力。

## 特性

- 🔒 **强隔离**: 基于 Shadow DOM 提供完全的样式和 DOM 隔离
- 🎨 **样式管理**: 智能的样式注入和 CSS 变量支持
- 🔧 **自定义元素**: 支持自定义 Web Components 容器
- 🎯 **插槽系统**: 完整的插槽管理和内容投影
- 📱 **事件隔离**: 可配置的事件隔离策略
- 🚀 **高性能**: 优化的 Shadow DOM 操作和内存管理

## 安装

```bash
npm install @micro-core/plugin-sandbox-webcomponent
```

## 基本用法

### 注册插件

```typescript
import { MicroCore } from '@micro-core/core';
import { WebComponentSandboxPlugin } from '@micro-core/plugin-sandbox-webcomponent';

const microCore = new MicroCore();

// 注册 WebComponent 沙箱插件
microCore.use(WebComponentSandboxPlugin, {
    enableLogging: true,
    enableStyleIsolation: true,
    enableDOMIsolation: true,
    enableEventIsolation: true,
    customElementPrefix: 'micro-app',
    shadowDOMMode: 'closed'
});
```

### 配置微应用使用 WebComponent 沙箱

```typescript
microCore.registerApp({
    name: 'my-app',
    entry: 'https://example.com/app.js',
    container: '#app-container',
    sandbox: 'webcomponent',
    sandboxOptions: {
        isolationLevel: 'strict',
        enableCSSVariables: true,
        debug: true
    }
});
```

### 直接使用沙箱

```typescript
import { WebComponentSandbox } from '@micro-core/plugin-sandbox-webcomponent';

// 创建沙箱实例
const sandbox = new WebComponentSandbox('my-sandbox', {
    enableLogging: true,
    enableStyleIsolation: true,
    shadowDOMMode: 'closed'
});

// 激活沙箱
const container = document.getElementById('container');
sandbox.activate(container);

// 加载应用
await sandbox.loadApp('https://example.com/app.html');

// 设置 CSS 变量
sandbox.setCSSVariable('primary-color', '#007bff');
sandbox.setCSSVariable('font-size', '16px');
```

## 自定义元素

### 使用 MicroAppElement

```html
<!-- 在 HTML 中使用 -->
<micro-app-container 
    app-name="my-app"
    app-entry="https://example.com/app.html"
    app-props='{"theme": "dark", "user": "admin"}'
    loading="true">
</micro-app-container>
```

```typescript
// 监听应用事件
const appElement = document.querySelector('micro-app-container');

appElement.addEventListener('micro-app-loaded', (event) => {
    console.log('应用加载完成:', event.detail);
});

appElement.addEventListener('micro-app-error', (event) => {
    console.error('应用加载失败:', event.detail.error);
});
```

### 使用 SandboxContainer

```html
<!-- 沙箱容器 -->
<micro-app-sandbox 
    sandbox-name="my-sandbox"
    isolation-level="strict"
    css-variables='{"primary-color": "#007bff", "font-size": "16px"}'
    debug="true">
    
    <!-- 内容会被投影到 Shadow DOM 中 -->
    <div>这是沙箱中的内容</div>
</micro-app-sandbox>
```

## 配置选项

### WebComponentSandboxPluginOptions

```typescript
interface WebComponentSandboxPluginOptions {
    /** 是否启用日志记录 */
    enableLogging?: boolean;
    /** 是否启用样式隔离 */
    enableStyleIsolation?: boolean;
    /** 是否启用 DOM 隔离 */
    enableDOMIsolation?: boolean;
    /** 是否启用事件隔离 */
    enableEventIsolation?: boolean;
    /** 自定义元素前缀 */
    customElementPrefix?: string;
    /** Shadow DOM 模式 */
    shadowDOMMode?: 'open' | 'closed';
    /** 是否启用插槽支持 */
    enableSlotSupport?: boolean;
    /** 是否启用 CSS 变量支持 */
    enableCSSVariables?: boolean;
}
```

### WebComponentSandboxOptions

```typescript
interface WebComponentSandboxOptions extends WebComponentSandboxPluginOptions {
    /** 隔离级别 */
    isolationLevel?: 'strict' | 'moderate' | 'loose';
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 自定义样式 */
    customStyles?: string;
    /** 预设 CSS 变量 */
    presetCSSVariables?: Record<string, string>;
}
```

## 高级用法

### 样式管理

```typescript
import { StyleInjector } from '@micro-core/plugin-sandbox-webcomponent';

const sandbox = new WebComponentSandbox('my-sandbox');
sandbox.activate();

const shadowRoot = sandbox.getShadowRoot();
const styleInjector = new StyleInjector(shadowRoot, true);

// 注入样式字符串
styleInjector.injectStyleString('custom-styles', `
    .my-component {
        color: var(--primary-color);
        font-size: var(--font-size);
    }
`);

// 注入外部样式表
await styleInjector.injectExternalStylesheet('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css');

// 设置 CSS 变量
styleInjector.injectCSSVariables({
    'primary-color': '#007bff',
    'secondary-color': '#6c757d',
    'font-size': '16px'
});
```

### 插槽管理

```typescript
import { SlotManager } from '@micro-core/plugin-sandbox-webcomponent';

const shadowRoot = sandbox.getShadowRoot();
const slotManager = new SlotManager(shadowRoot, true);

// 创建命名插槽
slotManager.createSlot({
    name: 'header',
    defaultContent: '<h1>默认标题</h1>',
    allowMultiple: false
});

slotManager.createSlot({
    name: 'content',
    defaultContent: '<p>默认内容</p>',
    allowMultiple: true
});

// 监听插槽变化
slotManager.addEventListener('slotchange', (data) => {
    console.log(`插槽 ${data.slotName} 内容发生变化:`, data.assignedNodes);
});

// 设置插槽内容
slotManager.setSlotContent('header', '<h1>新标题</h1>');
slotManager.appendSlotContent('content', '<p>追加的内容</p>');
```

### CSS 处理

```typescript
import { CSSProcessor } from '@micro-core/plugin-sandbox-webcomponent';

const processor = new CSSProcessor({
    enableIsolation: true,
    processCSSVariables: true,
    customPrefix: 'my-app'
});

// 处理 CSS
const originalCSS = `
    .button {
        background: var(--primary-color);
        color: white;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
`;

const processedCSS = processor.processCSS(originalCSS);
console.log('处理后的 CSS:', processedCSS);

// 验证 CSS 语法
const validation = processor.validateCSS(originalCSS);
if (!validation.isValid) {
    console.error('CSS 语法错误:', validation.errors);
}

// 获取 CSS 统计信息
const stats = processor.getCSSStats(originalCSS);
console.log('CSS 统计:', stats);
```

## 事件系统

### 沙箱事件

```typescript
const sandbox = new WebComponentSandbox('my-sandbox');

// 沙箱激活时
sandbox.addEventListener('activated', () => {
    console.log('沙箱已激活');
});

// 沙箱停用时
sandbox.addEventListener('deactivated', () => {
    console.log('沙箱已停用');
});

// 应用加载完成时
sandbox.addEventListener('app-loaded', (event) => {
    console.log('应用加载完成:', event.detail);
});
```

### 自定义元素事件

```typescript
// MicroAppElement 事件
const appElement = document.querySelector('micro-app-container');

appElement.addEventListener('micro-app-connected', (event) => {
    console.log('微应用元素已连接到 DOM');
});

appElement.addEventListener('micro-app-loading', (event) => {
    console.log('微应用开始加载:', event.detail);
});

appElement.addEventListener('micro-app-loaded', (event) => {
    console.log('微应用加载完成:', event.detail);
});

appElement.addEventListener('micro-app-error', (event) => {
    console.error('微应用加载失败:', event.detail.error);
});

// SandboxContainer 事件
const sandboxElement = document.querySelector('micro-app-sandbox');

sandboxElement.addEventListener('sandbox-connected', (event) => {
    console.log('沙箱容器已连接到 DOM');
});
```

## 工具函数

### Shadow DOM 工具

```typescript
import { 
    isShadowDOMSupported,
    isCustomElementsSupported,
    getShadowRoot,
    createShadowDOM,
    injectHTMLToShadowDOM,
    getShadowDOMStats
} from '@micro-core/plugin-sandbox-webcomponent';

// 检查浏览器支持
if (isShadowDOMSupported() && isCustomElementsSupported()) {
    console.log('浏览器支持 Shadow DOM 和自定义元素');
}

// 创建 Shadow DOM
const element = document.createElement('div');
const shadowRoot = createShadowDOM(element, { mode: 'closed' });

// 注入 HTML 内容
injectHTMLToShadowDOM(shadowRoot, '<p>Hello Shadow DOM!</p>');

// 获取统计信息
const stats = getShadowDOMStats(shadowRoot);
console.log('Shadow DOM 统计:', stats);
```

## 最佳实践

### 1. 选择合适的隔离级别

```typescript
// 严格隔离 - 最高安全性，适用于不受信任的第三方应用
const strictSandbox = new WebComponentSandbox('strict-app', {
    isolationLevel: 'strict',
    enableStyleIsolation: true,
    enableDOMIsolation: true,
    enableEventIsolation: true
});

// 中等隔离 - 平衡性能和安全性，适用于内部应用
const moderateSandbox = new WebComponentSandbox('moderate-app', {
    isolationLevel: 'moderate',
    enableStyleIsolation: true,
    enableEventIsolation: false
});

// 宽松隔离 - 最高性能，适用于可信应用
const looseSandbox = new WebComponentSandbox('loose-app', {
    isolationLevel: 'loose',
    enableStyleIsolation: false,
    enableEventIsolation: false
});
```

### 2. 优化样式管理

```typescript
// 使用 CSS 变量实现主题切换
const sandbox = new WebComponentSandbox('themed-app');
sandbox.activate();

// 设置主题变量
const setTheme = (theme: 'light' | 'dark') => {
    const variables = theme === 'dark' ? {
        'bg-color': '#1a1a1a',
        'text-color': '#ffffff',
        'border-color': '#333333'
    } : {
        'bg-color': '#ffffff',
        'text-color': '#000000',
        'border-color': '#e0e0e0'
    };
    
    Object.entries(variables).forEach(([key, value]) => {
        sandbox.setCSSVariable(key, value);
    });
};

setTheme('dark');
```

### 3. 错误处理和调试

```typescript
const sandbox = new WebComponentSandbox('debug-app', {
    enableLogging: true,
    debug: true
});

try {
    sandbox.activate();
    await sandbox.loadApp('https://example.com/app.html');
} catch (error) {
    console.error('沙箱操作失败:', error);
    
    // 获取调试信息
    const debugInfo = sandbox.getDebugInfo();
    console.log('调试信息:', debugInfo);
}
```

### 4. 性能优化

```typescript
// 延迟加载和预加载
const sandbox = new WebComponentSandbox('optimized-app');

// 预激活沙箱但不加载内容
sandbox.activate();

// 在需要时才加载应用
const loadAppWhenNeeded = async () => {
    if (!sandbox.isLoaded) {
        await sandbox.loadApp('https://example.com/app.html');
    }
};

// 使用 Intersection Observer 实现懒加载
const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
        if (entry.isIntersecting) {
            loadAppWhenNeeded();
            observer.unobserve(entry.target);
        }
    });
});

const container = sandbox.getCustomElement();
if (container) {
    observer.observe(container);
}
```

## 兼容性

### 浏览器支持

- Chrome 53+
- Firefox 63+
- Safari 10.1+
- Edge 79+

### Polyfill

对于不支持的浏览器，可以使用以下 polyfill：

```html
<!-- Web Components Polyfill -->
<script src="https://unpkg.com/@webcomponents/webcomponentsjs@2.6.0/webcomponents-bundle.js"></script>

<!-- Shadow DOM Polyfill -->
<script src="https://unpkg.com/@webcomponents/shadydom@1.8.0/shadydom.min.js"></script>
```

## 故障排除

### 常见问题

1. **样式不生效**
   ```typescript
   // 确保启用了样式隔离
   const sandbox = new WebComponentSandbox('my-app', {
       enableStyleIsolation: true
   });
   ```

2. **事件无法传播**
   ```typescript
   // 调整事件隔离级别
   const sandbox = new WebComponentSandbox('my-app', {
       isolationLevel: 'moderate', // 而不是 'strict'
       enableEventIsolation: false
   });
   ```

3. **CSS 变量不工作**
   ```typescript
   // 确保启用了 CSS 变量支持
   const sandbox = new WebComponentSandbox('my-app', {
       enableCSSVariables: true
   });
   ```

4. **自定义元素注册失败**
   ```typescript
   // 检查元素名称是否符合规范
   const plugin = new WebComponentSandboxPlugin({
       customElementPrefix: 'my-app' // 必须包含连字符
   });
   ```

### 调试技巧

```typescript
// 启用详细日志
const sandbox = new WebComponentSandbox('debug-app', {
    enableLogging: true,
    debug: true
});

// 监听所有事件
sandbox.addEventListener('*', (event) => {
    console.log('沙箱事件:', event);
});

// 获取详细的调试信息
const debugInfo = sandbox.getDebugInfo();
console.table(debugInfo);
```

## API 参考

### WebComponentSandbox

#### 方法

- `activate(container?: HTMLElement): void` - 激活沙箱
- `deactivate(): void` - 停用沙箱
- `destroy(): void` - 销毁沙箱
- `loadApp(entry: string): Promise<void>` - 加载应用
- `setCSSVariable(name: string, value: string): void` - 设置 CSS 变量
- `getCSSVariable(name: string): string | undefined` - 获取 CSS 变量
- `getShadowRoot(): ShadowRoot | null` - 获取 Shadow Root
- `getCustomElement(): HTMLElement | null` - 获取自定义元素
- `isActivated(): boolean` - 检查是否激活

#### 事件

- `activated` - 沙箱激活时触发
- `deactivated` - 沙箱停用时触发
- `app-loaded` - 应用加载完成时触发
- `error` - 发生错误时触发

### StyleInjector

#### 方法

- `injectStyleString(id: string, css: string): HTMLStyleElement` - 注入样式字符串
- `injectExternalStylesheet(id: string, href: string): Promise<HTMLLinkElement>` - 注入外部样式表
- `injectCSSVariables(variables: Record<string, string>): void` - 注入 CSS 变量
- `removeStyle(id: string): boolean` - 移除样式
- `clearAllStyles(): void` - 清空所有样式

### SlotManager

#### 方法

- `createSlot(config: SlotConfig): HTMLSlotElement` - 创建插槽
- `getSlot(name: string): HTMLSlotElement | undefined` - 获取插槽
- `removeSlot(name: string): boolean` - 移除插槽
- `setSlotContent(slotName: string, content: string | Node | Node[]): void` - 设置插槽内容
- `clearSlotContent(slotName: string): void` - 清空插槽内容

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v0.1.0

- 初始版本发布
- 基础 WebComponent 沙箱功能
- Shadow DOM 隔离支持
- 样式和事件隔离
- 自定义元素支持
- 插槽管理系统
