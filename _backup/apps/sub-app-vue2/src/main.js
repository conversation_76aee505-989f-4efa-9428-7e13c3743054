/**
 * @fileoverview Vue 2 微前端子应用入口 - 标准生命周期实现
 * <AUTHOR> <<EMAIL>>
 */

import Vue from 'vue'
import VueRouter from 'vue-router'
import App from './App.vue'

// 使用 Vue Router
Vue.use(VueRouter)

// 应用状态管理
let instance = null
let router = null
let isBootstrapped = false
let isMounted = false

/**
 * 微前端应用启动函数
 * 在应用首次加载时调用，用于初始化应用资源
 */
export async function bootstrap(props = {}) {
    try {
        console.log('🚀 Vue2 子应用开始启动', props)

        // 防止重复启动
        if (isBootstrapped) {
            console.warn('⚠️ Vue2 子应用已经启动，跳过重复启动')
            return
        }

        // 初始化应用资源
        await initializeResources(props)

        // 设置全局错误处理
        setupErrorHandling()

        // 标记为已启动
        isBootstrapped = true

        console.log('✅ Vue2 子应用启动完成')
    } catch (error) {
        console.error('❌ Vue2 子应用启动失败:', error)
        throw error
    }
}

/**
 * 微前端应用挂载函数
 * 将应用挂载到指定容器
 */
export async function mount(props = {}) {
    try {
        console.log('🔄 Vue2 子应用开始挂载', props)

        // 确保应用已启动
        if (!isBootstrapped) {
            await bootstrap(props)
        }

        // 防止重复挂载
        if (isMounted) {
            console.warn('⚠️ Vue2 子应用已经挂载，先卸载再重新挂载')
            await unmount(props)
        }

        // 获取容器元素
        const container = getContainer(props.container)
        if (!container) {
            throw new Error('找不到挂载容器')
        }

        // 创建路由实例
        const basename = props.basename || '/vue2'
        router = new VueRouter({
            mode: 'history',
            base: basename,
            routes: [
                {
                    path: '/',
                    name: 'Home',
                    component: () => import('./views/Home.vue')
                },
                {
                    path: '/about',
                    name: 'About',
                    component: () => import('./views/About.vue')
                }
            ]
        })

        // 创建 Vue 实例
        instance = new Vue({
            router,
            render: h => h(App, {
                props: {
                    ...props,
                    basename,
                    microAppInfo: {
                        name: 'sub-app-vue2',
                        framework: 'Vue2',
                        version: '2.7.0',
                        basename,
                        isMicroFrontend: !!window.__POWERED_BY_MICRO_CORE__
                    }
                }
            }),

            // 全局错误处理
            errorCaptured(error, vm, info) {
                console.error('Vue2 应用错误:', error, info)
                handleVueError(error, vm, info)
                return false
            }
        })

        // 挂载应用
        instance.$mount(container)

        // 标记为已挂载
        isMounted = true

        // 触发挂载完成事件
        dispatchLifecycleEvent('mounted', props)

        console.log('✅ Vue2 子应用挂载完成')
    } catch (error) {
        console.error('❌ Vue2 子应用挂载失败:', error)
        throw error
    }
}

/**
 * 微前端应用卸载函数
 * 清理应用资源和DOM
 */
export async function unmount(props = {}) {
    try {
        console.log('🔄 Vue2 子应用开始卸载', props)

        if (!isMounted) {
            console.warn('⚠️ Vue2 子应用未挂载，跳过卸载')
            return
        }

        // 卸载 Vue 应用
        if (instance) {
            instance.$destroy()
            if (instance.$el && instance.$el.parentNode) {
                instance.$el.parentNode.removeChild(instance.$el)
            }
            instance = null
        }

        // 清理路由
        if (router) {
            router = null
        }

        // 清理事件监听器
        cleanupEventListeners()

        // 清理定时器
        cleanupTimers()

        // 标记为未挂载
        isMounted = false

        // 触发卸载完成事件
        dispatchLifecycleEvent('unmounted', props)

        console.log('✅ Vue2 子应用卸载完成')
    } catch (error) {
        console.error('❌ Vue2 子应用卸载失败:', error)
        throw error
    }
}

/**
 * 微前端应用更新函数
 * 更新应用属性
 */
export async function update(props = {}) {
    try {
        console.log('🔄 Vue2 子应用开始更新', props)

        if (!isMounted) {
            console.warn('⚠️ Vue2 子应用未挂载，无法更新')
            return
        }

        // 重新挂载应用以应用新属性
        await unmount(props)
        await mount(props)

        console.log('✅ Vue2 子应用更新完成')
    } catch (error) {
        console.error('❌ Vue2 子应用更新失败:', error)
        throw error
    }
}

/**
 * 获取挂载容器
 */
function getContainer(container) {
    if (!container) {
        return document.getElementById('vue2-app') || document.getElementById('app')
    }

    if (typeof container === 'string') {
        return document.querySelector(container)
    }

    return container
}

/**
 * 初始化应用资源
 */
async function initializeResources(props) {
    // 初始化主题
    if (props.theme) {
        document.documentElement.setAttribute('data-theme', props.theme)
    }

    // 初始化用户上下文
    if (props.user) {
        // 设置用户相关的全局状态
    }
}

/**
 * 设置全局错误处理
 */
function setupErrorHandling() {
    // Vue 2 全局错误处理
    Vue.config.errorHandler = (error, vm, info) => {
        console.error('Vue2 全局错误:', error, info)
        handleVueError(error, vm, info)
    }

    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    window.addEventListener('error', handleGlobalError)
}

/**
 * 处理 Vue 应用错误
 */
function handleVueError(error, vm, info) {
    console.error('Vue2 组件错误:', error, info)
    // 可以发送到错误监控系统
}

/**
 * 处理未捕获的 Promise 拒绝
 */
function handleUnhandledRejection(event) {
    console.error('Vue2 子应用未捕获的 Promise 拒绝:', event.reason)
}

/**
 * 处理全局错误
 */
function handleGlobalError(event) {
    console.error('Vue2 子应用全局错误:', event.error)
}

/**
 * 清理事件监听器
 */
function cleanupEventListeners() {
    window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    window.removeEventListener('error', handleGlobalError)
}

/**
 * 清理定时器
 */
function cleanupTimers() {
    // 清理可能存在的定时器
    // 这里可以维护一个定时器列表进行统一清理
}

/**
 * 触发生命周期事件
 */
function dispatchLifecycleEvent(type, props) {
    const event = new CustomEvent(`vue2-app:${type}`, {
        detail: { props, timestamp: Date.now() }
    })
    window.dispatchEvent(event)
}

/**
 * 获取应用状态
 */
export function getAppStatus() {
    return {
        name: 'sub-app-vue2',
        isBootstrapped,
        isMounted,
        framework: 'Vue2',
        version: '2.7.0'
    }
}

// 独立运行模式
if (!window.__POWERED_BY_MICRO_CORE__) {
    console.log('🏃 Vue2 应用以独立模式运行')

    // 等待 DOM 加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            mount({
                container: '#app',
                basename: '/',
                theme: 'light'
            })
        })
    } else {
        mount({
            container: '#app',
            basename: '/',
            theme: 'light'
        })
    }
}

// 暴露给微前端框架的接口
if (typeof window !== 'undefined') {
    window.__VUE2_MICRO_APP__ = {
        bootstrap,
        mount,
        unmount,
        update,
        getAppStatus
    }
}

// 导出生命周期函数供微前端框架使用
export default {
    bootstrap,
    mount,
    unmount,
    update,
    getAppStatus
}