# @micro-core/builder-rollup

Rollup builder for Micro-Core micro-frontend architecture. This package provides seamless integration between Rollup build tool and Micro-Core's micro-frontend capabilities.

## Features

- 🚀 **High Performance**: Leverages Rollup's tree-shaking and optimization capabilities
- 📦 **ES Modules**: Native ESM support with code splitting
- 🔧 **Flexible Configuration**: Supports custom Rollup configurations
- 🔄 **Watch Mode**: Development server with hot reloading
- 🎯 **Micro-Frontend Ready**: Built-in support for micro-frontend patterns
- 📊 **Build Analytics**: Detailed build statistics and performance monitoring
- 🛡️ **Type Safe**: Full TypeScript support with comprehensive type definitions

## Installation

```bash
npm install @micro-core/builder-rollup
# or
yarn add @micro-core/builder-rollup
# or
pnpm add @micro-core/builder-rollup
```

## Quick Start

### Basic Usage

```javascript
// rollup.config.js
import microCore from '@micro-core/builder-rollup';

export default {
  input: 'src/index.js',
  output: {
    dir: 'dist',
    format: 'es'
  },
  plugins: [
    microCore({
      appName: 'my-micro-app',
      // Additional micro-frontend configuration
    })
  ]
};
```

### With TypeScript

```typescript
// rollup.config.ts
import { defineConfig } from 'rollup';
import microCore from '@micro-core/builder-rollup';

export default defineConfig({
  input: 'src/index.ts',
  output: {
    dir: 'dist',
    format: 'es',
    sourcemap: true
  },
  plugins: [
    microCore({
      appName: 'my-typescript-app',
      format: 'es',
      target: 'es2020'
    })
  ]
});
```

## Configuration Options

### RollupBuilderConfig

```typescript
interface RollupBuilderConfig {
  // Base configuration
  appName: string;                    // Application name
  entry?: string;                     // Entry point (default: 'src/index.js')
  outDir?: string;                    // Output directory (default: 'dist')
  mode?: 'development' | 'production'; // Build mode
  
  // Rollup-specific options
  rollupConfig?: RollupOptions;       // Custom Rollup configuration
  plugins?: Plugin[];                 // Additional Rollup plugins
  format?: 'es' | 'cjs' | 'umd' | 'iife'; // Output format
  target?: string;                    // Target environment
  externals?: string[];               // External dependencies
  
  // Micro-frontend options
  federation?: boolean;               // Enable module federation
  shared?: Record<string, any>;       // Shared dependencies
  remotes?: Record<string, string>;   // Remote applications
}
```

### Development Server Options

```typescript
interface DevServerConfig {
  port?: number;                      // Server port (default: 3000)
  host?: string;                      // Server host (default: 'localhost')
  open?: boolean;                     // Open browser (default: false)
  watch?: {
    include?: string[];               // Files to watch
    exclude?: string[];               // Files to ignore
  };
}
```

## API Reference

### RollupBuilder Class

#### Constructor

```typescript
constructor(options?: RollupBuilderOptions)
```

Creates a new RollupBuilder instance with optional configuration.

#### Methods

##### build(config: BaseBuilderConfig): Promise<BuildResult>

Executes a production build.

```typescript
const builder = new RollupBuilder();
const result = await builder.build({
  id: 'my-app',
  name: 'my-app',
  entry: './src/index.js',
  outDir: './dist',
  mode: 'production'
});

console.log('Build completed:', result.success);
console.log('Output files:', result.outputs);
console.log('Build stats:', result.stats);
```

##### serve(config: BaseBuilderConfig & { devServer?: DevServerConfig }): Promise<any>

Starts the development server with watch mode.

```typescript
const server = await builder.serve({
  id: 'my-app',
  name: 'my-app',
  entry: './src/index.js',
  outDir: './dist',
  mode: 'development',
  devServer: {
    port: 3000,
    host: 'localhost',
    open: true
  }
});
```

##### stop(): Promise<void>

Stops the development server.

```typescript
await builder.stop();
```

##### getStatus(): BuilderStatus

Returns the current builder status.

```typescript
const status = builder.getStatus();
console.log('Builder status:', status);
```

## Advanced Usage

### Custom Rollup Configuration

```javascript
// rollup.config.js
import microCore from '@micro-core/builder-rollup';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import typescript from '@rollup/plugin-typescript';

export default {
  input: 'src/index.ts',
  output: {
    dir: 'dist',
    format: 'es',
    sourcemap: true
  },
  plugins: [
    resolve({
      browser: true,
      preferBuiltins: false
    }),
    commonjs(),
    typescript(),
    microCore({
      appName: 'advanced-app',
      rollupConfig: {
        // Additional Rollup configuration
        treeshake: {
          moduleSideEffects: false
        },
        external: ['react', 'react-dom']
      }
    })
  ]
};
```

### Module Federation Setup

```javascript
// Main application
export default {
  plugins: [
    microCore({
      appName: 'main-app',
      federation: true,
      shared: {
        'react': { singleton: true },
        'react-dom': { singleton: true }
      },
      remotes: {
        'micro-app-1': 'http://localhost:3001/remoteEntry.js',
        'micro-app-2': 'http://localhost:3002/remoteEntry.js'
      }
    })
  ]
};

// Micro application
export default {
  plugins: [
    microCore({
      appName: 'micro-app-1',
      federation: true,
      shared: {
        'react': { singleton: true },
        'react-dom': { singleton: true }
      },
      exposes: {
        './Component': './src/Component'
      }
    })
  ]
};
```

### Performance Optimization

```javascript
export default {
  plugins: [
    microCore({
      appName: 'optimized-app',
      rollupConfig: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            utils: ['lodash', 'date-fns']
          }
        },
        treeshake: {
          moduleSideEffects: false,
          propertyReadSideEffects: false,
          unknownGlobalSideEffects: false
        }
      }
    })
  ]
};
```

## Build Output

The builder generates the following output structure:

```
dist/
├── main.[hash].js              # Main application bundle
├── main.[hash].js.map          # Source map
├── chunk-[name].[hash].js      # Code-split chunks
├── assets/
│   ├── style.[hash].css        # Extracted CSS
│   └── [name].[hash].[ext]     # Static assets
└── micro-app-manifest.json     # Micro-frontend manifest
```

### Manifest File

The generated manifest contains metadata for micro-frontend integration:

```json
{
  "name": "my-micro-app",
  "version": "1.0.0",
  "type": "micro-frontend",
  "entry": "main.js",
  "assets": {
    "js": ["main.[hash].js"],
    "css": ["assets/style.[hash].css"]
  },
  "shared": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  },
  "buildTime": "2025-01-26T10:30:00.000Z",
  "buildHash": "abc123def456"
}
```

## Integration Examples

### With React

```jsx
// src/App.jsx
import React from 'react';
import { createRoot } from 'react-dom/client';

const App = () => {
  return (
    <div>
      <h1>My Micro Frontend App</h1>
      <p>Built with Rollup and Micro-Core</p>
    </div>
  );
};

// Micro-frontend lifecycle hooks
export const mount = (element) => {
  const root = createRoot(element);
  root.render(<App />);
  return root;
};

export const unmount = (root) => {
  root.unmount();
};

export default App;
```

### With Vue

```vue
<!-- src/App.vue -->
<template>
  <div>
    <h1>My Vue Micro Frontend</h1>
    <p>Built with Rollup and Micro-Core</p>
  </div>
</template>

<script>
import { createApp } from 'vue';
import App from './App.vue';

// Micro-frontend lifecycle hooks
export const mount = (element) => {
  const app = createApp(App);
  app.mount(element);
  return app;
};

export const unmount = (app) => {
  app.unmount();
};

export default App;
</script>
```

## Troubleshooting

### Common Issues

#### Build Fails with Module Resolution Error

```bash
Error: Could not resolve './Component' from src/index.js
```

**Solution**: Ensure all imports use correct file extensions or configure Rollup's resolve plugin:

```javascript
import resolve from '@rollup/plugin-node-resolve';

export default {
  plugins: [
    resolve({
      extensions: ['.js', '.jsx', '.ts', '.tsx']
    }),
    microCore({...})
  ]
};
```

#### Watch Mode Not Working

**Solution**: Check file permissions and ensure watch patterns are correct:

```javascript
microCore({
  appName: 'my-app',
  devServer: {
    watch: {
      include: ['src/**/*'],
      exclude: ['node_modules/**', 'dist/**']
    }
  }
})
```

#### Large Bundle Size

**Solution**: Enable tree-shaking and code splitting:

```javascript
export default {
  output: {
    manualChunks: (id) => {
      if (id.includes('node_modules')) {
        return 'vendor';
      }
    }
  },
  treeshake: {
    moduleSideEffects: false
  }
};
```

## Performance Tips

1. **Enable Tree Shaking**: Use ES modules and avoid side effects
2. **Code Splitting**: Split vendor and application code
3. **Bundle Analysis**: Use Rollup's bundle analyzer
4. **Optimize Dependencies**: Mark large dependencies as external
5. **Source Maps**: Use external source maps in production

## Migration Guide

### From Webpack

```javascript
// Before (webpack.config.js)
module.exports = {
  entry: './src/index.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].[contenthash].js'
  },
  plugins: [
    new MicroCoreWebpackPlugin({
      appName: 'my-app'
    })
  ]
};

// After (rollup.config.js)
export default {
  input: './src/index.js',
  output: {
    dir: 'dist',
    format: 'es',
    entryFileNames: '[name].[hash].js'
  },
  plugins: [
    microCore({
      appName: 'my-app'
    })
  ]
};
```

## Contributing

Please read our [Contributing Guide](../../CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE](../../LICENSE) file for details.

## Support

- 📖 [Documentation](https://micro-core.dev/builders/rollup)
- 🐛 [Issue Tracker](https://github.com/echo008/micro-core/issues)
- 💬 [Discussions](https://github.com/echo008/micro-core/discussions)
- 📧 [Email Support](mailto:<EMAIL>)

---

**Built with ❤️ by the Micro-Core Team**
