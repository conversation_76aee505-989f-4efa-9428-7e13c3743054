/**
 * 共享工具包 - 公共基础设施层
 *
 * @description 提供微前端项目的公共工具、类型定义、常量和错误处理机制
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import type { AppConfig, AppInstance } from '../types';
/**
 * 验证应用配置
 * @param config 应用配置
 */
export declare function validateAppConfig(config: AppConfig): {
    valid: boolean;
    errors?: string[];
};
/**
 * 标准化应用配置
 * @param config 应用配置
 */
export declare function normalizeAppConfig(config: AppConfig): Required<AppConfig>;
/**
 * 检查应用是否应该激活
 * @param config 应用配置
 * @param location 当前位置
 */
export declare function shouldActivateApp(config: AppConfig, location?: Location): boolean;
/**
 * 获取应用状态描述
 * @param status 应用状态
 */
export declare function getAppStatusDescription(status: string): string;
/**
 * 检查应用状态是否为错误状态
 * @param status 应用状态
 */
export declare function isAppErrorStatus(status: string): boolean;
/**
 * 检查应用状态是否为活跃状态
 * @param status 应用状态
 */
export declare function isAppActiveStatus(status: string): boolean;
/**
 * 检查应用状态是否为过渡状态
 * @param status 应用状态
 */
export declare function isAppTransitionStatus(status: string): boolean;
/**
 * 创建应用实例
 * @param config 应用配置
 */
export declare function createAppInstance(config: AppConfig): AppInstance;
/**
 * 更新应用实例状态
 * @param app 应用实例
 * @param status 新状态
 */
export declare function updateAppStatus(app: AppInstance, status: string): void;
/**
 * 记录应用错误
 * @param app 应用实例
 * @param error 错误信息
 */
export declare function recordAppError(app: AppInstance, error: Error): void;
/**
 * 获取应用运行时间
 * @param app 应用实例
 */
export declare function getAppRuntime(app: AppInstance): number;
/**
 * 获取应用性能指标
 * @param app 应用实例
 */
export declare function getAppPerformanceMetrics(app: AppInstance): {
    loadTime: number;
    mountTime: number;
    unmountTime: number;
    totalTime: number;
    runtime: number;
    errorRate: number;
};
//# sourceMappingURL=app-management-utils.d.ts.map