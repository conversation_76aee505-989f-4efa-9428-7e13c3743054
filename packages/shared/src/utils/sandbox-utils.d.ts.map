{"version": 3, "file": "sandbox-utils.d.ts", "sourceRoot": "", "sources": ["sandbox-utils.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,KAAK,EAAE,aAAa,EAAE,cAAc,EAAE,eAAe,EAAE,WAAW,EAAE,MAAM,UAAU,CAAC;AAE5F;;GAEG;AACH,MAAM,WAAW,cAAc;IAC3B,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,IAAI,EAAE,WAAW,CAAC;IAClB,aAAa;IACb,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,cAAc;IACd,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;IAC3B,cAAc;IACd,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;IAC3B,eAAe;IACf,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,YAAY;IACZ,aAAa,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CACvC;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IACzB,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,IAAI,EAAE,WAAW,CAAC;IAClB,WAAW;IACX,MAAM,EAAE,OAAO,CAAC;IAChB,WAAW;IACX,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW;IACX,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,WAAW;IACX,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,aAAa;IACb,gBAAgB,EAAE,MAAM,CAAC;IACzB,aAAa;IACb,WAAW,CAAC,EAAE;QACV,IAAI,EAAE,MAAM,CAAC;QACb,KAAK,EAAE,MAAM,CAAC;KACjB,CAAC;IACF,WAAW;IACX,WAAW,CAAC,EAAE;QACV,oBAAoB,EAAE,MAAM,CAAC;QAC7B,kBAAkB,EAAE,MAAM,CAAC;KAC9B,CAAC;CACL;AAED;;GAEG;AACH,eAAO,MAAM,wBAAwB,UAgBpC,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,wBAAwB,UAKpC,CAAC;AAEF;;;;GAIG;AACH,wBAAgB,qBAAqB,CAAC,MAAM,EAAE,aAAa,GAAG,IAAI,CAgBjE;AAED;;;;GAIG;AACH,wBAAgB,sBAAsB,CAAC,OAAO,EAAE,cAAc,GAAG,aAAa,CAwB7E;AAED;;;;GAIG;AACH,wBAAgB,sBAAsB,CAAC,IAAI,EAAE,WAAW,GAAG,OAAO,CAiBjE;AAED;;;GAGG;AACH,wBAAgB,yBAAyB,IAAI,WAAW,CAkBvD;AAED;;;;;;GAMG;AACH,wBAAgB,oBAAoB,CAChC,IAAI,EAAE,MAAM,EACZ,MAAM,GAAE,MAA0B,EAClC,QAAQ,GAAE,QAA8B,GACzC,cAAc,CAQhB;AAED;;;;;;GAMG;AACH,wBAAgB,uBAAuB,CACnC,GAAG,EAAE,MAAM,EACX,SAAS,GAAE,MAAM,EAA6B,EAC9C,SAAS,GAAE,MAAM,EAA6B,GAC/C,OAAO,CAQT;AAED;;;;;;GAMG;AACH,wBAAgB,qBAAqB,CACjC,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAC5B,SAAS,GAAE,MAAM,EAA6B,EAC9C,SAAS,GAAE,MAAM,EAA6B,GAC/C,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAUrB;AAED;;;;;GAKG;AACH,wBAAgB,8BAA8B,CAC1C,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,GAClC,MAAM,CAcR;AAED;;;;GAIG;AACH,wBAAgB,yBAAyB,CAAC,IAAI,EAAE,MAAM,GAAG;IACrD,IAAI,EAAE,OAAO,CAAC;IACd,MAAM,EAAE,MAAM,EAAE,CAAC;CACpB,CAyCA;AAED;;;GAGG;AACH,wBAAgB,yBAAyB,CAAC,OAAO,EAAE,cAAc,GAAG,IAAI,CAoBvE;AAED;;;;GAIG;AACH,wBAAgB,4BAA4B,CAAC,OAAO,EAAE,eAAe,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAa1F;AAED;;;GAGG;AACH,wBAAgB,cAAc,IAAI;IAAE,IAAI,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,MAAM,CAAA;CAAE,GAAG,IAAI,CASvE;AAED;;;;;GAKG;AACH,wBAAgB,yBAAyB,CACrC,QAAQ,EAAE,eAAe,EACzB,QAAQ,EAAE,eAAe,GAC1B;IACC,MAAM,EAAE,MAAM,CAAC;IACf,UAAU,EAAE,MAAM,CAAC;IACnB,OAAO,EAAE;QACL,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC9B,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KACjC,CAAC;CACL,CAeA"}