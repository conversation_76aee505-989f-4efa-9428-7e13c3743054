import React, { useEffect, useState } from 'react'

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState({
    users: 1250,
    orders: 3420,
    revenue: 89500,
    growth: 12.5
  })

  const [chartData, setChartData] = useState<number[]>([])

  useEffect(() => {
    // 模拟数据加载
    const loadData = () => {
      const data = Array.from({ length: 12 }, () => Math.floor(Math.random() * 100))
      setChartData(data)
    }

    loadData()
    const interval = setInterval(loadData, 5000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="dashboard">
      <h3>仪表板概览</h3>

      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">👥</div>
          <div className="stat-content">
            <div className="stat-number">{stats.users.toLocaleString()}</div>
            <div className="stat-label">总用户数</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">📦</div>
          <div className="stat-content">
            <div className="stat-number">{stats.orders.toLocaleString()}</div>
            <div className="stat-label">订单总数</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <div className="stat-number">¥{stats.revenue.toLocaleString()}</div>
            <div className="stat-label">总收入</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">📈</div>
          <div className="stat-content">
            <div className="stat-number">{stats.growth}%</div>
            <div className="stat-label">增长率</div>
          </div>
        </div>
      </div>

      <div className="chart-section">
        <h4>月度趋势</h4>
        <div className="simple-chart">
          {chartData.map((value, index) => (
            <div
              key={index}
              className="chart-bar"
              style={{ height: `${value}%` }}
              title={`月份 ${index + 1}: ${value}%`}
            />
          ))}
        </div>
      </div>

      <div className="recent-activity">
        <h4>最近活动</h4>
        <div className="activity-list">
          <div className="activity-item">
            <span className="activity-time">2分钟前</span>
            <span className="activity-text">用户 张三 完成了订单支付</span>
          </div>
          <div className="activity-item">
            <span className="activity-time">5分钟前</span>
            <span className="activity-text">新用户 李四 注册成功</span>
          </div>
          <div className="activity-item">
            <span className="activity-time">10分钟前</span>
            <span className="activity-text">订单 #12345 已发货</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
