# 框架示例演练场

Micro-Core 框架示例演练场提供了各种主流前端框架的完整微前端集成示例，让您可以快速了解和体验不同框架的集成方式。

## 📋 目录

- [演练场概述](#演练场概述)
- [React 示例](#react-示例)
- [Vue 示例](#vue-示例)
- [Angular 示例](#angular-示例)
- [多框架协作](#多框架协作)
- [实时编辑器](#实时编辑器)
- [最佳实践](#最佳实践)

## 演练场概述

### 🎯 功能特性

```typescript
// 演练场核心功能
const playgroundFeatures = {
  // 框架支持
  frameworks: [
    'React 16.8+ / 17.x / 18.x',
    'Vue 2.7+ / 3.x',
    'Angular 12+',
    'Svelte 3.x+',
    'Solid.js 1.x+',
    '原生 HTML/JS'
  ],
  
  // 交互功能
  interactive: [
    '实时代码编辑',
    '即时预览',
    '错误提示',
    '性能监控',
    '调试工具'
  ],
  
  // 示例类型
  examples: [
    '基础集成',
    '高级特性',
    '性能优化',
    '错误处理',
    '最佳实践'
  ]
}
```

### 🚀 快速开始

```html
<!-- 演练场入口 -->
<div id="playground-container">
  <div class="playground-header">
    <h1>Micro-Core 框架示例演练场</h1>
    <div class="framework-selector">
      <button class="framework-btn active" data-framework="react">React</button>
      <button class="framework-btn" data-framework="vue">Vue</button>
      <button class="framework-btn" data-framework="angular">Angular</button>
      <button class="framework-btn" data-framework="svelte">Svelte</button>
    </div>
  </div>
  
  <div class="playground-content">
    <div class="code-editor">
      <div class="editor-tabs">
        <div class="tab active" data-file="main.js">主应用</div>
        <div class="tab" data-file="micro-app.js">微应用</div>
        <div class="tab" data-file="config.js">配置</div>
      </div>
      <div class="editor-content">
        <textarea id="code-editor" placeholder="在这里编写代码..."></textarea>
      </div>
    </div>
    
    <div class="preview-panel">
      <div class="preview-header">
        <span>实时预览</span>
        <div class="preview-controls">
          <button id="run-btn">运行</button>
          <button id="reset-btn">重置</button>
          <button id="share-btn">分享</button>
        </div>
      </div>
      <iframe id="preview-frame" src="about:blank"></iframe>
    </div>
  </div>
</div>
```

## React 示例

### 基础 React 微应用

```typescript
// React 主应用示例
import React from 'react'
import ReactDOM from 'react-dom/client'
import { MicroCore } from '@micro-core/core'
import { ReactAdapter } from '@micro-core/adapter-react'

// 创建微前端实例
const microCore = new MicroCore({
  adapters: [new ReactAdapter()]
})

// 注册 React 微应用
microCore.registerApp({
  name: 'react-todo',
  entry: 'http://localhost:3001/index.js',
  container: '#react-container',
  activeWhen: '/react-todo',
  framework: 'react',
  props: {
    title: 'React Todo App',
    theme: 'light'
  }
})

// React 主应用组件
function MainApp() {
  const [currentRoute, setCurrentRoute] = React.useState('/')
  
  React.useEffect(() => {
    // 启动微前端
    microCore.start()
    
    // 监听路由变化
    const handleRouteChange = (event) => {
      setCurrentRoute(event.detail.path)
    }
    
    window.addEventListener('micro-route-change', handleRouteChange)
    return () => window.removeEventListener('micro-route-change', handleRouteChange)
  }, [])
  
  return (
    <div className="main-app">
      <header className="app-header">
        <h1>Micro-Core React 示例</h1>
        <nav>
          <button onClick={() => microCore.navigateTo('/')}>首页</button>
          <button onClick={() => microCore.navigateTo('/react-todo')}>Todo 应用</button>
        </nav>
      </header>
      
      <main className="app-content">
        <div id="react-container"></div>
        {currentRoute === '/' && (
          <div className="welcome">
            <h2>欢迎使用 Micro-Core</h2>
            <p>点击导航栏体验 React 微应用</p>
          </div>
        )}
      </main>
    </div>
  )
}

// 启动应用
const root = ReactDOM.createRoot(document.getElementById('root'))
root.render(<MainApp />)
```

### React 微应用示例

```typescript
// React 微应用 (react-todo)
import React, { useState, useEffect } from 'react'
import ReactDOM from 'react-dom/client'

interface TodoItem {
  id: number
  text: string
  completed: boolean
}

function TodoApp({ title = 'Todo App', theme = 'light' }) {
  const [todos, setTodos] = useState<TodoItem[]>([])
  const [inputValue, setInputValue] = useState('')
  
  // 添加待办事项
  const addTodo = () => {
    if (inputValue.trim()) {
      setTodos([...todos, {
        id: Date.now(),
        text: inputValue,
        completed: false
      }])
      setInputValue('')
    }
  }
  
  // 切换完成状态
  const toggleTodo = (id: number) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ))
  }
  
  // 删除待办事项
  const deleteTodo = (id: number) => {
    setTodos(todos.filter(todo => todo.id !== id))
  }
  
  return (
    <div className={`todo-app theme-${theme}`}>
      <h2>{title}</h2>
      
      <div className="todo-input">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && addTodo()}
          placeholder="添加新的待办事项..."
        />
        <button onClick={addTodo}>添加</button>
      </div>
      
      <ul className="todo-list">
        {todos.map(todo => (
          <li key={todo.id} className={todo.completed ? 'completed' : ''}>
            <input
              type="checkbox"
              checked={todo.completed}
              onChange={() => toggleTodo(todo.id)}
            />
            <span>{todo.text}</span>
            <button onClick={() => deleteTodo(todo.id)}>删除</button>
          </li>
        ))}
      </ul>
      
      <div className="todo-stats">
        <span>总计: {todos.length}</span>
        <span>已完成: {todos.filter(t => t.completed).length}</span>
        <span>未完成: {todos.filter(t => !t.completed).length}</span>
      </div>
    </div>
  )
}

// 微应用生命周期
export async function bootstrap() {
  console.log('React 微应用启动中...')
}

export async function mount(props: any) {
  const container = props.container
  const root = ReactDOM.createRoot(container)
  
  root.render(<TodoApp {...props} />)
  
  // 保存 root 实例用于卸载
  ;(window as any).__REACT_TODO_ROOT__ = root
}

export async function unmount() {
  const root = (window as any).__REACT_TODO_ROOT__
  if (root) {
    root.unmount()
    delete (window as any).__REACT_TODO_ROOT__
  }
}
```

## Vue 示例

### Vue 3 微应用示例

```vue
<!-- Vue 主应用 -->
<template>
  <div class="main-app">
    <header class="app-header">
      <h1>Micro-Core Vue 示例</h1>
      <nav>
        <button @click="navigateTo('/')">首页</button>
        <button @click="navigateTo('/vue-dashboard')">仪表板</button>
      </nav>
    </header>
    
    <main class="app-content">
      <div id="vue-container"></div>
      <div v-if="currentRoute === '/'" class="welcome">
        <h2>欢迎使用 Micro-Core</h2>
        <p>点击导航栏体验 Vue 微应用</p>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { MicroCore } from '@micro-core/core'
import { VueAdapter } from '@micro-core/adapter-vue'

const currentRoute = ref('/')
const microCore = new MicroCore({
  adapters: [new VueAdapter()]
})

onMounted(async () => {
  // 注册 Vue 微应用
  await microCore.registerApp({
    name: 'vue-dashboard',
    entry: 'http://localhost:3002/index.js',
    container: '#vue-container',
    activeWhen: '/vue-dashboard',
    framework: 'vue',
    props: {
      title: 'Vue 仪表板',
      apiUrl: 'https://api.example.com'
    }
  })
  
  // 启动微前端
  await microCore.start()
  
  // 监听路由变化
  window.addEventListener('micro-route-change', (event: any) => {
    currentRoute.value = event.detail.path
  })
})

const navigateTo = (path: string) => {
  microCore.navigateTo(path)
}
</script>
```

### Vue 微应用 (仪表板)

```vue
<!-- Vue 微应用 (vue-dashboard) -->
<template>
  <div class="dashboard">
    <h2>{{ title }}</h2>
    
    <div class="dashboard-grid">
      <div class="card">
        <h3>用户统计</h3>
        <div class="stat-number">{{ stats.users }}</div>
        <div class="stat-change positive">+12%</div>
      </div>
      
      <div class="card">
        <h3>订单数量</h3>
        <div class="stat-number">{{ stats.orders }}</div>
        <div class="stat-change positive">+8%</div>
      </div>
      
      <div class="card">
        <h3>收入</h3>
        <div class="stat-number">${{ stats.revenue }}</div>
        <div class="stat-change negative">-3%</div>
      </div>
      
      <div class="card">
        <h3>转化率</h3>
        <div class="stat-number">{{ stats.conversion }}%</div>
        <div class="stat-change positive">+5%</div>
      </div>
    </div>
    
    <div class="chart-container">
      <h3>销售趋势</h3>
      <canvas ref="chartCanvas" width="400" height="200"></canvas>
    </div>
    
    <div class="recent-activities">
      <h3>最近活动</h3>
      <ul>
        <li v-for="activity in activities" :key="activity.id">
          <span class="activity-time">{{ formatTime(activity.time) }}</span>
          <span class="activity-text">{{ activity.text }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'

interface Props {
  title?: string
  apiUrl?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Dashboard',
  apiUrl: ''
})

const chartCanvas = ref<HTMLCanvasElement>()
const stats = ref({
  users: 1234,
  orders: 567,
  revenue: 12345,
  conversion: 3.2
})

const activities = ref([
  { id: 1, time: new Date(Date.now() - 1000 * 60 * 5), text: '新用户注册' },
  { id: 2, time: new Date(Date.now() - 1000 * 60 * 15), text: '订单完成' },
  { id: 3, time: new Date(Date.now() - 1000 * 60 * 30), text: '支付成功' },
])

onMounted(async () => {
  // 模拟数据加载
  await loadDashboardData()
  
  // 绘制图表
  await nextTick()
  drawChart()
})

const loadDashboardData = async () => {
  // 模拟 API 调用
  if (props.apiUrl) {
    try {
      // const response = await fetch(`${props.apiUrl}/dashboard`)
      // const data = await response.json()
      // stats.value = data
      console.log('Loading data from:', props.apiUrl)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    }
  }
}

const drawChart = () => {
  if (!chartCanvas.value) return
  
  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return
  
  // 简单的折线图
  const data = [10, 25, 15, 40, 30, 45, 35, 50]
  const width = chartCanvas.value.width
  const height = chartCanvas.value.height
  
  ctx.clearRect(0, 0, width, height)
  ctx.strokeStyle = '#3b82f6'
  ctx.lineWidth = 2
  
  ctx.beginPath()
  data.forEach((value, index) => {
    const x = (index / (data.length - 1)) * width
    const y = height - (value / 60) * height
    
    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })
  ctx.stroke()
}

const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`
  
  const days = Math.floor(hours / 24)
  return `${days}天前`
}
</script>

<script lang="ts">
// 微应用生命周期
export async function bootstrap() {
  console.log('Vue 微应用启动中...')
}

export async function mount(props: any) {
  const { createApp } = await import('vue')
  const app = createApp(DashboardApp, props)
  
  app.mount(props.container)
  
  // 保存应用实例用于卸载
  ;(window as any).__VUE_DASHBOARD_APP__ = app
}

export async function unmount() {
  const app = (window as any).__VUE_DASHBOARD_APP__
  if (app) {
    app.unmount()
    delete (window as any).__VUE_DASHBOARD_APP__
  }
}
</script>
```

## Angular 示例

### Angular 微应用示例

```typescript
// Angular 主应用
import { Component, OnInit } from '@angular/core'
import { MicroCore } from '@micro-core/core'
import { AngularAdapter } from '@micro-core/adapter-angular'

@Component({
  selector: 'app-root',
  template: `
    <div class="main-app">
      <header class="app-header">
        <h1>Micro-Core Angular 示例</h1>
        <nav>
          <button (click)="navigateTo('/')">首页</button>
          <button (click)="navigateTo('/angular-crm')">CRM 系统</button>
        </nav>
      </header>
      
      <main class="app-content">
        <div id="angular-container"></div>
        <div *ngIf="currentRoute === '/'" class="welcome">
          <h2>欢迎使用 Micro-Core</h2>
          <p>点击导航栏体验 Angular 微应用</p>
        </div>
      </main>
    </div>
  `
})
export class AppComponent implements OnInit {
  currentRoute = '/'
  private microCore: MicroCore
  
  constructor() {
    this.microCore = new MicroCore({
      adapters: [new AngularAdapter()]
    })
  }
  
  async ngOnInit() {
    // 注册 Angular 微应用
    await this.microCore.registerApp({
      name: 'angular-crm',
      entry: 'http://localhost:3003/main.js',
      container: '#angular-container',
      activeWhen: '/angular-crm',
      framework: 'angular',
      props: {
        title: 'CRM 管理系统',
        permissions: ['read', 'write', 'admin']
      }
    })
    
    // 启动微前端
    await this.microCore.start()
    
    // 监听路由变化
    window.addEventListener('micro-route-change', (event: any) => {
      this.currentRoute = event.detail.path
    })
  }
  
  navigateTo(path: string) {
    this.microCore.navigateTo(path)
  }
}
```

### Angular 微应用 (CRM 系统)

```typescript
// Angular 微应用 (angular-crm)
import { Component, Input, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'

interface Customer {
  id: number
  name: string
  email: string
  phone: string
  status: 'active' | 'inactive' | 'pending'
  lastContact: Date
}

@Component({
  selector: 'app-crm',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="crm-system">
      <h2>{{ title }}</h2>
      
      <div class="crm-toolbar">
        <input
          [(ngModel)]="searchTerm"
          (input)="filterCustomers()"
          placeholder="搜索客户..."
          class="search-input"
        />
        <button (click)="showAddForm = !showAddForm" class="add-btn">
          添加客户
        </button>
      </div>
      
      <div *ngIf="showAddForm" class="add-form">
        <h3>添加新客户</h3>
        <form (ngSubmit)="addCustomer()" #customerForm="ngForm">
          <input
            [(ngModel)]="newCustomer.name"
            name="name"
            placeholder="客户姓名"
            required
          />
          <input
            [(ngModel)]="newCustomer.email"
            name="email"
            type="email"
            placeholder="邮箱地址"
            required
          />
          <input
            [(ngModel)]="newCustomer.phone"
            name="phone"
            placeholder="电话号码"
            required
          />
          <select [(ngModel)]="newCustomer.status" name="status">
            <option value="active">活跃</option>
            <option value="inactive">非活跃</option>
            <option value="pending">待处理</option>
          </select>
          <button type="submit" [disabled]="!customerForm.form.valid">
            添加
          </button>
          <button type="button" (click)="cancelAdd()">取消</button>
        </form>
      </div>
      
      <div class="customers-table">
        <table>
          <thead>
            <tr>
              <th>姓名</th>
              <th>邮箱</th>
              <th>电话</th>
              <th>状态</th>
              <th>最后联系</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let customer of filteredCustomers">
              <td>{{ customer.name }}</td>
              <td>{{ customer.email }}</td>
              <td>{{ customer.phone }}</td>
              <td>
                <span [class]="'status-' + customer.status">
                  {{ getStatusText(customer.status) }}
                </span>
              </td>
              <td>{{ customer.lastContact | date:'short' }}</td>
              <td>
                <button (click)="editCustomer(customer)">编辑</button>
                <button (click)="deleteCustomer(customer.id)">删除</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div class="crm-stats">
        <div class="stat-card">
          <h4>总客户数</h4>
          <span>{{ customers.length }}</span>
        </div>
        <div class="stat-card">
          <h4>活跃客户</h4>
          <span>{{ getActiveCustomers() }}</span>
        </div>
        <div class="stat-card">
          <h4>待处理</h4>
          <span>{{ getPendingCustomers() }}</span>
        </div>
      </div>
    </div>
  `
})
export class CrmComponent implements OnInit {
  @Input() title = 'CRM 管理系统'
  @Input() permissions: string[] = []
  
  customers: Customer[] = []
  filteredCustomers: Customer[] = []
  searchTerm = ''
  showAddForm = false
  
  newCustomer = {
    name: '',
    email: '',
    phone: '',
    status: 'active' as const
  }
  
  ngOnInit() {
    this.loadCustomers()
  }
  
  loadCustomers() {
    // 模拟客户数据
    this.customers = [
      {
        id: 1,
        name: '张三',
        email: '<EMAIL>',
        phone: '13800138000',
        status: 'active',
        lastContact: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2)
      },
      {
        id: 2,
        name: '李四',
        email: '<EMAIL>',
        phone: '13800138001',
        status: 'pending',
        lastContact: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5)
      },
      {
        id: 3,
        name: '王五',
        email: '<EMAIL>',
        phone: '13800138002',
        status: 'inactive',
        lastContact: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10)
      }
    ]
    
    this.filteredCustomers = [...this.customers]
  }
  
  filterCustomers() {
    if (!this.searchTerm) {
      this.filteredCustomers = [...this.customers]
    } else {
      this.filteredCustomers = this.customers.filter(customer =>
        customer.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        customer.email.toLowerCase().includes(this.searchTerm.toLowerCase())
      )
    }
  }
  
  addCustomer() {
    if (this.newCustomer.name && this.newCustomer.email && this.newCustomer.phone) {
      const customer: Customer = {
        id: Date.now(),
        ...this.newCustomer,
        lastContact: new Date()
      }
      
      this.customers.push(customer)
      this.filterCustomers()
      this.cancelAdd()
    }
  }
  
  cancelAdd() {
    this.showAddForm = false
    this.newCustomer = {
      name: '',
      email: '',
      phone: '',
      status: 'active'
    }
  }
  
  editCustomer(customer: Customer) {
    // 实现编辑功能
    console.log('编辑客户:', customer)
  }
  
  deleteCustomer(id: number) {
    if (confirm('确定要删除这个客户吗？')) {
      this.customers = this.customers.filter(c => c.id !== id)
      this.filterCustomers()
    }
  }
  
  getStatusText(status: string): string {
    const statusMap = {
      active: '活跃',
      inactive: '非活跃',
      pending: '待处理'
    }
    return statusMap[status as keyof typeof statusMap] || status
  }
  
  getActiveCustomers(): number {
    return this.customers.filter(c => c.status === 'active').length
  }
  
  getPendingCustomers(): number {
    return this.customers.filter(c => c.status === 'pending').length
  }
}

// 微应用生命周期
export async function bootstrap() {
  console.log('Angular 微应用启动中...')
}

export async function mount(props: any) {
  const { bootstrapApplication } = await import('@angular/platform-browser')
  
  const app = await bootstrapApplication(CrmComponent, {
    providers: [
      // 提供必要的服务
    ]
  })
  
  // 保存应用实例用于卸载
  ;(window as any).__ANGULAR_CRM_APP__ = app
}

export async function unmount() {
  const app = (window as any).__ANGULAR_CRM_APP__
  if (app) {
    app.destroy()
    delete (window as any).__ANGULAR_CRM_APP__
  }
}
```

## 多框架协作

### 跨框架通信示例

```typescript
// 多框架协作演示
class MultiFrameworkDemo {
  private microCore: MicroCore
  
  constructor() {
    this.microCore = new MicroCore({
      adapters: [
        new ReactAdapter(),
        new VueAdapter(),
        new AngularAdapter()
      ]
    })
    
    this.setupCommunication()
  }
  
  async init() {
    // 注册多个框架的微应用
    await Promise.all([
      this.registerReactApp(),
      this.registerVueApp(),
      this.registerAngularApp()
    ])
    
    await this.microCore.start()
  }
  
  private async registerReactApp() {
    await this.microCore.registerApp({
      name: 'react-header',
      entry: 'http://localhost:3001/index.js',
      container: '#header-container',
      activeWhen: () => true, // 始终激活
      framework: 'react'
    })
  }
  
  private async registerVueApp() {
    await this.microCore.registerApp({
      name: 'vue-sidebar',
      entry: 'http://localhost:3002/index.js',
      container: '#sidebar-container',
      activeWhen: () => true, // 始终激活
      framework: 'vue'
    })
  }
  
  private async registerAngularApp() {
    await this.microCore.registerApp({
      name: 'angular-content',
      entry: 'http://localhost:3003/main.js',
      container: '#content-container',
      activeWhen: '/content',
      framework: 'angular'
    })
  }
  
  private setupCommunication() {
    // 设置跨框架通信
    this.microCore.eventBus.on('user-login', (userData) => {
      // 通知所有微应用用户登录
      this.microCore.eventBus.emit('global-user-update', userData)
    })
    
    this.microCore.eventBus.on('theme-change', (theme) => {
      // 全局主题变更
      document.body.className = `theme-${theme}`
      this.microCore.eventBus.emit('global-theme-update', theme)
    })
    
    this.microCore.eventBus.on('navigation-request', (route) => {
      // 处理导航请求
      this.microCore.navigateTo(route)
    })
  }
}

// 启动多框架演示
const demo = new MultiFrameworkDemo()
demo.init()
```

## 实时编辑器

### 代码编辑器集成

```typescript
// 实时编辑器实现
class PlaygroundEditor {
  private editor: any
  private previewFrame: HTMLIFrameElement
  private currentFramework = 'react'
  
  constructor() {
    this.initEditor()
    this.initPreview()
    this.setupEventListeners()
  }
  
  private initEditor() {
    // 使用 Monaco Editor
    import('monaco-editor').then(monaco => {
      this.editor = monaco.editor.create(document.getElementById('code-editor'), {
        value: this.getDefaultCode(this.currentFramework),
        language: 'typescript',
        theme: 'vs-dark',
        automaticLayout: true,
        minimap: { enabled: false },
        scrollBeyondLastLine: false
      })
      
      // 监听代码变化
      this.editor.onDidChangeModelContent(() => {
        this.debouncePreview()
      })
    })
  }
  
  private initPreview() {
    this.previewFrame = document.getElementById('preview-frame') as HTMLIFrameElement
  }
  
  private setupEventListeners() {
    // 框架切换
    document.querySelectorAll('.framework-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const framework = (e.target as HTMLElement).dataset.framework
        if (framework) {
          this.switchFramework(framework)
        }
      })
    })
    
    // 运行按钮
    document.getElementById('run-btn')?.addEventListener('click', () => {
      this.runCode()
    })
    
    // 重置按钮
    document.getElementById('reset-btn')?.addEventListener('click', () => {
      this.resetCode()
    })
    
    // 分享按钮
    document.getElementById('share-btn')?.addEventListener('click', () => {
      this.shareCode()
    })
  }
  
  private switchFramework(framework: string) {
    this.currentFramework = framework
    
    // 更新按钮状态
    document.querySelectorAll('.framework-btn').forEach(btn => {
      btn.classList.remove('active')
    })
    document.querySelector(`[data-framework="${framework}"]`)?.classList.add('active')
    
    // 更新编辑器代码
    if (this.editor) {
      this.editor.setValue(this.getDefaultCode(framework))
    }
  }
  
  private getDefaultCode(framework: string): string {
    const templates = {
      react: `// React 微应用示例
import React, { useState } from 'react'
import ReactDOM from 'react-dom/client'
import { MicroCore } from '@micro-core/core'

function App() {
  const [count, setCount] = useState(0)
  
  return (
    <div style={{ padding: '20px' }}>
      <h2>React 微应用</h2>
      <p>计数器: {count}</p>
      <button onClick={() => setCount(count + 1)}>
        增加
      </button>
    </div>
  )
}

// 微应用生命周期
export async function mount(props) {
  const root = ReactDOM.createRoot(props.container)
  root.render(<App />)
  window.__REACT_APP_ROOT__ = root
}

export async function unmount() {
  const root = window.__REACT_APP_ROOT__
  if (root) {
    root.unmount()
    delete window.__REACT_APP_ROOT__
  }
}`,

      vue: `<!-- Vue 微应用示例 -->
<template>
  <div style="padding: 20px">
    <h2>Vue 微应用</h2>
    <p>计数器: {{ count }}</p>
    <button @click="increment">增加</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const count = ref(0)

const increment = () => {
  count.value++
}
</script>

<script>
// 微应用生命周期
export async function mount(props) {
  const { createApp } = await import('vue')
  const app = createApp(AppComponent, props)
  app.mount(props.container)
  window.__VUE_APP__ = app
}

export async function unmount() {
  const app = window.__VUE_APP__
  if (app) {
    app.unmount()
    delete window.__VUE_APP__
  }
}
</script>`,

      angular: `// Angular 微应用示例
import { Component } from '@angular/core'
import { CommonModule } from '@angular/common'

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule],
  template: \`
    <div style="padding: 20px">
      <h2>Angular 微应用</h2>
      <p>计数器: {{ count }}</p>
      <button (click)="increment()">增加</button>
    </div>
  \`
})
export class AppComponent {
  count = 0
  
  increment() {
    this.count++
  }
}

// 微应用生命周期
export async function mount(props) {
  const { bootstrapApplication } = await import('@angular/platform-browser')
  const app = await bootstrapApplication(AppComponent)
  window.__ANGULAR_APP__ = app
}

export async function unmount() {
  const app = window.__ANGULAR_APP__
  if (app) {
    app.destroy()
    delete window.__ANGULAR_APP__
  }
}`
    }
    
    return templates[framework] || templates.react
  }
  
  private debouncePreview = this.debounce(() => {
    this.updatePreview()
  }, 1000)
  
  private debounce(func: Function, wait: number) {
    let timeout: NodeJS.Timeout
    return (...args: any[]) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func.apply(this, args), wait)
    }
  }
  
  private updatePreview() {
    const code = this.editor.getValue()
    this.runCodeInPreview(code)
  }
  
  private runCode() {
    const code = this.editor.getValue()
    this.runCodeInPreview(code)
  }
  
  private runCodeInPreview(code: string) {
    const previewHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Micro-Core 预览</title>
  <style>
    body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; }
    .error { color: red; padding: 20px; background: #fee; border: 1px solid #fcc; }
  </style>
</head>
<body>
  <div id="app"></div>
  <script type="module">
    try {
      ${this.transformCode(code)}
    } catch (error) {
      document.getElementById('app').innerHTML = 
        '<div class="error">错误: ' + error.message + '</div>'
    }
  </script>
</body>
</html>`
    
    const blob = new Blob([previewHtml], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    this.previewFrame.src = url
  }
  
  private transformCode(code: string): string {
    // 简单的代码转换，实际项目中可能需要使用 Babel 等工具
    return code
      .replace(/import\s+.*?from\s+['"]@micro-core\/core['"];?/g, '')
      .replace(/export\s+async\s+function\s+mount/g, 'window.mount = async function')
      .replace(/export\s+async\s+function\s+unmount/g, 'window.unmount = async function')
  }
  
  private resetCode() {
    if (this.editor) {
      this.editor.setValue(this.getDefaultCode(this.currentFramework))
    }
  }
  
  private shareCode() {
    const code = this.editor.getValue()
    const shareData = {
      framework: this.currentFramework,
      code: code,
      timestamp: Date.now()
    }
    
    // 生成分享链接
    const shareUrl = `${window.location.origin}${window.location.pathname}?share=${btoa(JSON.stringify(shareData))}`
    
    // 复制到剪贴板
    navigator.clipboard.writeText(shareUrl).then(() => {
      alert('分享链接已复制到剪贴板！')
    }).catch(() => {
      // 降级方案
      const textarea = document.createElement('textarea')
      textarea.value = shareUrl
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
      alert('分享链接已复制到剪贴板！')
    })
  }
  
  // 从 URL 加载分享的代码
  loadSharedCode() {
    const urlParams = new URLSearchParams(window.location.search)
    const shareData = urlParams.get('share')
    
    if (shareData) {
      try {
        const data = JSON.parse(atob(shareData))
        this.currentFramework = data.framework
        this.switchFramework(data.framework)
        
        if (this.editor) {
          this.editor.setValue(data.code)
        }
      } catch (error) {
        console.error('Failed to load shared code:', error)
      }
    }
  }
}

// 初始化编辑器
const editor = new PlaygroundEditor()
editor.loadSharedCode()
```

## 最佳实践

### 🎯 开发建议

1. **代码组织**
   - 保持组件小而专注
   - 使用 TypeScript 提供类型安全
   - 遵循各框架的最佳实践

2. **性能优化**
   - 使用懒加载减少初始包大小
   - 实现适当的缓存策略
   - 避免不必要的重渲染

3. **错误处理**
   - 实现全局错误边界
   - 提供友好的错误提示
   - 记录错误日志用于调试

4. **测试策略**
   - 编写单元测试覆盖核心逻辑
   - 实现集成测试验证应用协作
   - 使用 E2E 测试确保用户体验

### 📚 学习资源

- [React 官方文档](https://react.dev/)
- [Vue 官方文档](https://vuejs.org/)
- [Angular 官方文档](https://angular.io/)
- [Micro-Core API 参考](/api/)
- [最佳实践指南](/guide/best-practices/)

### 🔗 相关链接

- [配置生成器](/playground/config-generator)
- [高级特性演练](/playground/advanced-features)
- [性能测试](/playground/performance-test)
- [调试面板](/playground/debug-panel)

---

通过这个框架示例演练场，您可以快速体验和学习如何在不同框架中集成 Micro-Core，为您的项目选择最适合的技术方案。
