# 快速开始

通过这个5分钟的快速教程，你将学会如何创建第一个 micro-core 微前端应用。

## 🎯 目标

在本教程结束时，你将拥有：
- 一个运行中的主应用
- 两个独立的微应用（React 和 Vue）
- 完整的路由和通信功能

## 📋 前置要求

- Node.js 16+ 
- npm 或 yarn 或 pnpm
- 基础的 JavaScript/TypeScript 知识

## 🚀 第一步：创建主应用

### 1.1 初始化项目

```bash
# 创建项目目录
mkdir my-micro-frontend
cd my-micro-frontend

# 初始化主应用
mkdir main-app
cd main-app
npm init -y

# 安装依赖
npm install @micro-core/core @micro-core/plugins
npm install -D vite typescript @types/node
```

### 1.2 创建主应用入口

创建 `src/main.ts`：

```typescript
import { MicroCore } from '@micro-core/core';
import { RouterPlugin, CommunicationPlugin } from '@micro-core/plugins';

// 创建微前端核心实例
const microCore = new MicroCore({
  debug: true, // 开发环境启用调试
  sandbox: true, // 启用沙箱隔离
  prefetch: true // 启用预加载
});

// 注册插件
microCore.use(new RouterPlugin({
  mode: 'history',
  base: '/'
}));

microCore.use(new CommunicationPlugin({
  enableBroadcast: true
}));

// 注册微应用
microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001/index.js',
  container: '#react-app-container',
  activeWhen: '/react',
  framework: 'react'
});

microCore.registerApp({
  name: 'vue-app', 
  entry: 'http://localhost:3002/index.js',
  container: '#vue-app-container',
  activeWhen: '/vue',
  framework: 'vue'
});

// 启动应用
microCore.start();

// 全局暴露实例（用于调试）
(window as any).microCore = microCore;
```

### 1.3 创建 HTML 模板

创建 `index.html`：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Micro Frontend Demo</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
    }
    
    .header {
      background: #1e40af;
      color: white;
      padding: 1rem 2rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .nav {
      display: flex;
      gap: 1rem;
      margin-top: 1rem;
    }
    
    .nav a {
      color: white;
      text-decoration: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      transition: background 0.2s;
    }
    
    .nav a:hover,
    .nav a.active {
      background: rgba(255,255,255,0.2);
    }
    
    .container {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 2rem;
    }
    
    .app-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      min-height: 400px;
      padding: 2rem;
    }
    
    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #666;
    }
  </style>
</head>
<body>
  <header class="header">
    <h1>🚀 Micro Frontend Demo</h1>
    <nav class="nav">
      <a href="/" data-micro-link>首页</a>
      <a href="/react" data-micro-link>React 应用</a>
      <a href="/vue" data-micro-link>Vue 应用</a>
    </nav>
  </header>
  
  <div class="container">
    <!-- 首页内容 -->
    <div id="home-content" class="app-container">
      <h2>欢迎使用 Micro Core</h2>
      <p>这是一个微前端框架演示应用。点击上方导航体验不同的微应用。</p>
      
      <div style="margin-top: 2rem;">
        <h3>🎯 功能特性</h3>
        <ul style="margin-top: 1rem; padding-left: 2rem;">
          <li>🔧 多框架支持（React、Vue、Angular）</li>
          <li>🛡️ 沙箱隔离</li>
          <li>🚀 预加载优化</li>
          <li>💬 应用间通信</li>
          <li>🎨 样式隔离</li>
        </ul>
      </div>
    </div>
    
    <!-- React 应用容器 -->
    <div id="react-app-container" class="app-container" style="display: none;">
      <div class="loading">正在加载 React 应用...</div>
    </div>
    
    <!-- Vue 应用容器 -->
    <div id="vue-app-container" class="app-container" style="display: none;">
      <div class="loading">正在加载 Vue 应用...</div>
    </div>
  </div>
  
  <script type="module" src="/src/main.ts"></script>
  
  <script>
    // 简单的路由显示逻辑
    function showContainer(path) {
      // 隐藏所有容器
      document.getElementById('home-content').style.display = 'none';
      document.getElementById('react-app-container').style.display = 'none';
      document.getElementById('vue-app-container').style.display = 'none';
      
      // 更新导航状态
      document.querySelectorAll('.nav a').forEach(a => {
        a.classList.remove('active');
        if (a.getAttribute('href') === path) {
          a.classList.add('active');
        }
      });
      
      // 显示对应容器
      if (path === '/react') {
        document.getElementById('react-app-container').style.display = 'block';
      } else if (path === '/vue') {
        document.getElementById('vue-app-container').style.display = 'block';
      } else {
        document.getElementById('home-content').style.display = 'block';
      }
    }
    
    // 监听路由变化
    window.addEventListener('popstate', () => {
      showContainer(location.pathname);
    });
    
    // 初始化显示
    showContainer(location.pathname);
  </script>
</body>
</html>
```

### 1.4 配置构建工具

创建 `vite.config.ts`：

```typescript
import { defineConfig } from 'vite';

export default defineConfig({
  server: {
    port: 3000,
    cors: true
  },
  build: {
    target: 'es2015',
    rollupOptions: {
      external: ['@micro-core/core', '@micro-core/plugins']
    }
  }
});
```

创建 `package.json` 脚本：

```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  }
}
```

## 🔵 第二步：创建 React 微应用

### 2.1 初始化 React 应用

```bash
# 回到根目录
cd ..

# 创建 React 应用
mkdir react-app
cd react-app
npm init -y

# 安装依赖
npm install react react-dom
npm install -D vite @vitejs/plugin-react typescript @types/react @types/react-dom
```

### 2.2 创建 React 组件

创建 `src/App.tsx`：

```tsx
import React, { useState, useEffect } from 'react';

interface MicroCoreAPI {
  sendMessage: (target: string, message: any) => void;
  onMessage: (type: string, handler: (data: any) => void) => () => void;
  setState: (key: string, value: any) => void;
  getState: (key: string) => any;
}

declare global {
  interface Window {
    microCore: MicroCoreAPI;
  }
}

const App: React.FC = () => {
  const [count, setCount] = useState(0);
  const [messages, setMessages] = useState<string[]>([]);
  const [sharedData, setSharedData] = useState<any>(null);

  useEffect(() => {
    // 监听来自其他应用的消息
    const unsubscribe = window.microCore?.onMessage('COUNTER_UPDATE', (data) => {
      setMessages(prev => [...prev, `收到消息: ${JSON.stringify(data)}`]);
    });

    // 获取共享状态
    const shared = window.microCore?.getState('sharedCounter');
    if (shared) {
      setSharedData(shared);
    }

    return unsubscribe;
  }, []);

  const handleIncrement = () => {
    const newCount = count + 1;
    setCount(newCount);
    
    // 发送消息给其他应用
    window.microCore?.sendMessage('vue-app', {
      type: 'COUNTER_UPDATE',
      data: { count: newCount, from: 'react-app' }
    });
    
    // 更新共享状态
    window.microCore?.setState('sharedCounter', {
      value: newCount,
      updatedBy: 'react-app',
      timestamp: Date.now()
    });
  };

  const sendToVue = () => {
    window.microCore?.sendMessage('vue-app', {
      type: 'GREETING',
      data: { message: '你好，Vue！来自 React 的问候 👋' }
    });
  };

  return (
    <div style={{ padding: '2rem' }}>
      <div style={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        padding: '1.5rem',
        borderRadius: '8px',
        marginBottom: '2rem'
      }}>
        <h2>⚛️ React 微应用</h2>
        <p>这是一个独立的 React 应用，运行在微前端架构中</p>
      </div>

      <div style={{ display: 'grid', gap: '1.5rem', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))' }}>
        {/* 计数器功能 */}
        <div style={{ 
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          padding: '1.5rem',
          background: '#f8fafc'
        }}>
          <h3>📊 计数器</h3>
          <div style={{ fontSize: '2rem', margin: '1rem 0', color: '#667eea' }}>
            {count}
          </div>
          <button 
            onClick={handleIncrement}
            style={{
              background: '#667eea',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '1rem'
            }}
          >
            增加计数
          </button>
        </div>

        {/* 通信功能 */}
        <div style={{ 
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          padding: '1.5rem',
          background: '#f8fafc'
        }}>
          <h3>💬 应用通信</h3>
          <button 
            onClick={sendToVue}
            style={{
              background: '#4ade80',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '1rem',
              marginBottom: '1rem'
            }}
          >
            发送消息给 Vue
          </button>
          
          <div style={{ maxHeight: '150px', overflow: 'auto' }}>
            <h4>消息历史:</h4>
            {messages.length === 0 ? (
              <p style={{ color: '#64748b', fontStyle: 'italic' }}>暂无消息</p>
            ) : (
              messages.map((msg, index) => (
                <div key={index} style={{ 
                  background: '#e0f2fe',
                  padding: '0.5rem',
                  margin: '0.25rem 0',
                  borderRadius: '4px',
                  fontSize: '0.875rem'
                }}>
                  {msg}
                </div>
              ))
            )}
          </div>
        </div>

        {/* 共享状态 */}
        <div style={{ 
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          padding: '1.5rem',
          background: '#f8fafc',
          gridColumn: '1 / -1'
        }}>
          <h3>🔄 共享状态</h3>
          {sharedData ? (
            <div style={{ 
              background: '#f0f9ff',
              padding: '1rem',
              borderRadius: '6px',
              fontFamily: 'monospace'
            }}>
              <pre>{JSON.stringify(sharedData, null, 2)}</pre>
            </div>
          ) : (
            <p style={{ color: '#64748b', fontStyle: 'italic' }}>暂无共享状态</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default App;
```

### 2.3 创建微前端入口

创建 `src/main.tsx`：

```tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

// 微前端生命周期接口
interface MicroAppLifecycle {
  mount: (props: any) => Promise<void>;
  unmount: (props: any) => Promise<void>;
  bootstrap?: () => Promise<void>;
  update?: (props: any) => Promise<void>;
}

let root: ReactDOM.Root | null = null;

// 微前端生命周期实现
const microApp: MicroAppLifecycle = {
  async bootstrap() {
    console.log('React 应用引导完成');
  },

  async mount(props) {
    console.log('React 应用开始挂载', props);
    
    const container = props.container || document.getElementById('root');
    if (!container) {
      throw new Error('挂载容器不存在');
    }

    root = ReactDOM.createRoot(container);
    root.render(<App />);
  },

  async unmount(props) {
    console.log('React 应用开始卸载', props);
    
    if (root) {
      root.unmount();
      root = null;
    }
  },

  async update(props) {
    console.log('React 应用更新', props);
    // 可以在这里处理属性更新
  }
};

// 导出微前端接口
(window as any)['react-app'] = microApp;

// 独立运行时的处理
if (!window.__MICRO_CORE__) {
  microApp.mount({ container: document.getElementById('root') });
}
```

### 2.4 配置 React 应用

创建 `vite.config.ts`：

```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3001,
    cors: true
  },
  build: {
    lib: {
      entry: 'src/main.tsx',
      name: 'ReactApp',
      fileName: 'index',
      formats: ['umd']
    },
    rollupOptions: {
      external: ['react', 'react-dom'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM'
        }
      }
    }
  }
});
```

创建 `index.html`：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>React 微应用</title>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>
</html>
```

## 🟢 第三步：创建 Vue 微应用

### 3.1 初始化 Vue 应用

```bash
# 回到根目录
cd ..

# 创建 Vue 应用
mkdir vue-app
cd vue-app
npm init -y

# 安装依赖
npm install vue
npm install -D vite @vitejs/plugin-vue typescript
```

### 3.2 创建 Vue 组件

创建 `src/App.vue`：

```vue
<template>
  <div class="vue-app">
    <div class="header">
      <h2>💚 Vue 微应用</h2>
      <p>这是一个独立的 Vue 应用，运行在微前端架构中</p>
    </div>

    <div class="content-grid">
      <!-- 计数器功能 -->
      <div class="card">
        <h3>📊 计数器</h3>
        <div class="counter-display">{{ count }}</div>
        <button @click="handleIncrement" class="btn btn-primary">
          增加计数
        </button>
      </div>

      <!-- 通信功能 -->
      <div class="card">
        <h3>💬 应用通信</h3>
        <button @click="sendToReact" class="btn btn-success">
          发送消息给 React
        </button>
        
        <div class="message-history">
          <h4>消息历史:</h4>
          <div v-if="messages.length === 0" class="no-messages">
            暂无消息
          </div>
          <div 
            v-else
            v-for="(msg, index) in messages"
            :key="index"
            class="message-item"
          >
            {{ msg }}
          </div>
        </div>
      </div>

      <!-- 共享状态 -->
      <div class="card full-width">
        <h3>🔄 共享状态</h3>
        <div v-if="sharedData" class="shared-data">
          <pre>{{ JSON.stringify(sharedData, null, 2) }}</pre>
        </div>
        <div v-else class="no-data">
          暂无共享状态
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

interface MicroCoreAPI {
  sendMessage: (target: string, message: any) => void;
  onMessage: (type: string, handler: (data: any) => void) => () => void;
  setState: (key: string, value: any) => void;
  getState: (key: string) => any;
}

declare global {
  interface Window {
    microCore: MicroCoreAPI;
  }
}

const count = ref(0);
const messages = ref<string[]>([]);
const sharedData = ref<any>(null);

let unsubscribe: (() => void) | undefined;

onMounted(() => {
  // 监听来自其他应用的消息
  unsubscribe = window.microCore?.onMessage('COUNTER_UPDATE', (data) => {
    messages.value.push(`收到消息: ${JSON.stringify(data)}`);
  });

  // 监听问候消息
  const unsubscribeGreeting = window.microCore?.onMessage('GREETING', (data) => {
    messages.value.push(`问候消息: ${data.message}`);
  });

  // 获取共享状态
  const shared = window.microCore?.getState('sharedCounter');
  if (shared) {
    sharedData.value = shared;
  }
});

onUnmounted(() => {
  if (unsubscribe) {
    unsubscribe();
  }
});

const handleIncrement = () => {
  const newCount = count.value + 1;
  count.value = newCount;
  
  // 发送消息给其他应用
  window.microCore?.sendMessage('react-app', {
    type: 'COUNTER_UPDATE',
    data: { count: newCount, from: 'vue-app' }
  });
  
  // 更新共享状态
  window.microCore?.setState('sharedCounter', {
    value: newCount,
    updatedBy: 'vue-app',
    timestamp: Date.now()
  });
  
  // 更新本地共享状态显示
  sharedData.value = {
    value: newCount,
    updatedBy: 'vue-app',
    timestamp: Date.now()
  };
};

const sendToReact = () => {
  window.microCore?.sendMessage('react-app', {
    type: 'GREETING',
    data: { message: '你好，React！来自 Vue 的问候 💚' }
  });
};
</script>

<style scoped>
.vue-app {
  padding: 2rem;
}

.header {
  background: linear-gradient(135deg, #42b883 0%, #35495e 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.header h2 {
  margin: 0 0 0.5rem 0;
}

.header p {
  margin: 0;
  opacity: 0.9;
}

.content-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.card {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  background: #f8fafc;
}

.full-width {
  grid-column: 1 / -1;
}

.counter-display {
  font-size: 2rem;
  margin: 1rem 0;
  color: #42b883;
  font-weight: bold;
}

.btn {
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s;
}

.btn-primary {
  background: #42b883;
  color: white;
}

.btn-primary:hover {
  background: #369870;
}

.btn-success {
  background: #4ade80;
  color: white;
  margin-bottom: 1rem;
}

.btn-success:hover {
  background: #22c55e;
}

.message-history {
  max-height: 150px;
  overflow: auto;
}

.message-history h4 {
  margin: 0 0 0.5rem 0;
}

.no-messages {
  color: #64748b;
  font-style: italic;
}

.message-item {
  background: #e0f2fe;
  padding: 0.5rem;
  margin: 0.25rem 0;
  border-radius: 4px;
  font-size: 0.875rem;
}

.shared-data {
  background: #f0f9ff;
  padding: 1rem;
  border-radius: 6px;
  font-family: monospace;
  font-size: 0.875rem;
}

.no-data {
  color: #64748b;
  font-style: italic;
}
</style>
```

### 3.3 创建微前端入口

创建 `src/main.ts`：

```typescript
import { createApp, App as VueApp } from 'vue';
import App from './App.vue';

// 微前端生命周期接口
interface MicroAppLifecycle {
  mount: (props: any) => Promise<void>;
  unmount: (props: any) => Promise<void>;
  bootstrap?: () => Promise<void>;
  update?: (props: any) => Promise<void>;
}

let app: VueApp | null = null;

// 微前端生命周期实现
const microApp: MicroAppLifecycle = {
  async bootstrap() {
    console.log('Vue 应用引导完成');
  },

  async mount(props) {
    console.log('Vue 应用开始挂载', props);
    
    const container = props.container || document.getElementById('app');
    if (!container) {
      throw new Error('挂载容器不存在');
    }

    app = createApp(App);
    app.mount(container);
  },

  async unmount(props) {
    console.log('Vue 应用开始卸载', props);
    
    if (app) {
      app.unmount();
      app = null;
    }
  },

  async update(props) {
    console.log('Vue 应用更新', props);
    // 可以在这里处理属性更新
  }
};

// 导出微前端接口
(window as any)['vue-app'] = microApp;

// 独立运行时的处理
if (!window.__MICRO_CORE__) {
  microApp.mount({ container: document.getElementById('app') });
}
```

### 3.4 配置 Vue 应用

创建 `vite.config.ts`：

```typescript
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 3002,
    cors: true
  },
  build: {
    lib: {
      entry: 'src/main.ts',
      name: 'VueApp',
      fileName: 'index',
      formats: ['umd']
    },
    rollupOptions: {
      external: ['vue'],
      output: {
        globals: {
          vue: 'Vue'
        }
      }
    }
  }
});
```

创建 `index.html`：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vue 微应用</title>
</head>
<body>
  <div id="app"></div>
  <script type="module" src="/src/main.ts"></script>
</body>
</html>
```

## 🚀 第四步：运行应用

### 4.1 启动所有应用

打开三个终端窗口，分别运行：

```bash
# 终端 1: 启动主应用
cd main-app
npm run dev
# 访问 http://localhost:3000

# 终端 2: 启动 React 应用
cd react-app
npm run dev
# 访问 http://localhost:3001

# 终端 3: 启动 Vue 应用
cd vue-app
npm run dev
# 访问 http://localhost:3002
```

### 4.2 体验功能

1. **访问主应用**: 打开 http://localhost:3000
2. **导航测试**: 点击导航栏的 "React 应用" 和 "Vue 应用"
3. **通信测试**: 在 React 应用中点击 "发送消息给 Vue"
4. **状态共享**: 在任一应用中增加计数，观察共享状态变化

## 🎉 恭喜！

你已经成功创建了第一个 micro-core 微前端应用！现在你拥有：

✅ **主应用** - 负责路由和应用协调  
✅ **React 微应用** - 独立的 React 应用  
✅ **Vue 微应用** - 独立的 Vue 应用  
✅ **应用间通信** - 消息传递和状态共享  
✅ **路由管理** - 基于路径的应用切换  

## 🔍 深入了解

### 关键概念解析

1. **应用隔离**: 每个微应用运行在独立的端口，互不干扰
2. **生命周期**: 每个微应用实现 `mount`、`unmount` 等生命周期方法
3. **通信机制**: 通过 `sendMessage` 和 `onMessage` 实现应用间通信
4. **状态共享**: 使用 `setState` 和 `getState` 管理全局状态

### 项目结构

```
my-micro-frontend/
├── main-app/          # 主应用
│   ├── src/main.ts    # 微前端配置
│   ├── index.html     # 主页面
│   └── vite.config.ts # 构建配置
├── react-app/         # React 微应用
│   ├── src/App.tsx    # React 组件
│   ├── src/main.tsx   # 微前端入口
│   └── vite.config.ts # 构建配置
└── vue-app/           # Vue 微应用
    ├── src/App.vue    # Vue 组件
    ├── src/main.ts    # 微前端入口
    └── vite.config.ts # 构建配置
```

## 📚 下一步

现在你可以：

1. [学习更多配置选项](./basic-config.md)
2. [了解高级特性](../advanced/)
3. [查看实战案例](../real-world/)
4. [探索插件系统](../advanced/plugin-development.md)

## 🆘 遇到问题？

- 检查端口是否被占用
- 确认所有依赖都已安装
- 查看浏览器控制台的错误信息
- 参考 [常见问题](../../guide/faq.md)

---

**🎯 提示**: 这只是一个基础示例，micro-core 还支持更多高级功能，如沙箱隔离、预加载、插件系统等。继续探索文档了解更多！
