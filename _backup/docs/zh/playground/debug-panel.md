# 调试面板

Micro-Core 提供了强大的可视化调试面板，帮助开发者实时监控和调试微前端应用的运行状态。

## 📋 目录

- [面板概述](#面板概述)
- [启动调试面板](#启动调试面板)
- [核心功能](#核心功能)
- [应用监控](#应用监控)
- [状态管理](#状态管理)
- [通信调试](#通信调试)
- [性能分析](#性能分析)
- [错误追踪](#错误追踪)
- [配置管理](#配置管理)

## 面板概述

### 🎯 调试面板特性

```typescript
// 调试面板配置
const debugConfig = {
  // 面板显示设置
  display: {
    position: 'bottom-right',     // 面板位置
    theme: 'dark',                // 主题样式
    size: 'medium',               // 面板大小
    collapsed: false              // 是否折叠
  },
  
  // 监控功能
  monitoring: {
    realtime: true,               // 实时监控
    performance: true,            // 性能监控
    memory: true,                 // 内存监控
    network: true                 // 网络监控
  },
  
  // 日志设置
  logging: {
    level: 'debug',               // 日志级别
    maxEntries: 1000,             // 最大日志条数
    persist: true,                // 持久化日志
    export: true                  // 支持导出
  }
}
```

## 启动调试面板

### 1. 开发环境启动

```typescript
import { MicroCore, DebugPanel } from '@micro-core/core'

// 创建微前端实例
const microCore = new MicroCore({
  debug: true,  // 启用调试模式
  devtools: {
    enabled: true,
    panel: {
      position: 'bottom-right',
      theme: 'dark'
    }
  }
})

// 启动调试面板
const debugPanel = new DebugPanel(microCore, {
  autoOpen: true,
  features: {
    appMonitor: true,
    stateManager: true,
    communication: true,
    performance: true,
    errorTracker: true
  }
})

// 启动应用
microCore.start()
```

### 2. 生产环境调试

```typescript
// 生产环境条件启动
const microCore = new MicroCore({
  debug: process.env.NODE_ENV === 'development',
  devtools: {
    enabled: window.__MICRO_CORE_DEBUG__ || false,
    password: 'debug-password'  // 生产环境密码保护
  }
})

// 通过URL参数启用
// https://your-app.com?debug=true&password=debug-password
```

### 3. 快捷键启动

```typescript
// 注册快捷键
debugPanel.registerHotkeys({
  'Ctrl+Shift+D': 'toggle',      // 切换面板显示
  'Ctrl+Shift+C': 'clear',       // 清除日志
  'Ctrl+Shift+E': 'export',      // 导出调试数据
  'Ctrl+Shift+R': 'refresh'      // 刷新数据
})
```

## 核心功能

### 🎛️ 面板界面

调试面板提供直观的可视化界面，包含以下主要功能模块：

- **应用监控** - 实时查看应用状态和生命周期
- **状态管理** - 监控全局状态和应用状态变化
- **通信调试** - 追踪应用间通信和事件流
- **性能分析** - 分析加载性能和运行时性能
- **错误追踪** - 捕获和分析运行时错误
- **日志面板** - 查看详细的调试日志

## 应用监控

### 📱 应用状态监控

```typescript
// 应用监控功能
class AppMonitor {
  // 获取应用统计信息
  getAppStats() {
    return {
      total: 5,           // 总应用数
      active: 3,          // 活跃应用数
      loading: 1,         // 加载中应用数
      error: 0,           // 错误应用数
      memory: '45.2MB'    // 总内存使用
    }
  }
  
  // 获取应用详细信息
  getAppDetails(appName: string) {
    return {
      name: appName,
      status: 'mounted',
      loadTime: 1250,     // 加载时间(ms)
      memoryUsage: '12.5MB',
      errorCount: 0,
      lastActive: new Date()
    }
  }
}
```

### 🌳 应用树视图

应用监控面板以树状结构展示所有注册的微应用：

- **应用状态指示器** - 不同颜色表示应用状态
- **实时性能指标** - 显示加载时间和内存使用
- **操作按钮** - 支持重新加载、卸载等操作
- **详细信息面板** - 展开查看应用配置和运行信息

## 状态管理

### 🔄 状态监控

```typescript
// 状态监控示例
const stateMonitor = {
  // 全局状态
  globalState: {
    user: { id: 1, name: 'Admin' },
    theme: 'dark',
    language: 'zh-CN'
  },
  
  // 应用状态
  appStates: {
    'user-app': {
      currentPage: 'profile',
      formData: { ... }
    },
    'product-app': {
      selectedProduct: 123,
      cartItems: [...]
    }
  },
  
  // 状态变化历史
  stateHistory: [
    {
      timestamp: '2024-01-15 10:30:25',
      source: 'user-app',
      action: 'UPDATE_PROFILE',
      changes: { name: 'Admin' }
    }
  ]
}
```

### ⏰ 时间旅行调试

调试面板支持状态的时间旅行功能：

- **状态快照** - 记录每次状态变化
- **回滚功能** - 可以回滚到任意历史状态
- **差异对比** - 高亮显示状态变化
- **导出导入** - 支持状态数据的导出和导入

## 通信调试

### 📡 通信监控

```typescript
// 通信消息示例
const communicationLog = [
  {
    timestamp: '10:30:25.123',
    type: 'event',
    event: 'user:login',
    source: 'auth-app',
    target: 'broadcast',
    data: { userId: 123 },
    size: '0.5KB'
  },
  {
    timestamp: '10:30:26.456',
    type: 'direct',
    event: 'product:select',
    source: 'product-app',
    target: 'cart-app',
    data: { productId: 456 },
    size: '1.2KB'
  }
]
```

### 📊 通信图表

通信调试面板提供可视化的通信图表：

- **节点图** - 显示应用间的通信关系
- **消息流** - 实时显示消息传递路径
- **频率统计** - 统计通信频率和数据量
- **过滤功能** - 按应用、事件类型等过滤消息

## 性能分析

### 📈 性能指标

```typescript
// 性能监控数据
const performanceMetrics = {
  // 应用加载性能
  loadPerformance: {
    'user-app': { loadTime: 1250, size: '2.3MB' },
    'product-app': { loadTime: 890, size: '1.8MB' },
    'cart-app': { loadTime: 650, size: '1.2MB' }
  },
  
  // 内存使用情况
  memoryUsage: {
    total: '45.2MB',
    apps: {
      'user-app': '12.5MB',
      'product-app': '18.7MB',
      'cart-app': '8.3MB'
    }
  },
  
  // 网络请求统计
  networkStats: {
    totalRequests: 156,
    averageTime: 245,
    errorRate: '2.1%'
  }
}
```

### 🎯 性能建议

调试面板会根据性能数据提供优化建议：

- **加载优化** - 识别加载缓慢的应用
- **内存优化** - 检测内存泄漏和过度使用
- **网络优化** - 分析请求频率和响应时间
- **缓存建议** - 推荐缓存策略

## 错误追踪

### 🐛 错误监控

```typescript
// 错误信息示例
const errorLog = [
  {
    timestamp: '2024-01-15 10:35:12',
    level: 'error',
    app: 'product-app',
    message: 'Cannot read property of undefined',
    stack: 'at ProductList.render...',
    context: {
      url: '/products',
      userAgent: 'Chrome/120.0.0.0',
      userId: 123
    }
  },
  {
    timestamp: '2024-01-15 10:32:45',
    level: 'warning',
    app: 'cart-app',
    message: 'Deprecated API usage detected',
    details: 'Using legacy cart.add() method'
  }
]
```

### 🔍 错误分析

错误追踪功能包括：

- **实时错误捕获** - 自动捕获运行时错误
- **错误分类** - 按严重程度和类型分类
- **堆栈追踪** - 提供详细的错误堆栈信息
- **上下文信息** - 记录错误发生时的环境信息
- **错误统计** - 分析错误频率和趋势

## 配置管理

### ⚙️ 配置编辑器

```typescript
// 配置管理示例
const configManager = {
  // 当前配置
  currentConfig: {
    apps: [
      {
        name: 'user-app',
        entry: 'http://localhost:3001',
        activeWhen: '/user',
        container: '#user-container'
      }
    ],
    sandbox: {
      css: true,
      js: true
    },
    prefetch: true
  },
  
  // 配置验证
  validateConfig(config) {
    // 验证配置的有效性
    return {
      valid: true,
      errors: [],
      warnings: ['建议启用预加载功能']
    }
  },
  
  // 应用配置
  applyConfig(config) {
    // 应用新的配置
    microCore.updateConfig(config)
  }
}
```

### 📝 配置功能

配置管理面板提供：

- **可视化编辑** - 图形化配置编辑界面
- **配置验证** - 实时验证配置的正确性
- **配置导出** - 支持配置的导出和分享
- **配置历史** - 记录配置变更历史
- **一键应用** - 快速应用配置更改

## 使用技巧

### 💡 调试技巧

1. **快速定位问题**
   - 使用过滤功能快速找到相关日志
   - 利用时间线功能追踪问题发生时间
   - 通过通信图表分析应用间交互

2. **性能优化**
   - 关注应用加载时间和内存使用
   - 分析网络请求的频率和大小
   - 使用性能建议进行针对性优化

3. **错误排查**
   - 查看完整的错误堆栈信息
   - 分析错误发生的上下文环境
   - 利用状态历史回溯问题状态

### 🔧 高级功能

- **自定义面板** - 支持添加自定义调试面板
- **插件扩展** - 通过插件扩展调试功能
- **远程调试** - 支持远程设备的调试
- **团队协作** - 支持调试数据的分享和协作

## 总结

Micro-Core 调试面板是一个功能强大的开发工具，提供了全面的调试和监控功能。通过合理使用调试面板，可以大大提高微前端应用的开发效率和质量。

主要优势：
- 🎯 **直观可视** - 提供直观的可视化界面
- 🚀 **实时监控** - 实时监控应用状态和性能
- 🔍 **深度分析** - 提供深入的性能和错误分析
- 🛠️ **易于使用** - 简单易用的操作界面
- 🔧 **高度可定制** - 支持自定义和扩展

建议在开发过程中充分利用调试面板的各项功能，以提高开发效率和应用质量。