/**
 * 共享工具包 - 自动发现工具
 *
 * @description 提供应用自动发现、资源检测等功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * 发现配置接口
 */
export interface DiscoveryConfig {
    /** 扫描间隔（毫秒） */
    scanInterval?: number;
    /** 扫描路径 */
    scanPaths?: string[];
    /** 包含模式 */
    includePatterns?: RegExp[];
    /** 排除模式 */
    excludePatterns?: RegExp[];
    /** 是否启用缓存 */
    enableCache?: boolean;
    /** 缓存过期时间 */
    cacheExpiry?: number;
}
/**
 * 发现结果接口
 */
export interface DiscoveryResult {
    /** 发现的项目 */
    items: DiscoveredItem[];
    /** 发现时间 */
    timestamp: number;
    /** 扫描耗时 */
    duration: number;
}
/**
 * 发现项接口
 */
export interface DiscoveredItem {
    /** 项目名称 */
    name: string;
    /** 项目路径 */
    path: string;
    /** 项目类型 */
    type: string;
    /** 项目元数据 */
    metadata: Record<string, any>;
    /** 置信度 */
    confidence: number;
}
/**
 * 自动发现类
 */
export declare class AutoDiscovery {
    private config;
    private cache;
    private scanTimer?;
    constructor(config?: DiscoveryConfig);
    /**
     * 开始自动发现
     */
    start(): void;
    /**
     * 停止自动发现
     */
    stop(): void;
    /**
     * 执行扫描
     */
    scan(): Promise<DiscoveryResult>;
    /**
     * 扫描指定路径
     */
    private scanPath;
    /**
     * 获取发现结果
     */
    getResults(): DiscoveryResult | null;
    /**
     * 清除缓存
     */
    clearCache(): void;
    /**
     * 更新配置
     */
    updateConfig(newConfig: Partial<DiscoveryConfig>): void;
}
/**
 * 发现工具函数集合
 */
export declare const discoveryUtils: {
    /**
     * 创建自动发现实例
     */
    create: (config?: DiscoveryConfig) => AutoDiscovery;
    /**
     * 检查路径是否匹配模式
     */
    matchesPattern: (path: string, patterns: RegExp[]) => boolean;
    /**
     * 过滤发现结果
     */
    filterResults: (results: DiscoveredItem[], filter: (item: DiscoveredItem) => boolean) => DiscoveredItem[];
    /**
     * 按置信度排序
     */
    sortByConfidence: (results: DiscoveredItem[]) => DiscoveredItem[];
};
//# sourceMappingURL=discovery-utils.d.ts.map