# 配置迁移指南

本指南帮助您从其他微前端解决方案迁移到 Micro-Core，或从 Micro-Core 早期版本升级到最新版本。

## 从 qiankun 迁移

### 主应用配置迁移

#### qiankun 配置

```javascript
// qiankun 配置
import { registerMicroApps, start } from 'qiankun'

registerMicroApps([
  {
    name: 'reactApp',
    entry: '//localhost:7100',
    container: '#subapp-viewport',
    activeRule: '/react',
  },
  {
    name: 'vueApp',
    entry: { scripts: ['//localhost:7101/main.js'] },
    container: '#subapp-viewport',
    activeRule: '/vue',
  }
])

start()
```

#### Micro-Core 配置

```typescript
// Micro-Core 配置
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  container: '#subapp-viewport',
  apps: [
    {
      name: 'reactApp',
      entry: 'http://localhost:7100',
      routes: ['/react/*'],
      sandbox: {
        type: 'proxy',
        isolation: { css: true, js: true }
      }
    },
    {
      name: 'vueApp',
      entry: 'http://localhost:7101/main.js',
      routes: ['/vue/*'],
      sandbox: {
        type: 'proxy',
        isolation: { css: true, js: true }
      }
    }
  ],
  
  // 增强功能
  plugins: [
    '@micro-core/plugin-router',
    '@micro-core/plugin-sandbox',
    '@micro-core/plugin-communication'
  ]
})

microCore.start()
```

### 子应用配置迁移

#### qiankun 子应用

```javascript
// qiankun 子应用导出
export async function bootstrap() {
  console.log('react app bootstraped')
}

export async function mount(props) {
  console.log('props from main framework', props)
  ReactDOM.render(<App />, props.container ? props.container.querySelector('#root') : document.getElementById('root'))
}

export async function unmount(props) {
  ReactDOM.unmountComponentAtNode(
    props.container ? props.container.querySelector('#root') : document.getElementById('root')
  )
}
```

#### Micro-Core 子应用

```typescript
// Micro-Core 子应用适配器
import { ReactAdapter } from '@micro-core/adapter-react'

class ReactMicroApp extends ReactAdapter {
  name = 'reactApp'
  version = '1.0.0'
  framework = 'react'
  
  async mount(container: HTMLElement, props?: any) {
    console.log('props from main framework', props)
    
    const { createRoot } = await import('react-dom/client')
    const App = await import('./App')
    
    this.root = createRoot(container)
    this.root.render(<App.default {...props} />)
  }
  
  async unmount() {
    if (this.root) {
      this.root.unmount()
      this.root = null
    }
  }
}

export default new ReactMicroApp()
```

### 通信方式迁移

#### qiankun 通信

```javascript
// qiankun 全局状态
import { initGlobalState } from 'qiankun'

const actions = initGlobalState({
  user: 'qiankun',
})

actions.onGlobalStateChange((state, prev) => {
  console.log(state, prev)
})

actions.setGlobalState({
  ignore: 'master',
  user: {
    name: 'master',
  },
})
```

#### Micro-Core 通信

```typescript
// Micro-Core 全局状态和事件
import { GlobalState, EventBus } from '@micro-core/core'

// 全局状态管理
GlobalState.set('user', { name: 'micro-core' })

GlobalState.subscribe('user', (user, prevUser) => {
  console.log('User changed:', user, prevUser)
})

// 事件通信
EventBus.on('user:login', (userData) => {
  console.log('User logged in:', userData)
})

EventBus.emit('user:login', { name: 'micro-core', id: '123' })
```

## 从 single-spa 迁移

### 应用注册迁移

#### single-spa 配置

```javascript
// single-spa 配置
import { registerApplication, start } from 'single-spa'

registerApplication({
  name: '@org/navbar',
  app: () => System.import('@org/navbar'),
  activeWhen: ['/'],
})

registerApplication({
  name: '@org/home',
  app: () => System.import('@org/home'),
  activeWhen: ['/home'],
})

start()
```

#### Micro-Core 配置

```typescript
// Micro-Core 配置
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  apps: [
    {
      name: 'navbar',
      entry: () => System.import('@org/navbar'),
      routes: ['/'],
      mountParcel: true // 支持 parcel 模式
    },
    {
      name: 'home',
      entry: () => System.import('@org/home'),
      routes: ['/home/<USER>']
    }
  ],
  
  // SystemJS 支持
  loader: {
    type: 'systemjs',
    importMap: '/importmap.json'
  }
})

microCore.start()
```

### 生命周期迁移

#### single-spa 生命周期

```javascript
// single-spa 应用
export const bootstrap = () => Promise.resolve()

export const mount = (props) => {
  return new Promise((resolve) => {
    // 挂载逻辑
    resolve()
  })
}

export const unmount = (props) => {
  return new Promise((resolve) => {
    // 卸载逻辑
    resolve()
  })
}
```

#### Micro-Core 生命周期

```typescript
// Micro-Core 适配器
import { BaseAdapter } from '@micro-core/core'

class SingleSpaAdapter extends BaseAdapter {
  name = 'single-spa-app'
  version = '1.0.0'
  framework = 'single-spa'
  
  async bootstrap() {
    // 引导逻辑
  }
  
  async mount(container: HTMLElement, props?: any) {
    // 挂载逻辑
    await this.doMount(container, props)
  }
  
  async unmount() {
    // 卸载逻辑
    await this.doUnmount()
  }
}

export default new SingleSpaAdapter()
```

## 从 Module Federation 迁移

### Webpack 配置迁移

#### Module Federation 配置

```javascript
// webpack.config.js - Module Federation
const ModuleFederationPlugin = require('@module-federation/webpack')

module.exports = {
  plugins: [
    new ModuleFederationPlugin({
      name: 'shell',
      remotes: {
        mfe1: 'mfe1@http://localhost:4201/remoteEntry.js',
        mfe2: 'mfe2@http://localhost:4202/remoteEntry.js',
      },
    }),
  ],
}
```

#### Micro-Core + Module Federation

```typescript
// Micro-Core 配置支持 Module Federation
import { MicroCore } from '@micro-core/core'
import { ModuleFederationPlugin } from '@micro-core/plugin-module-federation'

const microCore = new MicroCore({
  apps: [
    {
      name: 'mfe1',
      entry: 'mfe1@http://localhost:4201/remoteEntry.js',
      routes: ['/mfe1/*'],
      loader: 'module-federation'
    },
    {
      name: 'mfe2', 
      entry: 'mfe2@http://localhost:4202/remoteEntry.js',
      routes: ['/mfe2/*'],
      loader: 'module-federation'
    }
  ],
  
  plugins: [
    new ModuleFederationPlugin({
      shared: {
        'react': { singleton: true },
        'react-dom': { singleton: true }
      }
    })
  ]
})
```

## 版本升级指南

### 从 v0.x 升级到 v1.x

#### 配置格式变更

```typescript
// v0.x 配置
const microCore = new MicroCore([
  {
    name: 'app1',
    entry: '/app1.js',
    activeRule: '/app1'
  }
])

// v1.x 配置
const microCore = new MicroCore({
  apps: [
    {
      name: 'app1',
      entry: '/app1.js',
      routes: ['/app1/*'] // activeRule 改为 routes
    }
  ]
})
```

#### API 变更

```typescript
// v0.x API
microCore.registerApp(appConfig)
microCore.unregisterApp(appName)

// v1.x API
microCore.loadApp(appName)
microCore.unloadApp(appName)
```

### 从 v1.x 升级到 v2.x

#### 插件系统变更

```typescript
// v1.x 插件配置
const microCore = new MicroCore({
  plugins: {
    router: { mode: 'history' },
    sandbox: { type: 'proxy' }
  }
})

// v2.x 插件配置
import { RouterPlugin, SandboxPlugin } from '@micro-core/plugins'

const microCore = new MicroCore({
  plugins: [
    new RouterPlugin({ mode: 'history' }),
    new SandboxPlugin({ type: 'proxy' })
  ]
})
```

#### 类型定义更新

```typescript
// v1.x 类型
interface AppConfig {
  name: string
  entry: string
  activeRule: string
}

// v2.x 类型
interface AppConfig {
  name: string
  entry: string | (() => Promise<any>)
  routes: string[]
  props?: any
  sandbox?: SandboxConfig
}
```

## 自动化迁移工具

### CLI 迁移命令

```bash
# 安装迁移工具
npm install -g @micro-core/migration-tool

# 从 qiankun 迁移
micro-core migrate --from qiankun --config ./qiankun.config.js

# 从 single-spa 迁移
micro-core migrate --from single-spa --config ./single-spa.config.js

# 版本升级
micro-core upgrade --from 1.x --to 2.x
```

### 迁移配置文件

```json
{
  "migration": {
    "from": "qiankun",
    "to": "micro-core",
    "options": {
      "preserveComments": true,
      "updateImports": true,
      "generateTypes": true
    },
    "mappings": {
      "registerMicroApps": "MicroCore.constructor",
      "start": "MicroCore.start",
      "initGlobalState": "GlobalState"
    }
  }
}
```

### 自定义迁移脚本

```javascript
// migrate.js
const { MigrationTool } = require('@micro-core/migration-tool')

const migrationTool = new MigrationTool({
  source: './src',
  target: './src-migrated',
  from: 'qiankun',
  to: 'micro-core'
})

// 自定义转换规则
migrationTool.addTransform('qiankun-to-micro-core', {
  // 转换 registerMicroApps 调用
  CallExpression(path) {
    if (path.node.callee.name === 'registerMicroApps') {
      // 转换逻辑
      path.node.callee.name = 'MicroCore'
      // ... 更多转换逻辑
    }
  }
})

// 执行迁移
migrationTool.run()
```

## 迁移检查清单

### 功能对比检查

- [ ] 应用注册和路由配置
- [ ] 生命周期钩子实现
- [ ] 应用间通信机制
- [ ] 样式隔离配置
- [ ] JavaScript 沙箱配置
- [ ] 错误处理机制
- [ ] 性能优化配置

### 兼容性检查

- [ ] 浏览器兼容性
- [ ] 框架版本兼容性
- [ ] 第三方库兼容性
- [ ] 构建工具兼容性

### 测试验证

```typescript
// 迁移后测试脚本
import { MicroCore } from '@micro-core/core'
import { expect } from '@jest/globals'

describe('Migration Tests', () => {
  let microCore: MicroCore
  
  beforeEach(() => {
    microCore = new MicroCore({
      apps: [
        {
          name: 'test-app',
          entry: '/test-app.js',
          routes: ['/test/*']
        }
      ]
    })
  })
  
  it('should load apps correctly', async () => {
    await microCore.start()
    await microCore.loadApp('test-app')
    
    expect(microCore.getApp('test-app')).toBeDefined()
  })
  
  it('should handle routing correctly', async () => {
    await microCore.start()
    
    // 模拟路由变化
    window.history.pushState({}, '', '/test/page1')
    
    // 验证应用是否正确加载
    expect(microCore.getCurrentApp()?.name).toBe('test-app')
  })
  
  it('should maintain communication', async () => {
    await microCore.start()
    
    let received = false
    EventBus.on('test-event', () => {
      received = true
    })
    
    EventBus.emit('test-event')
    
    expect(received).toBe(true)
  })
})
```

## 常见问题和解决方案

### 1. 路由冲突

**问题**：迁移后路由不工作

**解决方案**：
```typescript
// 检查路由配置
const microCore = new MicroCore({
  apps: [
    {
      name: 'app1',
      routes: ['/app1/*'], // 确保路径正确
      exact: false // 允许子路径匹配
    }
  ],
  
  router: {
    mode: 'history', // 确保路由模式正确
    base: '/' // 设置正确的基础路径
  }
})
```

### 2. 样式冲突

**问题**：应用样式相互影响

**解决方案**：
```typescript
// 启用样式隔离
const microCore = new MicroCore({
  apps: [
    {
      name: 'app1',
      entry: '/app1.js',
      routes: ['/app1/*'],
      sandbox: {
        type: 'proxy',
        isolation: {
          css: true, // 启用 CSS 隔离
          cssPrefix: 'app1-' // 添加 CSS 前缀
        }
      }
    }
  ]
})
```

### 3. 全局状态丢失

**问题**：迁移后全局状态不同步

**解决方案**：
```typescript
// 迁移全局状态
import { GlobalState } from '@micro-core/core'

// 从旧系统迁移状态
const oldGlobalState = window.__QIANKUN_GLOBAL_STATE__
if (oldGlobalState) {
  Object.keys(oldGlobalState).forEach(key => {
    GlobalState.set(key, oldGlobalState[key])
  })
}
```

## 性能优化建议

### 1. 渐进式迁移

```typescript
// 分阶段迁移
const migrationPhases = [
  {
    phase: 1,
    apps: ['admin-app'], // 先迁移低风险应用
    strategy: 'iframe' // 使用最安全的隔离策略
  },
  {
    phase: 2,
    apps: ['user-app', 'order-app'],
    strategy: 'sandbox' // 使用更好的集成策略
  },
  {
    phase: 3,
    apps: ['main-app'],
    strategy: 'native' // 最后迁移核心应用
  }
]
```

### 2. 性能监控

```typescript
// 添加性能监控
import { PerformanceMonitor } from '@micro-core/core'

PerformanceMonitor.configure({
  trackAppLoad: true,
  trackRouteChange: true,
  onMetric: (metric) => {
    console.log('Performance metric:', metric)
    // 发送到监控系统
  }
})
```

## 参考资料

- [Micro-Core 配置参考](/api/configuration)
- [插件系统文档](/guide/plugin-system)
- [适配器开发指南](/guide/adapters)
- [故障排除指南](/guide/troubleshooting)
