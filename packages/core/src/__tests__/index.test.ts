/**
 * 核心包入口测试
 * 
 * @description 测试核心包的导出
 * <AUTHOR> <<EMAIL>>
 */

import { describe, expect, it } from 'vitest'
import * as coreExports from '../index'
import { MicroCore, defaultMicroCore } from '../micro-core'

describe('核心包导出', () => {
    it('应该导出 MicroCore 类', () => {
        expect(coreExports.MicroCore).toBe(MicroCore)
        expect(typeof coreExports.MicroCore).toBe('function')
    })

    it('应该导出默认微内核实例', () => {
        expect(coreExports.defaultMicroCore).toBe(defaultMicroCore)
        expect(coreExports.defaultMicroCore).toBeInstanceOf(MicroCore)
    })

    it('应该从 shared 包重新导出类型', () => {
        // 这些是类型导出，在运行时不存在，但应该在编译时可用
        expect(typeof coreExports.createLogger).toBe('function')
        expect(typeof coreExports.createMicroCoreUtils).toBe('function')
        expect(typeof coreExports.MicroCoreError).toBe('function')
    })

    it('应该导出所有必需的内容', () => {
        const expectedExports = [
            'MicroCore',
            'defaultMicroCore',
            'createLogger',
            'createMicroCoreUtils',
            'MicroCoreError'
        ]

        expectedExports.forEach(exportName => {
            expect(coreExports).toHaveProperty(exportName)
        })
    })
})