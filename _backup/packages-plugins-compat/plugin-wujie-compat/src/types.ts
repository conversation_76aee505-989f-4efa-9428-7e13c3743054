/**
 * Wujie 兼容插件类型定义
 */

export interface WujieCompatOptions {
    /** 是否启用 Shadow DOM 隔离 */
    enableShadowDOM?: boolean;
    /** 是否启用 iframe 隔离 */
    enableIframeIsolation?: boolean;
    /** 是否启用事件总线通信 */
    enableEventBus?: boolean;
    /** 自定义 iframe 配置 */
    iframeConfig?: IframeConfig;
    /** 自定义 WebComponent 配置 */
    webComponentConfig?: WebComponentConfig;
}

export interface WujieAppConfig {
    /** 应用名称 */
    name: string;
    /** 应用入口 URL */
    url: string;
    /** 容器元素选择器或元素 */
    el: string | HTMLElement;
    /** 是否保活 */
    alive?: boolean;
    /** 应用属性 */
    props?: Record<string, any>;
    /** 生命周期钩子 */
    beforeLoad?: (appWindow: Window) => void;
    beforeMount?: (appWindow: Window) => void;
    afterMount?: (appWindow: Window) => void;
    beforeUnmount?: (appWindow: Window) => void;
    afterUnmount?: (appWindow: Window) => void;
    /** 自定义获取资源函数 */
    fetch?: (url: string, options?: RequestInit) => Promise<Response>;
    /** 插件配置 */
    plugins?: WujiePlugin[];
}

export interface IframeConfig {
    /** iframe 样式 */
    style?: Partial<CSSStyleDeclaration>;
    /** iframe 属性 */
    attrs?: Record<string, string>;
    /** 是否启用沙箱 */
    sandbox?: boolean;
}

export interface WebComponentConfig {
    /** 自定义元素标签名 */
    tagName?: string;
    /** Shadow DOM 模式 */
    shadowMode?: 'open' | 'closed';
    /** 样式隔离 */
    styleIsolation?: boolean;
}

export interface WujiePlugin {
    /** 插件名称 */
    name: string;
    /** 插件处理函数 */
    handler: (appWindow: Window, config: WujieAppConfig) => void;
}

export interface EventBusMessage {
    /** 消息类型 */
    type: string;
    /** 消息数据 */
    data: any;
    /** 发送方应用名称 */
    from: string;
    /** 接收方应用名称 */
    to?: string;
    /** 消息时间戳 */
    timestamp: number;
}

export interface AppInstance {
    /** 应用名称 */
    name: string;
    /** 应用窗口对象 */
    window: Window;
    /** 应用文档对象 */
    document: Document;
    /** 应用容器元素 */
    container: HTMLElement;
    /** 应用状态 */
    status: 'loading' | 'mounted' | 'unmounted' | 'error';
    /** 应用配置 */
    config: WujieAppConfig;
}