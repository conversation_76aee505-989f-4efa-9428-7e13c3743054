/**
 * Vue3 Adapter Utils 单元测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
    cleanupVue3Container,
    createDefaultVue3Config,
    createVue3Adapter,
    createVue3Container,
    createVue3ErrorInfo,
    createVue3MicroAppPlugin,
    enableVue3DevTools,
    extractVue3Component,
    formatVue3Error,
    getVue3Container,
    getVue3Version,
    isVue3App,
    isVue3Component,
    isVue3ComponentEnhanced,
    isVue3DevToolsAvailable,
    isVue3Entry,
    isVue3VersionCompatible,
    mergeVue3Configs,
    validateVue3Config,
    Vue3AdapterUtils
} from '../src/utils';

// Mock Vue3
const mockVue3 = {
    version: '3.4.0',
    createApp: vi.fn(),
    defineComponent: vi.fn(),
    h: vi.fn()
};

vi.mock('vue', () => mockVue3);

// Mock DOM
Object.defineProperty(global, 'document', {
    value: {
        createElement: vi.fn(() => ({
            id: '',
            className: '',
            setAttribute: vi.fn(),
            classList: {
                add: vi.fn()
            },
            appendChild: vi.fn(),
            removeChild: vi.fn(),
            firstChild: null,
            parentNode: null
        })),
        getElementById: vi.fn(),
        body: {
            appendChild: vi.fn()
        }
    }
});

// Mock window
Object.defineProperty(global, 'window', {
    value: {
        Vue: mockVue3,
        __VUE_DEVTOOLS_GLOBAL_HOOK__: {},
        location: { href: 'http://localhost:3000' },
        navigator: { userAgent: 'test-agent' }
    }
});

describe('Vue3 Adapter Utils', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('createVue3Adapter', () => {
        it('应该在缺少依赖时抛出错误', () => {
            expect(() => createVue3Adapter()).toThrow('All dependencies are required to create Vue3Adapter');
        });

        it('应该在提供所有依赖时创建适配器', () => {
            const dependencies = {
                lifecycleManager: {},
                sandboxManager: {},
                communicationManager: {},
                errorHandler: {}
            };

            // Mock Vue3Adapter constructor
            const MockVue3Adapter = vi.fn();
            vi.doMock('../src/vue3-adapter', () => ({
                Vue3Adapter: MockVue3Adapter
            }));

            expect(() => createVue3Adapter({}, dependencies)).not.toThrow();
        });
    });

    describe('isVue3App', () => {
        it('应该识别 Vue3 应用配置', () => {
            expect(isVue3App({ vue3: { vueVersion: '3.0' } })).toBe(true);
            expect(isVue3App({ component: { setup: () => ({}) } })).toBe(true);
            expect(isVue3App({ vueOptions: { setup: () => ({}) } })).toBe(true);
            expect(isVue3App({ entry: 'vue3-app.js' })).toBe(true);
            expect(isVue3App({})).toBe(false);
        });
    });

    describe('isVue3Entry', () => {
        it('应该识别 Vue3 入口文件', () => {
            expect(isVue3Entry('vue3-app.js')).toBe(true);
            expect(isVue3Entry('app.vue')).toBe(true);
            expect(isVue3Entry('main-vue3.js')).toBe(true);
            expect(isVue3Entry('index.js')).toBe(false);
        });
    });

    describe('getVue3Version', () => {
        it('应该从 Vue 获取版本', () => {
            expect(getVue3Version()).toBe('3.4.0');
        });

        it('应该在没有 Vue 时返回 null', () => {
            const originalVue = require('vue');
            vi.doMock('vue', () => ({}));

            // 重新导入函数以使用新的 mock
            const { getVue3Version: newGetVue3Version } = require('../src/utils');
            expect(newGetVue3Version()).toBe(null);

            // 恢复原始 mock
            vi.doMock('vue', () => originalVue);
        });
    });

    describe('isVue3VersionCompatible', () => {
        it('应该检查版本兼容性', () => {
            expect(isVue3VersionCompatible('3.4.0', '3.0.0')).toBe(true);
            expect(isVue3VersionCompatible('2.7.0', '3.0.0')).toBe(false);
            expect(isVue3VersionCompatible('3.0.0', '3.0.0')).toBe(true);
        });

        it('应该处理无效版本', () => {
            expect(isVue3VersionCompatible('invalid', '3.0.0')).toBe(false);
        });
    });

    describe('validateVue3Config', () => {
        it('应该验证有效配置', () => {
            const config = {
                name: 'test-app',
                component: { setup: () => ({}) }
            };
            expect(() => validateVue3Config(config)).not.toThrow();
        });

        it('应该在缺少名称时抛出错误', () => {
            const config = { component: { setup: () => ({}) } };
            expect(() => validateVue3Config(config)).toThrow('Vue 3 app name is required');
        });

        it('应该在缺少组件、选项和入口时抛出错误', () => {
            const config = { name: 'test-app' };
            expect(() => validateVue3Config(config)).toThrow('Either component, vueOptions, or entry must be specified');
        });
    });

    describe('createDefaultVue3Config', () => {
        it('应该创建默认配置', () => {
            const config = createDefaultVue3Config();
            expect(config.name).toBe('vue3-app');
            expect(config.vue3?.vueVersion).toBe('3.0');
            expect(config.vue3?.enableDevTools).toBe(false); // NODE_ENV !== 'development'
        });

        it('应该合并覆盖配置', () => {
            const overrides = { name: 'custom-app' };
            const config = createDefaultVue3Config(overrides);
            expect(config.name).toBe('custom-app');
        });
    });

    describe('extractVue3Component', () => {
        it('应该提取默认导出组件', () => {
            const TestComponent = { setup: () => ({}) };
            const module = { default: TestComponent };
            const result = extractVue3Component(module);
            expect(result).toBe(TestComponent);
        });

        it('应该提取命名导出组件', () => {
            const TestComponent = { setup: () => ({}) };
            const module = { TestComponent };
            const result = extractVue3Component(module);
            expect(result).toBe(TestComponent);
        });

        it('应该优先使用首选名称', () => {
            const PreferredComponent = { setup: () => ({}) };
            const OtherComponent = { setup: () => ({}) };
            const module = { PreferredComponent, OtherComponent };
            const result = extractVue3Component(module, { preferredName: 'PreferredComponent' });
            expect(result).toBe(PreferredComponent);
        });

        it('应该处理组件选项对象', () => {
            const componentOptions = { setup: () => ({}), template: '<div>Direct</div>' };
            const result = extractVue3Component(componentOptions);
            expect(result).toBe(componentOptions);
        });

        it('应该在找不到组件时抛出错误', () => {
            const module = { notAComponent: 'string' };
            expect(() => extractVue3Component(module)).toThrow('No Vue 3 component found in module');
        });

        it('应该处理多个组件', () => {
            const App = { setup: () => ({}) };
            const Component = { setup: () => ({}) };
            const module = { App, Component };
            const result = extractVue3Component(module);
            expect(result).toBe(App); // 'App' 在优先级列表中
        });

        it('应该返回所有组件当 allowMultiple 为 true', () => {
            const App = { setup: () => ({}) };
            const Component = { setup: () => ({}) };
            const module = { App, Component };
            const result = extractVue3Component(module, { allowMultiple: true });
            expect(result).toEqual({ App, Component });
        });

        it('应该在没有模块时抛出错误', () => {
            expect(() => extractVue3Component(null)).toThrow('Module is required for component extraction');
        });
    });

    describe('isVue3Component', () => {
        it('应该识别组件选项对象', () => {
            expect(isVue3Component({ setup: () => ({}) })).toBe(true);
            expect(isVue3Component({ template: '<div></div>' })).toBe(true);
            expect(isVue3Component({ render: vi.fn() })).toBe(true);
            expect(isVue3Component({ data: () => ({}) })).toBe(true);
            expect(isVue3Component({ computed: {} })).toBe(true);
            expect(isVue3Component({ methods: {} })).toBe(true);
            expect(isVue3Component({ props: [] })).toBe(true);
            expect(isVue3Component({ components: {} })).toBe(true);
            expect(isVue3Component({ emits: [] })).toBe(true);
            expect(isVue3Component({ expose: [] })).toBe(true);
        });

        it('应该识别函数式组件', () => {
            const FunctionComponent = vi.fn();
            FunctionComponent.__vccOpts = {};
            expect(isVue3Component(FunctionComponent)).toBe(true);
        });

        it('应该识别 Vue3 应用实例', () => {
            const appInstance = { _component: {}, _context: {} };
            expect(isVue3Component(appInstance)).toBe(true);
        });

        it('应该拒绝非组件值', () => {
            expect(isVue3Component(null)).toBe(false);
            expect(isVue3Component('string')).toBe(false);
            expect(isVue3Component(123)).toBe(false);
            expect(isVue3Component({})).toBe(false);
        });
    });

    describe('isVue3ComponentEnhanced', () => {
        it('应该识别更多 Vue3 组件类型', () => {
            expect(isVue3ComponentEnhanced({ watch: {} })).toBe(true);
            expect(isVue3ComponentEnhanced({ mixins: [] })).toBe(true);
            expect(isVue3ComponentEnhanced({ directives: {} })).toBe(true);
            expect(isVue3ComponentEnhanced({ provide: {} })).toBe(true);
            expect(isVue3ComponentEnhanced({ inject: [] })).toBe(true);
            expect(isVue3ComponentEnhanced({ beforeCreate: vi.fn() })).toBe(true);
            expect(isVue3ComponentEnhanced({ created: vi.fn() })).toBe(true);
            expect(isVue3ComponentEnhanced({ mounted: vi.fn() })).toBe(true);
            expect(isVue3ComponentEnhanced({ beforeUnmount: vi.fn() })).toBe(true);
            expect(isVue3ComponentEnhanced({ unmounted: vi.fn() })).toBe(true);
            expect(isVue3ComponentEnhanced({ errorCaptured: vi.fn() })).toBe(true);
            expect(isVue3ComponentEnhanced({ renderTracked: vi.fn() })).toBe(true);
            expect(isVue3ComponentEnhanced({ renderTriggered: vi.fn() })).toBe(true);
        });

        it('应该识别 Composition API 函数', () => {
            const ComponentWithCompositionAPI = function () {
                const count = ref(0);
                const doubled = computed(() => count.value * 2);
                onMounted(() => { });
                return { count, doubled };
            };
            expect(isVue3ComponentEnhanced(ComponentWithCompositionAPI)).toBe(true);
        });

        it('应该识别异步组件', () => {
            const AsyncComponent = function () { };
            expect(isVue3ComponentEnhanced(AsyncComponent)).toBe(true);
        });
    });

    describe('容器管理', () => {
        beforeEach(() => {
            vi.mocked(document.getElementById).mockReturnValue(null);
            vi.mocked(document.createElement).mockReturnValue({
                id: '',
                className: '',
                setAttribute: vi.fn(),
                classList: { add: vi.fn() },
                appendChild: vi.fn(),
                removeChild: vi.fn(),
                firstChild: null,
                parentNode: null
            } as any);
        });

        describe('createVue3Container', () => {
            it('应该创建 Vue3 容器', () => {
                const container = createVue3Container('test-app');
                expect(container).toBeDefined();
            });
        });

        describe('getVue3Container', () => {
            it('应该获取 Vue3 容器', () => {
                const mockElement = document.createElement('div');
                vi.mocked(document.getElementById).mockReturnValue(mockElement);

                const container = getVue3Container('test-app');
                expect(container).toBe(mockElement);
                expect(document.getElementById).toHaveBeenCalledWith('micro-app-test-app');
            });

            it('应该在容器不存在时返回 null', () => {
                vi.mocked(document.getElementById).mockReturnValue(null);

                const container = getVue3Container('non-existent');
                expect(container).toBe(null);
            });
        });
    });

    describe('开发工具', () => {
        describe('isVue3DevToolsAvailable', () => {
            it('应该检测 Vue3 DevTools 可用性', () => {
                expect(isVue3DevToolsAvailable()).toBe(true);
            });

            it('应该在没有 DevTools 时返回 false', () => {
                const originalHook = (global as any).window.__VUE_DEVTOOLS_GLOBAL_HOOK__;
                delete (global as any).window.__VUE_DEVTOOLS_GLOBAL_HOOK__;
                expect(isVue3DevToolsAvailable()).toBe(false);
                (global as any).window.__VUE_DEVTOOLS_GLOBAL_HOOK__ = originalHook;
            });
        });

        describe('enableVue3DevTools', () => {
            it('应该启用 Vue3 DevTools', () => {
                const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { });
                enableVue3DevTools('test-app');
                expect(consoleSpy).toHaveBeenCalledWith('Vue 3 DevTools enabled for app: test-app');
                consoleSpy.mockRestore();
            });
        });
    });

    describe('错误处理', () => {
        describe('createVue3ErrorInfo', () => {
            it('应该创建错误信息对象', () => {
                const error = new Error('Test error');
                const errorInfo = createVue3ErrorInfo(error, 'component stack');

                expect(errorInfo.componentStack).toBe('component stack');
                expect(errorInfo.errorBoundary).toBe('Vue3Adapter');
                expect(errorInfo.timestamp).toBeDefined();
                expect(errorInfo.userAgent).toBe('test-agent');
                expect(errorInfo.url).toBe('http://localhost:3000');
            });
        });

        describe('formatVue3Error', () => {
            it('应该格式化 Vue3 错误', () => {
                const error = new Error('Test error');
                const errorInfo = { componentStack: 'stack', errorBoundary: 'boundary' };
                const formatted = formatVue3Error(error, errorInfo);

                expect(formatted).toContain('Test error');
            });
        });
    });

    describe('配置合并', () => {
        describe('mergeVue3Configs', () => {
            it('应该合并 Vue3 配置', () => {
                const base = {
                    name: 'base-app',
                    vue3: { vueVersion: '3.0' }
                };
                const override = {
                    name: 'override-app',
                    vue3: { enableDevTools: true }
                };

                const merged = mergeVue3Configs(base, override);
                expect(merged.name).toBe('override-app');
                expect(merged.vue3?.vueVersion).toBe('3.0');
                expect(merged.vue3?.enableDevTools).toBe(true);
            });
        });
    });

    describe('微应用集成', () => {
        describe('createVue3MicroAppPlugin', () => {
            it('应该创建微应用插件', () => {
                const context = {
                    communication: {
                        emitToParent: vi.fn(),
                        sendToApp: vi.fn()
                    }
                };

                const plugin = createVue3MicroAppPlugin(context);
                expect(plugin).toBeDefined();
                expect(plugin.install).toBeInstanceOf(Function);
            });

            it('应该正确安装插件', () => {
                const context = {
                    communication: {
                        emitToParent: vi.fn(),
                        sendToApp: vi.fn()
                    }
                };

                const mockApp = {
                    provide: vi.fn(),
                    config: {
                        globalProperties: {}
                    }
                };

                const plugin = createVue3MicroAppPlugin(context);
                plugin.install(mockApp);

                expect(mockApp.provide).toHaveBeenCalledWith('microAppContext', context);
                expect(mockApp.config.globalProperties.$microApp).toBe(context);
                expect(mockApp.config.globalProperties.$emitToParent).toBeInstanceOf(Function);
                expect(mockApp.config.globalProperties.$sendToApp).toBeInstanceOf(Function);
            });
        });
    });

    describe('Vue3AdapterUtils', () => {
        it('应该导出所有工具函数', () => {
            expect(Vue3AdapterUtils.extractVue3Component).toBe(extractVue3Component);
            expect(Vue3AdapterUtils.isVue3Component).toBe(isVue3Component);
            expect(Vue3AdapterUtils.isVue3ComponentEnhanced).toBe(isVue3ComponentEnhanced);
            expect(Vue3AdapterUtils.isVue3App).toBe(isVue3App);
            expect(Vue3AdapterUtils.isVue3Entry).toBe(isVue3Entry);
            expect(Vue3AdapterUtils.getVue3Version).toBe(getVue3Version);
            expect(Vue3AdapterUtils.isVue3VersionCompatible).toBe(isVue3VersionCompatible);
            expect(Vue3AdapterUtils.validateVue3Config).toBe(validateVue3Config);
            expect(Vue3AdapterUtils.createDefaultVue3Config).toBe(createDefaultVue3Config);
            expect(Vue3AdapterUtils.mergeVue3Configs).toBe(mergeVue3Configs);
            expect(Vue3AdapterUtils.createVue3Container).toBe(createVue3Container);
            expect(Vue3AdapterUtils.cleanupVue3Container).toBe(cleanupVue3Container);
            expect(Vue3AdapterUtils.getVue3Container).toBe(getVue3Container);
            expect(Vue3AdapterUtils.formatVue3Error).toBe(formatVue3Error);
            expect(Vue3AdapterUtils.createVue3ErrorInfo).toBe(createVue3ErrorInfo);
            expect(Vue3AdapterUtils.isVue3DevToolsAvailable).toBe(isVue3DevToolsAvailable);
            expect(Vue3AdapterUtils.enableVue3DevTools).toBe(enableVue3DevTools);
            expect(Vue3AdapterUtils.createVue3Adapter).toBe(createVue3Adapter);
            expect(Vue3AdapterUtils.createVue3MicroAppPlugin).toBe(createVue3MicroAppPlugin);
        });
    });
});