{"name": "sub-app-vue2", "version": "0.1.0", "description": "Micro-Core Vue 2.7 子应用示例", "type": "module", "scripts": {"dev": "vite --port 3007", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3007", "lint": "eslint src --ext .js,.vue", "lint:fix": "eslint src --ext .js,.vue --fix", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/adapter-vue2": "workspace:*", "@micro-core/builder-vite": "workspace:*", "vue": "^2.7.0", "vue-router": "^3.6.0"}, "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-vue2": "^2.3.0", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "vite": "^5.4.0", "vitest": "^3.2.4", "@vitest/coverage-v8": "^3.2.4"}, "keywords": ["micro-frontend", "sub-app", "vue2", "vite", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}