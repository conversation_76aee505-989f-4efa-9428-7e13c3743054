# 生命周期管理

Micro-Core 提供了完整的应用生命周期管理系统，支持应用的注册、加载、挂载、更新、卸载等各个阶段的精确控制。

## 生命周期概览

### 应用生命周期阶段

```
NOT_LOADED → LOADING → LOADED → MOUNTING → MOUNTED → UNMOUNTING → UNMOUNTED
     ↑                                                                    ↓
     └─────────────────── 应用销毁/重新加载 ←─────────────────────────────┘
```

### 生命周期状态

```typescript
enum ApplicationStatus {
  NOT_LOADED = 'NOT_LOADED',     // 未加载
  LOADING = 'LOADING',           // 加载中
  LOADED = 'LOADED',             // 已加载
  MOUNTING = 'MOUNTING',         // 挂载中
  MOUNTED = 'MOUNTED',           // 已挂载
  UNMOUNTING = 'UNMOUNTING',     // 卸载中
  UNMOUNTED = 'UNMOUNTED',       // 已卸载
  ERROR = 'ERROR'                // 错误状态
}
```

## 生命周期钩子

### 1. 全局生命周期钩子

在内核级别监听所有应用的生命周期事件。

```typescript
import { MicroCoreKernel } from '@micro-core/core';

const kernel = new MicroCoreKernel();

// 监听应用加载前
kernel.on('application:before-load', (app) => {
  console.log(`应用 ${app.name} 即将开始加载`);
  
  // 可以在这里进行预处理
  // 例如：设置加载状态、准备资源等
});

// 监听应用加载后
kernel.on('application:after-load', (app) => {
  console.log(`应用 ${app.name} 加载完成`);
  
  // 可以在这里进行后处理
  // 例如：缓存应用资源、记录加载时间等
});

// 监听应用挂载前
kernel.on('application:before-mount', (app) => {
  console.log(`应用 ${app.name} 即将挂载`);
  
  // 准备挂载环境
  // 例如：清理容器、设置样式等
});

// 监听应用挂载后
kernel.on('application:after-mount', (app) => {
  console.log(`应用 ${app.name} 挂载完成`);
  
  // 挂载后处理
  // 例如：发送分析数据、更新导航状态等
});

// 监听应用卸载前
kernel.on('application:before-unmount', (app) => {
  console.log(`应用 ${app.name} 即将卸载`);
  
  // 卸载前清理
  // 例如：保存状态、清理定时器等
});

// 监听应用卸载后
kernel.on('application:after-unmount', (app) => {
  console.log(`应用 ${app.name} 卸载完成`);
  
  // 卸载后清理
  // 例如：清理缓存、释放资源等
});

// 监听应用错误
kernel.on('application:error', (error, app) => {
  console.error(`应用 ${app.name} 发生错误:`, error);
  
  // 错误处理
  // 例如：错误上报、降级处理等
});
```

### 2. 应用级生命周期钩子

在注册应用时定义特定应用的生命周期处理。

```typescript
kernel.registerApplication({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeWhen: '/user',
  lifecycle: {
    beforeLoad: async (app) => {
      console.log(`${app.name} 开始加载前的准备工作`);
      
      // 异步预处理
      await prepareUserAppEnvironment();
      
      // 设置应用特定的全局变量
      window.__USER_APP_CONFIG__ = {
        apiUrl: 'https://api.example.com',
        version: '1.0.0'
      };
    },
    
    afterLoad: async (app) => {
      console.log(`${app.name} 加载完成后的处理`);
      
      // 验证应用是否正确加载
      if (!app.instance) {
        throw new Error('应用实例未正确创建');
      }
      
      // 记录加载性能
      const loadTime = performance.now() - app.loadStartTime;
      console.log(`${app.name} 加载耗时: ${loadTime}ms`);
    },
    
    beforeMount: async (app) => {
      console.log(`${app.name} 挂载前的准备`);
      
      // 准备挂载容器
      const container = document.querySelector(app.container);
      if (container) {
        container.innerHTML = ''; // 清空容器
        container.className = `micro-app ${app.name}-container`;
      }
      
      // 设置应用特定的样式
      const style = document.createElement('style');
      style.textContent = `
        .${app.name}-container {
          width: 100%;
          height: 100%;
          overflow: auto;
        }
      `;
      document.head.appendChild(style);
    },
    
    afterMount: async (app) => {
      console.log(`${app.name} 挂载完成后的处理`);
      
      // 发送挂载成功事件
      kernel.emit('user-app:mounted', {
        name: app.name,
        mountTime: Date.now()
      });
      
      // 初始化应用特定的功能
      if (app.instance && app.instance.initialize) {
        await app.instance.initialize();
      }
    },
    
    beforeUnmount: async (app) => {
      console.log(`${app.name} 卸载前的清理`);
      
      // 保存应用状态
      if (app.instance && app.instance.saveState) {
        const state = await app.instance.saveState();
        localStorage.setItem(`${app.name}-state`, JSON.stringify(state));
      }
      
      // 清理定时器和事件监听器
      if (app.instance && app.instance.cleanup) {
        await app.instance.cleanup();
      }
    },
    
    afterUnmount: async (app) => {
      console.log(`${app.name} 卸载完成后的清理`);
      
      // 清理应用特定的样式
      const styles = document.querySelectorAll(`style[data-app="${app.name}"]`);
      styles.forEach(style => style.remove());
      
      // 清理全局变量
      delete window.__USER_APP_CONFIG__;
      
      // 发送卸载完成事件
      kernel.emit('user-app:unmounted', {
        name: app.name,
        unmountTime: Date.now()
      });
    },
    
    onError: (error, app) => {
      console.error(`${app.name} 运行时错误:`, error);
      
      // 错误上报
      reportError({
        appName: app.name,
        error: error.message,
        stack: error.stack,
        timestamp: Date.now()
      });
      
      // 尝试恢复应用
      if (error.recoverable) {
        setTimeout(() => {
          kernel.remountApplication(app.name);
        }, 1000);
      }
    }
  }
});
```

## 微应用生命周期实现

### React 微应用生命周期

```typescript
// src/index.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import { ReactAdapter } from '@micro-core/adapter-react';
import App from './App';

let root: ReactDOM.Root | null = null;
let appInstance: any = null;

// 导出生命周期函数
export const bootstrap = ReactAdapter.bootstrap(async () => {
  console.log('[React App] 应用启动初始化');
  
  // 初始化应用级别的配置
  initializeAppConfig();
  
  // 注册错误边界
  setupErrorBoundary();
});

export const mount = ReactAdapter.mount(async (props) => {
  console.log('[React App] 应用挂载', props);
  
  const { container, ...otherProps } = props;
  
  // 创建 React 根节点
  root = ReactDOM.createRoot(container);
  
  // 渲染应用
  root.render(
    <React.StrictMode>
      <App {...otherProps} />
    </React.StrictMode>
  );
  
  // 保存应用实例引用
  appInstance = {
    initialize: async () => {
      console.log('[React App] 应用初始化完成');
      // 执行应用特定的初始化逻辑
    },
    
    saveState: async () => {
      console.log('[React App] 保存应用状态');
      // 返回需要保存的状态
      return {
        currentRoute: window.location.pathname,
        userPreferences: getUserPreferences()
      };
    },
    
    cleanup: async () => {
      console.log('[React App] 清理应用资源');
      // 清理定时器、事件监听器等
      clearAllTimers();
      removeEventListeners();
    }
  };
  
  return appInstance;
});

export const unmount = ReactAdapter.unmount(async () => {
  console.log('[React App] 应用卸载');
  
  if (root) {
    root.unmount();
    root = null;
  }
  
  appInstance = null;
  
  // 清理全局状态
  cleanupGlobalState();
});

export const update = ReactAdapter.update(async (props) => {
  console.log('[React App] 应用更新', props);
  
  if (root && appInstance) {
    const { container, ...otherProps } = props;
    
    // 重新渲染应用
    root.render(
      <React.StrictMode>
        <App {...otherProps} />
      </React.StrictMode>
    );
  }
});

// 辅助函数
function initializeAppConfig() {
  // 初始化应用配置
}

function setupErrorBoundary() {
  // 设置错误边界
}

function getUserPreferences() {
  // 获取用户偏好设置
  return {};
}

function clearAllTimers() {
  // 清理所有定时器
}

function removeEventListeners() {
  // 移除事件监听器
}

function cleanupGlobalState() {
  // 清理全局状态
}
```

### Vue 微应用生命周期

```typescript
// src/main.ts
import { createApp, App as VueApp } from 'vue';
import { VueAdapter } from '@micro-core/adapter-vue3';
import App from './App.vue';
import router from './router';
import store from './store';

let app: VueApp | null = null;
let appInstance: any = null;

// 导出生命周期函数
export const bootstrap = VueAdapter.bootstrap(async () => {
  console.log('[Vue App] 应用启动初始化');
  
  // 初始化全局配置
  initializeGlobalConfig();
});

export const mount = VueAdapter.mount(async (props) => {
  console.log('[Vue App] 应用挂载', props);
  
  const { container, basename, ...otherProps } = props;
  
  // 创建 Vue 应用实例
  app = createApp(App);
  
  // 配置路由基础路径
  if (basename) {
    router.options.history.base = basename;
  }
  
  // 注入 props 到全局属性
  app.config.globalProperties.$microProps = otherProps;
  
  // 使用插件
  app.use(router);
  app.use(store);
  
  // 挂载应用
  app.mount(container);
  
  // 创建应用实例接口
  appInstance = {
    initialize: async () => {
      console.log('[Vue App] 应用初始化完成');
      
      // 初始化路由守卫
      setupRouterGuards();
      
      // 初始化全局组件
      registerGlobalComponents();
    },
    
    saveState: async () => {
      console.log('[Vue App] 保存应用状态');
      
      return {
        route: router.currentRoute.value,
        storeState: store.state,
        userSettings: getUserSettings()
      };
    },
    
    cleanup: async () => {
      console.log('[Vue App] 清理应用资源');
      
      // 清理路由守卫
      cleanupRouterGuards();
      
      // 清理全局事件监听器
      cleanupGlobalListeners();
    },
    
    updateProps: (newProps) => {
      console.log('[Vue App] 更新属性', newProps);
      app.config.globalProperties.$microProps = newProps;
    }
  };
  
  return appInstance;
});

export const unmount = VueAdapter.unmount(async () => {
  console.log('[Vue App] 应用卸载');
  
  if (app) {
    app.unmount();
    app = null;
  }
  
  appInstance = null;
  
  // 重置路由
  router.replace('/');
  
  // 清理 store
  store.commit('RESET_STATE');
});

export const update = VueAdapter.update(async (props) => {
  console.log('[Vue App] 应用更新', props);
  
  if (appInstance && appInstance.updateProps) {
    appInstance.updateProps(props);
  }
});

// 辅助函数
function initializeGlobalConfig() {
  // 初始化全局配置
}

function setupRouterGuards() {
  // 设置路由守卫
}

function registerGlobalComponents() {
  // 注册全局组件
}

function getUserSettings() {
  // 获取用户设置
  return {};
}

function cleanupRouterGuards() {
  // 清理路由守卫
}

function cleanupGlobalListeners() {
  // 清理全局监听器
}
```

## 生命周期管理最佳实践

### 1. 错误处理和恢复

```typescript
// 应用级错误处理
kernel.registerApplication({
  name: 'resilient-app',
  entry: 'http://localhost:3001',
  container: '#app',
  activeWhen: '/app',
  lifecycle: {
    onError: async (error, app) => {
      console.error(`应用 ${app.name} 发生错误:`, error);
      
      // 错误分类处理
      if (error.type === 'LOAD_ERROR') {
        // 加载错误 - 尝试重新加载
        await retryLoadApplication(app);
      } else if (error.type === 'MOUNT_ERROR') {
        // 挂载错误 - 清理并重新挂载
        await cleanupAndRemount(app);
      } else if (error.type === 'RUNTIME_ERROR') {
        // 运行时错误 - 显示错误页面
        showErrorPage(app, error);
      }
    }
  }
});

// 错误恢复策略
async function retryLoadApplication(app, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await kernel.loadApplication(app.name);
      console.log(`应用 ${app.name} 重新加载成功`);
      return;
    } catch (error) {
      console.warn(`应用 ${app.name} 第 ${i + 1} 次重新加载失败:`, error);
      
      if (i === maxRetries - 1) {
        // 最后一次尝试失败，显示错误页面
        showErrorPage(app, error);
      } else {
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
  }
}
```

### 2. 性能监控

```typescript
// 生命周期性能监控
const performanceMonitor = {
  timings: new Map(),
  
  startTiming(appName, phase) {
    const key = `${appName}-${phase}`;
    this.timings.set(key, performance.now());
  },
  
  endTiming(appName, phase) {
    const key = `${appName}-${phase}`;
    const startTime = this.timings.get(key);
    
    if (startTime) {
      const duration = performance.now() - startTime;
      console.log(`[Performance] ${appName} ${phase}: ${duration.toFixed(2)}ms`);
      
      // 发送性能数据
      this.reportPerformance(appName, phase, duration);
      
      this.timings.delete(key);
    }
  },
  
  reportPerformance(appName, phase, duration) {
    // 发送到分析服务
    if (window.analytics) {
      window.analytics.track('micro-app-performance', {
        appName,
        phase,
        duration,
        timestamp: Date.now()
      });
    }
  }
};

// 在生命周期钩子中使用性能监控
kernel.on('application:before-load', (app) => {
  performanceMonitor.startTiming(app.name, 'load');
});

kernel.on('application:after-load', (app) => {
  performanceMonitor.endTiming(app.name, 'load');
});

kernel.on('application:before-mount', (app) => {
  performanceMonitor.startTiming(app.name, 'mount');
});

kernel.on('application:after-mount', (app) => {
  performanceMonitor.endTiming(app.name, 'mount');
});
```

### 3. 状态持久化

```typescript
// 应用状态持久化管理
const stateManager = {
  // 保存应用状态
  async saveAppState(appName, state) {
    try {
      const stateKey = `micro-app-state-${appName}`;
      const stateData = {
        state,
        timestamp: Date.now(),
        version: '1.0.0'
      };
      
      localStorage.setItem(stateKey, JSON.stringify(stateData));
      console.log(`应用 ${appName} 状态已保存`);
    } catch (error) {
      console.error(`保存应用 ${appName} 状态失败:`, error);
    }
  },
  
  // 恢复应用状态
  async restoreAppState(appName) {
    try {
      const stateKey = `micro-app-state-${appName}`;
      const stateData = localStorage.getItem(stateKey);
      
      if (stateData) {
        const { state, timestamp, version } = JSON.parse(stateData);
        
        // 检查状态是否过期（24小时）
        const isExpired = Date.now() - timestamp > 24 * 60 * 60 * 1000;
        
        if (!isExpired && version === '1.0.0') {
          console.log(`应用 ${appName} 状态已恢复`);
          return state;
        } else {
          // 清理过期状态
          localStorage.removeItem(stateKey);
        }
      }
    } catch (error) {
      console.error(`恢复应用 ${appName} 状态失败:`, error);
    }
    
    return null;
  },
  
  // 清理应用状态
  clearAppState(appName) {
    const stateKey = `micro-app-state-${appName}`;
    localStorage.removeItem(stateKey);
    console.log(`应用 ${appName} 状态已清理`);
  }
};

// 在生命周期钩子中使用状态管理
kernel.registerApplication({
  name: 'stateful-app',
  entry: 'http://localhost:3001',
  container: '#app',
  activeWhen: '/app',
  lifecycle: {
    afterMount: async (app) => {
      // 恢复应用状态
      const savedState = await stateManager.restoreAppState(app.name);
      
      if (savedState && app.instance && app.instance.restoreState) {
        await app.instance.restoreState(savedState);
      }
    },
    
    beforeUnmount: async (app) => {
      // 保存应用状态
      if (app.instance && app.instance.saveState) {
        const state = await app.instance.saveState();
        await stateManager.saveAppState(app.name, state);
      }
    }
  }
});
```

### 4. 资源清理

```typescript
// 资源清理管理器
const resourceManager = {
  resources: new Map(),
  
  // 注册资源
  registerResource(appName, resourceType, resource) {
    if (!this.resources.has(appName)) {
      this.resources.set(appName, new Map());
    }
    
    const appResources = this.resources.get(appName);
    
    if (!appResources.has(resourceType)) {
      appResources.set(resourceType, []);
    }
    
    appResources.get(resourceType).push(resource);
  },
  
  // 清理应用资源
  cleanupAppResources(appName) {
    const appResources = this.resources.get(appName);
    
    if (appResources) {
      // 清理定时器
      const timers = appResources.get('timers') || [];
      timers.forEach(timerId => clearTimeout(timerId));
      
      // 清理事件监听器
      const listeners = appResources.get('listeners') || [];
      listeners.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler);
      });
      
      // 清理样式
      const styles = appResources.get('styles') || [];
      styles.forEach(styleElement => {
        if (styleElement.parentNode) {
          styleElement.parentNode.removeChild(styleElement);
        }
      });
      
      // 清理脚本
      const scripts = appResources.get('scripts') || [];
      scripts.forEach(scriptElement => {
        if (scriptElement.parentNode) {
          scriptElement.parentNode.removeChild(scriptElement);
        }
      });
      
      // 清理应用资源记录
      this.resources.delete(appName);
      
      console.log(`应用 ${appName} 资源清理完成`);
    }
  }
};

// 在应用中注册资源
export const mount = async (props) => {
  const appName = 'my-app';
  
  // 注册定时器
  const timerId = setInterval(() => {
    console.log('定时任务执行');
  }, 1000);
  resourceManager.registerResource(appName, 'timers', timerId);
  
  // 注册事件监听器
  const handler = (event) => console.log('点击事件', event);
  document.addEventListener('click', handler);
  resourceManager.registerResource(appName, 'listeners', {
    element: document,
    event: 'click',
    handler
  });
  
  // 注册样式
  const style = document.createElement('style');
  style.textContent = '.my-app { color: red; }';
  document.head.appendChild(style);
  resourceManager.registerResource(appName, 'styles', style);
};

// 在卸载时清理资源
export const unmount = async () => {
  resourceManager.cleanupAppResources('my-app');
};
```

## 调试和监控

### 生命周期调试工具

```typescript
// 生命周期调试器
const lifecycleDebugger = {
  enabled: process.env.NODE_ENV === 'development',
  logs: [],
  
  log(phase, appName, data = {}) {
    if (!this.enabled) return;
    
    const logEntry = {
      timestamp: Date.now(),
      phase,
      appName,
      data,
      stack: new Error().stack
    };
    
    this.logs.push(logEntry);
    
    console.group(`[Lifecycle] ${phase} - ${appName}`);
    console.log('时间:', new Date(logEntry.timestamp).toISOString());
    console.log('数据:', data);
    console.log('调用栈:', logEntry.stack);
    console.groupEnd();
    
    // 保持最近100条日志
    if (this.logs.length > 100) {
      this.logs.shift();
    }
  },
  
  getLogs(appName) {
    return appName 
      ? this.logs.filter(log => log.appName === appName)
      : this.logs;
  },
  
  exportLogs() {
    return JSON.stringify(this.logs, null, 2);
  }
};

// 在生命周期钩子中使用调试器
kernel.on('application:before-load', (app) => {
  lifecycleDebugger.log('before-load', app.name, { entry: app.entry });
});

kernel.on('application:after-load', (app) => {
  lifecycleDebugger.log('after-load', app.name, { 
    status: app.status,
    loadTime: app.loadTime 
  });
});

// 在控制台中查看日志
if (typeof window !== 'undefined') {
  window.getLifecycleLogs = lifecycleDebugger.getLogs.bind(lifecycleDebugger);
  window.exportLifecycleLogs = lifecycleDebugger.exportLogs.bind(lifecycleDebugger);
}
```
