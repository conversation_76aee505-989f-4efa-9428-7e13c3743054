/**
 * 插件注册表实现
 *
 * @description 插件注册和发现机制
 * <AUTHOR> <<EMAIL>>
 */
import { BasePlugin } from './base-plugin';
import type { PluginConfig, PluginMetadata, PluginRegistryEntry } from './types';
/**
 * 插件注册表
 * 负责插件的注册、发现和元数据管理
 */
export declare class PluginRegistry {
    private readonly logger;
    private readonly entries;
    private readonly metadataCache;
    private readonly dependencyGraph;
    constructor();
    /**
     * 注册插件
     */
    register(pluginId: string, pluginClass: typeof BasePlugin, config?: PluginConfig, source?: string): void;
    /**
     * 注销插件
     */
    unregister(pluginId: string): boolean;
    /**
     * 获取插件类
     */
    getPluginClass(pluginId: string): typeof BasePlugin | undefined;
    /**
     * 获取插件配置
     */
    getPluginConfig(pluginId: string): PluginConfig | undefined;
    /**
     * 获取插件元数据
     */
    getPluginMetadata(pluginId: string): PluginMetadata | undefined;
    /**
     * 获取注册表项
     */
    getEntry(pluginId: string): PluginRegistryEntry | undefined;
    /**
     * 检查插件是否已注册
     */
    isRegistered(pluginId: string): boolean;
    /**
     * 获取所有已注册的插件ID
     */
    getRegisteredPluginIds(): string[];
    /**
     * 获取所有注册表项
     */
    getAllEntries(): PluginRegistryEntry[];
    /**
     * 按名称搜索插件
     */
    searchByName(name: string): PluginRegistryEntry[];
    /**
     * 按作者搜索插件
     */
    searchByAuthor(author: string): PluginRegistryEntry[];
    /**
     * 按关键词搜索插件
     */
    searchByKeywords(keywords: string[]): PluginRegistryEntry[];
    /**
     * 获取插件依赖
     */
    getDependencies(pluginId: string): string[];
    /**
     * 获取插件依赖者
     */
    getDependents(pluginId: string): string[];
    /**
     * 检查依赖关系
     */
    checkDependencies(pluginId: string): {
        missing: string[];
        circular: string[];
    };
    /**
     * 拓扑排序
     */
    topologicalSort(pluginIds?: string[]): string[];
    /**
     * 获取统计信息
     */
    getStats(): {
        totalPlugins: number;
        pluginsBySource: Record<string, number>;
        dependencyCount: number;
        averageDependencies: number;
    };
    /**
     * 清空注册表
     */
    clear(): void;
    /**
     * 导出注册表数据
     */
    export(): {
        entries: PluginRegistryEntry[];
        metadata: Record<string, PluginMetadata>;
        dependencies: Record<string, string[]>;
    };
    /**
     * 导入注册表数据
     */
    import(data: {
        entries: PluginRegistryEntry[];
        metadata: Record<string, PluginMetadata>;
        dependencies: Record<string, string[]>;
    }): void;
    /**
     * 验证插件ID
     */
    private validatePluginId;
    /**
     * 验证插件类
     */
    private validatePluginClass;
    /**
     * 验证元数据
     */
    private validateMetadata;
    /**
     * 更新依赖图
     */
    private updateDependencyGraph;
}
//# sourceMappingURL=plugin-registry.d.ts.map