/**
 * @fileoverview 性能监控工具函数
 * @description 提供性能监控相关的工具函数
 * <AUTHOR> <<EMAIL>>
 */

export interface PerformanceMetric {
    startTime: number
    endTime?: number
    duration?: number
}

export interface PerformanceMonitor {
    start(name: string): void
    end(name: string): void
    measure<T>(name: string, fn: () => T | Promise<T>): Promise<T>
    getMetrics(): Record<string, PerformanceMetric>
    clear(): void
}

/**
 * 创建性能监控器
 */
export function createPerformanceMonitor(): PerformanceMonitor {
    const metrics = new Map<string, PerformanceMetric>()

    return {
        start(name: string): void {
            metrics.set(name, {
                startTime: performance.now()
            })
        },

        end(name: string): void {
            const metric = metrics.get(name)
            if (metric && !metric.endTime) {
                metric.endTime = performance.now()
                metric.duration = metric.endTime - metric.startTime
            }
        },

        async measure<T>(name: string, fn: () => T | Promise<T>): Promise<T> {
            this.start(name)
            try {
                const result = await fn()
                this.end(name)
                return result
            } catch (error) {
                this.end(name)
                throw error
            }
        },

        getMetrics(): Record<string, PerformanceMetric> {
            const result: Record<string, PerformanceMetric> = {}
            metrics.forEach((metric, name) => {
                result[name] = { ...metric }
            })
            return result
        },

        clear(): void {
            metrics.clear()
        }
    }
}