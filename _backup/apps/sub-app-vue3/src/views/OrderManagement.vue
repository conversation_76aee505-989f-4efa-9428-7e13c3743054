<template>
  <div class="order-management">
    <div class="page-header">
      <h3>订单管理</h3>
      <div class="header-actions">
        <select v-model="statusFilter" class="status-filter">
          <option value="">全部状态</option>
          <option value="pending">待处理</option>
          <option value="processing">处理中</option>
          <option value="shipped">已发货</option>
          <option value="delivered">已送达</option>
          <option value="cancelled">已取消</option>
        </select>
        <button @click="refreshOrders" class="refresh-btn">刷新</button>
      </div>
    </div>

    <div class="order-stats">
      <div class="stat-card">
        <div class="stat-icon">📋</div>
        <div class="stat-content">
          <div class="stat-number">{{ filteredOrders.length }}</div>
          <div class="stat-label">订单总数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">💰</div>
        <div class="stat-content">
          <div class="stat-number">¥{{ totalRevenue.toLocaleString() }}</div>
          <div class="stat-label">总收入</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">⏳</div>
        <div class="stat-content">
          <div class="stat-number">{{ pendingOrders }}</div>
          <div class="stat-label">待处理</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <div class="stat-number">{{ completedOrders }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
    </div>

    <div class="order-table">
      <table>
        <thead>
          <tr>
            <th>订单号</th>
            <th>客户</th>
            <th>产品</th>
            <th>金额</th>
            <th>状态</th>
            <th>下单时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="order in filteredOrders" :key="order.id">
            <td class="order-id">#{{ order.id }}</td>
            <td>{{ order.customer }}</td>
            <td>{{ order.product }}</td>
            <td class="amount">¥{{ order.amount.toLocaleString() }}</td>
            <td>
              <span :class="['status', order.status]">
                {{ getStatusText(order.status) }}
              </span>
            </td>
            <td>{{ formatDate(order.createdAt) }}</td>
            <td>
              <button @click="viewOrder(order)" class="view-btn">查看</button>
              <button 
                v-if="order.status === 'pending'" 
                @click="processOrder(order)" 
                class="process-btn"
              >
                处理
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface Order {
  id: string
  customer: string
  product: string
  amount: number
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
  createdAt: Date
}

const statusFilter = ref('')

const orders = ref<Order[]>([
  {
    id: '20240101001',
    customer: '张三',
    product: 'iPhone 15 Pro',
    amount: 8999,
    status: 'delivered',
    createdAt: new Date('2024-01-01')
  },
  {
    id: '20240101002',
    customer: '李四',
    product: 'MacBook Pro',
    amount: 15999,
    status: 'shipped',
    createdAt: new Date('2024-01-02')
  },
  {
    id: '20240101003',
    customer: '王五',
    product: 'iPad Air',
    amount: 4599,
    status: 'processing',
    createdAt: new Date('2024-01-03')
  },
  {
    id: '20240101004',
    customer: '赵六',
    product: 'Apple Watch',
    amount: 2999,
    status: 'pending',
    createdAt: new Date('2024-01-04')
  },
  {
    id: '20240101005',
    customer: '孙七',
    product: 'AirPods Pro',
    amount: 1899,
    status: 'cancelled',
    createdAt: new Date('2024-01-05')
  }
])

const filteredOrders = computed(() => {
  if (!statusFilter.value) {
    return orders.value
  }
  return orders.value.filter(order => order.status === statusFilter.value)
})

const totalRevenue = computed(() => {
  return orders.value
    .filter(order => order.status !== 'cancelled')
    .reduce((total, order) => total + order.amount, 0)
})

const pendingOrders = computed(() => {
  return orders.value.filter(order => order.status === 'pending').length
})

const completedOrders = computed(() => {
  return orders.value.filter(order => order.status === 'delivered').length
})

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    shipped: '已发货',
    delivered: '已送达',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const viewOrder = (order: Order) => {
  alert(`订单详情:\n订单号: ${order.id}\n客户: ${order.customer}\n产品: ${order.product}\n金额: ¥${order.amount}`)
}

const processOrder = (order: Order) => {
  if (confirm(`确定要处理订单 #${order.id} 吗？`)) {
    order.status = 'processing'
  }
}

const refreshOrders = () => {
  console.log('刷新订单列表')
  // 这里可以添加刷新逻辑
}

onMounted(() => {
  console.log('订单管理页面已加载')
})
</script>

<style scoped>
.order-management {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.page-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.status-filter {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

.refresh-btn {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.refresh-btn:hover {
  background: #138496;
}

.order-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.stat-label {
  color: #6c757d;
  font-size: 0.9rem;
}

.order-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

tbody tr:hover {
  background: #f8f9fa;
}

.order-id {
  font-family: monospace;
  font-weight: bold;
  color: #007bff;
}

.amount {
  font-weight: bold;
  color: #28a745;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status.pending {
  background: #fff3cd;
  color: #856404;
}

.status.processing {
  background: #cce5ff;
  color: #004085;
}

.status.shipped {
  background: #d1ecf1;
  color: #0c5460;
}

.status.delivered {
  background: #d4edda;
  color: #155724;
}

.status.cancelled {
  background: #f8d7da;
  color: #721c24;
}

.view-btn, .process-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  margin-right: 0.5rem;
  transition: background-color 0.3s;
}

.view-btn {
  background: #6c757d;
  color: white;
}

.view-btn:hover {
  background: #545b62;
}

.process-btn {
  background: #28a745;
  color: white;
}

.process-btn:hover {
  background: #218838;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .header-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .order-stats {
    grid-template-columns: 1fr;
  }
  
  .order-table {
    overflow-x: auto;
  }
  
  table {
    min-width: 800px;
  }
}
</style>