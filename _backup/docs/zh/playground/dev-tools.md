# 开发工具

Micro-Core 提供了完整的开发工具套件，包含调试面板、性能分析、配置生成器、代码生成器等工具，为微前端开发提供全方位的支持。

## 📋 目录

- [工具概述](#工具概述)
- [调试工具](#调试工具)
- [性能工具](#性能工具)
- [配置工具](#配置工具)
- [代码生成器](#代码生成器)
- [测试工具](#测试工具)
- [构建工具](#构建工具)
- [部署工具](#部署工具)
- [监控工具](#监控工具)

## 工具概述

### 🛠️ 开发工具架构

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core 开发工具生态                      │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    开发时工具                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 调试面板     │  │ 性能分析器   │  │ 配置生成器           │ │ │
│  │  │ • 应用监控   │  │ • 实时监控   │  │ • 可视化配置        │ │ │
│  │  │ • 状态管理   │  │ • 性能报告   │  │ • 配置验证          │ │ │
│  │  │ • 通信调试   │  │ • 优化建议   │  │ • 模板生成          │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    构建时工具                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 代码生成器   │  │ 构建优化器   │  │ 测试工具            │ │ │
│  │  │ • 应用模板   │  │ • 打包优化   │  │ • 单元测试          │ │ │
│  │  │ • 组件生成   │  │ • 代码分割   │  │ • 集成测试          │ │ │
│  │  │ • 路由生成   │  │ • 资源优化   │  │ • E2E测试           │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    运行时工具                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 监控工具     │  │ 部署工具     │  │ 运维工具            │ │ │
│  │  │ • 性能监控   │  │ • 自动部署   │  │ • 日志分析          │ │ │
│  │  │ • 错误追踪   │  │ • 版本管理   │  │ • 健康检查          │ │ │
│  │  │ • 用户分析   │  │ • 回滚机制   │  │ • 告警通知          │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 🎯 工具特性

```typescript
// 开发工具配置
const devToolsConfig = {
  // 调试工具
  debug: {
    panel: true,           // 调试面板
    console: true,         // 控制台增强
    network: true,         // 网络监控
    performance: true      // 性能监控
  },
  
  // 开发服务器
  devServer: {
    port: 3000,
    hot: true,             // 热更新
    proxy: {},             // 代理配置
    mock: true             // 数据模拟
  },
  
  // 构建工具
  build: {
    analyzer: true,        // 打包分析
    optimization: true,    // 构建优化
    sourcemap: true,       // 源码映射
    minification: true     // 代码压缩
  },
  
  // 测试工具
  testing: {
    unit: true,            // 单元测试
    integration: true,     // 集成测试
    e2e: true,             // 端到端测试
    coverage: true         // 覆盖率报告
  }
}
```

## 调试工具

### 🔍 调试面板

```typescript
// 调试面板集成
import { DebugPanel } from '@micro-core/dev-tools'

const debugPanel = new DebugPanel(microCore, {
  // 面板配置
  position: 'bottom-right',
  theme: 'dark',
  size: 'medium',
  
  // 功能模块
  modules: {
    appMonitor: true,      // 应用监控
    stateManager: true,    // 状态管理
    communication: true,   // 通信调试
    performance: true,     // 性能分析
    errorTracker: true,    // 错误追踪
    networkMonitor: true,  // 网络监控
    configEditor: true     // 配置编辑
  },
  
  // 快捷键
  hotkeys: {
    'Ctrl+Shift+D': 'toggle',
    'Ctrl+Shift+C': 'clear',
    'Ctrl+Shift+E': 'export'
  }
})

// 启动调试面板
debugPanel.start()
```

### 🖥️ 控制台增强

```typescript
// 控制台增强工具
class ConsoleEnhancer {
  constructor(private microCore: MicroCore) {
    this.enhanceConsole()
  }
  
  private enhanceConsole() {
    // 增强 console.log
    const originalLog = console.log
    console.log = (...args) => {
      // 添加时间戳和应用信息
      const timestamp = new Date().toISOString()
      const currentApp = this.microCore.getCurrentApp()
      const prefix = `[${timestamp}] [${currentApp?.name || 'main'}]`
      
      originalLog(prefix, ...args)
      
      // 发送到调试面板
      this.sendToDebugPanel('log', args)
    }
    
    // 增强 console.error
    const originalError = console.error
    console.error = (...args) => {
      const timestamp = new Date().toISOString()
      const currentApp = this.microCore.getCurrentApp()
      const prefix = `[${timestamp}] [${currentApp?.name || 'main'}] [ERROR]`
      
      originalError(prefix, ...args)
      
      // 发送到错误追踪
      this.sendToErrorTracker(args)
    }
  }
  
  private sendToDebugPanel(level: string, args: any[]) {
    // 发送日志到调试面板
    window.postMessage({
      type: 'micro-core-log',
      level,
      args,
      timestamp: Date.now()
    }, '*')
  }
  
  private sendToErrorTracker(args: any[]) {
    // 发送错误到错误追踪系统
    window.postMessage({
      type: 'micro-core-error',
      args,
      timestamp: Date.now(),
      stack: new Error().stack
    }, '*')
  }
}
```

## 性能工具

### 📊 性能分析器

```typescript
// 性能分析工具
import { PerformanceAnalyzer } from '@micro-core/dev-tools'

const performanceAnalyzer = new PerformanceAnalyzer(microCore, {
  // 监控配置
  monitoring: {
    webVitals: true,       // Web Vitals 指标
    loading: true,         // 加载性能
    runtime: true,         // 运行时性能
    memory: true,          // 内存使用
    network: true,         // 网络性能
    rendering: true        // 渲染性能
  },
  
  // 采样设置
  sampling: {
    interval: 1000,        // 采样间隔
    duration: 300000,      // 监控时长
    bufferSize: 1000       // 缓冲区大小
  },
  
  // 性能阈值
  thresholds: {
    loadTime: 3000,
    fcp: 1800,
    lcp: 2500,
    fid: 100,
    cls: 0.1
  }
})

// 开始性能监控
performanceAnalyzer.start()

// 获取性能报告
const report = await performanceAnalyzer.getReport()
console.log('性能报告:', report)
```

### 🎯 性能优化建议

```typescript
// 性能优化建议生成器
class PerformanceOptimizer {
  generateOptimizationPlan(report: PerformanceReport): OptimizationPlan {
    const plan: OptimizationPlan = {
      priority: 'high',
      estimatedImpact: 'significant',
      tasks: []
    }
    
    // 分析加载性能
    if (report.loadTime > 3000) {
      plan.tasks.push({
        category: 'loading',
        title: '优化应用加载时间',
        description: '当前加载时间过长，需要优化',
        actions: [
          '启用代码分割',
          '优化资源加载',
          '使用预加载策略',
          '启用缓存机制'
        ],
        priority: 'high',
        estimatedTime: '2-4 小时'
      })
    }
    
    // 分析内存使用
    if (report.memoryUsage > 50 * 1024 * 1024) {
      plan.tasks.push({
        category: 'memory',
        title: '优化内存使用',
        description: '内存使用量较高，可能存在内存泄漏',
        actions: [
          '检查事件监听器',
          '优化对象引用',
          '使用对象池',
          '定期垃圾回收'
        ],
        priority: 'medium',
        estimatedTime: '4-8 小时'
      })
    }
    
    return plan
  }
}
```

## 配置工具

### ⚙️ 可视化配置编辑器

```typescript
// 配置编辑器
import { ConfigEditor } from '@micro-core/dev-tools'

const configEditor = new ConfigEditor({
  // 配置模式
  mode: 'visual',        // visual | code | hybrid
  
  // 验证规则
  validation: {
    enabled: true,
    strict: false,
    customRules: []
  },
  
  // 预设模板
  templates: [
    'basic-spa',
    'multi-app',
    'micro-frontend',
    'custom'
  ],
  
  // 实时预览
  preview: {
    enabled: true,
    autoRefresh: true,
    delay: 500
  }
})

// 加载配置
configEditor.loadConfig(currentConfig)

// 监听配置变化
configEditor.on('config-change', (newConfig) => {
  // 验证配置
  const validation = configEditor.validateConfig(newConfig)
  if (validation.valid) {
    // 应用新配置
    microCore.updateConfig(newConfig)
  }
})
```

### 🎨 配置生成器界面

```vue
<template>
  <div class="config-editor">
    <div class="editor-header">
      <h2>微前端配置编辑器</h2>
      <div class="editor-actions">
        <button @click="loadTemplate" class="btn-template">加载模板</button>
        <button @click="validateConfig" class="btn-validate">验证配置</button>
        <button @click="previewConfig" class="btn-preview">预览</button>
        <button @click="exportConfig" class="btn-export">导出</button>
      </div>
    </div>
    
    <div class="editor-content">
      <!-- 应用配置 -->
      <div class="config-section">
        <h3>应用配置</h3>
        <div class="app-list">
          <div 
            v-for="(app, index) in config.apps" 
            :key="index"
            class="app-item"
          >
            <div class="app-header">
              <input v-model="app.name" placeholder="应用名称" />
              <button @click="removeApp(index)" class="btn-remove">删除</button>
            </div>
            
            <div class="app-fields">
              <div class="field-group">
                <label>入口地址:</label>
                <input v-model="app.entry" placeholder="http://localhost:3001" />
              </div>
              
              <div class="field-group">
                <label>激活条件:</label>
                <input v-model="app.activeWhen" placeholder="/app-name" />
              </div>
              
              <div class="field-group">
                <label>容器选择器:</label>
                <input v-model="app.container" placeholder="#app-container" />
              </div>
            </div>
          </div>
          
          <button @click="addApp" class="btn-add-app">添加应用</button>
        </div>
      </div>
      
      <!-- 全局配置 -->
      <div class="config-section">
        <h3>全局配置</h3>
        <div class="global-config">
          <div class="field-group">
            <label>
              <input type="checkbox" v-model="config.prefetch" />
              启用预加载
            </label>
          </div>
          
          <div class="field-group">
            <label>
              <input type="checkbox" v-model="config.sandbox.css" />
              CSS 沙箱
            </label>
          </div>
          
          <div class="field-group">
            <label>
              <input type="checkbox" v-model="config.sandbox.js" />
              JavaScript 沙箱
            </label>
          </div>
        </div>
      </div>
      
      <!-- 插件配置 -->
      <div class="config-section">
        <h3>插件配置</h3>
        <div class="plugin-list">
          <div 
            v-for="plugin in availablePlugins" 
            :key="plugin.name"
            class="plugin-item"
          >
            <label>
              <input 
                type="checkbox" 
                :checked="isPluginEnabled(plugin.name)"
                @change="togglePlugin(plugin.name)"
              />
              {{ plugin.displayName }}
            </label>
            <p class="plugin-description">{{ plugin.description }}</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 配置预览 -->
    <div class="config-preview" v-if="showPreview">
      <h3>配置预览</h3>
      <pre class="config-json">{{ configJson }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const config = ref({
  apps: [],
  prefetch: true,
  sandbox: {
    css: true,
    js: true
  },
  plugins: []
})

const showPreview = ref(false)

const configJson = computed(() => {
  return JSON.stringify(config.value, null, 2)
})

const availablePlugins = ref([
  {
    name: 'router',
    displayName: '路由插件',
    description: '提供统一的路由管理功能'
  },
  {
    name: 'communication',
    displayName: '通信插件',
    description: '提供应用间通信功能'
  },
  {
    name: 'auth',
    displayName: '认证插件',
    description: '提供统一的身份认证功能'
  }
])

const addApp = () => {
  config.value.apps.push({
    name: '',
    entry: '',
    activeWhen: '',
    container: '#app-container'
  })
}

const removeApp = (index: number) => {
  config.value.apps.splice(index, 1)
}

const togglePlugin = (pluginName: string) => {
  const index = config.value.plugins.indexOf(pluginName)
  if (index > -1) {
    config.value.plugins.splice(index, 1)
  } else {
    config.value.plugins.push(pluginName)
  }
}

const isPluginEnabled = (pluginName: string) => {
  return config.value.plugins.includes(pluginName)
}
</script>
```

## 代码生成器

### 🏗️ 应用模板生成器

```typescript
// 代码生成器
import { CodeGenerator } from '@micro-core/dev-tools'

const codeGenerator = new CodeGenerator({
  // 模板配置
  templates: {
    'react-app': {
      name: 'React 微应用',
      description: '基于 React 的微前端应用模板',
      files: [
        'src/index.tsx',
        'src/App.tsx',
        'src/bootstrap.tsx',
        'package.json',
        'webpack.config.js'
      ]
    },
    
    'vue-app': {
      name: 'Vue 微应用',
      description: '基于 Vue 的微前端应用模板',
      files: [
        'src/main.ts',
        'src/App.vue',
        'src/bootstrap.ts',
        'package.json',
        'vite.config.ts'
      ]
    }
  },
  
  // 生成配置
  generation: {
    outputDir: './generated',
    overwrite: false,
    format: true
  }
})

// 生成应用
const appConfig = {
  name: 'my-app',
  template: 'react-app',
  framework: 'react',
  typescript: true,
  router: true,
  stateManagement: 'redux'
}

await codeGenerator.generateApp(appConfig)
```

### 📝 代码模板

```typescript
// React 应用模板
const reactAppTemplate = {
  'src/index.tsx': `
import React from 'react'
import ReactDOM from 'react-dom/client'
import { MicroApp } from '@micro-core/react'
import App from './App'

// 微应用配置
const microApp = new MicroApp({
  name: '{{appName}}',
  mount: (props) => {
    const container = props.container || document.getElementById('root')
    const root = ReactDOM.createRoot(container)
    root.render(<App {...props} />)
    return root
  },
  unmount: (root) => {
    root.unmount()
  }
})

// 导出生命周期函数
export const { bootstrap, mount, unmount } = microApp.getLifecycles()

// 独立运行
if (!window.__MICRO_CORE__) {
  const root = ReactDOM.createRoot(document.getElementById('root')!)
  root.render(<App />)
}
`,

  'src/App.tsx': `
import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Home from './pages/Home'
import About from './pages/About'

interface AppProps {
  basename?: string
}

const App: React.FC<AppProps> = ({ basename = '/' }) => {
  return (
    <Router basename={basename}>
      <div className="app">
        <header>
          <h1>{{appName}}</h1>
        </header>
        
        <main>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
          </Routes>
        </main>
      </div>
    </Router>
  )
}

export default App
`,

  'package.json': `
{
  "name": "{{appName}}",
  "version": "1.0.0",
  "scripts": {
    "dev": "webpack serve --mode development",
    "build": "webpack --mode production",
    "test": "jest"
  },
  "dependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "react-router-dom": "^6.0.0",
    "@micro-core/react": "^1.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "typescript": "^5.0.0",
    "webpack": "^5.0.0",
    "webpack-cli": "^5.0.0",
    "webpack-dev-server": "^4.0.0"
  }
}
`
}
```

## 测试工具

### 🧪 测试套件

```typescript
// 测试工具集成
import { TestRunner } from '@micro-core/dev-tools'

const testRunner = new TestRunner({
  // 测试配置
  config: {
    testMatch: ['**/__tests__/**/*.test.{js,ts,tsx}'],
    collectCoverage: true,
    coverageDirectory: 'coverage',
    coverageReporters: ['text', 'html', 'lcov']
  },
  
  // 微前端测试环境
  microFrontend: {
    mockApps: true,
    isolateTests: true,
    sharedState: false
  }
})

// 运行测试
await testRunner.runTests()

// 生成覆盖率报告
await testRunner.generateCoverageReport()
```

### 🔬 微前端测试工具

```typescript
// 微前端测试辅助工具
class MicroFrontendTestUtils {
  // 创建测试环境
  static createTestEnvironment(config: TestEnvironmentConfig) {
    return {
      microCore: new MicroCore(config.microCoreConfig),
      mockApps: this.createMockApps(config.apps),
      testContainer: this.createTestContainer()
    }
  }
  
  // 创建模拟应用
  static createMockApps(appConfigs: AppConfig[]) {
    return appConfigs.map(config => ({
      name: config.name,
      mount: jest.fn(),
      unmount: jest.fn(),
      update: jest.fn()
    }))
  }
  
  // 模拟应用通信
  static mockCommunication() {
    const eventBus = {
      emit: jest.fn(),
      on: jest.fn(),
      off: jest.fn()
    }
    
    return eventBus
  }
  
  // 等待应用加载
  static async waitForAppLoad(appName: string, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`应用 ${appName} 加载超时`))
      }, timeout)
      
      window.addEventListener('micro-app-loaded', (event) => {
        if (event.detail.appName === appName) {
          clearTimeout(timer)
          resolve(event.detail)
        }
      })
    })
  }
}

// 测试示例
describe('微前端应用测试', () => {
  let testEnv: any
  
  beforeEach(() => {
    testEnv = MicroFrontendTestUtils.createTestEnvironment({
      microCoreConfig: {
        apps: [
          { name: 'app-a', entry: '/mock-app-a' },
          { name: 'app-b', entry: '/mock-app-b' }
        ]
      },
      apps: [
        { name: 'app-a' },
        { name: 'app-b' }
      ]
    })
  })
  
  test('应用加载测试', async () => {
    await testEnv.microCore.loadApp('app-a')
    
    expect(testEnv.mockApps[0].mount).toHaveBeenCalled()
  })
  
  test('应用通信测试', () => {
    const eventBus = MicroFrontendTestUtils.mockCommunication()
    
    eventBus.emit('test-event', { data: 'test' })
    
    expect(eventBus.emit).toHaveBeenCalledWith('test-event', { data: 'test' })
  })
})
```

## 构建工具

### 📦 构建优化器

```typescript
// 构建优化工具
import { BuildOptimizer } from '@micro-core/dev-tools'

const buildOptimizer = new BuildOptimizer({
  // 优化配置
  optimization: {
    codesplitting: true,      // 代码分割
    treeshaking: true,        // 摇树优化
    minification: true,       // 代码压缩
    bundleAnalysis: true      // 打包分析
  },
  
  // 输出配置
  output: {
    format: 'umd',           // 输出格式
    library: 'MicroApp',     // 库名称
    filename: '[name].[hash].js',
    chunkFilename: '[name].[chunkhash].js'
  },
  
  // 外部依赖
  externals: {
    'react': 'React',
    'react-dom': 'ReactDOM',
    'vue': 'Vue'
  }
})

// 构建应用
await buildOptimizer.build({
  entry: './src/index.ts',
  output: './dist'
})

// 分析打包结果
const analysis = await buildOptimizer.analyze()
console.log('打包分析:', analysis)
```

### 🎯 Webpack 配置生成器

```typescript
// Webpack 配置生成器
class WebpackConfigGenerator {
  generateConfig(options: WebpackOptions): webpack.Configuration {
    const config: webpack.Configuration = {
      mode: options.mode || 'development',
      
      entry: {
        main: options.entry || './src/index.ts'
      },
      
      output: {
        path: path.resolve(options.outputPath || './dist'),
        filename: '[name].[contenthash].js',
        library: options.libraryName,
        libraryTarget: 'umd',
        globalObject: 'this'
      },
      
      module: {
        rules: [
          // TypeScript
          {
            test: /\.tsx?$/,
            use: 'ts-loader',
            exclude: /node_modules/
          },
          
          // CSS
          {
            test: /\.css$/,
            use: [
              'style-loader',
              'css-loader',
              'postcss-loader'
            ]
          },
          
          // 资源文件
          {
            test: /\.(png|jpg|gif|svg)$/,
            type: 'asset/resource'
          }
        ]
      },
      
      plugins: [
        new webpack.DefinePlugin({
          'process.env.NODE_ENV': JSON.stringify(options.mode)
        }),
        
        new HtmlWebpackPlugin({
          template: options.template || './public/index.html'
        })
      ],
      
      resolve: {
        extensions: ['.tsx', '.ts', '.js', '.jsx']
      },
      
      externals: options.externals || {}
    }
    
    // 开发环境配置
    if (options.mode === 'development') {
      config.devtool = 'eval-source-map'
      config.devServer = {
        port: options.port || 3000,
        hot: true,
        historyApiFallback: true
      }
    }
    
    // 生产环境配置
    if (options.mode === 'production') {
      config.optimization = {
        minimize: true,
        minimizer: [
          new TerserPlugin(),
          new CssMinimizerPlugin()
        ],
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all'
            }
          }
        }
      }
    }
    
    return config
  }
}
```

## 部署工具

### 🚀 自动化部署

```typescript
// 部署工具
import { DeploymentManager } from '@micro-core/dev-tools'

const deploymentManager = new DeploymentManager({
  // 部署配置
  environments: {
    development: {
      host: 'dev.example.com',
      path: '/var/www/dev',
      branch: 'develop'
    },
    staging: {
      host: 'staging.example.com',
      path: '/var/www/staging',
      branch: 'staging'
    },
    production: {
      host: 'prod.example.com',
      path: '/var/www/prod',
      branch: 'main'
    }
  },
  
  // 部署策略
  strategy: {
    type: 'blue-green',      // 蓝绿部署
    healthCheck: true,       // 健康检查
    rollback: true,          // 自动回滚
    notifications: true      // 部署通知
  }
})

// 部署到指定环境
await deploymentManager.deploy('production', {
  version: '1.2.0',
  description: '新功能发布'
})

// 回滚部署
await deploymentManager.rollback('production', {
  version: '1.1.0',
  reason: '发现严重bug'
})
```

### 📋 部署脚本生成器

```typescript
// 部署脚本生成器
class DeploymentScriptGenerator {
  generateDockerfile(config: DockerConfig): string {
    return `
FROM node:18-alpine

WORKDIR /app

# 复制依赖文件
COPY package*.json ./
RUN npm ci --only=production

# 复制应用代码
COPY dist/ ./dist/
COPY public/ ./public/

# 暴露端口
EXPOSE ${config.port || 3000}

# 启动应用
CMD ["npm", "start"]
`
  }
  
  generateDockerCompose(apps: AppConfig[]): string {
    const services = apps.map(app => `
  ${app.name}:
    build: ./${app.name}
    ports:
      - "${app.port}:3000"
    environment:
      - NODE_ENV=production
    networks:
      - micro-frontend-network
`).join('')
    
    return `
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
${apps.map(app => `      - ${app.name}`).join('\n')}
    networks:
      - micro-frontend-network

${services}

networks:
  micro-frontend-network:
    driver: bridge
`
  }
  
  generateNginxConfig(apps: AppConfig[]): string {
    const upstreams = apps.map(app => `
upstream ${app.name} {
    server ${app.name}:3000;
}
`).join('')
    
    const locations = apps.map(app => `
        location ${app.path} {
            proxy_pass http://${app.name};
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
`).join('')
    
    return `
events {
    worker_connections 1024;
}

http {
${upstreams}

    server {
        listen 80;
        server_name localhost;

        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri $uri/ /index.html;
        }

${locations}
    }
}
`
  }
}
```

## 监控工具

### 📊 应用监控

```typescript
// 监控工具
import { MonitoringService } from '@micro-core/dev-tools'

const monitoringService = new MonitoringService({
  // 监控配置
  metrics: {
    performance: true,       // 性能监控
    errors: true,           // 错误监控
    usage: true,            // 使用情况监控
    business: true          // 业务指标监控
  },
  
  // 数据收集
  collection: {
    interval: 30000,        // 收集间隔
    batchSize: 100,         // 批量大小
    storage: 'localStorage' // 存储方式
  },
  
  // 告警配置
  alerts: {
    errorRate: 0.05,        // 错误率阈值
    responseTime: 3000,     // 响应时间阈值
    memoryUsage: 0.8        // 内存使用阈值
  }
})

// 开始监控
monitoringService.start()

// 自定义指标
monitoringService.track('user-action', {
  action: 'click',
  element: 'button',
  page: '/dashboard'
})

// 获取监控报告
const report = await monitoringService.getReport()
console.log('监控报告:', report)
```

### 🔔 告警系统

```typescript
// 告警系统
class AlertSystem {
  private rules: AlertRule[] = []
  private channels: AlertChannel[] = []
  
  // 添加告警规则
  addRule(rule: AlertRule) {
    this.rules.push(rule)
  }
  
  // 添加告警渠道
  addChannel(channel: AlertChannel) {
    this.channels.push(channel)
  }
  
  // 检查告警
  checkAlerts(metrics: Metrics) {
    for (const rule of this.rules) {
      if (this.evaluateRule(rule, metrics)) {
        this.triggerAlert(rule, metrics)
      }
    }
  }
  
  private evaluateRule(rule: AlertRule, metrics: Metrics): boolean {
    switch (rule.type) {
      case 'threshold':
        return metrics[rule.metric] > rule.threshold
      
      case 'trend':
        return this.evaluateTrend(rule, metrics)
      
      case 'anomaly':
        return this.detectAnomaly(rule, metrics)
      
      default:
        return false
    }
  }
  
  private triggerAlert(rule: AlertRule, metrics: Metrics) {
    const alert: Alert = {
      id: Date.now().toString(),
      rule: rule.name,
      severity: rule.severity,
      message: rule.message,
      timestamp: new Date(),
      metrics
    }
    
    // 发送到所有渠道
    for (const channel of this.channels) {
      channel.send(alert)
    }
  }
}

// 告警规则示例
const alertRules: AlertRule[] = [
  {
    name: '错误率过高',
    type: 'threshold',
    metric: 'errorRate',
    threshold: 0.05,
    severity: 'high',
    message: '应用错误率超过5%'
  },
  {
    name: '响应时间过长',
    type: 'threshold',
    metric: 'responseTime',
    threshold: 3000,
    severity: 'medium',
    message: '应用响应时间超过3秒'
  }
]
```

## 工具集成

### 🔧 开发工具套件

```typescript
// 完整的开发工具套件
import { DevToolsSuite } from '@micro-core/dev-tools'

const devTools = new DevToolsSuite({
  // 工具配置
  tools: {
    debugPanel: {
      enabled: true,
      position: 'bottom-right'
    },
    
    performanceAnalyzer: {
      enabled: true,
      monitoring: ['webVitals', 'loading', 'runtime']
    },
    
    configEditor: {
      enabled: true,
      mode: 'visual'
    },
    
    codeGenerator: {
      enabled: true,
      templates: ['react-app', 'vue-app']
    },
    
    testRunner: {
      enabled: true,
      coverage: true
    },
    
    buildOptimizer: {
      enabled: true,
      analysis: true
    },
    
    deploymentManager: {
      enabled: false // 生产环境禁用
    },
    
    monitoringService: {
      enabled: true,
      alerts: true
    }
  },
  
  // 全局配置
  global: {
    theme: 'dark',
    language: 'zh-CN',
    shortcuts: true
  }
})

// 初始化工具套件
await devTools.initialize(microCore)

// 启动所有工具
devTools.start()

// 获取工具实例
const debugPanel = devTools.getTool('debugPanel')
const performanceAnalyzer = devTools.getTool('performanceAnalyzer')
```

### 🎨 工具界面集成

```vue
<template>
  <div class="dev-tools-suite">
    <!-- 工具栏 -->
    <div class="tools-toolbar">
      <div class="tool-tabs">
        <button 
          v-for="tool in enabledTools" 
          :key="tool.name"
          :class="['tool-tab', { active: activeTool === tool.name }]"
          @click="activeTool = tool.name"
        >
          <i :class="tool.icon"></i>
          {{ tool.displayName }}
        </button>
      </div>
      
      <div class="toolbar-actions">
        <button @click="toggleTheme" class="btn-theme">
          <i :class="theme === 'dark' ? 'icon-sun' : 'icon-moon'"></i>
        </button>
        <button @click="exportData" class="btn-export">
          <i class="icon-download"></i>
        </button>
        <button @click="showSettings" class="btn-settings">
          <i class="icon-settings"></i>
        </button>
      </div>
    </div>
    
    <!-- 工具内容 -->
    <div class="tools-content">
      <component 
        :is="activeToolComponent" 
        :config="activeToolConfig"
        @action="handleToolAction"
      />
    </div>
    
    <!-- 设置面板 -->
    <div v-if="settingsVisible" class="settings-panel">
      <h3>开发工具设置</h3>
      
      <div class="setting-group">
        <h4>启用的工具</h4>
        <div class="tool-toggles">
          <label v-for="tool in allTools" :key="tool.name">
            <input 
              type="checkbox" 
              :checked="tool.enabled"
              @change="toggleTool(tool.name)"
            />
            {{ tool.displayName }}
          </label>
        </div>
      </div>
      
      <div class="setting-group">
        <h4>界面设置</h4>
        <div class="ui-settings">
          <label>
            主题:
            <select v-model="theme">
              <option value="light">浅色</option>
              <option value="dark">深色</option>
              <option value="auto">自动</option>
            </select>
          </label>
          
          <label>
            语言:
            <select v-model="language">
              <option value="zh-CN">中文</option>
              <option value="en-US">English</option>
            </select>
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import DebugPanel from './tools/DebugPanel.vue'
import PerformanceAnalyzer from './tools/PerformanceAnalyzer.vue'
import ConfigEditor from './tools/ConfigEditor.vue'
import CodeGenerator from './tools/CodeGenerator.vue'

const activeTool = ref('debugPanel')
const theme = ref('dark')
const language = ref('zh-CN')
const settingsVisible = ref(false)

const allTools = ref([
  { name: 'debugPanel', displayName: '调试面板', icon: 'icon-debug', enabled: true },
  { name: 'performanceAnalyzer', displayName: '性能分析', icon: 'icon-chart', enabled: true },
  { name: 'configEditor', displayName: '配置编辑', icon: 'icon-settings', enabled: true },
  { name: 'codeGenerator', displayName: '代码生成', icon: 'icon-code', enabled: false }
])

const enabledTools = computed(() => {
  return allTools.value.filter(tool => tool.enabled)
})

const activeToolComponent = computed(() => {
  const components = {
    debugPanel: DebugPanel,
    performanceAnalyzer: PerformanceAnalyzer,
    configEditor: ConfigEditor,
    codeGenerator: CodeGenerator
  }
  return components[activeTool.value]
})
</script>
```

## 最佳实践

### 🎯 开发工具使用建议

1. **合理配置工具**
   ```typescript
   // 开发环境：启用所有工具
   const devConfig = {
     debugPanel: true,
     performanceAnalyzer: true,
     configEditor: true,
     codeGenerator: true
   }
   
   // 生产环境：只启用监控工具
   const prodConfig = {
     monitoringService: true,
     alertSystem: true
   }
   ```

2. **性能优化**
   ```typescript
   // 按需加载工具
   const devTools = new DevToolsSuite({
     lazyLoad: true,
     tools: {
       debugPanel: { enabled: process.env.NODE_ENV === 'development' }
     }
   })
   ```

3. **团队协作**
   ```typescript
   // 共享配置
   const teamConfig = {
     templates: ['team-react-app', 'team-vue-app'],
     codeStyle: 'team-standard',
     testConfig: 'team-jest-config'
   }
   ```

### ⚠️ 注意事项

- 开发工具会增加应用体积，生产环境应禁用不必要的工具
- 性能监控工具可能影响应用性能，需要合理配置采样频率
- 敏感配置信息不应在开发工具中暴露
- 定期更新开发工具版本以获得最新功能和修复

## 总结

Micro-Core 开发工具套件提供了完整的微前端开发支持：

- 🔍 **调试工具** - 实时监控和调试应用状态
- 📊 **性能工具** - 全面的性能分析和优化建议
- ⚙️ **配置工具** - 可视化配置编辑和管理
- 🏗️ **代码生成器** - 快速生成应用模板和代码
- 🧪 **测试工具** - 完整的测试解决方案
- 📦 **构建工具** - 优化的构建和打包流程
- 🚀 **部署工具** - 自动化部署和运维
- 📈 **监控工具** - 生产环境监控和告警

通过合理使用这些开发工具，可以显著提高微前端应用的开发效率和质量。

---

## 相关链接

- [调试面板](/playground/debug-panel) - 详细的调试面板功能
- [性能分析](/playground/performance-analysis) - 性能分析工具
- [配置生成器](/playground/config-generator) - 配置生成工具
- [API 文档](/api/core) - 核心 API 参考
