/**
 * @fileoverview Solid.js Signal Bridge Integration Tests
 * 测试 Solid.js 信号桥接与微前端系统的集成
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SolidAdapter } from '../../src/index';
import type { MicroCoreKernel } from '@micro-core/core';

// Mock Solid.js signals
const mockSignals = new Map<string, { get: () => any; set: (value: any) => void }>();

vi.mock('solid-js', () => ({
  createSignal: vi.fn((initialValue) => {
    const signalId = Math.random().toString(36);
    let value = initialValue;
    
    const getter = () => value;
    const setter = (newValue: any) => {
      value = typeof newValue === 'function' ? newValue(value) : newValue;
    };
    
    mockSignals.set(signalId, { get: getter, set: setter });
    return [getter, setter];
  }),
  createEffect: vi.fn((fn) => {
    // Mock effect that runs immediately
    try {
      fn();
    } catch (error) {
      console.warn('Effect error:', error);
    }
  }),
  createMemo: vi.fn((fn) => {
    return () => fn();
  }),
  batch: vi.fn((fn) => fn()),
  untrack: vi.fn((fn) => fn())
}));

vi.mock('solid-js/web', () => ({
  render: vi.fn((component, container) => {
    const dispose = vi.fn();
    if (container && typeof component === 'function') {
      container.innerHTML = '<div>Solid Signal Bridge App</div>';
    }
    return dispose;
  })
}));

// Mock MicroCore Kernel with enhanced global state
const mockGlobalState = {
  data: new Map<string, any>(),
  subscribers: new Map<string, Set<Function>>(),
  
  get: vi.fn((key: string) => mockGlobalState.data.get(key)),
  set: vi.fn((key: string, value: any) => {
    const oldValue = mockGlobalState.data.get(key);
    mockGlobalState.data.set(key, value);
    
    // Notify subscribers
    const subs = mockGlobalState.subscribers.get(key);
    if (subs) {
      subs.forEach(callback => {
        try {
          callback(value, oldValue);
        } catch (error) {
          console.error('Global state subscriber error:', error);
        }
      });
    }
  }),
  subscribe: vi.fn((key: string, callback: Function) => {
    if (!mockGlobalState.subscribers.has(key)) {
      mockGlobalState.subscribers.set(key, new Set());
    }
    mockGlobalState.subscribers.get(key)!.add(callback);
    
    return () => {
      mockGlobalState.subscribers.get(key)?.delete(callback);
    };
  }),
  unsubscribe: vi.fn(),
  clear: vi.fn(() => {
    mockGlobalState.data.clear();
    mockGlobalState.subscribers.clear();
  })
};

const mockKernel: Partial<MicroCoreKernel> = {
  registerAdapter: vi.fn(),
  unregisterAdapter: vi.fn(),
  getEventBus: vi.fn(() => ({
    emit: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
    once: vi.fn()
  })),
  getGlobalState: vi.fn(() => mockGlobalState),
  getLogger: vi.fn(() => ({
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn()
  }))
};

describe('Solid.js Signal Bridge Integration', () => {
  let adapter: SolidAdapter;
  let mockContainer: HTMLElement;

  beforeEach(() => {
    adapter = new SolidAdapter({
      enableSignalBridge: true,
      enableContextProvider: true,
      enableHMR: false
    });
    
    mockContainer = document.createElement('div');
    mockContainer.id = 'solid-signal-container';
    document.body.appendChild(mockContainer);
    
    adapter.install(mockKernel as MicroCoreKernel);
    
    // Clear mock data
    mockGlobalState.clear();
    mockSignals.clear();
    vi.clearAllMocks();
  });

  afterEach(() => {
    if (mockContainer.parentNode) {
      mockContainer.parentNode.removeChild(mockContainer);
    }
    adapter.uninstall(mockKernel as MicroCoreKernel);
  });

  describe('Signal to Global State Bridge', () => {
    it('should bridge Solid signals to global state', async () => {
      const appConfig = {
        name: 'signal-bridge-app',
        entry: './src/SignalApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('signal-app', appConfig);
      
      const props = {
        container: mockContainer,
        signals: {
          user: 'John Doe',
          count: 42,
          settings: { theme: 'dark', lang: 'en' }
        }
      };

      await lifecycles.mount(props);

      // Verify signals are bridged to global state
      expect(mockGlobalState.set).toHaveBeenCalledWith('user', 'John Doe');
      expect(mockGlobalState.set).toHaveBeenCalledWith('count', 42);
      expect(mockGlobalState.set).toHaveBeenCalledWith('settings', { theme: 'dark', lang: 'en' });
    });

    it('should sync signal updates to global state', async () => {
      const { createSignal } = await import('solid-js');
      
      const appConfig = {
        name: 'signal-sync-app',
        entry: './src/SyncApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('sync-app', appConfig);
      
      await lifecycles.mount({ container: mockContainer });

      // Simulate signal creation and update
      const [count, setCount] = createSignal(0);
      
      // Update signal
      setCount(10);
      
      // Should trigger global state update
      await new Promise(resolve => setTimeout(resolve, 0)); // Allow async updates
      
      expect(createSignal).toHaveBeenCalled();
    });

    it('should handle complex nested signal structures', async () => {
      const appConfig = {
        name: 'nested-signals-app',
        entry: './src/NestedApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('nested-app', appConfig);
      
      const complexSignals = {
        user: {
          profile: {
            name: 'Alice',
            age: 30,
            preferences: {
              theme: 'light',
              notifications: true
            }
          },
          permissions: ['read', 'write', 'admin']
        },
        app: {
          state: 'active',
          features: {
            darkMode: false,
            beta: true
          }
        }
      };

      const props = {
        container: mockContainer,
        signals: complexSignals
      };

      await lifecycles.mount(props);

      // Verify nested structures are properly bridged
      expect(mockGlobalState.set).toHaveBeenCalledWith('user', complexSignals.user);
      expect(mockGlobalState.set).toHaveBeenCalledWith('app', complexSignals.app);
    });
  });

  describe('Global State to Signal Bridge', () => {
    it('should sync global state changes to Solid signals', async () => {
      const appConfig = {
        name: 'state-to-signal-app',
        entry: './src/StateApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('state-app', appConfig);
      
      await lifecycles.mount({ container: mockContainer });

      // Simulate global state change
      mockGlobalState.set('theme', 'dark');
      
      // Should trigger signal update
      expect(mockGlobalState.subscribers.get('theme')?.size).toBeGreaterThan(0);
    });

    it('should handle multiple subscribers to same global state key', async () => {
      const appConfig = {
        name: 'multi-subscriber-app',
        entry: './src/MultiApp.tsx',
        container: mockContainer
      };

      // Create multiple app instances
      const lifecycles1 = adapter.createLifecycles('multi-app-1', appConfig);
      const lifecycles2 = adapter.createLifecycles('multi-app-2', appConfig);
      
      await lifecycles1.mount({ container: mockContainer });
      await lifecycles2.mount({ container: mockContainer });

      // Both should subscribe to global state
      expect(mockGlobalState.subscribe).toHaveBeenCalled();
      
      // Update global state
      mockGlobalState.set('sharedData', 'updated value');
      
      // Both apps should receive the update
      expect(mockGlobalState.set).toHaveBeenCalledWith('sharedData', 'updated value');
    });
  });

  describe('Bidirectional Signal Synchronization', () => {
    it('should maintain bidirectional sync between signals and global state', async () => {
      const appConfig = {
        name: 'bidirectional-app',
        entry: './src/BidirectionalApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('bidirectional-app', appConfig);
      
      const props = {
        container: mockContainer,
        signals: {
          counter: 0,
          message: 'Hello'
        }
      };

      await lifecycles.mount(props);

      // Initial sync: signals -> global state
      expect(mockGlobalState.set).toHaveBeenCalledWith('counter', 0);
      expect(mockGlobalState.set).toHaveBeenCalledWith('message', 'Hello');

      // Simulate global state change
      mockGlobalState.set('counter', 5);
      
      // Should trigger signal update (simulated)
      const subscribers = mockGlobalState.subscribers.get('counter');
      expect(subscribers?.size).toBeGreaterThan(0);
    });

    it('should prevent infinite loops in bidirectional sync', async () => {
      const appConfig = {
        name: 'loop-prevention-app',
        entry: './src/LoopApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('loop-app', appConfig);
      
      await lifecycles.mount({ 
        container: mockContainer,
        signals: { value: 'initial' }
      });

      // Rapid updates should not cause infinite loops
      for (let i = 0; i < 10; i++) {
        mockGlobalState.set('value', `update-${i}`);
      }

      // Should handle updates gracefully without stack overflow
      expect(mockGlobalState.set).toHaveBeenCalled();
    });
  });

  describe('Signal Bridge Performance', () => {
    it('should handle high-frequency signal updates efficiently', async () => {
      const appConfig = {
        name: 'high-freq-app',
        entry: './src/HighFreqApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('high-freq-app', appConfig);
      
      await lifecycles.mount({ container: mockContainer });

      const startTime = performance.now();

      // Simulate 1000 rapid updates
      for (let i = 0; i < 1000; i++) {
        mockGlobalState.set(`signal_${i % 10}`, i);
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(1000);
    });

    it('should batch signal updates for performance', async () => {
      const { batch } = await import('solid-js');
      
      const appConfig = {
        name: 'batch-app',
        entry: './src/BatchApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('batch-app', appConfig);
      
      await lifecycles.mount({ container: mockContainer });

      // Simulate batched updates
      const updates = [
        { key: 'a', value: 1 },
        { key: 'b', value: 2 },
        { key: 'c', value: 3 }
      ];

      updates.forEach(update => {
        mockGlobalState.set(update.key, update.value);
      });

      // Batch should be used for performance
      expect(batch).toHaveBeenCalled();
    });
  });

  describe('Signal Bridge Error Handling', () => {
    it('should handle signal creation errors gracefully', async () => {
      const { createSignal } = await import('solid-js');
      
      // Mock createSignal to throw error
      (createSignal as any).mockImplementationOnce(() => {
        throw new Error('Signal creation failed');
      });

      const appConfig = {
        name: 'error-signal-app',
        entry: './src/ErrorApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('error-app', appConfig);
      
      // Should handle error gracefully
      await expect(lifecycles.mount({ 
        container: mockContainer,
        signals: { test: 'value' }
      })).resolves.toBeUndefined();
    });

    it('should handle global state subscription errors', async () => {
      // Mock subscribe to throw error
      mockGlobalState.subscribe.mockImplementationOnce(() => {
        throw new Error('Subscription failed');
      });

      const appConfig = {
        name: 'sub-error-app',
        entry: './src/SubErrorApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('sub-error-app', appConfig);
      
      // Should handle subscription error gracefully
      await expect(lifecycles.mount({ container: mockContainer }))
        .resolves.toBeUndefined();
    });

    it('should handle circular reference in signals', async () => {
      const appConfig = {
        name: 'circular-app',
        entry: './src/CircularApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('circular-app', appConfig);
      
      // Create circular reference
      const objA: any = { name: 'A' };
      const objB: any = { name: 'B' };
      objA.ref = objB;
      objB.ref = objA;

      const props = {
        container: mockContainer,
        signals: {
          circular: objA
        }
      };

      // Should handle circular references without crashing
      await expect(lifecycles.mount(props)).resolves.toBeUndefined();
    });
  });

  describe('Signal Bridge Memory Management', () => {
    it('should clean up signal subscriptions on unmount', async () => {
      const appConfig = {
        name: 'cleanup-app',
        entry: './src/CleanupApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('cleanup-app', appConfig);
      
      await lifecycles.mount({ 
        container: mockContainer,
        signals: { data: 'test' }
      });

      // Verify subscription was created
      expect(mockGlobalState.subscribe).toHaveBeenCalled();

      await lifecycles.unmount({ container: mockContainer });

      // Should clean up subscriptions
      // Note: In real implementation, this would clear the subscription
      expect(true).toBe(true); // Placeholder for actual cleanup verification
    });

    it('should prevent memory leaks with large signal datasets', async () => {
      const appConfig = {
        name: 'large-signals-app',
        entry: './src/LargeSignalsApp.tsx',
        container: mockContainer
      };

      const lifecycles = adapter.createLifecycles('large-signals-app', appConfig);
      
      // Create large signal dataset
      const largeSignals: Record<string, any> = {};
      for (let i = 0; i < 1000; i++) {
        largeSignals[`signal_${i}`] = {
          id: i,
          data: new Array(100).fill(i),
          nested: { value: i * 2 }
        };
      }

      await lifecycles.mount({
        container: mockContainer,
        signals: largeSignals
      });

      await lifecycles.unmount({ container: mockContainer });

      // Memory should be cleaned up
      expect(mockGlobalState.clear).not.toHaveBeenCalled(); // Global state should persist
    });
  });

  describe('Cross-App Signal Communication', () => {
    it('should enable signal communication between different apps', async () => {
      const appConfig1 = {
        name: 'sender-app',
        entry: './src/SenderApp.tsx',
        container: mockContainer
      };

      const appConfig2 = {
        name: 'receiver-app',
        entry: './src/ReceiverApp.tsx',
        container: mockContainer
      };

      const senderLifecycles = adapter.createLifecycles('sender', appConfig1);
      const receiverLifecycles = adapter.createLifecycles('receiver', appConfig2);

      // Mount both apps
      await senderLifecycles.mount({ 
        container: mockContainer,
        signals: { message: 'Hello from sender' }
      });
      
      await receiverLifecycles.mount({ container: mockContainer });

      // Sender updates signal
      mockGlobalState.set('message', 'Updated message');

      // Receiver should receive the update
      expect(mockGlobalState.set).toHaveBeenCalledWith('message', 'Updated message');
      
      // Both apps should have access to the shared signal
      expect(mockGlobalState.subscribe).toHaveBeenCalled();
    });
  });
});
