import { MicroCoreKernel } from '@micro-core/core'
import { CommunicationPlugin } from '@micro-core/plugin-communication'
import { RouterPlugin } from '@micro-core/plugin-router'

// Types for enhanced configuration
interface ApplicationConfig {
  name: string
  entry: string
  container: string
  activeWhen: string | ((location: Location) => boolean)
  props?: Record<string, any>
  loading?: string
  errorBoundary?: boolean
  timeout?: number
  retries?: number
}

interface PluginConfig {
  name: string
  plugin: any
  options?: Record<string, any>
  enabled: boolean
}

interface MicroCoreConfig {
  debug: boolean
  logLevel: 'error' | 'warn' | 'info' | 'debug'
  errorHandler?: (error: Error, context: string) => void
  performanceMonitoring: boolean
  maxRetries: number
  loadTimeout: number
}

// Enhanced logging system
class Logger {
  private logLevel: string
  private context: string

  constructor(context: string, logLevel: string = 'info') {
    this.context = context
    this.logLevel = logLevel
  }

  private shouldLog(level: string): boolean {
    const levels = ['error', 'warn', 'info', 'debug']
    return levels.indexOf(level) <= levels.indexOf(this.logLevel)
  }

  private formatMessage(level: string, message: string, data?: any): void {
    if (!this.shouldLog(level)) return

    const timestamp = new Date().toISOString()
    const prefix = `[${timestamp}] [${level.toUpperCase()}] [${this.context}]`

    if (data) {
      console[level as keyof Console](prefix, message, data)
    } else {
      console[level as keyof Console](prefix, message)
    }
  }

  error(message: string, data?: any) {
    this.formatMessage('error', message, data)
  }

  warn(message: string, data?: any) {
    this.formatMessage('warn', message, data)
  }

  info(message: string, data?: any) {
    this.formatMessage('info', message, data)
  }

  debug(message: string, data?: any) {
    this.formatMessage('debug', message, data)
  }
}

// Application registry for dynamic management
class ApplicationRegistry {
  private applications: Map<string, ApplicationConfig> = new Map()
  private loadedApps: Set<string> = new Set()
  private logger: Logger

  constructor(logger: Logger) {
    this.logger = logger
  }

  register(config: ApplicationConfig): void {
    this.logger.info(`Registering application: ${config.name}`, config)
    this.applications.set(config.name, config)
  }

  unregister(name: string): void {
    this.logger.info(`Unregistering application: ${name}`)
    this.applications.delete(name)
    this.loadedApps.delete(name)
  }

  get(name: string): ApplicationConfig | undefined {
    return this.applications.get(name)
  }

  getAll(): ApplicationConfig[] {
    return Array.from(this.applications.values())
  }

  isLoaded(name: string): boolean {
    return this.loadedApps.has(name)
  }

  markAsLoaded(name: string): void {
    this.loadedApps.add(name)
  }

  markAsUnloaded(name: string): void {
    this.loadedApps.delete(name)
  }

  getLoadedApps(): string[] {
    return Array.from(this.loadedApps)
  }
}

// Plugin manager for better plugin handling
class PluginManager {
  private plugins: Map<string, PluginConfig> = new Map()
  private logger: Logger

  constructor(logger: Logger) {
    this.logger = logger
  }

  register(config: PluginConfig): void {
    this.logger.info(`Registering plugin: ${config.name}`, config)
    this.plugins.set(config.name, config)
  }

  unregister(name: string): void {
    this.logger.info(`Unregistering plugin: ${name}`)
    this.plugins.delete(name)
  }

  get(name: string): PluginConfig | undefined {
    return this.plugins.get(name)
  }

  getEnabled(): PluginConfig[] {
    return Array.from(this.plugins.values()).filter(p => p.enabled)
  }

  enable(name: string): void {
    const plugin = this.plugins.get(name)
    if (plugin) {
      plugin.enabled = true
      this.logger.info(`Enabled plugin: ${name}`)
    }
  }

  disable(name: string): void {
    const plugin = this.plugins.get(name)
    if (plugin) {
      plugin.enabled = false
      this.logger.info(`Disabled plugin: ${name}`)
    }
  }
}

// Enhanced micro-core manager
class MicroCoreManager {
  private kernel: MicroCoreKernel | null = null
  private config: MicroCoreConfig
  private logger: Logger
  private applicationRegistry: ApplicationRegistry
  private pluginManager: PluginManager
  private performanceMetrics: Map<string, number> = new Map()

  constructor(config: Partial<MicroCoreConfig> = {}) {
    this.config = {
      debug: import.meta.env.DEV,
      logLevel: import.meta.env.DEV ? 'debug' : 'warn',
      performanceMonitoring: true,
      maxRetries: 3,
      loadTimeout: 10000,
      ...config
    }

    this.logger = new Logger('MicroCore', this.config.logLevel)
    this.applicationRegistry = new ApplicationRegistry(this.logger)
    this.pluginManager = new PluginManager(this.logger)

    // Set up global error handler
    if (this.config.errorHandler) {
      window.addEventListener('error', (event) => {
        this.config.errorHandler!(event.error, 'Global Error')
      })
    }
  }

  async initialize(): Promise<MicroCoreKernel> {
    if (this.kernel) {
      this.logger.warn('MicroCore already initialized')
      return this.kernel
    }

    try {
      this.logger.info('Initializing MicroCore', this.config)

      // Create kernel instance
      this.kernel = new MicroCoreKernel({
        debug: this.config.debug
      })

      // Register default plugins
      await this.registerDefaultPlugins()

      // Register default applications
      await this.registerDefaultApplications()

      // Set up event listeners
      this.setupEventListeners()

      // Start the kernel
      await this.kernel.start()

      this.logger.info('MicroCore initialized successfully')
      return this.kernel

    } catch (error) {
      this.logger.error('Failed to initialize MicroCore', error)
      throw error
    }
  }

  private async registerDefaultPlugins(): Promise<void> {
    const defaultPlugins: PluginConfig[] = [
      {
        name: 'router',
        plugin: RouterPlugin,
        enabled: true,
        options: {
          mode: 'history'
        }
      },
      {
        name: 'communication',
        plugin: CommunicationPlugin,
        enabled: true,
        options: {
          enableGlobalState: true
        }
      }
    ]

    for (const pluginConfig of defaultPlugins) {
      try {
        this.pluginManager.register(pluginConfig)
        if (pluginConfig.enabled && this.kernel) {
          await this.kernel.use(pluginConfig.plugin, pluginConfig.options)
          this.logger.info(`Plugin ${pluginConfig.name} registered successfully`)
        }
      } catch (error) {
        this.logger.error(`Failed to register plugin ${pluginConfig.name}`, error)
      }
    }
  }

  private async registerDefaultApplications(): Promise<void> {
    const defaultApps: ApplicationConfig[] = [
      {
        name: 'react-app',
        entry: 'http://localhost:3001',
        container: '#react-app-container',
        activeWhen: '/react-app',
        props: { theme: 'light' },
        timeout: 10000,
        retries: 3,
        errorBoundary: true
      },
      {
        name: 'vue3-app',
        entry: 'http://localhost:3002',
        container: '#vue3-app-container',
        activeWhen: '/vue3-app',
        props: { theme: 'light' },
        timeout: 10000,
        retries: 3,
        errorBoundary: true
      },
      {
        name: 'vue2-app',
        entry: 'http://localhost:3003',
        container: '#vue2-app-container',
        activeWhen: '/vue2-app',
        props: { theme: 'light' },
        timeout: 10000,
        retries: 3,
        errorBoundary: true
      },
      {
        name: 'angular-app',
        entry: 'http://localhost:3004',
        container: '#angular-app-container',
        activeWhen: '/angular-app',
        props: { theme: 'light' },
        timeout: 10000,
        retries: 3,
        errorBoundary: true
      },
      {
        name: 'svelte-app',
        entry: 'http://localhost:3005',
        container: '#svelte-app-container',
        activeWhen: '/svelte-app',
        props: { theme: 'light' },
        timeout: 10000,
        retries: 3,
        errorBoundary: true
      },
      {
        name: 'solid-app',
        entry: 'http://localhost:3006',
        container: '#solid-app-container',
        activeWhen: '/solid-app',
        props: { theme: 'light' },
        timeout: 10000,
        retries: 3,
        errorBoundary: true
      },
      {
        name: 'html-app',
        entry: 'http://localhost:3007',
        container: '#html-app-container',
        activeWhen: '/html-app',
        props: { theme: 'light' },
        timeout: 10000,
        retries: 3,
        errorBoundary: true
      }
    ]

    for (const appConfig of defaultApps) {
      try {
        this.applicationRegistry.register(appConfig)
        if (this.kernel) {
          await this.kernel.registerApplication(appConfig)
          this.logger.info(`Application ${appConfig.name} registered successfully`)
        }
      } catch (error) {
        this.logger.error(`Failed to register application ${appConfig.name}`, error)
      }
    }
  }

  private setupEventListeners(): void {
    if (!this.kernel) return

    // Application lifecycle events
    this.kernel.on('app-loading', (data: any) => {
      this.logger.info(`Application loading: ${data.name}`)
      if (this.config.performanceMonitoring) {
        this.performanceMetrics.set(`${data.name}-load-start`, Date.now())
      }
    })

    this.kernel.on('app-mounted', (data: any) => {
      this.logger.info(`Application mounted: ${data.name}`)
      this.applicationRegistry.markAsLoaded(data.name)

      if (this.config.performanceMonitoring) {
        const startTime = this.performanceMetrics.get(`${data.name}-load-start`)
        if (startTime) {
          const loadTime = Date.now() - startTime
          this.logger.info(`Application ${data.name} load time: ${loadTime}ms`)
          this.performanceMetrics.set(`${data.name}-load-time`, loadTime)
        }
      }
    })

    this.kernel.on('app-unmounted', (data: any) => {
      this.logger.info(`Application unmounted: ${data.name}`)
      this.applicationRegistry.markAsUnloaded(data.name)
    })

    this.kernel.on('app-error', (data: any) => {
      this.logger.error(`Application error: ${data.name}`, data.error)
      if (this.config.errorHandler) {
        this.config.errorHandler(data.error, `Application: ${data.name}`)
      }
    })
  }

  // Public API methods
  async loadApplication(name: string, props?: Record<string, any>): Promise<void> {
    if (!this.kernel) {
      throw new Error('MicroCore not initialized')
    }

    const appConfig = this.applicationRegistry.get(name)
    if (!appConfig) {
      throw new Error(`Application ${name} not found`)
    }

    try {
      this.logger.info(`Loading application: ${name}`, props)

      const mergedProps = { ...appConfig.props, ...props }
      await this.kernel.loadApp(name, mergedProps)

    } catch (error) {
      this.logger.error(`Failed to load application: ${name}`, error)
      throw error
    }
  }

  async unloadApplication(name: string): Promise<void> {
    if (!this.kernel) {
      throw new Error('MicroCore not initialized')
    }

    try {
      this.logger.info(`Unloading application: ${name}`)
      await this.kernel.unloadApp(name)
    } catch (error) {
      this.logger.error(`Failed to unload application: ${name}`, error)
      throw error
    }
  }

  registerApplication(config: ApplicationConfig): void {
    this.applicationRegistry.register(config)
    if (this.kernel) {
      this.kernel.registerApplication(config)
    }
  }

  unregisterApplication(name: string): void {
    this.applicationRegistry.unregister(name)
    if (this.kernel) {
      this.kernel.unregisterApplication(name)
    }
  }

  getApplicationRegistry(): ApplicationRegistry {
    return this.applicationRegistry
  }

  getPluginManager(): PluginManager {
    return this.pluginManager
  }

  getPerformanceMetrics(): Map<string, number> {
    return new Map(this.performanceMetrics)
  }

  getKernel(): MicroCoreKernel | null {
    return this.kernel
  }

  destroy(): void {
    if (this.kernel) {
      this.kernel.destroy()
      this.kernel = null
    }
    this.applicationRegistry = new ApplicationRegistry(this.logger)
    this.pluginManager = new PluginManager(this.logger)
    this.performanceMetrics.clear()
    this.logger.info('MicroCore destroyed')
  }
}

// Global instance
let microCoreManager: MicroCoreManager | null = null

// Enhanced initialization function
export async function initMicroCore(config?: Partial<MicroCoreConfig>): Promise<MicroCoreKernel> {
  if (microCoreManager) {
    return microCoreManager.getKernel()!
  }

  microCoreManager = new MicroCoreManager(config)
  return await microCoreManager.initialize()
}

// Enhanced getter function
export function getMicroCore(): MicroCoreKernel | null {
  return microCoreManager?.getKernel() || null
}

// Get the manager instance for advanced operations
export function getMicroCoreManager(): MicroCoreManager | null {
  return microCoreManager
}

// Utility functions for application management
export async function loadApp(name: string, props?: Record<string, any>): Promise<void> {
  if (!microCoreManager) {
    throw new Error('MicroCore not initialized')
  }
  return await microCoreManager.loadApplication(name, props)
}

export async function unloadApp(name: string): Promise<void> {
  if (!microCoreManager) {
    throw new Error('MicroCore not initialized')
  }
  return await microCoreManager.unloadApplication(name)
}

export function registerApp(config: ApplicationConfig): void {
  if (!microCoreManager) {
    throw new Error('MicroCore not initialized')
  }
  microCoreManager.registerApplication(config)
}

export function unregisterApp(name: string): void {
  if (!microCoreManager) {
    throw new Error('MicroCore not initialized')
  }
  microCoreManager.unregisterApplication(name)
}

// Performance and debugging utilities
export function getPerformanceMetrics(): Map<string, number> {
  return microCoreManager?.getPerformanceMetrics() || new Map()
}

export function getLoadedApplications(): string[] {
  return microCoreManager?.getApplicationRegistry().getLoadedApps() || []
}

export function getAllApplications(): ApplicationConfig[] {
  return microCoreManager?.getApplicationRegistry().getAll() || []
}

// Cleanup function
export function destroyMicroCore(): void {
  if (microCoreManager) {
    microCoreManager.destroy()
    microCoreManager = null
  }
}