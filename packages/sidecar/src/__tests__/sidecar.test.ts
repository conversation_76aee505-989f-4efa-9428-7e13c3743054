/**
 * @fileoverview Sidecar模式测试
 * @description 测试Sidecar模式的功能
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi } from 'vitest'
import { init, SidecarManager } from '../index'

describe('Sidecar模式', () => {
    let sidecarManager: SidecarManager

    beforeEach(() => {
        sidecarManager = new SidecarManager()
        vi.clearAllMocks()
    })

    describe('一行代码接入', () => {
        it('应该能够通过init函数快速启动', async () => {
            const result = await init({ autoStart: true })
            expect(result).toBeDefined()
            expect(result.isStarted()).toBe(true)
        })

        it('应该支持零配置启动', async () => {
            const result = await init()
            expect(result).toBeDefined()
        })

        it('应该支持自定义配置', async () => {
            const config = {
                autoStart: true,
                autoDiscovery: true,
                port: 3000,
                host: 'localhost'
            }

            const result = await init(config)
            expect(result).toBeDefined()
        })
    })

    describe('自动发现', () => {
        it('应该能够自动发现微应用', async () => {
            const discoveredApps = await sidecarManager.discoverApplications()
            expect(Array.isArray(discoveredApps)).toBe(true)
        })

        it('应该能够注册发现的应用', async () => {
            const mockApp = {
                name: 'test-app',
                entry: 'http://localhost:3001',
                activeRule: '/test'
            }

            await sidecarManager.registerApplication(mockApp)
            expect(sidecarManager.hasApplication('test-app')).toBe(true)
        })
    })

    describe('渐进式迁移', () => {
        it('应该支持现有应用无缝接入', async () => {
            const existingApp = {
                name: 'existing-app',
                mount: vi.fn(),
                unmount: vi.fn()
            }

            await sidecarManager.integrateExistingApp(existingApp)
            expect(sidecarManager.hasApplication('existing-app')).toBe(true)
        })

        it('应该能够处理遗留代码', async () => {
            const legacyCode = 'window.myApp = { init: function() {} }'
            const result = await sidecarManager.wrapLegacyCode(legacyCode)
            expect(result).toBeDefined()
        })
    })

    describe('生命周期管理', () => {
        it('应该能够启动Sidecar', async () => {
            await sidecarManager.start()
            expect(sidecarManager.isStarted()).toBe(true)
        })

        it('应该能够停止Sidecar', async () => {
            await sidecarManager.start()
            await sidecarManager.stop()
            expect(sidecarManager.isStarted()).toBe(false)
        })

        it('应该能够重启Sidecar', async () => {
            await sidecarManager.start()
            await sidecarManager.restart()
            expect(sidecarManager.isStarted()).toBe(true)
        })
    })

    describe('应用管理', () => {
        it('应该能够加载应用', async () => {
            const app = {
                name: 'test-app',
                entry: 'http://localhost:3001',
                activeRule: '/test'
            }

            await sidecarManager.registerApplication(app)
            await sidecarManager.loadApplication('test-app')
            expect(sidecarManager.isApplicationLoaded('test-app')).toBe(true)
        })

        it('应该能够卸载应用', async () => {
            const app = {
                name: 'test-app',
                entry: 'http://localhost:3001',
                activeRule: '/test'
            }

            await sidecarManager.registerApplication(app)
            await sidecarManager.loadApplication('test-app')
            await sidecarManager.unloadApplication('test-app')
            expect(sidecarManager.isApplicationLoaded('test-app')).toBe(false)
        })
    })

    describe('错误处理', () => {
        it('应该处理应用加载失败', async () => {
            const app = {
                name: 'failing-app',
                entry: 'http://invalid-url',
                activeRule: '/failing'
            }

            await sidecarManager.registerApplication(app)
            await expect(sidecarManager.loadApplication('failing-app')).rejects.toThrow()
        })

        it('应该处理网络错误', async () => {
            const networkError = new Error('Network error')
            vi.spyOn(global, 'fetch').mockRejectedValue(networkError)

            await expect(sidecarManager.discoverApplications()).rejects.toThrow('Network error')
        })
    })

    describe('性能优化', () => {
        it('应该支持预加载', async () => {
            const app = {
                name: 'preload-app',
                entry: 'http://localhost:3001',
                activeRule: '/preload',
                preload: true
            }

            await sidecarManager.registerApplication(app)
            await sidecarManager.preloadApplication('preload-app')
            expect(sidecarManager.isApplicationPreloaded('preload-app')).toBe(true)
        })

        it('应该支持懒加载', async () => {
            const app = {
                name: 'lazy-app',
                entry: 'http://localhost:3001',
                activeRule: '/lazy',
                lazy: true
            }

            await sidecarManager.registerApplication(app)
            // 懒加载应用不会立即加载
            expect(sidecarManager.isApplicationLoaded('lazy-app')).toBe(false)
        })
    })

    describe('配置管理', () => {
        it('应该能够更新配置', () => {
            const newConfig = {
                autoStart: false,
                port: 4000
            }

            sidecarManager.updateConfig(newConfig)
            const config = sidecarManager.getConfig()
            expect(config.port).toBe(4000)
        })

        it('应该能够重置配置', () => {
            sidecarManager.updateConfig({ port: 4000 })
            sidecarManager.resetConfig()
            const config = sidecarManager.getConfig()
            expect(config.port).toBe(3000) // 默认端口
        })
    })

    describe('事件系统', () => {
        it('应该能够监听应用事件', () => {
            const handler = vi.fn()
            sidecarManager.on('application:loaded', handler)
            sidecarManager.emit('application:loaded', { name: 'test-app' })
            expect(handler).toHaveBeenCalledWith({ name: 'test-app' })
        })

        it('应该能够取消事件监听', () => {
            const handler = vi.fn()
            sidecarManager.on('application:loaded', handler)
            sidecarManager.off('application:loaded', handler)
            sidecarManager.emit('application:loaded', { name: 'test-app' })
            expect(handler).not.toHaveBeenCalled()
        })
    })

    describe('开发工具集成', () => {
        it('应该能够启用开发模式', () => {
            sidecarManager.enableDevMode()
            expect(sidecarManager.isDevMode()).toBe(true)
        })

        it('应该能够获取调试信息', () => {
            const debugInfo = sidecarManager.getDebugInfo()
            expect(debugInfo).toBeDefined()
            expect(debugInfo.applications).toBeDefined()
            expect(debugInfo.performance).toBeDefined()
        })
    })
})