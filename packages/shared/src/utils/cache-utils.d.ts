/**
 * 共享工具包 - 缓存工具函数
 *
 * @description 提供多种缓存策略和管理功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
    value: T;
    expiry: number;
    accessCount: number;
    lastAccess: number;
    size?: number;
}
/**
 * 缓存配置接口
 */
export interface CacheConfig {
    /** 默认过期时间（毫秒） */
    defaultTTL?: number;
    /** 最大缓存项数量 */
    maxSize?: number;
    /** 最大内存使用量（字节） */
    maxMemory?: number;
    /** 清理策略 */
    evictionPolicy?: 'LRU' | 'LFU' | 'FIFO';
    /** 自动清理间隔（毫秒） */
    cleanupInterval?: number;
}
/**
 * 内存缓存类
 */
export declare class MemoryCache<T = any> {
    private cache;
    private config;
    private cleanupTimer?;
    private currentMemoryUsage;
    constructor(config?: CacheConfig);
    /**
     * 设置缓存
     */
    set(key: string, value: T, ttl?: number): void;
    /**
     * 获取缓存
     */
    get(key: string): T | undefined;
    /**
     * 检查缓存是否存在且未过期
     */
    has(key: string): boolean;
    /**
     * 删除缓存
     */
    delete(key: string): boolean;
    /**
     * 清空缓存
     */
    clear(): void;
    /**
     * 获取缓存大小
     */
    size(): number;
    /**
     * 获取内存使用量
     */
    getMemoryUsage(): number;
    /**
     * 获取所有键
     */
    keys(): string[];
    /**
     * 获取所有值
     */
    values(): T[];
    /**
     * 获取缓存统计信息
     */
    getStats(): {
        size: number;
        memoryUsage: number;
        hitRate: number;
        items: Array<{
            key: string;
            accessCount: number;
            lastAccess: number;
            expiry: number;
        }>;
    };
    /**
     * 清理过期缓存
     */
    cleanup(): void;
    /**
     * 确保有足够空间
     */
    private ensureSpace;
    /**
     * 驱逐一个缓存项
     */
    private evictOne;
    /**
     * 查找最近最少使用的键
     */
    private findLRUKey;
    /**
     * 查找最少使用的键
     */
    private findLFUKey;
    /**
     * 计算值的大小
     */
    private calculateSize;
    /**
     * 启动清理定时器
     */
    private startCleanupTimer;
    /**
     * 销毁缓存
     */
    destroy(): void;
}
/**
 * 简单内存缓存类（向后兼容）
 */
export declare class SimpleCache<T = any> extends MemoryCache<T> {
    constructor(defaultTTL?: number);
}
/**
 * 本地存储缓存类
 */
export declare class LocalStorageCache<T = any> {
    private prefix;
    private defaultTTL;
    constructor(prefix?: string, defaultTTL?: number);
    /**
     * 生成存储键
     */
    private getStorageKey;
    /**
     * 设置缓存
     */
    set(key: string, value: T, ttl?: number): void;
    /**
     * 获取缓存
     */
    get(key: string): T | undefined;
    /**
     * 检查缓存是否存在
     */
    has(key: string): boolean;
    /**
     * 删除缓存
     */
    delete(key: string): boolean;
    /**
     * 清空缓存
     */
    clear(): void;
    /**
     * 获取所有键
     */
    keys(): string[];
}
/**
 * 缓存装饰器
 */
export declare function cache(ttl?: number, keyGenerator?: (...args: any[]) => string): (target: any, propertyName: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
/**
 * 全局缓存实例
 */
export declare const globalCache: MemoryCache<any>;
/**
 * 全局本地存储缓存实例
 */
export declare const globalLocalStorageCache: LocalStorageCache<any>;
//# sourceMappingURL=cache-utils.d.ts.map