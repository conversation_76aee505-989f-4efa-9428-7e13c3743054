/**
 * 微前端微内核包主入口
 *
 * @description 微内核架构的核心实现，提供最小运行时功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
export { defaultMicroCore, MicroCore } from './micro-core';
export type { ApplicationConfig, ApplicationInstance, ApplicationManager, ApplicationStatus, EventBus, ManagerInterface, MicroCoreConfig, MicroCoreStatus, PluginInterface, PluginManager } from '@micro-core/shared';
export { createLogger, createMicroCoreUtils, MicroCoreError } from '@micro-core/shared';
//# sourceMappingURL=index.d.ts.map