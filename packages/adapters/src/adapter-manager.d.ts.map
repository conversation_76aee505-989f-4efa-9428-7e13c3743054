{"version": 3, "file": "adapter-manager.d.ts", "sourceRoot": "", "sources": ["adapter-manager.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAGH,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AACzD,OAAO,KAAK,EACR,aAAa,EACb,cAAc,EAEd,YAAY,EACZ,aAAa,EAChB,MAAM,qBAAqB,CAAC;AAE7B;;GAEG;AACH,cAAM,YAAY;IACd,OAAO,CAAC,SAAS,CAAoC;IAErD,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI;IAarC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI;IAO3C,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI;CAM/C;AAsBD;;GAEG;AACH,qBAAa,cAAe,SAAQ,YAAY;IAC5C,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAS;IAChC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAsC;IAC/D,OAAO,CAAC,QAAQ,CAAC,SAAS,CAA4C;IACtE,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAsC;;IAS/D;;OAEG;IACH,eAAe,CACX,aAAa,EAAE,aAAa,EAC5B,OAAO,EAAE,cAAc,GACxB,IAAI;IA0BP;;OAEG;IACH,iBAAiB,CAAC,aAAa,EAAE,aAAa,GAAG,OAAO;IASxD;;OAEG;IACG,aAAa,CACf,SAAS,EAAE,MAAM,EACjB,aAAa,EAAE,aAAa,EAC5B,MAAM,GAAE,aAAkB,GAC3B,OAAO,CAAC,WAAW,CAAC;IAmDvB;;OAEG;IACG,iBAAiB,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAwBzD;;OAEG;IACG,YAAY,CACd,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,WAAW,EACtB,KAAK,CAAC,EAAE,GAAG,GACZ,OAAO,CAAC,IAAI,CAAC;IAwBhB;;OAEG;IACG,cAAc,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAwBtD;;OAEG;IACG,cAAc,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAmBtD;;OAEG;IACH,UAAU,CAAC,SAAS,EAAE,MAAM,GAAG,WAAW,GAAG,SAAS;IAKtD;;OAEG;IACH,eAAe,CAAC,SAAS,EAAE,MAAM,GAAG,YAAY,GAAG,SAAS;IAK5D;;OAEG;IACH,gBAAgB,CAAC,SAAS,EAAE,MAAM,GAAG,aAAa,GAAG,SAAS;IAK9D;;OAEG;IACH,UAAU,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO;IAItC;;OAEG;IACH,aAAa,IAAI,MAAM,EAAE;IAIzB;;OAEG;IACH,kBAAkB,CAAC,KAAK,EAAE,YAAY,GAAG,MAAM,EAAE;IAUjD;;OAEG;IACH,sBAAsB,IAAI,aAAa,EAAE;IAIzC;;OAEG;IACH,oBAAoB,CAAC,aAAa,EAAE,aAAa,GAAG,OAAO;IAI3D;;OAEG;IACH,eAAe,CAAC,SAAS,EAAE,WAAW,GAAG,aAAa,GAAG,IAAI;IAoC7D;;OAEG;IACG,cAAc,CAChB,OAAO,EAAE,KAAK,CAAC;QACX,EAAE,EAAE,MAAM,CAAC;QACX,aAAa,EAAE,aAAa,CAAC;QAC7B,MAAM,CAAC,EAAE,aAAa,CAAC;KAC1B,CAAC,GACH,OAAO,CAAC,WAAW,EAAE,CAAC;IAqBzB;;OAEG;IACH,QAAQ,IAAI;QACR,aAAa,EAAE,MAAM,CAAC;QACtB,eAAe,EAAE,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QAC9C,mBAAmB,EAAE,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QACnD,mBAAmB,EAAE,MAAM,CAAC;QAC5B,eAAe,EAAE,MAAM,CAAC;KAC3B;IA+BD;;OAEG;IACG,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAwB9B;;OAEG;IACH,OAAO,CAAC,eAAe;IAQvB;;OAEG;IACH,OAAO,CAAC,WAAW;IASnB;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAU1B;;OAEG;IACH,OAAO,CAAC,eAAe;IAcvB;;OAEG;IACH,OAAO,CAAC,yBAAyB;IAMjC;;OAEG;IACH,OAAO,CAAC,WAAW;IAcnB;;OAEG;IACH,OAAO,CAAC,SAAS;IAgCjB;;OAEG;IACH,OAAO,CAAC,aAAa;IAerB;;OAEG;IACH,OAAO,CAAC,YAAY;IAQpB;;OAEG;IACH,OAAO,CAAC,WAAW;CAYtB"}