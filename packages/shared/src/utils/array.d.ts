/**
 * @fileoverview 数组处理工具函数
 * @description 提供各种数组操作和转换功能
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 数组去重
 */
export declare function unique<T>(array: T[]): T[];
/**
 * 数组交集
 */
export declare function intersection<T>(array1: T[], array2: T[]): T[];
/**
 * 数组差集
 */
export declare function difference<T>(array1: T[], array2: T[]): T[];
/**
 * 数组并集
 */
export declare function union<T>(...arrays: T[][]): T[];
/**
 * 数组分块
 */
export declare function chunk<T>(array: T[], size: number): T[][];
/**
 * 移除假值
 */
export declare function compact<T>(array: (T | null | undefined | false | 0 | '')[]): T[];
/**
 * 扁平化数组
 */
export declare function flatten<T>(array: (T | T[])[], depth?: number): T[];
/**
 * 压缩数组
 */
export declare function zip<T>(...arrays: T[][]): T[][];
/**
 * 解压缩数组
 */
export declare function unzip<T>(array: T[][]): T[][];
/**
 * 数组分区
 */
export declare function partition<T>(array: T[], predicate: (item: T) => boolean): [T[], T[]];
/**
 * 数组洗牌
 */
export declare function shuffle<T>(array: T[]): T[];
/**
 * 随机采样
 */
export declare function sample<T>(array: T[], count?: number): T[];
/**
 * 按条件排序
 */
export declare function sortBy<T>(array: T[], keyGetter: (item: T) => any): T[];
/**
 * 按条件分组（数组版本）
 */
export declare function groupBy<T>(array: T[], keyGetter: (item: T) => string): Record<string, T[]>;
/**
 * 按键索引（数组版本）
 */
export declare function keyBy<T>(array: T[], keyGetter: (item: T) => string): Record<string, T>;
//# sourceMappingURL=array.d.ts.map