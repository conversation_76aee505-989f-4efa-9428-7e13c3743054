/**
 * @fileoverview 沙箱管理器测试
 * @description 测试沙箱管理器的功能
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi } from 'vitest'
import { SandboxManager } from '../sandbox-manager'
import { SandboxType } from '../types'

describe('SandboxManager', () => {
    let sandboxManager: SandboxManager

    beforeEach(() => {
        sandboxManager = new SandboxManager()
    })

    describe('创建沙箱', () => {
        it('应该能够创建Proxy沙箱', () => {
            const sandbox = sandboxManager.createSandbox('test-proxy', SandboxType.PROXY)
            expect(sandbox).toBeDefined()
            expect(sandbox.getName()).toBe('test-proxy')
            expect(sandbox.getType()).toBe('proxy')
        })

        it('应该能够创建DefineProperty沙箱', () => {
            const sandbox = sandboxManager.createSandbox('test-define', SandboxType.DEFINE_PROPERTY)
            expect(sandbox).toBeDefined()
            expect(sandbox.getName()).toBe('test-define')
            expect(sandbox.getType()).toBe('defineProperty')
        })

        it('应该能够创建iframe沙箱', () => {
            const sandbox = sandboxManager.createSandbox('test-iframe', SandboxType.IFRAME)
            expect(sandbox).toBeDefined()
            expect(sandbox.getName()).toBe('test-iframe')
            expect(sandbox.getType()).toBe('iframe')
        })

        it('应该能够创建WebComponent沙箱', () => {
            const sandbox = sandboxManager.createSandbox('test-webcomponent', SandboxType.WEB_COMPONENT)
            expect(sandbox).toBeDefined()
            expect(sandbox.getName()).toBe('test-webcomponent')
            expect(sandbox.getType()).toBe('webComponent')
        })

        it('应该能够创建Namespace沙箱', () => {
            const sandbox = sandboxManager.createSandbox('test-namespace', SandboxType.NAMESPACE)
            expect(sandbox).toBeDefined()
            expect(sandbox.getName()).toBe('test-namespace')
            expect(sandbox.getType()).toBe('namespace')
        })

        it('应该能够创建Federation沙箱', () => {
            const sandbox = sandboxManager.createSandbox('test-federation', SandboxType.FEDERATION)
            expect(sandbox).toBeDefined()
            expect(sandbox.getName()).toBe('test-federation')
            expect(sandbox.getType()).toBe('federation')
        })
    })

    describe('沙箱管理', () => {
        it('应该能够获取沙箱', () => {
            const sandbox = sandboxManager.createSandbox('test', SandboxType.PROXY)
            const retrieved = sandboxManager.getSandbox('test')
            expect(retrieved).toBe(sandbox)
        })

        it('应该能够检查沙箱是否存在', () => {
            sandboxManager.createSandbox('test', SandboxType.PROXY)
            expect(sandboxManager.hasSandbox('test')).toBe(true)
            expect(sandboxManager.hasSandbox('nonexistent')).toBe(false)
        })

        it('应该能够销毁沙箱', () => {
            const sandbox = sandboxManager.createSandbox('test', SandboxType.PROXY)
            const destroySpy = vi.spyOn(sandbox, 'destroy')

            sandboxManager.destroySandbox('test')

            expect(destroySpy).toHaveBeenCalled()
            expect(sandboxManager.hasSandbox('test')).toBe(false)
        })

        it('应该能够获取所有沙箱', () => {
            sandboxManager.createSandbox('test1', SandboxType.PROXY)
            sandboxManager.createSandbox('test2', SandboxType.IFRAME)

            const sandboxes = sandboxManager.getAllSandboxes()
            expect(sandboxes).toHaveLength(2)
        })

        it('应该能够清理所有沙箱', () => {
            sandboxManager.createSandbox('test1', SandboxType.PROXY)
            sandboxManager.createSandbox('test2', SandboxType.IFRAME)

            sandboxManager.clear()

            expect(sandboxManager.getAllSandboxes()).toHaveLength(0)
        })
    })

    describe('沙箱生命周期', () => {
        it('应该能够激活和停用沙箱', () => {
            const sandbox = sandboxManager.createSandbox('test', SandboxType.PROXY)

            expect(sandbox.isActive()).toBe(false)

            sandbox.activate()
            expect(sandbox.isActive()).toBe(true)

            sandbox.deactivate()
            expect(sandbox.isActive()).toBe(false)
        })

        it('应该能够执行代码', () => {
            const sandbox = sandboxManager.createSandbox('test', SandboxType.PROXY)
            sandbox.activate()

            const result = sandbox.execute('1 + 1')
            expect(result).toBe(2)
        })

        it('应该能够设置和获取全局变量', () => {
            const sandbox = sandboxManager.createSandbox('test', SandboxType.PROXY)
            sandbox.activate()

            sandbox.setGlobal('testVar', 'testValue')
            expect(sandbox.getGlobal('testVar')).toBe('testValue')
        })
    })

    describe('错误处理', () => {
        it('应该在创建重复沙箱时抛出错误', () => {
            sandboxManager.createSandbox('test', SandboxType.PROXY)

            expect(() => {
                sandboxManager.createSandbox('test', SandboxType.PROXY)
            }).toThrow('沙箱 test 已存在')
        })

        it('应该在获取不存在的沙箱时返回undefined', () => {
            expect(sandboxManager.getSandbox('nonexistent')).toBeUndefined()
        })

        it('应该在销毁不存在的沙箱时不抛出错误', () => {
            expect(() => {
                sandboxManager.destroySandbox('nonexistent')
            }).not.toThrow()
        })
    })
})