/**
 * @fileoverview 日志工具
 * @description 提供统一的日志记录功能
 * <AUTHOR> <<EMAIL>>
 */

export interface Logger {
    info(message: string, ...args: any[]): void
    warn(message: string, ...args: any[]): void
    error(message: string, ...args: any[]): void
    debug(message: string, ...args: any[]): void
}

/**
 * 创建日志记录器
 */
export function createLogger(namespace: string): Logger {
    const prefix = `[${namespace}]`

    return {
        info(message: string, ...args: any[]): void {
            console.log(`${prefix} ${message}`, ...args)
        },
        warn(message: string, ...args: any[]): void {
            console.warn(`${prefix} ${message}`, ...args)
        },
        error(message: string, ...args: any[]): void {
            console.error(`${prefix} ${message}`, ...args)
        },
        debug(message: string, ...args: any[]): void {
            if (process.env['NODE_ENV'] === 'development') {
                console.debug(`${prefix} ${message}`, ...args)
            }
        }
    }
}