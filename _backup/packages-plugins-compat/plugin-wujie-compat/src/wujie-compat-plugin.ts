/**
 * Wujie 兼容插件
 * 为 Micro-Core 提供 Wujie 兼容性支持
 */

import type { MicroCoreKernel, Plugin } from '@micro-core/core';
import type { WujieCompatConfig } from './types';
import { setupWujieCompat } from './wujie-api';

/**
 * Wujie 兼容插件类
 */
export class WujieCompatPlugin implements Plugin {
    name = 'wujie-compat';
    version = '0.1.0';

    private config: WujieCompatConfig;

    constructor(config: WujieCompatConfig = {}) {
        this.config = {
            debug: false,
            keepAlive: false,
            sandbox: {
                iframe: true,
                webComponent: true,
                styleIsolation: true
            },
            preload: {
                enabled: false,
                strategy: 'idle'
            },
            router: {
                sync: false,
                mode: 'history'
            },
            ...config
        };
    }

    /**
     * 插件安装
     */
    install(kernel: MicroCoreKernel): void {
        // 初始化 Wujie 兼容层
        setupWujieCompat(kernel);

        // 注册全局 Wujie API
        this.registerGlobalAPI();

        // 设置调试模式
        if (this.config.debug) {
            this.enableDebugMode();
        }

        console.info('[Wujie Compat] Plugin installed successfully');
    }

    /**
     * 插件卸载
     */
    uninstall(kernel: MicroCoreKernel): void {
        // 清理全局 API
        this.cleanupGlobalAPI();

        console.info('[Wujie Compat] Plugin uninstalled');
    }

    /**
     * 注册全局 Wujie API
     */
    private registerGlobalAPI(): void {
        // 动态导入 API 函数以避免循环依赖
        import('./wujie-api').then(api => {
            // 将 Wujie API 挂载到全局对象
            (window as any).wujie = {
                startApp: api.startApp,
                setupApp: api.setupApp,
                destroyApp: api.destroyApp,
                preloadApp: api.preloadApp,
                getApp: api.getApp,
                getAllApps: api.getAllApps,
                hasApp: api.hasApp,
                activateApp: api.activateApp,
                deactivateApp: api.deactivateApp,
                bus: api.bus,
                clearApps: api.clearApps,
                sendMessage: api.sendMessage,
                broadcastMessage: api.broadcastMessage,
                onMessage: api.onMessage,
                offMessage: api.offMessage,
                getAppStatus: api.getAppStatus,
                isAppMounted: api.isAppMounted,
                isAppAlive: api.isAppAlive,
                setAppProps: api.setAppProps,
                getAppProps: api.getAppProps,
                reloadApp: api.reloadApp
            };

            // 兼容旧版本 API
            (window as any).startApp = api.startApp;
            (window as any).setupApp = api.setupApp;
            (window as any).destroyApp = api.destroyApp;
            (window as any).preloadApp = api.preloadApp;
        });
    }

    /**
     * 清理全局 API
     */
    private cleanupGlobalAPI(): void {
        delete (window as any).wujie;
        delete (window as any).startApp;
        delete (window as any).setupApp;
        delete (window as any).destroyApp;
        delete (window as any).preloadApp;
    }

    /**
     * 启用调试模式
     */
    private enableDebugMode(): void {
        // 设置调试标志
        (window as any).__WUJIE_DEBUG__ = true;

        // 监听所有 Wujie 事件并打印日志
        import('./wujie-api').then(api => {
            const events = [
                'app:started',
                'app:destroyed',
                'app:preloaded',
                'app:activated',
                'app:deactivated',
                'app:error',
                'app:props-changed',
                'app:reloaded'
            ];

            events.forEach(event => {
                api.bus.$on(event, (...args: any[]) => {
                    console.debug(`[Wujie Debug] ${event}:`, ...args);
                });
            });
        });
    }

    /**
     * 获取插件配置
     */
    getConfig(): WujieCompatConfig {
        return { ...this.config };
    }

    /**
     * 更新插件配置
     */
    updateConfig(newConfig: Partial<WujieCompatConfig>): void {
        this.config = { ...this.config, ...newConfig };
    }
}

/**
 * 创建 Wujie 兼容插件实例
 */
export function createWujieCompatPlugin(config?: WujieCompatConfig): WujieCompatPlugin {
    return new WujieCompatPlugin(config);
}

/**
 * 默认导出
 */
export default WujieCompatPlugin;