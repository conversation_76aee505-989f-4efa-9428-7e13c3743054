/**
 * 通信管理器实现
 *
 * @description 统一的通信接口和状态管理
 * <AUTHOR> <<EMAIL>>
 */
import type { CommunicationManagerOptions, CommunicationMetrics, CommunicationState, EventHandler, ExtendedCommunicationConfig, MessageHandler } from './types';
/**
 * 通信管理器
 * 统一管理事件总线和消息通道
 */
export declare class CommunicationManager {
    private readonly logger;
    private readonly config;
    private readonly eventBus;
    private readonly messageChannel;
    private readonly state;
    private readonly subscriptions;
    constructor(config?: Partial<ExtendedCommunicationConfig>);
    /**
     * 初始化通信管理器
     */
    initialize(options?: CommunicationManagerOptions): Promise<void>;
    /**
     * 订阅事件
     */
    on(event: string, handler: EventHandler, options?: any): string;
    /**
     * 订阅事件（仅触发一次）
     */
    once(event: string, handler: EventHandler, options?: any): string;
    /**
     * 发布事件
     */
    emit(event: string, data?: any): Promise<boolean>;
    /**
     * 同步发布事件
     */
    emitSync(event: string, data?: any): boolean;
    /**
     * 创建消息通道
     */
    createChannel(channelId: string, options?: any): void;
    /**
     * 订阅消息通道
     */
    subscribe(channelId: string, handler: MessageHandler, options?: any): string;
    /**
     * 发送消息
     */
    send(channelId: string, data: any, options?: any): Promise<boolean>;
    /**
     * 广播消息
     */
    broadcast(data: any, options?: any): Promise<number>;
    /**
     * 取消订阅
     */
    unsubscribe(subscriptionId: string): boolean;
    /**
     * 获取事件监听器数量
     */
    getEventListenerCount(event: string): number;
    /**
     * 获取通道订阅者数量
     */
    getChannelSubscriberCount(channelId: string): number;
    /**
     * 获取所有事件名称
     */
    getEventNames(): string[];
    /**
     * 获取所有通道ID
     */
    getChannelIds(): string[];
    /**
     * 获取通信状态
     */
    getState(): CommunicationState;
    /**
     * 获取性能指标
     */
    getMetrics(): CommunicationMetrics;
    /**
     * 清理所有订阅和通道
     */
    clear(): void;
    /**
     * 销毁通信管理器
     */
    destroy(): void;
    /**
     * 设置通信桥接
     */
    private setupCommunicationBridge;
    /**
     * 确保管理器已初始化
     */
    private ensureInitialized;
    /**
     * 确保事件总线已激活
     */
    private ensureEventBusActive;
    /**
     * 确保消息通道已激活
     */
    private ensureMessageChannelActive;
    /**
     * 生成订阅ID
     */
    private generateSubscriptionId;
}
//# sourceMappingURL=communication-manager.d.ts.map