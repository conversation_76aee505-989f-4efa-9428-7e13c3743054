# 通信 API (第三部分)

## 常见问题与解决方案

### 通信超时

**问题**：通信请求超时，无法获取响应。

**解决方案**：

1. 增加超时时间
2. 实现重试机制
3. 检查网络连接和目标应用状态

```typescript
// 增加超时时间
channel.request('getData', { id: 1 }, {
  timeout: 10000 // 10秒
})
  .then(data => {
    console.log('数据:', data);
  })
  .catch(error => {
    console.error('请求失败:', error);
  });

// 实现重试机制
channel.request('getData', { id: 1 }, {
  retry: 3,
  retryDelay: 1000
})
  .then(data => {
    console.log('数据:', data);
  })
  .catch(error => {
    console.error('请求失败:', error);
  });

// 手动实现重试
async function requestWithRetry(type, data, options = {}) {
  const maxRetries = options.retry || 3;
  const retryDelay = options.retryDelay || 1000;
  
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await channel.request(type, data, {
        ...options,
        timeout: options.timeout || 5000
      });
    } catch (error) {
      lastError = error;
      
      if (i < maxRetries) {
        console.warn(`请求失败，${i + 1}/${maxRetries} 次重试`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }
  
  throw lastError;
}
```

### 消息丢失

**问题**：发送的消息没有被接收或处理。

**解决方案**：

1. 使用确认机制
2. 实现消息队列
3. 添加消息持久化

```typescript
// 使用确认机制
channel.send('importantMessage', { id: 1, data: 'critical' }, {
  requireAck: true,
  onAck: () => {
    console.log('消息已确认接收');
  },
  onTimeout: () => {
    console.error('消息确认超时');
    // 重新发送
    retryMessage();
  }
});

// 实现消息队列
class MessageQueue {
  constructor(channel) {
    this.channel = channel;
    this.queue = [];
    this.processing = false;
  }
  
  send(type, data, options = {}) {
    return new Promise((resolve, reject) => {
      this.queue.push({
        type,
        data,
        options,
        resolve,
        reject
      });
      
      this.processQueue();
    });
  }
  
  async processQueue() {
    if (this.processing || this.queue.length === 0) {
      return;
    }
    
    this.processing = true;
    
    const { type, data, options, resolve, reject } = this.queue.shift();
    
    try {
      const result = await this.channel.request(type, data, options);
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.processing = false;
      this.processQueue();
    }
  }
}

const messageQueue = new MessageQueue(channel);

messageQueue.send('getData', { id: 1 })
  .then(data => {
    console.log('数据:', data);
  });
```

### 跨域问题

**问题**：跨域限制导致通信失败。

**解决方案**：

1. 使用 postMessage 通信
2. 配置 CORS 头
3. 使用代理服务器

```typescript
// 使用 postMessage 通信
const channel = new MessageChannel({
  name: 'cross-origin-channel',
  transport: 'postMessage'
});

// 在主应用中
window.addEventListener('message', (event) => {
  // 验证来源
  if (event.origin !== 'https://trusted-app.com') {
    return;
  }
  
  // 处理消息
  const { type, data } = event.data;
  handleMessage(type, data);
});

// 在微应用中
window.parent.postMessage({
  type: 'getData',
  data: { id: 1 }
}, 'https://main-app.com');

// 使用代理服务器
const bridge = new AppBridge({
  name: 'main-app',
  transport: {
    type: 'http',
    endpoint: '/api/bridge'
  }
});
```

### 状态同步问题

**问题**：多个应用之间的状态不同步。

**解决方案**：

1. 使用全局状态管理
2. 实现状态广播
3. 使用发布-订阅模式

```typescript
// 使用全局状态管理
const state = new GlobalState({
  user: {
    id: null,
    name: '',
    isLoggedIn: false
  },
  theme: 'light'
});

// 在主应用中
state.subscribe('user', (newValue) => {
  console.log('用户状态变化:', newValue);
  updateUserUI(newValue);
});

// 在微应用中
state.subscribe('user', (newValue) => {
  console.log('用户状态变化:', newValue);
  updateUserUI(newValue);
});

// 更新状态（在任何应用中）
state.set('user', {
  id: 1,
  name: 'Admin',
  isLoggedIn: true
});

// 实现状态广播
function broadcastState(state) {
  channel.broadcast('stateUpdate', state);
}

// 监听状态广播
channel.on('stateUpdate', (state) => {
  updateLocalState(state);
});
```

### 安全问题

**问题**：通信过程中的数据安全问题。

**解决方案**：

1. 使用消息签名
2. 实现访问控制
3. 添加数据加密

```typescript
// 使用消息签名
const secureChannel = new MessageChannel({
  name: 'secure-channel',
  security: {
    sign: true,
    secretKey: 'your-secret-key'
  }
});

// 实现访问控制
const bridge = new AppBridge({
  name: 'main-app',
  security: {
    accessControl: {
      rules: {
        'getData': ['app1', 'app2'],
        'updateData': ['app1'],
        'adminOperation': ['admin-app']
      }
    }
  }
});

// 添加数据加密
const encryptedChannel = new MessageChannel({
  name: 'encrypted-channel',
  security: {
    encrypt: true,
    algorithm: 'aes-256-gcm',
    key: 'your-encryption-key'
  }
});

// 自定义安全中间件
channel.use((message, next) => {
  // 验证消息
  if (!verifyMessage(message)) {
    throw new Error('消息验证失败');
  }
  
  return next();
});
```

### 性能问题

**问题**：频繁通信导致性能下降。

**解决方案**：

1. 批量处理消息
2. 实现消息节流和防抖
3. 使用缓存

```typescript
// 批量处理消息
const batchProcessor = {
  queue: [],
  timeout: null,
  
  add(message) {
    this.queue.push(message);
    
    if (!this.timeout) {
      this.timeout = setTimeout(() => {
        this.process();
      }, 100);
    }
  },
  
  process() {
    if (this.queue.length === 0) {
      return;
    }
    
    const batch = this.queue;
    this.queue = [];
    this.timeout = null;
    
    channel.send('batchProcess', batch);
  }
};

// 添加消息
batchProcessor.add({ type: 'update', id: 1, value: 10 });
batchProcessor.add({ type: 'update', id: 2, value: 20 });

// 实现消息节流
function throttle(fn, delay) {
  let lastCall = 0;
  
  return function(...args) {
    const now = Date.now();
    
    if (now - lastCall >= delay) {
      lastCall = now;
      return fn.apply(this, args);
    }
  };
}

const throttledSend = throttle((type, data) => {
  channel.send(type, data);
}, 200);

// 使用缓存
const cache = new Map();

function getCachedData(id) {
  if (cache.has(id)) {
    return Promise.resolve(cache.get(id));
  }
  
  return channel.request('getData', { id })
    .then(data => {
      cache.set(id, data);
      return data;
    });
}
```

## 最佳实践

### 通信设计模式

#### 发布-订阅模式

```typescript
// 创建事件总线
const eventBus = new EventBus();

// 发布者
function publishEvent(event, data) {
  eventBus.emit(event, data);
}

// 订阅者
eventBus.on('userLoggedIn', (data) => {
  console.log('用户登录:', data);
});

eventBus.on('dataUpdated', (data) => {
  console.log('数据更新:', data);
});

// 发布事件
publishEvent('userLoggedIn', { userId: 1, username: 'Admin' });
```

#### 请求-响应模式

```typescript
// 创建通道
const channel = new MessageChannel({
  name: 'req-res-channel',
  type: 'request-response'
});

// 服务提供者
channel.respond('getData', (params) => {
  return fetchData(params.id);
});

channel.respond('updateData', (params) => {
  return updateData(params.id, params.data);
});

// 服务消费者
channel.request('getData', { id: 1 })
  .then(data => {
    console.log('数据:', data);
  });

channel.request('updateData', { id: 1, data: { name: 'New Name' } })
  .then(result => {
    console.log('更新结果:', result);
  });
```

#### 命令模式

```typescript
// 创建命令处理器
const commandHandler = {
  commands: new Map(),
  
  register(name, handler) {
    this.commands.set(name, handler);
  },
  
  execute(name, params) {
    const handler = this.commands.get(name);
    
    if (!handler) {
      throw new Error(`未知命令: ${name}`);
    }
    
    return handler(params);
  }
};

// 注册命令
commandHandler.register('createUser', (params) => {
  return createUser(params);
});

commandHandler.register('deleteUser', (params) => {
  return deleteUser(params.id);
});

// 创建通道
const channel = new MessageChannel({
  name: 'command-channel'
});

// 监听命令
channel.on('command', (data) => {
  const { command, params } = data;
  
  try {
    const result = commandHandler.execute(command, params);
    channel.send('commandResult', {
      id: data.id,
      success: true,
      result
    });
  } catch (error) {
    channel.send('commandResult', {
      id: data.id,
      success: false,
      error: error.message
    });
  }
});

// 发送命令
function sendCommand(command, params) {
  const id = generateId();
  
  return new Promise((resolve, reject) => {
    // 监听结果
    const handler = (data) => {
      if (data.id === id) {
        channel.off('commandResult', handler);
        
        if (data.success) {
          resolve(data.result);
        } else {
          reject(new Error(data.error));
        }
      }
    };
    
    channel.on('commandResult', handler);
    
    // 发送命令
    channel.send('command', {
      id,
      command,
      params
    });
    
    // 设置超时
    setTimeout(() => {
      channel.off('commandResult', handler);
      reject(new Error('命令执行超时'));
    }, 5000);
  });
}

// 使用命令
sendCommand('createUser', { name: 'New User', email: '<EMAIL>' })
  .then(user => {
    console.log('用户创建成功:', user);
  })
  .catch(error => {
    console.error('用户创建失败:', error);
  });
```

#### 观察者模式

```typescript
// 创建可观察对象
class Observable {
  constructor() {
    this.observers = new Map();
  }
  
  subscribe(event, observer) {
    if (!this.observers.has(event)) {
      this.observers.set(event, new Set());
    }
    
    this.observers.get(event).add(observer);
    
    return {
      unsubscribe: () => {
        this.observers.get(event).delete(observer);
        
        if (this.observers.get(event).size === 0) {
          this.observers.delete(event);
        }
      }
    };
  }
  
  notify(event, data) {
    if (this.observers.has(event)) {
      this.observers.get(event).forEach(observer => {
        observer(data);
      });
    }
  }
}

// 创建通道
const channel = new MessageChannel({
  name: 'observer-channel'
});

// 创建可观察对象
const observable = new Observable();

// 监听通道消息
channel.on('event', (data) => {
  observable.notify(data.event, data.data);
});

// 订阅事件
const subscription = observable.subscribe('userUpdated', (data) => {
  console.log('用户更新:', data);
});

// 发布事件
channel.send('event', {
  event: 'userUpdated',
  data: { id: 1, name: 'Updated Name' }
});

// 取消订阅
subscription.unsubscribe();
```

### 通信架构

#### 集中式通信

```typescript
// 创建中央通信总线
const centralBus = new EventBus();

// 注册应用
function registerApp(appName, apis) {
  Object.entries(apis).forEach(([name, handler]) => {
    centralBus.on(`${appName}:${name}`, handler);
  });
}

// 调用 API
function callAPI(appName, apiName, params) {
  return new Promise((resolve, reject) => {
    const requestId = generateId();
    
    // 监听响应
    const responseHandler = (response) => {
      if (response.requestId === requestId) {
        centralBus.off(`${appName}:${apiName}:response`, responseHandler);
        
        if (response.error) {
          reject(new Error(response.error));
        } else {
          resolve(response.data);
        }
      }
    };
    
    centralBus.on(`${appName}:${apiName}:response`, responseHandler);
    
    // 发送请求
    centralBus.emit(`${appName}:${apiName}`, {
      requestId,
      params,
      respond: (data, error) => {
        centralBus.emit(`${appName}:${apiName}:response`, {
          requestId,
          data,
          error
        });
      }
    });
    
    // 设置超时
    setTimeout(() => {
      centralBus.off(`${appName}:${apiName}:response`, responseHandler);
      reject(new Error('API 调用超时'));
    }, 5000);
  });
}

// 注册应用
registerApp('userService', {
  getUser: (request) => {
    const { params, respond } = request;
    
    fetchUser(params.id)
      .then(user => {
        respond(user);
      })
      .catch(error => {
        respond(null, error.message);
      });
  },
  
  updateUser: (request) => {
    const { params, respond } = request;
    
    updateUser(params.id, params.data)
      .then(result => {
        respond(result);
      })
      .catch(error => {
        respond(null, error.message);
      });
  }
});

// 调用 API
callAPI('userService', 'getUser', { id: 1 })
  .then(user => {
    console.log('用户:', user);
  })
  .catch(error => {
    console.error('获取用户失败:', error);
  });
```

#### 分布式通信

```typescript
// 创建应用桥接器
class AppConnector {
  constructor(appName) {
    this.appName = appName;
    this.connections = new Map();
    this.apis = new Map();
    
    // 创建通道
    this.channel = new MessageChannel({
      name: `${appName}-channel`
    });
    
    // 监听 API 调用
    this.channel.on('api:call', (data) => {
      this.handleAPICall(data);
    });
    
    // 监听 API 响应
    this.channel.on('api:response', (data) => {
      this.handleAPIResponse(data);
    });
  }
  
  // 注册 API
  registerAPI(name, handler) {
    this.apis.set(name, handler);
  }
  
  // 连接到其他应用
  connect(appName) {
    if (this.connections.has(appName)) {
      return;
    }
    
    const connection = {
      appName,
      channel: new MessageChannel({
        name: `${this.appName}-to-${appName}-channel`
      }),
      pendingRequests: new Map()
    };
    
    this.connections.set(appName, connection);
    
    return connection;
  }
  
  // 调用其他应用的 API
  callAPI(appName, apiName, params) {
    let connection = this.connections.get(appName);
    
    if (!connection) {
      connection = this.connect(appName);
    }
    
    return new Promise((resolve, reject) => {
      const requestId = generateId();
      
      // 保存请求
      connection.pendingRequests.set(requestId, {
        resolve,
        reject,
        timeout: setTimeout(() => {
          connection.pendingRequests.delete(requestId);
          reject(new Error('API 调用超时'));
        }, 5000)
      });
      
      // 发送请求
      connection.channel.send('api:call', {
        requestId,
        from: this.appName,
        to: appName,
        api: apiName,
        params
      });
    });
  }
  
  // 处理 API 调用
  handleAPICall(data) {
    const { requestId, from, api, params } = data;
    
    const handler = this.apis.get(api);
    
    if (!handler) {
      this.channel.send('api:response', {
        requestId,
        from: this.appName,
        to: from,
        error: `API 不存在: ${api}`
      });
      return;
    }
    
    Promise.resolve()
      .then(() => handler(params))
      .then(result => {
        this.channel.send('api:response', {
          requestId,
          from: this.appName,
          to: from,
          result
        });
      })
      .catch(error => {
        this.channel.send('api:response', {
          requestId,
          from: this.appName,
          to: from,
          error: error.message
        });
      });
  }
  
  // 处理 API 响应
  handleAPIResponse(data) {
    const { requestId, from, result, error } = data;
    
    const connection = this.connections.get(from);
    
    if (!connection) {
      return;
    }
    
    const request = connection.pendingRequests.get(requestId);
    
    if (!request) {
      return;
    }
    
    clearTimeout(request.timeout);
    connection.pendingRequests.delete(requestId);
    
    if (error) {
      request.reject(new Error(error));
    } else {
      request.resolve(result);
    }
  }
}

// 创建应用连接器
const userApp = new AppConnector('userApp');
const dataApp = new AppConnector('dataApp');

// 注册 API
userApp.registerAPI('getUserProfile', (params) => {
  return fetchUserProfile(params.userId);
});

dataApp.registerAPI('getData', (params) => {
  return fetchData(params.id);
});

// 调用 API
userApp.callAPI('dataApp', 'getData', { id: 1 })
  .then(data => {
    console.log('数据:', data);
  })
  .catch(error => {
    console.error('获取数据失败:', error);
  });

dataApp.callAPI('userApp', 'getUserProfile', { userId: 1 })
  .then(profile => {
    console.log('用户资料:', profile);
  })
  .catch(error => {
    console.error('获取用户资料失败:', error);
  });
```

### 通信测试

```typescript
// 创建测试通道
const testChannel = new MessageChannel({
  name: 'test-channel'
});

// 模拟响应
testChannel.respond('getData', (params) => {
  // 返回模拟数据
  return {
    id: params.id,
    name: `Test Data ${params.id}`,
    value: Math.random()
  };
});

// 测试请求
async function testRequest() {
  try {
    const data = await testChannel.request('getData', { id: 1 });
    console.log('测试数据:', data);
    assert(data.id === 1, 'ID 应该匹配');
    assert(data.name === 'Test Data 1', '名称应该匹配');
    console.log('测试通过');
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 测试超时
async function testTimeout() {
  try {
    // 设置很短的超时时间
    await testChannel.request('slowOperation', { id: 1 }, {
      timeout: 10
    });
    console.error('测试失败: 应该超时');
  } catch (error) {
    console.log('测试通过: 正确捕获超时错误');
  }
}

// 测试错误处理
testChannel.respond('errorOperation', () => {
  throw new Error('测试错误');
});

async function testErrorHandling() {
  try {
    await testChannel.request('errorOperation');
    console.error('测试失败: 应该抛出错误');
  } catch (error) {
    console.log('测试通过: 正确捕获错误');
    assert(error.message === '测试错误', '错误消息应该匹配');
  }
}

// 运行测试
async function runTests() {
  await testRequest();
  await testTimeout();
  await testErrorHandling();
}

runTests();
```

## 总结

Micro-Core 通信 API 提供了强大而灵活的通信功能，适用于微前端架构中的各种场景。通过本文档，你可以了解通信 API 的基本用法、高级特性以及最佳实践，帮助你构建高效、可靠的微前端应用。

主要特性包括：

1. **多种通信模式**：支持广播、点对点、请求-响应等通信模式
2. **全局状态管理**：提供全局状态共享和同步机制
3. **事件总线**：支持发布-订阅模式的事件传递
4. **应用桥接**：实现主应用和微应用之间的无缝通信
5. **安全机制**：提供消息签名、访问控制和数据加密等安全功能
6. **性能优化**：支持批量处理、消息节流和缓存等性能优化机制

通过合理使用这些功能，你可以构建出通信高效、可靠的微前端应用。