/**
 * @fileoverview Vue 3 微前端应用 Composable
 * <AUTHOR> <<EMAIL>>
 */

import { computed, inject } from 'vue'

// 微前端应用属性接口
export interface MicroAppProps {
    name?: string
    basename?: string
    theme?: 'light' | 'dark'
    user?: any
    container?: HTMLElement | string
    [key: string]: any
}

// 微前端应用信息接口
export interface MicroAppInfo {
    name: string
    framework: string
    version: string
    basename: string
    isMicroFrontend: boolean
}

/**
 * 使用微前端应用属性的 Composable
 */
export function useMicroApp() {
    const microProps = inject<MicroAppProps>('microProps', {})

    return {
        props: computed(() => microProps),
        name: computed(() => microProps.name || 'sub-app-vue3'),
        basename: computed(() => microProps.basename || '/vue3'),
        theme: computed(() => microProps.theme || 'light'),
        user: computed(() => microProps.user),
        container: computed(() => microProps.container)
    }
}

/**
 * 使用微前端应用信息的 Composable
 */
export function useMicroAppInfo() {
    const microAppInfo = inject<MicroAppInfo>('microAppInfo', {
        name: 'sub-app-vue3',
        framework: 'Vue3',
        version: '3.4.0',
        basename: '/vue3',
        isMicroFrontend: false
    })

    return {
        info: computed(() => microAppInfo),
        name: computed(() => microAppInfo.name),
        framework: computed(() => microAppInfo.framework),
        version: computed(() => microAppInfo.version),
        basename: computed(() => microAppInfo.basename),
        isMicroFrontend: computed(() => microAppInfo.isMicroFrontend),
        isStandalone: computed(() => !microAppInfo.isMicroFrontend)
    }
}

/**
 * 使用主题的 Composable
 */
export function useTheme() {
    const { theme } = useMicroApp()

    return {
        theme,
        isDark: computed(() => theme.value === 'dark'),
        isLight: computed(() => theme.value === 'light' || !theme.value),
        toggleTheme: () => {
            // 这里可以实现主题切换逻辑
            const newTheme = theme.value === 'dark' ? 'light' : 'dark'
            document.documentElement.setAttribute('data-theme', newTheme)
        }
    }
}

/**
 * 使用用户信息的 Composable
 */
export function useUser() {
    const { user } = useMicroApp()

    return {
        user,
        isLoggedIn: computed(() => !!user.value),
        userId: computed(() => user.value?.id),
        userName: computed(() => user.value?.name),
        userRole: computed(() => user.value?.role),
        userAvatar: computed(() => user.value?.avatar)
    }
}

/**
 * 使用微前端通信的 Composable
 */
export function useMicroCommunication() {
    /**
     * 发送消息到主应用
     */
    const sendToMain = (type: string, data: any) => {
        window.postMessage({
            type: 'MICRO_APP_MESSAGE',
            source: 'sub-app-vue3',
            target: 'main',
            data: { type, data, timestamp: Date.now() }
        }, '*')
    }

    /**
     * 广播消息到所有应用
     */
    const broadcast = (type: string, data: any) => {
        window.postMessage({
            type: 'MICRO_APP_BROADCAST',
            source: 'sub-app-vue3',
            data: { type, data, timestamp: Date.now() }
        }, '*')
    }

    /**
     * 监听消息
     */
    const onMessage = (callback: (event: MessageEvent) => void) => {
        const handler = (event: MessageEvent) => {
            if (event.data?.type === 'MICRO_APP_MESSAGE' || event.data?.type === 'MICRO_APP_BROADCAST') {
                callback(event)
            }
        }

        window.addEventListener('message', handler)

        // 返回清理函数
        return () => {
            window.removeEventListener('message', handler)
        }
    }

    return {
        sendToMain,
        broadcast,
        onMessage
    }
}

/**
 * 使用微前端路由的 Composable
 */
export function useMicroRouter() {
    const { basename } = useMicroApp()

    /**
     * 获取完整路径
     */
    const getFullPath = (path: string) => {
        const base = basename.value.endsWith('/') ? basename.value.slice(0, -1) : basename.value
        const cleanPath = path.startsWith('/') ? path : `/${path}`
        return `${base}${cleanPath}`
    }

    /**
     * 获取相对路径
     */
    const getRelativePath = (fullPath: string) => {
        const base = basename.value.endsWith('/') ? basename.value.slice(0, -1) : basename.value
        return fullPath.startsWith(base) ? fullPath.slice(base.length) || '/' : fullPath
    }

    return {
        basename,
        getFullPath,
        getRelativePath
    }
}