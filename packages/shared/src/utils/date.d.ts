/**
 * @fileoverview 日期处理工具函数
 * @description 提供各种日期操作和格式化功能
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 格式化日期
 */
export declare function formatDate(date: Date, format?: string): string;
/**
 * 解析日期字符串
 */
export declare function parseDate(dateString: string): Date;
/**
 * 添加天数
 */
export declare function addDays(date: Date, days: number): Date;
/**
 * 减去天数
 */
export declare function subtractDays(date: Date, days: number): Date;
/**
 * 计算日期差（天数）
 */
export declare function diffDays(date1: Date, date2: Date): number;
/**
 * 检查是否为今天
 */
export declare function isToday(date: Date): boolean;
/**
 * 检查是否为明天
 */
export declare function isTomorrow(date: Date): boolean;
/**
 * 检查是否为昨天
 */
export declare function isYesterday(date: Date): boolean;
/**
 * 获取一天的开始时间
 */
export declare function startOfDay(date: Date): Date;
/**
 * 获取一天的结束时间
 */
export declare function endOfDay(date: Date): Date;
/**
 * 获取时间戳
 */
export declare function getTimestamp(date?: Date): number;
//# sourceMappingURL=date.d.ts.map