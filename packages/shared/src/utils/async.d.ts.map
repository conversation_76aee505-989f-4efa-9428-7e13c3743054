{"version": 3, "file": "async.d.ts", "sourceRoot": "", "sources": ["async.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;GAEG;AACH,wBAAgB,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAE/C;AAED;;GAEG;AACH,wBAAgB,OAAO,CAAC,CAAC,EACrB,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,EACnB,EAAE,EAAE,MAAM,EACV,YAAY,SAAS,GACtB,OAAO,CAAC,CAAC,CAAC,CAOZ;AAED;;GAEG;AACH,wBAAsB,KAAK,CAAC,CAAC,EACzB,EAAE,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EACpB,OAAO,GAAE;IACL,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,OAAO,CAAC,EAAE,QAAQ,GAAG,aAAa,CAAC;IACnC,SAAS,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,OAAO,CAAC;CACpC,GACP,OAAO,CAAC,CAAC,CAAC,CA6BZ;AAED;;GAEG;AACH,wBAAsB,QAAQ,CAAC,CAAC,EAC5B,KAAK,EAAE,KAAK,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,EAC9B,WAAW,SAAW,GACvB,OAAO,CAAC,CAAC,EAAE,CAAC,CAwBd;AAED;;GAEG;AACH,wBAAsB,MAAM,CAAC,CAAC,EAC1B,KAAK,EAAE,KAAK,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,GAC/B,OAAO,CAAC,CAAC,EAAE,CAAC,CASd;AAED;;GAEG;AACH,wBAAsB,SAAS,CAAC,CAAC,EAC7B,KAAK,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,GACzC,OAAO,CAAC,CAAC,CAAC,CAQZ;AAED;;GAEG;AACH,wBAAgB,IAAI,CAAC,CAAC,EAClB,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GACvB,OAAO,CAAC,CAAC,CAAC,CAEZ;AAED;;GAEG;AACH,wBAAgB,SAAS,CAAC,CAAC,SAAS,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,EACxD,EAAE,EAAE,CAAC,GACN,CAAC,GAAG,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,CAY1C;AAED;;GAEG;AACH,wBAAgB,WAAW,CAAC,CAAC,SAAS,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,OAAO,CAAC,GAAG,CAAC,EAClE,EAAE,EAAE,CAAC,GACN,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,EAAE,MAAM,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,KAAK,IAAI,CASpF;AAED;;GAEG;AACH,wBAAgB,KAAK,CAAC,CAAC,KAAK;IACxB,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IACpB,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;IAC7C,MAAM,EAAE,CAAC,MAAM,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC;CAClC,CAUA"}