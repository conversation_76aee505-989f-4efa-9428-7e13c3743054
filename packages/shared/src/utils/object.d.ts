/**
 * @fileoverview 对象处理工具函数
 * @description 提供各种对象操作和转换功能
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 深度克隆对象
 */
export declare function deepClone<T>(obj: T): T;
/**
 * 深度合并对象
 */
export declare function deepMerge<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T;
/**
 * 选择对象属性
 */
export declare function pick<T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Pick<T, K>;
/**
 * 排除对象属性
 */
export declare function omit<T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Omit<T, K>;
/**
 * 获取嵌套属性值
 */
export declare function get<T = any>(obj: any, path: string, defaultValue?: T): T;
/**
 * 设置嵌套属性值
 */
export declare function set(obj: any, path: string, value: any): void;
/**
 * 检查是否有嵌套属性
 */
export declare function has(obj: any, path: string): boolean;
/**
 * 删除嵌套属性
 */
export declare function unset(obj: any, path: string): boolean;
/**
 * 扁平化对象
 */
export declare function flattenObject(obj: Record<string, any>, prefix?: string, separator?: string): Record<string, any>;
/**
 * 反扁平化对象
 */
export declare function unflattenObject(obj: Record<string, any>, separator?: string): Record<string, any>;
/**
 * 映射对象键
 */
export declare function mapKeys<T extends Record<string, any>>(obj: T, mapper: (key: string, value: any) => string): Record<string, any>;
/**
 * 映射对象值
 */
export declare function mapValues<T extends Record<string, any>, R>(obj: T, mapper: (value: any, key: string) => R): Record<keyof T, R>;
/**
 * 反转对象键值
 */
export declare function invert(obj: Record<string, any>): Record<string, string>;
/**
 * 计数分组
 */
export declare function countBy<T>(array: T[], keyGetter: (item: T) => string): Record<string, number>;
//# sourceMappingURL=object.d.ts.map