/**
 * @fileoverview 异步处理工具函数
 * @description 提供各种异步操作和Promise处理功能
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 延迟执行
 */
export declare function sleep(ms: number): Promise<void>;
/**
 * 超时Promise
 */
export declare function timeout<T>(promise: Promise<T>, ms: number, errorMessage?: string): Promise<T>;
/**
 * 重试函数
 */
export declare function retry<T>(fn: () => Promise<T>, options?: {
    retries?: number;
    delay?: number;
    backoff?: 'linear' | 'exponential';
    condition?: (error: Error) => boolean;
}): Promise<T>;
/**
 * 并行执行
 */
export declare function parallel<T>(tasks: Array<() => Promise<T>>, concurrency?: number): Promise<T[]>;
/**
 * 串行执行
 */
export declare function series<T>(tasks: Array<() => Promise<T>>): Promise<T[]>;
/**
 * 瀑布流执行
 */
export declare function waterfall<T>(tasks: Array<(prev?: any) => Promise<T>>): Promise<T>;
/**
 * 竞速执行
 */
export declare function race<T>(promises: Promise<T>[]): Promise<T>;
/**
 * Promise化回调函数
 */
export declare function promisify<T extends (...args: any[]) => void>(fn: T): (...args: Parameters<T>) => Promise<any>;
/**
 * 回调化Promise函数
 */
export declare function callbackify<T extends (...args: any[]) => Promise<any>>(fn: T): (...args: [...Parameters<T>, (error: Error | null, result?: any) => void]) => void;
/**
 * 延迟Promise
 */
export declare function defer<T>(): {
    promise: Promise<T>;
    resolve: (value: T | PromiseLike<T>) => void;
    reject: (reason?: any) => void;
};
//# sourceMappingURL=async.d.ts.map