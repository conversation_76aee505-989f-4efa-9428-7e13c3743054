# 构建工具集成

Micro-Core 提供了对主流构建工具的全面支持，包括 Vite、Webpack、Rollup、esbuild、Rspack、Parcel 和 Turbopack。通过统一的构建器接口，开发者可以无缝集成现有的构建流程，实现微前端应用的高效构建和部署。

## 设计理念

### 构建器统一接口

所有构建器都实现了统一的 `MicroAppBuilder` 接口：

```typescript
interface MicroAppBuilder {
  name: string;
  version: string;
  
  // 构建方法
  build(config: BuildConfig): Promise<BuildResult>;
  dev(config: DevConfig): Promise<DevServer>;
  
  // 配置方法
  createConfig(options: BuilderOptions): BuildConfig;
  mergeConfig(base: BuildConfig, override: BuildConfig): BuildConfig;
  
  // 生命周期钩子
  beforeBuild?(config: BuildConfig): Promise<void>;
  afterBuild?(result: BuildResult): Promise<void>;
}
```

### 零配置启动

支持零配置启动，同时提供丰富的自定义选项：

```typescript
import { MicroCore } from '@micro-core/core';
import { ViteBuilder } from '@micro-core/builder-vite';

const microCore = new MicroCore({
  builder: new ViteBuilder(), // 零配置
  
  apps: [
    {
      name: 'react-app',
      entry: './src/react-app',
      builder: 'vite' // 使用默认配置
    }
  ]
});
```

## 官方构建器

### Vite 构建器 (@micro-core/builder-vite)

基于 Vite 7.0.6 的现代化构建工具，提供极速的开发体验。

#### 基础配置

```typescript
import { ViteBuilder } from '@micro-core/builder-vite';

const viteBuilder = new ViteBuilder({
  // 基础配置
  root: process.cwd(),
  mode: 'development',
  
  // 服务器配置
  server: {
    port: 3000,
    host: 'localhost',
    open: true,
    cors: true
  },
  
  // 构建配置
  build: {
    outDir: 'dist',
    sourcemap: true,
    minify: 'esbuild',
    target: 'es2020'
  },
  
  // 微前端特定配置
  microFrontend: {
    // 模块联邦配置
    federation: {
      name: 'host',
      remotes: {
        'react-app': 'http://localhost:3001/assets/remoteEntry.js'
      },
      shared: {
        react: { singleton: true },
        'react-dom': { singleton: true }
      }
    },
    
    // 入口配置
    entry: {
      type: 'module', // 'module' | 'script' | 'html'
      format: 'esm' // 'esm' | 'cjs' | 'umd'
    }
  }
});
```

#### 插件生态集成

```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { microCore } from '@micro-core/vite-plugin';

export default defineConfig({
  plugins: [
    react(),
    microCore({
      // Micro-Core 插件配置
      apps: [
        {
          name: 'react-app',
          entry: './src/main.tsx',
          template: './index.html'
        }
      ],
      
      // 开发服务器配置
      devServer: {
        proxy: {
          '/api': 'http://localhost:8080'
        }
      }
    })
  ]
});
```

### Webpack 构建器 (@micro-core/builder-webpack)

基于 Webpack 5 的成熟构建方案，支持模块联邦和丰富的插件生态。

#### 基础配置

```typescript
import { WebpackBuilder } from '@micro-core/builder-webpack';

const webpackBuilder = new WebpackBuilder({
  // 入口配置
  entry: {
    main: './src/index.js'
  },
  
  // 输出配置
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].[contenthash].js',
    publicPath: 'auto'
  },
  
  // 模式配置
  mode: 'development',
  
  // 微前端配置
  microFrontend: {
    // 模块联邦
    federation: {
      name: 'host',
      filename: 'remoteEntry.js',
      remotes: {
        'react-app': 'reactApp@http://localhost:3001/remoteEntry.js',
        'vue-app': 'vueApp@http://localhost:3002/remoteEntry.js'
      },
      shared: {
        react: { singleton: true, eager: true },
        'react-dom': { singleton: true, eager: true }
      }
    }
  }
});
```

#### 模块联邦配置

```typescript
const ModuleFederationPlugin = require('@module-federation/webpack');

const webpackBuilder = new WebpackBuilder({
  plugins: [
    new ModuleFederationPlugin({
      name: 'host',
      remotes: {
        'micro-app-1': 'microApp1@http://localhost:3001/remoteEntry.js',
        'micro-app-2': 'microApp2@http://localhost:3002/remoteEntry.js'
      },
      shared: {
        // 共享依赖配置
        react: {
          singleton: true,
          requiredVersion: '^18.0.0'
        },
        'react-dom': {
          singleton: true,
          requiredVersion: '^18.0.0'
        }
      }
    })
  ]
});
```

### Rollup 构建器 (@micro-core/builder-rollup)

基于 Rollup 的轻量级构建方案，适合库和组件的构建。

```typescript
import { RollupBuilder } from '@micro-core/builder-rollup';

const rollupBuilder = new RollupBuilder({
  // 输入配置
  input: {
    main: 'src/index.js',
    utils: 'src/utils.js'
  },
  
  // 输出配置
  output: [
    {
      dir: 'dist/esm',
      format: 'esm',
      entryFileNames: '[name].js',
      chunkFileNames: '[name]-[hash].js'
    },
    {
      dir: 'dist/cjs',
      format: 'cjs',
      entryFileNames: '[name].cjs',
      chunkFileNames: '[name]-[hash].cjs'
    }
  ],
  
  // 外部依赖
  external: ['react', 'react-dom'],
  
  // 微前端配置
  microFrontend: {
    format: 'esm',
    globals: {
      react: 'React',
      'react-dom': 'ReactDOM'
    }
  }
});
```

### esbuild 构建器 (@micro-core/builder-esbuild)

基于 esbuild 的超快构建方案，适合开发环境和简单应用。

```typescript
import { EsbuildBuilder } from '@micro-core/builder-esbuild';

const esbuildBuilder = new EsbuildBuilder({
  // 入口文件
  entryPoints: ['src/index.tsx'],
  
  // 输出配置
  outdir: 'dist',
  bundle: true,
  splitting: true,
  format: 'esm',
  target: 'es2020',
  
  // 开发服务器
  serve: {
    port: 3000,
    host: 'localhost'
  },
  
  // 微前端配置
  microFrontend: {
    // 代码分割
    splitting: true,
    
    // 外部依赖
    external: ['react', 'react-dom'],
    
    // 全局变量
    define: {
      'process.env.NODE_ENV': '"development"'
    }
  }
});
```

### Rspack 构建器 (@micro-core/builder-rspack)

基于 Rust 的高性能构建工具，兼容 Webpack 生态。

```typescript
import { RspackBuilder } from '@micro-core/builder-rspack';

const rspackBuilder = new RspackBuilder({
  // 入口配置
  entry: {
    main: './src/index.js'
  },
  
  // 输出配置
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].[contenthash].js'
  },
  
  // 模式
  mode: 'development',
  
  // 优化配置
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        }
      }
    }
  },
  
  // 微前端配置
  microFrontend: {
    // 模块联邦（兼容 Webpack）
    federation: {
      name: 'host',
      remotes: {
        'remote-app': 'remoteApp@http://localhost:3001/remoteEntry.js'
      }
    }
  }
});
```

### Parcel 构建器 (@micro-core/builder-parcel)

零配置的构建工具，适合快速原型和小型项目。

```typescript
import { ParcelBuilder } from '@micro-core/builder-parcel';

const parcelBuilder = new ParcelBuilder({
  // 入口文件
  entries: ['src/index.html', 'src/index.js'],
  
  // 输出目录
  distDir: 'dist',
  
  // 开发服务器
  serveOptions: {
    port: 3000,
    host: 'localhost'
  },
  
  // 构建选项
  buildOptions: {
    minify: true,
    sourceMaps: true
  },
  
  // 微前端配置
  microFrontend: {
    // 资源处理
    assets: {
      publicUrl: '/assets/'
    },
    
    // 代码分割
    splitting: true
  }
});
```

### Turbopack 构建器 (@micro-core/builder-turbopack)

基于 Rust 的下一代构建工具，专为 React 和 Next.js 优化。

```typescript
import { TurbopackBuilder } from '@micro-core/builder-turbopack';

const turbopackBuilder = new TurbopackBuilder({
  // 入口配置
  entry: {
    main: './src/index.tsx'
  },
  
  // 开发配置
  dev: {
    port: 3000,
    hostname: 'localhost'
  },
  
  // React 配置
  react: {
    refresh: true, // React Fast Refresh
    runtime: 'automatic'
  },
  
  // 微前端配置
  microFrontend: {
    // 模块系统
    moduleSystem: 'esm',
    
    // 代码分割
    codeSplitting: {
      strategy: 'smart'
    }
  }
});
```

## 构建器配置

### 通用配置选项

```typescript
interface BuilderConfig {
  // 基础配置
  name: string;
  version: string;
  root: string;
  mode: 'development' | 'production';
  
  // 入口配置
  entry: string | string[] | Record<string, string>;
  
  // 输出配置
  output: {
    dir: string;
    format: 'esm' | 'cjs' | 'umd' | 'iife';
    filename: string;
    publicPath: string;
  };
  
  // 开发服务器
  devServer: {
    port: number;
    host: string;
    open: boolean;
    proxy: Record<string, string>;
  };
  
  // 微前端特定配置
  microFrontend: {
    type: 'host' | 'remote';
    federation?: FederationConfig;
    sandbox?: SandboxConfig;
    routing?: RoutingConfig;
  };
}
```

### 环境配置

```typescript
// 开发环境配置
const devConfig = {
  mode: 'development',
  devtool: 'eval-source-map',
  devServer: {
    hot: true,
    liveReload: true,
    overlay: true
  },
  optimization: {
    minimize: false
  }
};

// 生产环境配置
const prodConfig = {
  mode: 'production',
  devtool: 'source-map',
  optimization: {
    minimize: true,
    splitChunks: {
      chunks: 'all'
    }
  },
  performance: {
    maxAssetSize: 250000,
    maxEntrypointSize: 250000
  }
};
```

## 构建流程集成

### CI/CD 集成

```typescript
// GitHub Actions 配置
const ciConfig = {
  build: {
    steps: [
      'npm ci',
      'npm run build',
      'npm run test',
      'npm run deploy'
    ]
  },
  
  deploy: {
    strategy: 'blue-green',
    environments: ['staging', 'production']
  }
};

// 构建器配置
const builder = new ViteBuilder({
  ci: {
    enabled: process.env.CI === 'true',
    
    // CI 特定优化
    optimization: {
      cache: false,
      parallel: true,
      progress: false
    }
  }
});
```

### 多环境构建

```typescript
const createBuilder = (env: string) => {
  const baseConfig = {
    root: process.cwd(),
    mode: env === 'production' ? 'production' : 'development'
  };
  
  const envConfig = {
    development: {
      devServer: { port: 3000 },
      sourcemap: true
    },
    staging: {
      devServer: { port: 4000 },
      sourcemap: true,
      minify: true
    },
    production: {
      sourcemap: false,
      minify: true,
      optimization: { splitChunks: true }
    }
  };
  
  return new ViteBuilder({
    ...baseConfig,
    ...envConfig[env]
  });
};
```

## 性能优化

### 构建缓存

```typescript
const builder = new WebpackBuilder({
  cache: {
    type: 'filesystem',
    cacheDirectory: path.resolve(__dirname, '.cache'),
    
    // 缓存策略
    buildDependencies: {
      config: [__filename]
    }
  },
  
  // 持久化缓存
  optimization: {
    moduleIds: 'deterministic',
    chunkIds: 'deterministic'
  }
});
```

### 并行构建

```typescript
const builder = new RspackBuilder({
  // 并行处理
  parallelism: 4,
  
  // 多线程优化
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        parallel: true,
        terserOptions: {
          compress: {
            drop_console: true
          }
        }
      })
    ]
  }
});
```

### 增量构建

```typescript
const builder = new ViteBuilder({
  // 增量构建
  build: {
    incremental: true,
    
    // 变更检测
    watch: {
      include: ['src/**/*'],
      exclude: ['node_modules/**/*']
    }
  }
});
```

## 调试和监控

### 构建分析

```typescript
const builder = new WebpackBuilder({
  // 构建分析
  analysis: {
    enabled: true,
    
    // Bundle 分析
    bundleAnalyzer: {
      analyzerMode: 'server',
      openAnalyzer: true
    },
    
    // 性能监控
    performance: {
      hints: 'warning',
      maxAssetSize: 250000
    }
  }
});
```

### 错误处理

```typescript
const builder = new ViteBuilder({
  // 错误处理
  errorHandling: {
    // 构建错误
    onBuildError: (error) => {
      console.error('Build error:', error);
      
      // 发送错误报告
      reportError({
        type: 'build-error',
        error: error.message,
        stack: error.stack
      });
    },
    
    // 开发服务器错误
    onDevServerError: (error) => {
      console.error('Dev server error:', error);
    }
  }
});
```

## 最佳实践

### 1. 构建器选择指南

- **现代化项目**: 推荐使用 Vite 或 Turbopack
- **大型企业项目**: 推荐使用 Webpack 或 Rspack
- **库和组件**: 推荐使用 Rollup
- **快速原型**: 推荐使用 Parcel 或 esbuild
- **性能要求极高**: 推荐使用 esbuild 或 Rspack

### 2. 配置管理

```typescript
// 配置文件分离
const baseConfig = require('./build.base.config');
const devConfig = require('./build.dev.config');
const prodConfig = require('./build.prod.config');

const config = {
  ...baseConfig,
  ...(process.env.NODE_ENV === 'production' ? prodConfig : devConfig)
};

export default config;
```

### 3. 依赖管理

```typescript
const builder = new ViteBuilder({
  // 依赖优化
  optimizeDeps: {
    include: [
      '@micro-core/core',
      'react',
      'react-dom'
    ],
    exclude: [
      '@micro-core/dev-tools'
    ]
  },
  
  // 外部依赖
  external: [
    'react',
    'react-dom'
  ]
});
```

通过 Micro-Core 的构建工具集成，开发者可以选择最适合项目需求的构建方案，实现高效的微前端应用构建和部署。
