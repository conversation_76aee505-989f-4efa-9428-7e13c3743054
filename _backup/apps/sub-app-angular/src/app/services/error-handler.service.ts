/**
 * @fileoverview Angular 错误处理服务
 * <AUTHOR> <<EMAIL>>
 */

import { ErrorHandler, Injectable } from '@angular/core'

@Injectable()
export class MicroAppErrorHandler implements ErrorHandler {

    handleError(error: any): void {
        console.error('Angular 应用错误:', error)

        // 报告错误到监控系统
        this.reportError(error)

        // 在开发模式下显示详细错误信息
        if (this.isDevelopment()) {
            this.showErrorDetails(error)
        }
    }

    /**
     * 报告错误到监控系统
     */
    private reportError(error: any): void {
        const errorReport = {
            message: error.message || 'Unknown error',
            stack: error.stack,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            framework: 'Angular'
        }

        // 发送到错误监控服务
        console.log('错误报告:', errorReport)

        // 这里可以集成 Sentry 等错误监控服务
        // Sentry.captureException(error, { extra: errorReport })
    }

    /**
     * 显示错误详情（开发模式）
     */
    private showErrorDetails(error: any): void {
        // 在开发模式下可以显示更详细的错误信息
        console.group('Angular 错误详情')
        console.error('错误对象:', error)
        console.error('错误堆栈:', error.stack)
        console.groupEnd()
    }

    /**
     * 检查是否为开发模式
     */
    private isDevelopment(): boolean {
        return !environment.production
    }
}

// 临时环境配置
const environment = {
    production: false
}