import { defineConfig } from 'vite'
import solid from 'vite-plugin-solid'

export default defineConfig({
    plugins: [solid()],

    server: {
        port: 3005,
        host: true,
        cors: true,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
    },

    build: {
        outDir: 'dist',
        sourcemap: true,
        minify: 'esbuild',
        target: 'es2020',
        lib: {
            entry: 'src/main.tsx',
            name: 'SubAppSolid',
            fileName: 'sub-app-solid',
            formats: ['umd']
        },
        rollupOptions: {
            external: ['solid-js'],
            output: {
                globals: {
                    'solid-js': 'SolidJS'
                }
            }
        }
    },

    define: {
        __MICRO_APP_NAME__: JSON.stringify('sub-app-solid'),
        __POWERED_BY_MICRO_CORE__: true
    },

    resolve: {
        alias: {
            '@': '/src'
        }
    },

    optimizeDeps: {
        include: ['solid-js']
    }
})