<template>
  <div id="app" class="main-app">
    <!-- 主应用头部 -->
    <header class="app-header">
      <div class="header-content">
        <h1 class="app-title">Micro-Core 主应用</h1>
        <div class="header-actions">
          <button @click="toggleTheme" class="theme-btn">
            {{ theme === 'light' ? '🌙' : '☀️' }} {{ theme === 'light' ? '深色' : '浅色' }}
          </button>
          <span class="user-info">欢迎，管理员</span>
        </div>
      </div>
    </header>

    <!-- 主导航 -->
    <nav class="main-nav">
      <div class="nav-content">
        <router-link to="/" class="nav-link" exact-active-class="active">
          🏠 首页
        </router-link>
        <router-link to="/react-app" class="nav-link" active-class="active">
          ⚛️ React
        </router-link>
        <router-link to="/vue3-app" class="nav-link" active-class="active">
          💚 Vue3
        </router-link>
        <router-link to="/vue2-app" class="nav-link" active-class="active">
          🟢 Vue2
        </router-link>
        <router-link to="/angular-app" class="nav-link" active-class="active">
          🅰️ Angular
        </router-link>
        <router-link to="/svelte-app" class="nav-link" active-class="active">
          🔥 Svelte
        </router-link>
        <router-link to="/solid-app" class="nav-link" active-class="active">
          💎 Solid
        </router-link>
        <router-link to="/html-app" class="nav-link" active-class="active">
          📄 HTML
        </router-link>
        <router-link to="/debug" class="nav-link" active-class="active" v-if="showDebugPanel">
          🐛 调试
        </router-link>
      </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 微应用容器 -->
      <div id="micro-app-container" class="micro-container">
        <router-view />
      </div>
    </main>

    <!-- 状态栏 -->
    <footer class="app-footer">
      <div class="footer-content">
        <span>Micro-Core v0.1.0</span>
        <span>当前主题: {{ theme }}</span>
        <span>活跃应用: {{ activeApp || '无' }}</span>
        <span>已加载: {{ loadedAppsCount }}</span>
        <span v-if="performanceInfo">加载时间: {{ performanceInfo }}</span>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { getMicroCoreManager, getLoadedApplications, getPerformanceMetrics } from './micro-config'

// 响应式数据
const theme = ref<'light' | 'dark'>('light')
const activeApp = ref<string>('')
const loadedApps = ref<string[]>([])
const performanceMetrics = ref<Map<string, number>>(new Map())
const showDebugPanel = ref(import.meta.env.DEV)

const route = useRoute()

// 计算属性
const loadedAppsCount = computed(() => loadedApps.value.length)
const performanceInfo = computed(() => {
  if (activeApp.value && performanceMetrics.value.has(`${activeApp.value}-load-time`)) {
    const loadTime = performanceMetrics.value.get(`${activeApp.value}-load-time`)
    return loadTime ? `${loadTime}ms` : ''
  }
  return ''
})

// 主题切换
const toggleTheme = () => {
  theme.value = theme.value === 'light' ? 'dark' : 'light'
  document.documentElement.setAttribute('data-theme', theme.value)
  
  // 保存主题偏好
  localStorage.setItem('micro-core-theme', theme.value)
  
  // 向子应用广播主题变更
  broadcastThemeChange(theme.value)
}

// 广播主题变更到子应用
const broadcastThemeChange = (newTheme: string) => {
  const manager = getMicroCoreManager()
  if (!manager) return

  // 通过 postMessage 向所有子应用发送主题变更消息
  const iframes = document.querySelectorAll('iframe')
  iframes.forEach(iframe => {
    try {
      iframe.contentWindow?.postMessage({
        type: 'THEME_CHANGE',
        theme: newTheme,
        timestamp: Date.now()
      }, '*')
    } catch (error) {
      console.warn('Failed to send theme change to iframe:', error)
    }
  })
  
  // 通过自定义事件向同域子应用发送消息
  window.dispatchEvent(new CustomEvent('micro-core:theme-change', {
    detail: { 
      theme: newTheme,
      timestamp: Date.now()
    }
  }))

  // 通过微前端通信插件广播
  const kernel = manager.getKernel()
  if (kernel) {
    try {
      kernel.emit('global-theme-change', {
        theme: newTheme,
        timestamp: Date.now()
      })
    } catch (error) {
      console.warn('Failed to emit theme change through kernel:', error)
    }
  }
}

// 监听子应用消息
const handleMessage = (event: MessageEvent) => {
  try {
    const { type, data } = event.data

    switch (type) {
      case 'MICRO_APP_MOUNTED':
        activeApp.value = data.appName
        updateAppStatus()
        break
      
      case 'MICRO_APP_UNMOUNTED':
        if (activeApp.value === data.appName) {
          activeApp.value = ''
        }
        updateAppStatus()
        break
      
      case 'MICRO_APP_ERROR':
        console.error(`Micro app error from ${data.appName}:`, data.error)
        break
      
      case 'MICRO_APP_PERFORMANCE':
        console.info(`Performance data from ${data.appName}:`, data.metrics)
        break
      
      default:
        // Handle other message types
        break
    }
  } catch (error) {
    console.warn('Failed to handle message:', error)
  }
}

// 更新应用状态
const updateAppStatus = () => {
  try {
    loadedApps.value = getLoadedApplications()
    performanceMetrics.value = getPerformanceMetrics()
  } catch (error) {
    console.warn('Failed to update app status:', error)
  }
}

// 监听路由变化
const handleRouteChange = () => {
  const path = route.path
  console.log('[Main App] Route changed:', path)
  
  // 根据路由确定当前活跃应用
  const appRoutes = [
    { path: '/react-app', name: 'react-app' },
    { path: '/vue3-app', name: 'vue3-app' },
    { path: '/vue2-app', name: 'vue2-app' },
    { path: '/angular-app', name: 'angular-app' },
    { path: '/svelte-app', name: 'svelte-app' },
    { path: '/solid-app', name: 'solid-app' },
    { path: '/html-app', name: 'html-app' }
  ]
  
  const currentApp = appRoutes.find(app => path.startsWith(app.path))
  if (currentApp) {
    activeApp.value = currentApp.name
  } else if (path === '/') {
    activeApp.value = ''
  }
  
  updateAppStatus()
}

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + Shift + D 打开调试面板
  if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'D') {
    showDebugPanel.value = !showDebugPanel.value
    event.preventDefault()
  }
  
  // Ctrl/Cmd + Shift + T 切换主题
  if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
    toggleTheme()
    event.preventDefault()
  }
}

// 监听路由变化
watch(() => route.path, handleRouteChange, { immediate: true })

// 生命周期
onMounted(() => {
  // 恢复保存的主题
  const savedTheme = localStorage.getItem('micro-core-theme') as 'light' | 'dark'
  if (savedTheme) {
    theme.value = savedTheme
  }
  
  // 初始化主题
  document.documentElement.setAttribute('data-theme', theme.value)
  
  // 监听子应用消息
  window.addEventListener('message', handleMessage)
  
  // 监听键盘快捷键
  window.addEventListener('keydown', handleKeydown)
  
  // 定期更新状态
  const statusInterval = setInterval(updateAppStatus, 5000)
  
  // 清理函数
  onUnmounted(() => {
    window.removeEventListener('message', handleMessage)
    window.removeEventListener('keydown', handleKeydown)
    clearInterval(statusInterval)
  })
  
  // 初始状态更新
  updateAppStatus()
  
  console.log('[Main App] App component mounted successfully')
})
</script>

<style scoped>
/* 主应用样式 */
.main-app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s, color 0.3s;
}

/* 头部样式 */
.app-header {
  background: var(--header-bg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.theme-btn {
  background: var(--button-bg);
  color: var(--button-text);
  border: 1px solid var(--border-color);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.9rem;
}

.theme-btn:hover {
  background: var(--button-hover-bg);
  transform: translateY(-1px);
}

.user-info {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* 导航样式 */
.main-nav {
  background: var(--nav-bg);
  border-bottom: 1px solid var(--border-color);
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  gap: 0;
}

.nav-link {
  display: block;
  padding: 1rem 1.5rem;
  color: var(--nav-link-color);
  text-decoration: none;
  border-bottom: 3px solid transparent;
  transition: all 0.3s;
  font-weight: 500;
}

.nav-link:hover {
  background: var(--nav-link-hover-bg);
  color: var(--nav-link-hover-color);
}

.nav-link.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background: var(--nav-link-active-bg);
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.micro-container {
  min-height: 500px;
  background: var(--container-bg);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 底部状态栏 */
.app-footer {
  background: var(--footer-bg);
  border-top: 1px solid var(--border-color);
  padding: 0.75rem 0;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-content {
    padding: 0 1rem;
    flex-wrap: wrap;
  }
  
  .nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .footer-content {
    padding: 0 1rem;
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}
</style>

<style>
/* 全局 CSS 变量 */
:root {
  --primary-color: #42b883;
  --bg-color: #ffffff;
  --text-color: #2c3e50;
  --text-secondary: #6c757d;
  --header-bg: #ffffff;
  --nav-bg: #ffffff;
  --container-bg: #ffffff;
  --footer-bg: #f8f9fa;
  --border-color: #e9ecef;
  --button-bg: #f8f9fa;
  --button-text: #495057;
  --button-hover-bg: #e9ecef;
  --nav-link-color: #6c757d;
  --nav-link-hover-color: #495057;
  --nav-link-hover-bg: #f8f9fa;
  --nav-link-active-bg: rgba(66, 184, 131, 0.1);
}

[data-theme="dark"] {
  --bg-color: #1a1a1a;
  --text-color: #e0e0e0;
  --text-secondary: #a0a0a0;
  --header-bg: #2d2d2d;
  --nav-bg: #2d2d2d;
  --container-bg: #2d2d2d;
  --footer-bg: #1a1a1a;
  --border-color: #404040;
  --button-bg: #404040;
  --button-text: #e0e0e0;
  --button-hover-bg: #505050;
  --nav-link-color: #a0a0a0;
  --nav-link-hover-color: #e0e0e0;
  --nav-link-hover-bg: #404040;
  --nav-link-active-bg: rgba(66, 184, 131, 0.2);
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

#app {
  min-height: 100vh;
}
</style>