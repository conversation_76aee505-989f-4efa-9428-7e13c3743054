/**
 * @fileoverview 核心工具集
 * @description 提供微内核工具集合
 * <AUTHOR> <<EMAIL>>
 */

import { CacheManager, createCacheManager } from './cache'
import { createEventEmitter, EventEmitter } from './event'
import { createLogger, Logger } from './logger'
import { createPerformanceMonitor, PerformanceMonitor } from './performance'

export interface MicroCoreUtils {
    logger: Logger
    eventEmitter: EventEmitter
    cache: CacheManager
    performance: PerformanceMonitor
}

/**
 * 创建微内核工具集
 */
export function createMicroCoreUtils(namespace = 'MicroCore'): MicroCoreUtils {
    return {
        logger: createLogger(namespace),
        eventEmitter: createEventEmitter(),
        cache: createCacheManager(),
        performance: createPerformanceMonitor()
    }
}