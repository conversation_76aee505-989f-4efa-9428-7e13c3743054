# @micro-core/plugin-loader-worker

Micro-Core Worker 加载器插件 - 利用 Web Worker 实现高性能后台资源加载

## 特性

- 🚀 **后台加载**: 在 Web Worker 中执行资源加载，不阻塞主线程 UI 渲染
- ⚡ **并行处理**: 支持多个 Worker 并行加载不同资源，提升加载效率
- 🧠 **智能调度**: 根据资源优先级和网络状况智能调度加载任务
- 💾 **缓存管理**: 内置智能缓存机制，避免重复加载相同资源
- 📊 **进度监控**: 提供详细的加载进度和性能监控数据

## 安装

```bash
npm install @micro-core/plugin-loader-worker
# 或
pnpm add @micro-core/plugin-loader-worker
```

## 使用方法

### 基本使用

```typescript
import { MicroCoreKernel } from '@micro-core/core';
import { WorkerLoaderPlugin } from '@micro-core/plugin-loader-worker';

const kernel = new MicroCoreKernel();

// 注册 Worker 加载器插件
const workerLoader = new WorkerLoaderPlugin({
  maxWorkers: 4,
  cacheStrategy: 'memory',
  enableProgressTracking: true
});

kernel.use(workerLoader);

// 使用 Worker 加载资源
const result = await kernel.workerLoader.loadResource('https://cdn.example.com/app.js');
console.log('资源加载完成:', result);
```

### 配置选项

```typescript
interface WorkerLoaderOptions {
  /** 最大 Worker 数量 */
  maxWorkers?: number; // 默认: 4
  /** 缓存策略 */
  cacheStrategy?: 'memory' | 'localStorage' | 'indexedDB'; // 默认: 'memory'
  /** 启用进度跟踪 */
  enableProgressTracking?: boolean; // 默认: true
  /** 超时时间 (毫秒) */
  timeout?: number; // 默认: 30000
  /** 重试次数 */
  retryCount?: number; // 默认: 3
  /** 并发限制 */
  concurrency?: number; // 默认: 6
}
```

### API 方法

#### loadResource(url, type, priority)

加载单个资源

```typescript
const result = await workerLoader.loadResource(
  'https://cdn.example.com/app.js',
  'script',
  'high'
);
```

#### loadResources(urls)

批量加载资源

```typescript
const results = await workerLoader.loadResources([
  'https://cdn.example.com/app1.js',
  'https://cdn.example.com/app2.css'
]);
```

#### preloadResources(urls)

预加载资源（低优先级，后台执行）

```typescript
workerLoader.preloadResources([
  'https://cdn.example.com/future-app.js'
]);
```

#### getStats()

获取加载统计信息

```typescript
const stats = workerLoader.getStats();
console.log('加载统计:', stats);
```

#### getCacheStats()

获取缓存统计信息

```typescript
const cacheStats = workerLoader.getCacheStats();
console.log('缓存统计:', cacheStats);
```

## 性能优势

- **非阻塞加载**: 主线程保持响应，用户体验更流畅
- **并行处理**: 多个资源同时加载，显著提升加载速度
- **智能缓存**: 避免重复加载，节省带宽和时间
- **内存优化**: 高效的内存管理，减少内存占用

## 浏览器兼容性

- Chrome 4+
- Firefox 3.5+
- Safari 4+
- Edge 12+

## 许可证

MIT License