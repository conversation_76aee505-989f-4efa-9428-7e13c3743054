import type { ParcelTransformerOptions } from './types';

/**
 * 创建 Parcel 微前端转换器
 */
export function createParcelTransformer(options: ParcelTransformerOptions) {
    return {
        name: 'micro-core-parcel-transformer',

        async transform({ asset, config }: any) {
            const { appName } = options;

            // 只处理 JavaScript 文件
            if (asset.type !== 'js') {
                return [asset];
            }

            // 获取原始代码
            const code = await asset.getCode();

            // 注入微前端生命周期代码
            const injectedCode = `
// 微前端生命周期注入
window.__MICRO_APP_NAME__ = '${appName}';
window.__POWERED_BY_MICRO_CORE__ = true;

${code}

// 导出微前端生命周期函数
if (typeof bootstrap === 'function') {
    window.__MICRO_APP_BOOTSTRAP__ = bootstrap;
}
if (typeof mount === 'function') {
    window.__MICRO_APP_MOUNT__ = mount;
}
if (typeof unmount === 'function') {
    window.__MICRO_APP_UNMOUNT__ = unmount;
}
`;

            // 更新资源内容
            asset.setCode(injectedCode);

            return [asset];
        }
    };
}