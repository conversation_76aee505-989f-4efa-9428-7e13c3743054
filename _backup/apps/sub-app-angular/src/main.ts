/**
 * @fileoverview Angular 微前端子应用入口 - 标准生命周期实现
 * <AUTHOR> <<EMAIL>>
 */

import { NgModuleRef } from '@angular/core'
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic'
import { AppModule } from './app/app.module'

// 微前端应用属性接口
interface MicroAppProps {
    container?: HTMLElement | string
    basename?: string
    theme?: 'light' | 'dark'
    user?: any
    [key: string]: any
}

// 应用状态管理
let appModuleRef: NgModuleRef<any> | null = null
let isBootstrapped = false
let isMounted = false

/**
 * 微前端应用启动函数
 * 在应用首次加载时调用，用于初始化应用资源
 */
export async function bootstrap(props: MicroAppProps = {}): Promise<void> {
    try {
        console.log('🚀 Angular 子应用开始启动', props)

        // 防止重复启动
        if (isBootstrapped) {
            console.warn('⚠️ Angular 子应用已经启动，跳过重复启动')
            return
        }

        // 初始化应用资源
        await initializeResources(props)

        // 设置全局错误处理
        setupErrorHandling()

        // 标记为已启动
        isBootstrapped = true

        console.log('✅ Angular 子应用启动完成')
    } catch (error) {
        console.error('❌ Angular 子应用启动失败:', error)
        throw error
    }
}

/**
 * 微前端应用挂载函数
 * 将应用挂载到指定容器
 */
export async function mount(props: MicroAppProps = {}): Promise<void> {
    try {
        console.log('🔄 Angular 子应用开始挂载', props)

        // 确保应用已启动
        if (!isBootstrapped) {
            await bootstrap(props)
        }

        // 防止重复挂载
        if (isMounted) {
            console.warn('⚠️ Angular 子应用已经挂载，先卸载再重新挂载')
            await unmount(props)
        }

        // 获取容器元素
        const container = getContainer(props.container)
        if (!container) {
            throw new Error('找不到挂载容器')
        }

        // 创建 Angular 应用实例
        const platform = platformBrowserDynamic([
            {
                provide: 'MICRO_APP_PROPS',
                useValue: props
            },
            {
                provide: 'MICRO_APP_INFO',
                useValue: {
                    name: 'sub-app-angular',
                    framework: 'Angular',
                    version: '16.0.0',
                    basename: props.basename || '/angular',
                    isMicroFrontend: !!(window as any).__POWERED_BY_MICRO_CORE__
                }
            }
        ])

        // 启动 Angular 模块
        appModuleRef = await platform.bootstrapModule(AppModule, {
            ngZone: 'zone.js'
        })

        // 获取应用根元素
        const appRoot = document.querySelector('app-root')
        if (appRoot && container) {
            // 将应用根元素移动到指定容器
            container.appendChild(appRoot)
        }

        // 标记为已挂载
        isMounted = true

        // 触发挂载完成事件
        dispatchLifecycleEvent('mounted', props)

        console.log('✅ Angular 子应用挂载完成')
    } catch (error) {
        console.error('❌ Angular 子应用挂载失败:', error)
        throw error
    }
}

/**
 * 微前端应用卸载函数
 * 清理应用资源和DOM
 */
export async function unmount(props: MicroAppProps = {}): Promise<void> {
    try {
        console.log('🔄 Angular 子应用开始卸载', props)

        if (!isMounted) {
            console.warn('⚠️ Angular 子应用未挂载，跳过卸载')
            return
        }

        // 销毁 Angular 应用
        if (appModuleRef) {
            appModuleRef.destroy()
            appModuleRef = null
        }

        // 清理 DOM
        const container = getContainer(props.container)
        if (container) {
            container.innerHTML = ''
        }

        // 清理事件监听器
        cleanupEventListeners()

        // 清理定时器
        cleanupTimers()

        // 标记为未挂载
        isMounted = false

        // 触发卸载完成事件
        dispatchLifecycleEvent('unmounted', props)

        console.log('✅ Angular 子应用卸载完成')
    } catch (error) {
        console.error('❌ Angular 子应用卸载失败:', error)
        throw error
    }
}

/**
 * 微前端应用更新函数
 * 更新应用属性
 */
export async function update(props: MicroAppProps = {}): Promise<void> {
    try {
        console.log('🔄 Angular 子应用开始更新', props)

        if (!isMounted) {
            console.warn('⚠️ Angular 子应用未挂载，无法更新')
            return
        }

        // 重新挂载应用以应用新属性
        await unmount(props)
        await mount(props)

        console.log('✅ Angular 子应用更新完成')
    } catch (error) {
        console.error('❌ Angular 子应用更新失败:', error)
        throw error
    }
}

/**
 * 获取挂载容器
 */
function getContainer(container?: HTMLElement | string): HTMLElement | null {
    if (!container) {
        return document.getElementById('angular-app') || document.getElementById('app')
    }

    if (typeof container === 'string') {
        return document.querySelector(container)
    }

    return container
}

/**
 * 初始化应用资源
 */
async function initializeResources(props: MicroAppProps): Promise<void> {
    // 初始化主题
    if (props.theme) {
        document.documentElement.setAttribute('data-theme', props.theme)
    }

    // 初始化用户上下文
    if (props.user) {
        // 设置用户相关的全局状态
    }
}

/**
 * 设置全局错误处理
 */
function setupErrorHandling(): void {
    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    window.addEventListener('error', handleGlobalError)
}

/**
 * 处理未捕获的 Promise 拒绝
 */
function handleUnhandledRejection(event: PromiseRejectionEvent): void {
    console.error('Angular 子应用未捕获的 Promise 拒绝:', event.reason)
}

/**
 * 处理全局错误
 */
function handleGlobalError(event: ErrorEvent): void {
    console.error('Angular 子应用全局错误:', event.error)
}

/**
 * 清理事件监听器
 */
function cleanupEventListeners(): void {
    window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    window.removeEventListener('error', handleGlobalError)
}

/**
 * 清理定时器
 */
function cleanupTimers(): void {
    // 清理可能存在的定时器
    // 这里可以维护一个定时器列表进行统一清理
}

/**
 * 触发生命周期事件
 */
function dispatchLifecycleEvent(type: string, props: MicroAppProps): void {
    const event = new CustomEvent(`angular-app:${type}`, {
        detail: { props, timestamp: Date.now() }
    })
    window.dispatchEvent(event)
}

/**
 * 获取应用状态
 */
export function getAppStatus() {
    return {
        name: 'sub-app-angular',
        isBootstrapped,
        isMounted,
        framework: 'Angular',
        version: '16.0.0'
    }
}

// 独立运行模式
if (!(window as any).__POWERED_BY_MICRO_CORE__) {
    console.log('🏃 Angular 应用以独立模式运行')

    // 等待 DOM 加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            mount({
                container: document.body,
                basename: '/',
                theme: 'light'
            })
        })
    } else {
        mount({
            container: document.body,
            basename: '/',
            theme: 'light'
        })
    }
}

// 暴露给微前端框架的接口
declare global {
    interface Window {
        __ANGULAR_MICRO_APP__?: {
            bootstrap: typeof bootstrap
            mount: typeof mount
            unmount: typeof unmount
            update: typeof update
            getAppStatus: typeof getAppStatus
        }
    }
}

if (typeof window !== 'undefined') {
    (window as any).__ANGULAR_MICRO_APP__ = {
        bootstrap,
        mount,
        unmount,
        update,
        getAppStatus
    }
}

// 导出生命周期函数供微前端框架使用
export default {
    bootstrap,
    mount,
    unmount,
    update,
    getAppStatus
}