2025-07-24T11:53:01.945178Z  WARN build:build_inner: turborepo_repository::package_graph::builder: Unable to calculate transitive closures: Workspace 'packages/adapters/adapter-vue3' not found in lockfile
2025-07-24T11:53:02.248111Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/.turbo"), AnchoredSystemPathBuf("packages/core/.turbo")}
2025-07-24T11:53:02.248132Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }, WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T11:53:03.047051Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753357983015-941f8ff095263.mjs")}
2025-07-24T11:53:03.047060Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T11:53:03.084790Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T11:56:17.245721Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/index.ts")}
2025-07-24T11:56:17.245741Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T11:56:32.245182Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/index.ts")}
2025-07-24T11:56:32.245204Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T11:56:34.245455Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753358194135-2d441ed6ae1c58.mjs")}
2025-07-24T11:56:34.245468Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T11:56:34.245496Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T11:56:54.254010Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/app-registry.ts")}
2025-07-24T11:56:54.254031Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T11:57:09.251063Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/lifecycle.ts")}
2025-07-24T11:57:09.251093Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T11:57:22.245779Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/lifecycle.ts")}
2025-07-24T11:57:22.246193Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T11:57:50.946796Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/lifecycle.ts")}
2025-07-24T11:57:50.946815Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:16:22.390762Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/kernel.ts")}
2025-07-24T12:16:22.391197Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:16:22.561399Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T12:17:27.392101Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/kernel.ts")}
2025-07-24T12:17:27.392991Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:17:30.882472Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753359450783-e271bf9972d69.mjs")}
2025-07-24T12:17:30.882488Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:17:30.882519Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T12:17:35.481676Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-wujie-compat/package.json")}
2025-07-24T12:17:35.481706Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:17:40.480873Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-wujie-compat/package.json")}
2025-07-24T12:17:40.480881Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:18:46.580246Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/utils.ts")}
2025-07-24T12:18:46.580263Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:18:48.480603Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753359528408-b9d1c898c7ac3.mjs")}
2025-07-24T12:18:48.480615Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:18:48.480640Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T12:20:02.709323Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages子包开发状态深度分析报告.md")}
2025-07-24T12:20:02.712610Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:20:23.606542Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/utils.ts")}
2025-07-24T12:20:23.606553Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:20:25.080254Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-wujie-compat/src/index.ts")}
2025-07-24T12:20:25.080266Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:20:26.179603Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753359626087-c6bee3aecb7f58.mjs")}
2025-07-24T12:20:26.179624Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:20:26.179656Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T12:20:29.179712Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-wujie-compat/src/index.ts")}
2025-07-24T12:20:29.179729Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:21:41.979186Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/utils.ts")}
2025-07-24T12:21:41.979225Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:22:16.678556Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-wujie-compat/src/types.ts")}
2025-07-24T12:22:16.678621Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:22:20.977732Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-wujie-compat/src/types.ts")}
2025-07-24T12:22:20.977752Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:22:44.178276Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-wujie-compat/src/wujie-api.ts")}
2025-07-24T12:22:44.178318Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:22:48.477452Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-wujie-compat/src/wujie-api.ts")}
2025-07-24T12:22:48.477470Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:23:29.684548Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-wujie-compat/src/wujie-api.ts")}
2025-07-24T12:23:29.684563Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:23:58.576757Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-wujie-compat/src/wujie-adapter.ts")}
2025-07-24T12:23:58.576768Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:24:03.476044Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-wujie-compat/src/wujie-adapter.ts")}
2025-07-24T12:24:03.476052Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:24:22.976371Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-wujie-compat/src/wujie-compat-plugin.ts")}
2025-07-24T12:24:22.976393Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:24:27.476141Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-wujie-compat/src/wujie-compat-plugin.ts")}
2025-07-24T12:24:27.476152Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:24:29.675704Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753359869598-aa56176b92fd68.mjs")}
2025-07-24T12:24:29.675725Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:24:29.675817Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T12:24:46.479258Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/lifecycle.ts")}
2025-07-24T12:24:46.479276Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:24:51.477986Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/utils.ts")}
2025-07-24T12:24:51.478003Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:24:53.475033Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753359893426-272f0db0be4a08.mjs")}
2025-07-24T12:24:53.475048Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:24:53.490160Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T12:26:31.731780Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/lifecycle.ts")}
2025-07-24T12:26:31.731821Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:26:36.830890Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/index.ts")}
2025-07-24T12:26:36.830904Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:26:40.531508Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753360000508-5351104d1c44c.mjs")}
2025-07-24T12:26:40.531546Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:26:40.647272Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T12:28:18.440562Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-24T12:28:18.440595Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:30:54.575294Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753360254500-9a35c9e30fef7.mjs")}
2025-07-24T12:30:54.575311Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:30:54.629721Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T12:45:16.291985Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753361116277-bdab04b4d8903.mjs")}
2025-07-24T12:45:16.292000Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:45:16.339362Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T12:46:59.497023Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages子包开发状态深度分析报告.md")}
2025-07-24T12:46:59.497104Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:47:04.422855Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/src/index.ts")}
2025-07-24T12:47:04.423233Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T12:47:36.792088Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/src/index.ts")}
2025-07-24T12:47:36.792098Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T12:47:39.390819Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753361259379-aec749e6b46ae8.mjs")}
2025-07-24T12:47:39.390861Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:47:39.431971Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T12:48:16.091541Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/src/app.ts")}
2025-07-24T12:48:16.091580Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T12:48:26.891645Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/src/router.ts")}
2025-07-24T12:48:26.891661Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T12:48:36.594574Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/src/sandbox.ts")}
2025-07-24T12:48:36.594594Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T12:49:01.314059Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/src/communication.ts")}
2025-07-24T12:49:01.314120Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T12:49:07.991351Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/src/lifecycle.ts")}
2025-07-24T12:49:07.991365Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T12:49:12.191744Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/src/event.ts")}
2025-07-24T12:49:12.191755Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T12:49:17.191341Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/src/event.ts")}
2025-07-24T12:49:17.191351Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T12:49:23.191908Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/src/utils.ts")}
2025-07-24T12:49:23.191923Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T12:49:28.191518Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/src/utils.ts")}
2025-07-24T12:49:28.191528Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T12:49:59.191238Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/src/index.ts")}
2025-07-24T12:49:59.191248Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T12:50:01.391736Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753361401342-1d51eeed9f3388.mjs")}
2025-07-24T12:50:01.391754Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:50:01.400215Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T12:50:06.591877Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/package.json")}
2025-07-24T12:50:06.591885Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:50:11.297261Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/package.json")}
2025-07-24T12:50:11.297270Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:50:11.591140Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/index.ts")}
2025-07-24T12:50:11.591157Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:50:16.192043Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/index.ts")}
2025-07-24T12:50:16.192052Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:50:48.880225Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/src/index.ts")}
2025-07-24T12:50:48.880334Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T12:50:55.981943Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/types.ts")}
2025-07-24T12:50:55.982000Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:51:02.284432Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/types.ts"), AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753361462204-caa929b0c7e558.mjs")}
2025-07-24T12:51:02.284470Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T12:51:02.486063Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T12:51:02.486271Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/types.ts")}
2025-07-24T12:51:02.486281Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T12:51:12.678587Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/tsconfig.tsbuildinfo")}
2025-07-24T12:51:12.678657Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T13:35:52.434676Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/src/index.ts")}
2025-07-24T13:35:52.435423Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T13:35:55.628065Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753364155607-5b16a2ac019c28.mjs")}
2025-07-24T13:35:55.628092Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T13:35:55.667457Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T13:36:05.727789Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/types.ts")}
2025-07-24T13:36:05.727809Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:36:52.830248Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/auth-plugin.ts")}
2025-07-24T13:36:52.830345Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:36:57.429723Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/auth-plugin.ts")}
2025-07-24T13:36:57.429740Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:37:31.229288Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/token-manager.ts")}
2025-07-24T13:37:31.229308Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:37:36.329263Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/token-manager.ts")}
2025-07-24T13:37:36.329273Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:37:50.229383Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/auth-guard.ts")}
2025-07-24T13:37:50.229405Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:37:55.437482Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/auth-guard.ts")}
2025-07-24T13:37:55.437593Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:39:37.704945Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/permission-checker.ts")}
2025-07-24T13:39:37.704972Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:39:42.302018Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/permission-checker.ts")}
2025-07-24T13:39:42.302026Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:39:57.397306Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-auth/src/index.ts")}
2025-07-24T13:39:57.397317Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:39:59.196305Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/.turbo")}
2025-07-24T13:39:59.196318Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T13:39:59.196332Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T13:39:59.598013Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753364399555-6dbc2537ac053.mjs")}
2025-07-24T13:39:59.598036Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T13:39:59.684663Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T13:40:03.997113Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-prefetch/package.json")}
2025-07-24T13:40:03.997341Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:40:08.296177Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-prefetch/package.json")}
2025-07-24T13:40:08.296188Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:40:27.608449Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-prefetch/src/types.ts")}
2025-07-24T13:40:27.608466Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:40:32.295440Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-prefetch/src/types.ts")}
2025-07-24T13:40:32.295450Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:41:48.369042Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-prefetch/src/prefetch-plugin.ts")}
2025-07-24T13:41:48.369101Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:41:53.476432Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-prefetch/src/prefetch-plugin.ts")}
2025-07-24T13:41:53.476931Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:41:56.864946Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-prefetch/src/resource-prefetcher.ts")}
2025-07-24T13:41:56.864956Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:42:01.362734Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-prefetch/src/resource-prefetcher.ts")}
2025-07-24T13:42:01.362746Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:51:51.930190Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/worker-script.ts")}
2025-07-24T13:51:51.930399Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:51:56.218820Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/worker-script.ts")}
2025-07-24T13:51:56.218834Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:52:51.723280Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/worker-manager.ts")}
2025-07-24T13:52:51.723293Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:52:56.018866Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/worker-manager.ts")}
2025-07-24T13:52:56.018875Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:53:04.117994Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/cache-manager.ts")}
2025-07-24T13:53:04.118007Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:53:08.418519Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/cache-manager.ts")}
2025-07-24T13:53:08.418528Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:54:24.518550Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/cache-manager.ts")}
2025-07-24T13:54:24.518567Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:55:09.554583Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/worker-loader-plugin.ts")}
2025-07-24T13:55:09.554593Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:55:14.018018Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/worker-loader-plugin.ts")}
2025-07-24T13:55:14.018032Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:55:15.518485Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/index.ts")}
2025-07-24T13:55:15.518497Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:55:19.818512Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/index.ts")}
2025-07-24T13:55:19.818525Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:55:21.917997Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/vite.config.ts")}
2025-07-24T13:55:21.918007Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T13:55:26.118223Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/vite.config.ts")}
2025-07-24T13:55:26.118238Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:06:03.617723Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/package.json")}
2025-07-24T14:06:03.617734Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:06:07.817983Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/package.json")}
2025-07-24T14:06:07.817992Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:06:38.930467Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/types.ts")}
2025-07-24T14:06:38.930485Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:06:43.116672Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/types.ts")}
2025-07-24T14:06:43.116682Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:07:57.918365Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/wasm-module-manager.ts")}
2025-07-24T14:07:57.918392Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:08:02.417018Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/wasm-module-manager.ts")}
2025-07-24T14:08:02.417028Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:08:30.117033Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/wasm-module-manager.ts")}
2025-07-24T14:08:30.117051Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:09:50.133962Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/wasm-instance-pool.ts")}
2025-07-24T14:09:50.133979Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:09:54.617565Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/wasm-instance-pool.ts")}
2025-07-24T14:09:54.617575Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:10:54.316753Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/wasm-loader-plugin.ts")}
2025-07-24T14:10:54.316770Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:10:59.627884Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/wasm-loader-plugin.ts")}
2025-07-24T14:10:59.627921Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:11:15.517319Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/wasm-loader-plugin.ts")}
2025-07-24T14:11:15.517329Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:11:16.716974Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/index.ts")}
2025-07-24T14:11:16.716993Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:11:21.516753Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/index.ts")}
2025-07-24T14:11:21.516764Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:11:23.716285Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/.turbo")}
2025-07-24T14:11:23.716295Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:11:28.716938Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/package.json")}
2025-07-24T14:11:28.716952Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:11:33.517302Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/package.json")}
2025-07-24T14:11:33.517312Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:11:49.416755Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/vite.config.ts")}
2025-07-24T14:11:49.416765Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:11:54.517502Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/vite.config.ts")}
2025-07-24T14:11:54.517514Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:11:54.816012Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/tsconfig.json")}
2025-07-24T14:11:54.816022Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:11:59.516840Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/tsconfig.json")}
2025-07-24T14:11:59.516851Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:12:22.916300Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/src/types.ts")}
2025-07-24T14:12:22.916335Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:12:27.518399Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/src/types.ts")}
2025-07-24T14:12:27.518408Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:16:12.289947Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/tsconfig.json")}
2025-07-24T14:16:12.289969Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T14:16:51.890620Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/src/types.ts")}
2025-07-24T14:16:51.890629Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:00:23.139668Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/.turbo")}
2025-07-24T23:00:23.139700Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:00:42.787463Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/tsconfig.json")}
2025-07-24T23:00:42.787483Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:00:43.238535Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/tsconfig.json")}
2025-07-24T23:00:43.238546Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:00:47.544880Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/tsconfig.json")}
2025-07-24T23:00:47.544890Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:00:48.238280Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/vite.config.ts")}
2025-07-24T23:00:48.238289Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:00:52.537161Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/vite.config.ts")}
2025-07-24T23:00:52.537169Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:01:02.037563Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/src/types.ts")}
2025-07-24T23:01:02.037583Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:02:33.540858Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/src/devtools-plugin.ts")}
2025-07-24T23:02:33.541004Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:02:38.232294Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/src/devtools-plugin.ts")}
2025-07-24T23:02:38.232305Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:02:40.434946Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/src/index.ts")}
2025-07-24T23:02:40.434970Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:02:44.731615Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-devtools/src/index.ts")}
2025-07-24T23:02:44.731623Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:02:58.133021Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_tmp_16422_fc90750bbc0dd8f52e26df83a84d1b9a"), AnchoredSystemPathBuf("")}
2025-07-24T23:02:58.133050Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:03:07.734373Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-24T23:03:07.734387Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:03:39.130733Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-24T23:03:39.130748Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:03:46.542712Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-24T23:03:46.542740Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:03:53.130413Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-24T23:03:53.130433Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:03:59.031689Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-24T23:03:59.031735Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:04:13.638778Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-24T23:04:13.638790Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:04:18.245242Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-24T23:04:18.245252Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:05:26.427713Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("项目完成总结.md")}
2025-07-24T23:05:26.427828Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:05:30.726181Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("项目完成总结.md")}
2025-07-24T23:05:30.726194Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:05:37.538043Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_tmp_16920_33d79fa660c3f16786a4e436f25a7d83"), AnchoredSystemPathBuf("")}
2025-07-24T23:05:37.538078Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:05:51.624480Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_tmp_17076_c023c76ba01b73f6db753ce1e821c347"), AnchoredSystemPathBuf("")}
2025-07-24T23:05:51.624508Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:06:12.035315Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/.turbo")}
2025-07-24T23:06:12.035358Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T23:06:12.243641Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T23:08:07.099008Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("最终项目完成报告.md")}
2025-07-24T23:08:07.099431Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:08:11.334693Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("最终项目完成报告.md")}
2025-07-24T23:08:11.334705Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:08:21.717347Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup")}
2025-07-24T23:08:21.717354Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:08:22.318004Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(""), AnchoredSystemPathBuf("_tmp_17656_8a274c22d377fdf7bd9330c03bf9a6c6")}
2025-07-24T23:08:22.318015Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:08:23.918931Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_tmp_17738_b20fcb5c5a82334031ee0cae783c141b"), AnchoredSystemPathBuf("")}
2025-07-24T23:08:23.918941Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:08:26.918755Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/types/.turbo")}
2025-07-24T23:08:26.918787Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/types"), path: AnchoredSystemPathBuf("packages/shared/types") }}))
2025-07-24T23:08:26.953797Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T23:08:35.217754Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup"), AnchoredSystemPathBuf(".backup")}
2025-07-24T23:08:35.217767Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:08:46.516706Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup"), AnchoredSystemPathBuf(".backup")}
2025-07-24T23:08:46.516721Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:09:01.417172Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup/micro-core-architecture"), AnchoredSystemPathBuf(".kiro/specs/micro-core-architecture")}
2025-07-24T23:09:01.417181Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:09:05.816304Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".kiro")}
2025-07-24T23:09:05.816321Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:09:25.515131Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup/操作记录.md"), AnchoredSystemPathBuf("操作记录.md")}
2025-07-24T23:09:25.515154Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:09:28.720731Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("package.json")}
2025-07-24T23:09:28.720741Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(All)
2025-07-24T23:09:28.939134Z  WARN build:build_inner: turborepo_repository::package_graph::builder: Unable to calculate transitive closures: Workspace 'packages/plugins/plugin-loader-wasm' not found in lockfile
2025-07-24T23:09:30.915374Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/package.json")}
2025-07-24T23:09:30.915389Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:09:35.114774Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/package.json")}
2025-07-24T23:09:35.114798Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:09:35.615119Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/vite.config.ts")}
2025-07-24T23:09:35.615131Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:09:39.823982Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/vite.config.ts")}
2025-07-24T23:09:39.824013Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:09:40.229275Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/main.ts")}
2025-07-24T23:09:40.229294Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:09:44.558323Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/main.ts")}
2025-07-24T23:09:44.558333Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:09:59.913819Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/App.vue")}
2025-07-24T23:09:59.913829Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:10:04.146598Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/App.vue")}
2025-07-24T23:10:04.146618Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:10:06.213299Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/micro-config.ts")}
2025-07-24T23:10:06.213591Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:10:10.513477Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/micro-config.ts")}
2025-07-24T23:10:10.513496Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:10:21.317562Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/views/Home.vue"), AnchoredSystemPathBuf("apps/main-app-vite/src/views")}
2025-07-24T23:10:21.317603Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:10:25.626016Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/views/Home.vue")}
2025-07-24T23:10:25.626047Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:10:42.000178Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/views/MicroAppContainer.vue")}
2025-07-24T23:10:42.000227Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:10:46.637996Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/views/MicroAppContainer.vue")}
2025-07-24T23:10:46.638009Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:11:00.445828Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/package.json")}
2025-07-24T23:11:00.446730Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:11:05.640323Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/package.json")}
2025-07-24T23:11:05.640331Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:11:06.148030Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/vite.config.ts")}
2025-07-24T23:11:06.148048Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:11:10.640614Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/vite.config.ts")}
2025-07-24T23:11:10.640628Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:11:11.147600Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/index.tsx")}
2025-07-24T23:11:11.147621Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:11:15.841689Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/index.tsx")}
2025-07-24T23:11:15.841732Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:11:19.843350Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/App.tsx")}
2025-07-24T23:11:19.843395Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:11:24.351796Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/App.tsx")}
2025-07-24T23:11:24.351806Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:11:45.945480Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/components/Dashboard.tsx"), AnchoredSystemPathBuf("apps/sub-app-react/src/components")}
2025-07-24T23:11:45.945498Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:11:50.640366Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/components/Dashboard.tsx")}
2025-07-24T23:11:50.640374Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:12:09.752410Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/components/UserManagement.tsx")}
2025-07-24T23:12:09.753554Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:12:14.776011Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/components/UserManagement.tsx")}
2025-07-24T23:12:14.776038Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:13:04.638747Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/App.css")}
2025-07-24T23:13:04.638939Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:13:09.938236Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/App.css")}
2025-07-24T23:13:09.938252Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:13:12.440563Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/package.json")}
2025-07-24T23:13:12.440573Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:13:17.646040Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/package.json")}
2025-07-24T23:13:17.646083Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:14:09.935773Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/components/Dashboard.tsx")}
2025-07-24T23:14:09.935788Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:17:15.935396Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/components/Dashboard.tsx")}
2025-07-24T23:17:15.935740Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:17:16.478095Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/vite.config.ts")}
2025-07-24T23:17:16.478112Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:17:20.729978Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/vite.config.ts")}
2025-07-24T23:17:20.730022Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:17:53.027804Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/src/main.ts")}
2025-07-24T23:17:53.027825Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:17:57.227490Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/src/main.ts")}
2025-07-24T23:17:57.227499Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:18:07.746042Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/src/App.vue")}
2025-07-24T23:18:07.764662Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:18:12.027377Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/src/App.vue")}
2025-07-24T23:18:12.027398Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:18:12.734064Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/src/router/index.ts"), AnchoredSystemPathBuf("apps/sub-app-vue3/src/router")}
2025-07-24T23:18:12.734082Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:18:17.126338Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/src/router/index.ts")}
2025-07-24T23:18:17.126354Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:19:14.525405Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/src/views"), AnchoredSystemPathBuf("apps/sub-app-vue3/src/views/ProductManagement.vue")}
2025-07-24T23:19:14.525653Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:19:18.825285Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/src/views/ProductManagement.vue")}
2025-07-24T23:19:18.825295Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:20:01.296171Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/src/views/OrderManagement.vue")}
2025-07-24T23:20:01.296253Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:20:05.523030Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/src/views/OrderManagement.vue")}
2025-07-24T23:20:05.523049Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:20:11.525368Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/package.json")}
2025-07-24T23:20:11.525389Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:20:11.687153Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/package.json")}
2025-07-24T23:20:11.687174Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:20:43.475506Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/tsconfig.json")}
2025-07-24T23:20:43.475544Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:20:47.722084Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/tsconfig.json")}
2025-07-24T23:20:47.722100Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:20:48.221526Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/env.d.ts")}
2025-07-24T23:20:48.221544Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:20:52.607048Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/env.d.ts")}
2025-07-24T23:20:52.607072Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:20:53.024994Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/index.html")}
2025-07-24T23:20:53.025016Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:20:57.321637Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-vue3/index.html")}
2025-07-24T23:20:57.321665Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:22:13.819907Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/components/Dashboard.tsx")}
2025-07-24T23:22:13.819932Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:22:22.919491Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/package.json")}
2025-07-24T23:22:22.919528Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:22:28.084959Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/vite.config.ts")}
2025-07-24T23:22:28.084991Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:22:44.419290Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/main.ts")}
2025-07-24T23:22:44.422796Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:23:29.921069Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/App.vue")}
2025-07-24T23:23:29.921104Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:23:31.516610Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/router"), AnchoredSystemPathBuf("apps/main-app-vite/src/router/index.ts")}
2025-07-24T23:23:31.516626Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:23:35.716502Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/router/index.ts")}
2025-07-24T23:23:35.716602Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:26:24.374171Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/vite.config.ts")}
2025-07-24T23:26:24.374196Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:26:24.822183Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/tsconfig.json")}
2025-07-24T23:26:24.822198Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:26:29.008042Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/tsconfig.json")}
2025-07-24T23:26:29.008054Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:26:33.910944Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/package.json")}
2025-07-24T23:26:33.910987Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:26:38.609541Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts")}
2025-07-24T23:26:38.609562Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:26:53.409635Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/index.ts")}
2025-07-24T23:26:53.409655Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:27:04.810254Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types.ts")}
2025-07-24T23:27:04.810317Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:27:31.911274Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/kernel.ts"), AnchoredSystemPathBuf("packages/core/src/runtime")}
2025-07-24T23:27:31.911299Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:27:36.206380Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/kernel.ts")}
2025-07-24T23:27:36.206396Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:28:08.435392Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/lifecycle.ts")}
2025-07-24T23:28:08.435437Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:28:12.805816Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/lifecycle.ts")}
2025-07-24T23:28:12.805831Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:28:40.702971Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/app-registry.ts")}
2025-07-24T23:28:40.702989Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:28:52.129137Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/plugin-system.ts")}
2025-07-24T23:28:52.130478Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:28:56.450508Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/plugin-system.ts")}
2025-07-24T23:28:56.450520Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:29:08.108400Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/resource-manager.ts")}
2025-07-24T23:29:08.108462Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:29:12.403962Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/resource-manager.ts")}
2025-07-24T23:29:12.403973Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:29:15.333046Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/error-handler.ts")}
2025-07-24T23:29:15.333131Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:29:19.604351Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/error-handler.ts")}
2025-07-24T23:29:19.604362Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:29:42.909688Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/global-state.ts")}
2025-07-24T23:29:42.909982Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:29:47.100738Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/global-state.ts")}
2025-07-24T23:29:47.100748Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:30:09.305081Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/sandbox/sandbox-manager.ts"), AnchoredSystemPathBuf("packages/core/src/sandbox")}
2025-07-24T23:30:09.305739Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:30:13.602296Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/sandbox/sandbox-manager.ts")}
2025-07-24T23:30:13.602315Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:30:31.331807Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/router"), AnchoredSystemPathBuf("packages/core/src/router/router-manager.ts")}
2025-07-24T23:30:31.332020Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:30:35.501750Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/router/router-manager.ts")}
2025-07-24T23:30:35.501784Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:31:02.698856Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/communication/communication-manager.ts")}
2025-07-24T23:31:02.698880Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:31:31.998810Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/utils.ts")}
2025-07-24T23:31:31.998830Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:31:36.601230Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/tsconfig.json")}
2025-07-24T23:31:36.601252Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:31:41.497337Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/package.json")}
2025-07-24T23:31:41.497354Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-24T23:31:53.599157Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/src/index.ts")}
2025-07-24T23:31:53.599180Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-24T23:32:09.328385Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/vite.config.ts")}
2025-07-24T23:32:09.328407Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-24T23:32:13.697773Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/tsconfig.json")}
2025-07-24T23:32:13.697805Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-24T23:32:15.198418Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vite.config.ts.timestamp-1753399935040-12b4c1f7e907.mjs")}
2025-07-24T23:32:15.198442Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:32:15.276446Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-24T23:42:03.563596Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/app-loader.ts")}
2025-07-24T23:42:03.564984Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:42:07.998989Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/app-loader.ts")}
2025-07-24T23:42:07.999033Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:43:10.096992Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/sandbox/sandbox-manager.ts")}
2025-07-24T23:43:10.097029Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:43:44.797166Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/router/router-manager.ts")}
2025-07-24T23:43:44.797196Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:44:21.803489Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/communication/communication-manager.ts")}
2025-07-24T23:44:21.803508Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:44:58.193481Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/communication/event-bus.ts")}
2025-07-24T23:44:58.193495Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:45:05.996445Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-router/package.json")}
2025-07-24T23:45:05.996465Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-router"), path: AnchoredSystemPathBuf("packages/plugins/plugin-router") }}))
2025-07-24T23:45:06.693683Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-router/vite.config.ts")}
2025-07-24T23:45:06.693708Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-router"), path: AnchoredSystemPathBuf("packages/plugins/plugin-router") }}))
2025-07-24T23:45:11.036173Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-router/vite.config.ts")}
2025-07-24T23:45:11.036192Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-router"), path: AnchoredSystemPathBuf("packages/plugins/plugin-router") }}))
2025-07-24T23:45:11.592688Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-router/tsconfig.json")}
2025-07-24T23:45:11.592708Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-router"), path: AnchoredSystemPathBuf("packages/plugins/plugin-router") }}))
2025-07-24T23:45:15.792438Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-router/tsconfig.json")}
2025-07-24T23:45:15.792456Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-router"), path: AnchoredSystemPathBuf("packages/plugins/plugin-router") }}))
2025-07-24T23:45:54.492679Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-router/src/index.ts")}
2025-07-24T23:45:54.492957Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-router"), path: AnchoredSystemPathBuf("packages/plugins/plugin-router") }}))
2025-07-24T23:46:18.500874Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-router/src/router-sync.ts")}
2025-07-24T23:46:18.500893Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-router"), path: AnchoredSystemPathBuf("packages/plugins/plugin-router") }}))
2025-07-24T23:46:22.791766Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-router/src/router-sync.ts")}
2025-07-24T23:46:22.791805Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-router"), path: AnchoredSystemPathBuf("packages/plugins/plugin-router") }}))
2025-07-24T23:46:57.096081Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-router/src/history-adapter.ts")}
2025-07-24T23:46:57.098502Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-router"), path: AnchoredSystemPathBuf("packages/plugins/plugin-router") }}))
2025-07-24T23:47:01.889719Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-router/src/history-adapter.ts")}
2025-07-24T23:47:01.889743Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-router"), path: AnchoredSystemPathBuf("packages/plugins/plugin-router") }}))
2025-07-24T23:47:10.187870Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-sandbox-proxy/package.json")}
2025-07-24T23:47:10.187895Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-sandbox-proxy"), path: AnchoredSystemPathBuf("packages/plugins/plugin-sandbox-proxy") }}))
2025-07-24T23:47:27.298926Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-sandbox-proxy/vite.config.ts")}
2025-07-24T23:47:27.298952Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-sandbox-proxy"), path: AnchoredSystemPathBuf("packages/plugins/plugin-sandbox-proxy") }}))
2025-07-24T23:47:31.588351Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-sandbox-proxy/vite.config.ts")}
2025-07-24T23:47:31.588368Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-sandbox-proxy"), path: AnchoredSystemPathBuf("packages/plugins/plugin-sandbox-proxy") }}))
2025-07-24T23:47:49.590785Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-sandbox-proxy/src/index.ts")}
2025-07-24T23:47:49.590816Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-sandbox-proxy"), path: AnchoredSystemPathBuf("packages/plugins/plugin-sandbox-proxy") }}))
2025-07-24T23:48:24.886600Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-sandbox-proxy/src/proxy-sandbox.ts")}
2025-07-24T23:48:24.886622Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-sandbox-proxy"), path: AnchoredSystemPathBuf("packages/plugins/plugin-sandbox-proxy") }}))
2025-07-24T23:49:07.626424Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-sandbox-proxy/src/proxy-handler.ts")}
2025-07-24T23:49:07.626464Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-sandbox-proxy"), path: AnchoredSystemPathBuf("packages/plugins/plugin-sandbox-proxy") }}))
2025-07-24T23:49:11.985626Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-sandbox-proxy/src/proxy-handler.ts")}
2025-07-24T23:49:11.985637Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-sandbox-proxy"), path: AnchoredSystemPathBuf("packages/plugins/plugin-sandbox-proxy") }}))
2025-07-24T23:50:34.483705Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types.ts")}
2025-07-24T23:50:34.483761Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:50:59.486934Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types.ts")}
2025-07-24T23:50:59.486975Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:51:04.181981Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types.ts")}
2025-07-24T23:51:04.181995Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:52:03.679719Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/runtime/lifecycle.ts")}
2025-07-24T23:52:03.679744Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-24T23:52:11.978483Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-communication/package.json")}
2025-07-24T23:52:11.978497Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-communication"), path: AnchoredSystemPathBuf("packages/plugins/plugin-communication") }}))
2025-07-24T23:52:43.677813Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-communication/src/index.ts")}
2025-07-24T23:52:43.677838Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-communication"), path: AnchoredSystemPathBuf("packages/plugins/plugin-communication") }}))
2025-07-24T23:53:10.285412Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-react/package.json")}
2025-07-24T23:53:10.291441Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-react"), path: AnchoredSystemPathBuf("packages/adapters/adapter-react") }}))
2025-07-24T23:53:21.878466Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-react/src/index.ts")}
2025-07-24T23:53:21.878500Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-react"), path: AnchoredSystemPathBuf("packages/adapters/adapter-react") }}))
2025-07-24T23:53:50.779192Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-react/src/lifecycles.ts")}
2025-07-24T23:53:50.779603Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-react"), path: AnchoredSystemPathBuf("packages/adapters/adapter-react") }}))
2025-07-24T23:53:55.276044Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-react/src/lifecycles.ts")}
2025-07-24T23:53:55.276062Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-react"), path: AnchoredSystemPathBuf("packages/adapters/adapter-react") }}))
2025-07-24T23:54:02.288945Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-vue3/package.json")}
2025-07-24T23:54:02.288974Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-vue3"), path: AnchoredSystemPathBuf("packages/adapters/adapter-vue3") }}))
2025-07-24T23:54:30.637716Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-vue3/src/index.ts")}
2025-07-24T23:54:30.638740Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-vue3"), path: AnchoredSystemPathBuf("packages/adapters/adapter-vue3") }}))
2025-07-24T23:54:55.952485Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-vue3/src/lifecycles.ts")}
2025-07-24T23:54:55.964514Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-vue3"), path: AnchoredSystemPathBuf("packages/adapters/adapter-vue3") }}))
2025-07-24T23:55:00.475486Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-vue3/src/lifecycles.ts")}
2025-07-24T23:55:00.475504Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-vue3"), path: AnchoredSystemPathBuf("packages/adapters/adapter-vue3") }}))
2025-07-24T23:55:05.674967Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-react/tsconfig.json")}
2025-07-24T23:55:05.674983Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-react"), path: AnchoredSystemPathBuf("packages/adapters/adapter-react") }}))
2025-07-24T23:55:06.380987Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-vue3/tsconfig.json")}
2025-07-24T23:55:06.381002Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-vue3"), path: AnchoredSystemPathBuf("packages/adapters/adapter-vue3") }}))
2025-07-24T23:55:10.674849Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-vue3/tsconfig.json")}
2025-07-24T23:55:10.674934Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-vue3"), path: AnchoredSystemPathBuf("packages/adapters/adapter-vue3") }}))
2025-07-24T23:55:36.007295Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/example-main-app"), AnchoredSystemPathBuf("apps/example-main-app/package.json")}
2025-07-24T23:55:36.007387Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:55:40.327651Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/example-main-app/package.json")}
2025-07-24T23:55:40.349960Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:55:42.480188Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/example-main-app/index.html")}
2025-07-24T23:55:42.480207Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:55:47.078555Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/example-main-app/index.html")}
2025-07-24T23:55:47.078584Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:55:57.276134Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/example-main-app/src"), AnchoredSystemPathBuf("apps/example-main-app/src/main.ts")}
2025-07-24T23:55:57.276266Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:56:01.673728Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/example-main-app/src/main.ts")}
2025-07-24T23:56:01.673751Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-24T23:57:23.771446Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-24T23:57:23.771478Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
