# @micro-core/sidecar

微前端边车模式支持包 - 提供零配置的微前端接入方案

## 特性

- 🚀 **一行代码接入** - 零配置快速接入微前端
- 🔧 **自动发现** - 自动发现和注册微应用
- 🎯 **框架检测** - 自动检测当前框架环境
- ⚙️ **配置管理** - 多源配置加载和管理
- 🛡️ **错误处理** - 完善的错误处理和恢复机制
- 📊 **状态监控** - 实时状态监控和统计

## 安装

```bash
npm install @micro-core/sidecar
# 或
yarn add @micro-core/sidecar
# 或
pnpm add @micro-core/sidecar
```

## 快速开始

### 基础用法

```typescript
import { init } from '@micro-core/sidecar';

// 一行代码初始化
await init();
```

### 完整配置

```typescript
import { init, registerApp } from '@micro-core/sidecar';

// 初始化配置
await init({
  name: 'my-app',
  debug: true,
  autoStart: true,
  autoDiscovery: true,
  container: '#micro-app-container',
  apps: [
    {
      name: 'sub-app-1',
      entry: 'http://localhost:3001',
      container: '#sub-app-1',
      activeWhen: '/sub-app-1'
    }
  ]
});

// 动态注册应用
registerApp({
  name: 'sub-app-2',
  entry: 'http://localhost:3002',
  activeWhen: '/sub-app-2'
});
```

### CDN 使用

```html
<script src="https://unpkg.com/@micro-core/sidecar/dist/index.js"></script>
<script>
  // 全局变量 MicroCoreSidecar
  MicroCoreSidecar.init({
    autoStart: true
  });
</script>
```

## API 文档

### init(options?)

初始化 Sidecar 实例

**参数:**
- `options` - 初始化选项

**选项:**
- `name?: string` - 应用名称
- `debug?: boolean` - 调试模式
- `autoStart?: boolean` - 是否自动启动（默认: true）
- `autoDiscovery?: boolean` - 是否自动发现应用（默认: true）
- `container?: string | HTMLElement` - 容器选择器或元素
- `apps?: MicroAppConfig[]` - 应用配置列表
- `errorHandler?: (error: Error) => void` - 错误处理器

### start()

启动 Sidecar

```typescript
import { start } from '@micro-core/sidecar';

await start();
```

### stop()

停止 Sidecar

```typescript
import { stop } from '@micro-core/sidecar';

await stop();
```

### registerApp(config)

注册微应用

```typescript
import { registerApp } from '@micro-core/sidecar';

registerApp({
  name: 'my-app',
  entry: 'http://localhost:3000',
  container: '#app-container',
  activeWhen: '/my-app'
});
```

### getStatus()

获取 Sidecar 状态

```typescript
import { getStatus } from '@micro-core/sidecar';

const status = getStatus();
console.log(status);
```

## 配置加载

Sidecar 支持多种配置来源，按优先级排序：

1. **URL 参数** - 最高优先级
2. **Script 标签** - `<script data-micro-core-config="{}"></script>`
3. **全局变量** - `window.__MICRO_CORE_CONFIG__`
4. **localStorage** - `micro-core-config`
5. **远程配置** - 通过 URL 加载
6. **默认配置** - 最低优先级

### 配置示例

```html
<!-- Script 标签配置 -->
<script data-micro-core-config='{"debug": true, "autoStart": false}'></script>

<!-- 全局变量配置 -->
<script>
  window.__MICRO_CORE_CONFIG__ = {
    debug: true,
    apps: [
      {
        name: 'app1',
        entry: 'http://localhost:3001'
      }
    ]
  };
</script>

<!-- URL 参数配置 -->
<!-- ?debug=true&development=true -->
```

## 自动发现

Sidecar 支持自动发现微应用：

### 应用清单文件

在以下位置放置 `micro-apps.json` 文件：

```json
{
  "apps": [
    {
      "name": "app1",
      "version": "1.0.0",
      "entry": "http://localhost:3001",
      "activeWhen": "/app1",
      "framework": "react"
    }
  ]
}
```

### DOM 标记

```html
<div data-micro-app='{"name": "app1", "entry": "http://localhost:3001", "activeWhen": "/app1"}'></div>
```

## 错误处理

```typescript
import { init } from '@micro-core/sidecar';

await init({
  errorHandler: (error) => {
    console.error('Sidecar 错误:', error);
    // 自定义错误处理逻辑
  }
});
```

## TypeScript 支持

包含完整的 TypeScript 类型定义：

```typescript
import type { 
  SidecarInitOptions, 
  MicroAppConfig, 
  SidecarStatus 
} from '@micro-core/sidecar';
```

## 许可证

MIT License