/**
 * @fileoverview Playground 主应用入口
 * <AUTHOR> <<EMAIL>>
 */

import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import React from 'react';
import ReactDOM from 'react-dom/client';

import App from './App';
import { ErrorBoundary } from './components/ErrorBoundary';
import { initializeMicroCore } from './core/micro-core-setup';

import './styles/global.css';

// 设置 dayjs 中文语言
dayjs.locale('zh-cn');

// 全局错误处理
const handleError = (error: Error, errorInfo?: any) => {
    console.error('应用错误:', error, errorInfo);

    // 发送错误到监控系统（如果有的话）
    if (window.__MICRO_CORE_PLAYGROUND__?.showError) {
        window.__MICRO_CORE_PLAYGROUND__.showError(
            `应用运行时错误: ${error.message}`
        );
    }
};

// 初始化应用
async function initializeApp() {
    try {
        console.log('🚀 开始初始化 Micro-Core Playground...');

        // 1. 初始化微前端核心
        await initializeMicroCore();
        console.log('✅ 微前端核心初始化完成');

        // 2. 创建 React 根节点
        const container = document.getElementById('root');
        if (!container) {
            throw new Error('找不到根节点 #root');
        }

        const root = ReactDOM.createRoot(container);

        // 3. 渲染应用
        root.render(
            <React.StrictMode>
                <ErrorBoundary onError={handleError}>
                    <ConfigProvider
                        locale={zhCN}
                        theme={{
                            token: {
                                colorPrimary: '#1890ff',
                                borderRadius: 6,
                                fontSize: 14
                            }
                        }}
                    >
                        <App />
                    </ConfigProvider>
                </ErrorBoundary>
            </React.StrictMode>
        );

        console.log('✅ 应用渲染完成');

        // 4. 隐藏加载页面
        if (window.__MICRO_CORE_PLAYGROUND__?.hideLoading) {
            window.__MICRO_CORE_PLAYGROUND__.hideLoading();
        }

        console.log('🎉 Micro-Core Playground 初始化完成！');

    } catch (error) {
        console.error('❌ 应用初始化失败:', error);
        handleError(error as Error);
    }
}

// 启动应用
initializeApp();

// 开发模式下的热重载支持
if (import.meta.hot) {
    import.meta.hot.accept('./App', () => {
        console.log('🔄 热重载: App 组件已更新');
    });

    import.meta.hot.accept('./core/micro-core-setup', () => {
        console.log('🔄 热重载: 微前端核心配置已更新');
    });
}

// 导出类型供其他模块使用
export type { };
