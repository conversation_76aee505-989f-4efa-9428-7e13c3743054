import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
    {
        path: '/',
        name: 'Home',
        component: () => import('../views/Home.vue'),
        meta: {
            title: '首页',
            description: 'Micro-Core 微前端架构主页'
        }
    },
    {
        path: '/react-app/:pathMatch(.*)*',
        name: 'ReactApp',
        component: () => import('../views/MicroAppContainer.vue'),
        props: { appName: 'react-app' },
        meta: {
            title: 'React 应用',
            description: 'React 18 微前端子应用'
        }
    },
    {
        path: '/vue3-app/:pathMatch(.*)*',
        name: 'Vue3App',
        component: () => import('../views/MicroAppContainer.vue'),
        props: { appName: 'vue3-app' },
        meta: {
            title: 'Vue3 应用',
            description: 'Vue 3 微前端子应用'
        }
    },
    {
        path: '/vue2-app/:pathMatch(.*)*',
        name: 'Vue2App',
        component: () => import('../views/MicroAppContainer.vue'),
        props: { appName: 'vue2-app' },
        meta: {
            title: 'Vue2 应用',
            description: 'Vue 2 微前端子应用'
        }
    },
    {
        path: '/angular-app/:pathMatch(.*)*',
        name: 'AngularApp',
        component: () => import('../views/MicroAppContainer.vue'),
        props: { appName: 'angular-app' },
        meta: {
            title: 'Angular 应用',
            description: 'Angular 16+ 微前端子应用'
        }
    },
    {
        path: '/svelte-app/:pathMatch(.*)*',
        name: 'SvelteApp',
        component: () => import('../views/MicroAppContainer.vue'),
        props: { appName: 'svelte-app' },
        meta: {
            title: 'Svelte 应用',
            description: 'Svelte 微前端子应用'
        }
    },
    {
        path: '/solid-app/:pathMatch(.*)*',
        name: 'SolidApp',
        component: () => import('../views/MicroAppContainer.vue'),
        props: { appName: 'solid-app' },
        meta: {
            title: 'Solid 应用',
            description: 'SolidJS 微前端子应用'
        }
    },
    {
        path: '/html-app/:pathMatch(.*)*',
        name: 'HtmlApp',
        component: () => import('../views/MicroAppContainer.vue'),
        props: { appName: 'html-app' },
        meta: {
            title: 'HTML 应用',
            description: '原生 HTML/JS 微前端子应用'
        }
    },
    {
        path: '/debug',
        name: 'Debug',
        component: () => import('../views/DebugPanel.vue'),
        meta: {
            title: '调试面板',
            description: '微前端系统调试和监控面板'
        }
    },
    {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        component: () => import('../views/NotFound.vue'),
        meta: {
            title: '页面未找到',
            description: '请求的页面不存在'
        }
    }
]

export default routes