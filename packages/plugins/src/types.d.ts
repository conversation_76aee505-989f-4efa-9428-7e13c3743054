/**
 * 插件系统类型定义
 *
 * @description 插件系统相关的类型定义
 * <AUTHOR> <<EMAIL>>
 */
import { BasePlugin } from './base-plugin';
/**
 * 插件状态
 */
export type PluginState = 'registered' | 'loaded' | 'active' | 'inactive' | 'unloaded' | 'error';
/**
 * 插件生命周期钩子
 */
export type PluginLifecycleHook = 'beforeInit' | 'afterInit' | 'beforeRegister' | 'afterRegister' | 'beforeLoad' | 'afterLoad' | 'beforeActivate' | 'afterActivate' | 'beforeDeactivate' | 'afterDeactivate' | 'beforeUnload' | 'afterUnload' | 'beforeUnregister' | 'afterUnregister' | 'beforeDestroy' | 'afterDestroy';
/**
 * 插件配置
 */
export interface PluginConfig {
    /** 插件名称 */
    name?: string;
    /** 插件版本 */
    version?: string;
    /** 插件描述 */
    description?: string;
    /** 插件作者 */
    author?: string;
    /** 是否启用 */
    enabled?: boolean;
    /** 插件依赖 */
    dependencies?: PluginDependency[];
    /** 自定义配置 */
    [key: string]: any;
}
/**
 * 插件元数据
 */
export interface PluginMetadata {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 插件描述 */
    description?: string;
    /** 插件作者 */
    author?: string;
    /** 插件主页 */
    homepage?: string;
    /** 插件仓库 */
    repository?: string;
    /** 插件许可证 */
    license?: string;
    /** 插件关键词 */
    keywords?: string[];
    /** 插件依赖 */
    dependencies?: PluginDependency[];
    /** 插件提供的扩展点 */
    extensionPoints?: string[];
    /** 插件消费的扩展点 */
    consumedExtensions?: string[];
    /** 插件创建时间 */
    createdAt?: number;
    /** 插件更新时间 */
    updatedAt?: number;
}
/**
 * 插件依赖
 */
export interface PluginDependency {
    /** 依赖插件ID */
    id: string;
    /** 依赖版本 */
    version?: string;
    /** 是否可选依赖 */
    optional?: boolean;
    /** 依赖描述 */
    description?: string;
}
/**
 * 插件管理器选项
 */
export interface PluginManagerOptions {
    /** 是否启用懒加载 */
    enableLazyLoading?: boolean;
    /** 是否启用依赖解析 */
    enableDependencyResolution?: boolean;
    /** 是否启用热重载 */
    enableHotReload?: boolean;
    /** 最大并发加载数 */
    maxConcurrentLoads?: number;
    /** 加载超时时间（毫秒） */
    loadTimeout?: number;
    /** 插件搜索路径 */
    searchPaths?: string[];
    /** 插件白名单 */
    whitelist?: string[];
    /** 插件黑名单 */
    blacklist?: string[];
}
/**
 * 插件注册表项
 */
export interface PluginRegistryEntry {
    /** 插件ID */
    id: string;
    /** 插件类 */
    pluginClass: typeof BasePlugin;
    /** 插件配置 */
    config: PluginConfig;
    /** 注册时间 */
    registeredAt: number;
    /** 注册来源 */
    source?: string;
}
/**
 * 插件加载器接口
 */
export interface PluginLoader {
    /** 加载插件 */
    load(pluginPath: string): Promise<typeof BasePlugin>;
    /** 卸载插件 */
    unload(pluginPath: string): Promise<void>;
    /** 检查插件是否存在 */
    exists(pluginPath: string): boolean;
    /** 获取插件信息 */
    getInfo(pluginPath: string): Promise<PluginMetadata>;
}
/**
 * 插件市场接口
 */
export interface PluginMarketplace {
    /** 搜索插件 */
    search(query: string, options?: SearchOptions): Promise<PluginSearchResult[]>;
    /** 获取插件详情 */
    getPlugin(pluginId: string): Promise<PluginMarketplaceEntry>;
    /** 下载插件 */
    download(pluginId: string, version?: string): Promise<string>;
    /** 安装插件 */
    install(pluginId: string, version?: string): Promise<void>;
    /** 卸载插件 */
    uninstall(pluginId: string): Promise<void>;
    /** 更新插件 */
    update(pluginId: string, version?: string): Promise<void>;
    /** 获取已安装插件列表 */
    getInstalled(): Promise<PluginMarketplaceEntry[]>;
    /** 获取可更新插件列表 */
    getUpdatable(): Promise<PluginMarketplaceEntry[]>;
}
/**
 * 插件搜索选项
 */
export interface SearchOptions {
    /** 分类 */
    category?: string;
    /** 标签 */
    tags?: string[];
    /** 作者 */
    author?: string;
    /** 排序方式 */
    sortBy?: 'name' | 'downloads' | 'rating' | 'updated';
    /** 排序顺序 */
    sortOrder?: 'asc' | 'desc';
    /** 页码 */
    page?: number;
    /** 每页数量 */
    pageSize?: number;
}
/**
 * 插件搜索结果
 */
export interface PluginSearchResult {
    /** 插件ID */
    id: string;
    /** 插件名称 */
    name: string;
    /** 插件描述 */
    description: string;
    /** 插件版本 */
    version: string;
    /** 插件作者 */
    author: string;
    /** 下载次数 */
    downloads: number;
    /** 评分 */
    rating: number;
    /** 更新时间 */
    updatedAt: number;
    /** 插件图标 */
    icon?: string;
    /** 插件截图 */
    screenshots?: string[];
}
/**
 * 插件市场条目
 */
export interface PluginMarketplaceEntry extends PluginSearchResult {
    /** 插件详细描述 */
    readme: string;
    /** 插件变更日志 */
    changelog: string;
    /** 插件许可证 */
    license: string;
    /** 插件主页 */
    homepage: string;
    /** 插件仓库 */
    repository: string;
    /** 插件依赖 */
    dependencies: PluginDependency[];
    /** 插件版本历史 */
    versions: PluginVersion[];
    /** 是否已安装 */
    installed: boolean;
    /** 已安装版本 */
    installedVersion?: string;
}
/**
 * 插件版本信息
 */
export interface PluginVersion {
    /** 版本号 */
    version: string;
    /** 发布时间 */
    publishedAt: number;
    /** 版本描述 */
    description: string;
    /** 下载地址 */
    downloadUrl: string;
    /** 文件大小 */
    size: number;
    /** 文件哈希 */
    hash: string;
}
/**
 * 插件扩展点
 */
export interface PluginExtensionPoint {
    /** 扩展点ID */
    id: string;
    /** 扩展点名称 */
    name: string;
    /** 扩展点描述 */
    description?: string;
    /** 扩展点类型 */
    type: 'hook' | 'provider' | 'consumer';
    /** 扩展点接口 */
    interface?: any;
    /** 是否必需 */
    required?: boolean;
    /** 默认实现 */
    defaultImplementation?: any;
}
/**
 * 插件扩展
 */
export interface PluginExtension {
    /** 扩展ID */
    id: string;
    /** 扩展点ID */
    extensionPointId: string;
    /** 扩展实现 */
    implementation: any;
    /** 扩展优先级 */
    priority?: number;
    /** 扩展配置 */
    config?: any;
}
/**
 * 插件事件
 */
export interface PluginEvent {
    /** 事件类型 */
    type: string;
    /** 插件ID */
    pluginId: string;
    /** 事件数据 */
    data?: any;
    /** 事件时间戳 */
    timestamp: number;
}
/**
 * 插件性能指标
 */
export interface PluginMetrics {
    /** 插件ID */
    pluginId: string;
    /** 加载时间 */
    loadTime: number;
    /** 激活时间 */
    activationTime: number;
    /** 内存使用量 */
    memoryUsage: number;
    /** CPU使用率 */
    cpuUsage: number;
    /** 错误次数 */
    errorCount: number;
    /** 最后活动时间 */
    lastActivity: number;
}
/**
 * 插件沙箱配置
 */
export interface PluginSandboxConfig {
    /** 是否启用沙箱 */
    enabled: boolean;
    /** 沙箱类型 */
    type: 'iframe' | 'worker' | 'vm' | 'proxy';
    /** 允许的API */
    allowedAPIs?: string[];
    /** 禁止的API */
    blockedAPIs?: string[];
    /** 资源限制 */
    resourceLimits?: {
        memory?: number;
        cpu?: number;
        network?: boolean;
        filesystem?: boolean;
    };
    /** 超时设置 */
    timeout?: number;
}
/**
 * 插件权限
 */
export interface PluginPermission {
    /** 权限ID */
    id: string;
    /** 权限名称 */
    name: string;
    /** 权限描述 */
    description: string;
    /** 权限级别 */
    level: 'low' | 'medium' | 'high' | 'critical';
    /** 是否默认授予 */
    defaultGranted?: boolean;
}
/**
 * 插件安全策略
 */
export interface PluginSecurityPolicy {
    /** 是否启用签名验证 */
    requireSignature: boolean;
    /** 信任的发布者 */
    trustedPublishers?: string[];
    /** 允许的权限 */
    allowedPermissions?: string[];
    /** 沙箱配置 */
    sandboxConfig?: PluginSandboxConfig;
    /** 网络策略 */
    networkPolicy?: {
        allowedDomains?: string[];
        blockedDomains?: string[];
        allowHTTPS?: boolean;
        allowHTTP?: boolean;
    };
}
//# sourceMappingURL=types.d.ts.map