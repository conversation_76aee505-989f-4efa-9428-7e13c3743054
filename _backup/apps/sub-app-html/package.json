{"name": "sub-app-html", "version": "0.1.0", "description": "Micro-Core 原生 HTML/JavaScript 子应用示例", "type": "module", "scripts": {"dev": "vite --port 3004", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3004", "lint": "eslint src --ext .js", "lint:fix": "eslint src --ext .js --fix", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/adapter-html": "workspace:*", "@micro-core/builder-vite": "workspace:*"}, "devDependencies": {"@types/node": "^20.0.0", "eslint": "^8.0.0", "vite": "^5.4.0", "vitest": "^3.2.4", "@vitest/coverage-v8": "^3.2.4"}, "keywords": ["micro-frontend", "sub-app", "html", "javascript", "vite", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}