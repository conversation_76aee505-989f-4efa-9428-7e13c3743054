# 核心 API

本章节详细介绍 Micro-Core 的核心 API，包括主要类、接口和方法的使用说明。

## MicroCore

`MicroCore` 是 Micro-Core 的核心类，负责管理微应用的整个生命周期。

### 构造函数

```typescript
constructor(config?: MicroCoreConfig)
```

#### 参数

- `config` - 可选的配置选项

```typescript
interface MicroCoreConfig {
  // 容器元素
  container?: string | HTMLElement
  
  // 调试模式
  debug?: boolean
  
  // 路由配置
  router?: RouterConfig
  
  // 沙箱配置
  sandbox?: SandboxConfig
  
  // 预加载配置
  prefetch?: PrefetchConfig
  
  // 错误处理配置
  errorHandler?: ErrorHandlerConfig
  
  // 全局状态
  globalState?: Record<string, any>
  
  // 自定义获取器
  fetch?: (url: string, options?: RequestInit) => Promise<Response>
}
```

#### 示例

```typescript
import { MicroCore, type MicroCoreConfig } from '@micro-core/core'

const config: MicroCoreConfig = {
  container: '#app',
  debug: process.env.NODE_ENV === 'development',
  router: {
    mode: 'history',
    base: '/'
  },
  sandbox: {
    type: 'proxy',
    css: true,
    js: true
  },
  errorHandler: {
    onJSError: (error, app) => {
      console.error(`应用 ${app?.name} 发生JS错误:`, error)
    },
    onLoadError: (error, app) => {
      console.error(`应用 ${app?.name} 加载失败:`, error)
    }
  }
}

const microCore = new MicroCore(config)
```

### 方法

#### registerApp

注册一个微应用。

```typescript
registerApp(config: AppConfig): Promise<void>
```

##### 参数

```typescript
interface AppConfig {
  // 应用名称（必需，唯一）
  name: string
  
  // 应用入口（必需）
  entry: string | AppEntry
  
  // 挂载容器（可选，默认使用全局容器）
  container?: string | HTMLElement | (() => HTMLElement)
  
  // 激活条件（必需）
  activeRule: string | RegExp | Array<string | RegExp> | ((location: Location) => boolean)
  
  // 应用属性（可选）
  props?: Record<string, any>
  
  // 加载器（可选）
  loader?: () => Promise<LifecycleFns>
  
  // 自定义获取器（可选）
  fetch?: (url: string, options?: RequestInit) => Promise<Response>
  
  // 沙箱配置（可选）
  sandbox?: SandboxConfig
  
  // 生命周期钩子
  beforeBootstrap?: LifecycleHook
  afterBootstrap?: LifecycleHook
  beforeMount?: LifecycleHook
  afterMount?: LifecycleHook
  beforeUnmount?: LifecycleHook
  afterUnmount?: LifecycleHook
  
  // 错误边界（可选）
  errorBoundary?: ErrorBoundaryConfig
}
```

##### 示例

```typescript
// 基本注册
await microCore.registerApp({
  name: 'user-center',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeRule: '/user'
})

// 高级配置
await microCore.registerApp({
  name: 'order-system',
  entry: {
    scripts: ['http://localhost:3002/static/js/main.js'],
    styles: ['http://localhost:3002/static/css/main.css']
  },
  container: () => document.getElementById('order-container'),
  activeRule: ['/order', '/orders', /^\/order\//],
  props: {
    theme: 'dark',
    apiBaseUrl: process.env.API_BASE_URL
  },
  sandbox: {
    type: 'proxy',
    css: true,
    js: true
  },
  beforeMount: (app) => {
    console.log(`应用 ${app.name} 即将挂载`)
  },
  afterMount: (app) => {
    console.log(`应用 ${app.name} 已挂载`)
  },
  errorBoundary: {
    fallback: '<div class="error">订单系统暂时不可用</div>',
    onError: (error) => {
      console.error('订单系统错误:', error)
    }
  }
})
```

#### unregisterApp

注销一个微应用。

```typescript
unregisterApp(name: string): Promise<void>
```

##### 参数

- `name` - 应用名称

##### 示例

```typescript
await microCore.unregisterApp('user-center')
```

#### start

启动微前端系统。

```typescript
start(options?: StartOptions): Promise<void>
```

##### 参数

```typescript
interface StartOptions {
  // 是否预加载所有应用
  preload?: boolean
  
  // 初始路由
  initialRoute?: string
  
  // 启动回调
  onStart?: () => void
}
```

##### 示例

```typescript
await microCore.start({
  preload: true,
  initialRoute: '/dashboard',
  onStart: () => {
    console.log('微前端系统启动完成')
  }
})
```

#### loadApp

手动加载一个微应用。

```typescript
loadApp(name: string, props?: Record<string, any>): Promise<void>
```

##### 参数

- `name` - 应用名称
- `props` - 可选的属性对象

##### 示例

```typescript
await microCore.loadApp('user-center', {
  userId: 123,
  theme: 'light'
})
```

#### unloadApp

手动卸载一个微应用。

```typescript
unloadApp(name: string): Promise<void>
```

##### 参数

- `name` - 应用名称

##### 示例

```typescript
await microCore.unloadApp('user-center')
```

#### preloadApp

预加载一个微应用。

```typescript
preloadApp(name: string): Promise<void>
```

##### 参数

- `name` - 应用名称

##### 示例

```typescript
// 预加载用户中心应用
await microCore.preloadApp('user-center')
```

#### prefetchApp

预取一个微应用的资源。

```typescript
prefetchApp(name: string): Promise<void>
```

##### 参数

- `name` - 应用名称

##### 示例

```typescript
// 预取订单系统资源
await microCore.prefetchApp('order-system')
```

#### getApp

获取已注册的微应用信息。

```typescript
getApp(name: string): MicroApp | undefined
```

##### 参数

- `name` - 应用名称

##### 返回值

返回微应用对象，如果不存在则返回 `undefined`。

##### 示例

```typescript
const app = microCore.getApp('user-center')
if (app) {
  console.log('应用入口:', app.entry)
  console.log('激活条件:', app.activeRule)
}
```

#### getApps

获取所有已注册的微应用。

```typescript
getApps(): MicroApp[]
```

##### 返回值

返回所有微应用的数组。

##### 示例

```typescript
const apps = microCore.getApps()
console.log('已注册应用数量:', apps.length)
apps.forEach(app => {
  console.log(`应用: ${app.name}, 状态: ${app.status}`)
})
```

#### getActiveApps

获取当前激活的微应用。

```typescript
getActiveApps(): MicroApp[]
```

##### 返回值

返回当前激活的微应用数组。

##### 示例

```typescript
const activeApps = microCore.getActiveApps()
console.log('当前激活的应用:', activeApps.map(app => app.name))
```

#### use

注册插件。

```typescript
use<T extends MicroCorePlugin>(plugin: T | PluginConstructor<T>, options?: any): void
```

##### 参数

- `plugin` - 插件实例或构造函数
- `options` - 可选的插件配置

##### 示例

```typescript
import { RouterPlugin } from '@micro-core/plugin-router'
import { CommunicationPlugin } from '@micro-core/plugin-communication'

// 使用插件实例
microCore.use(new RouterPlugin({
  mode: 'history',
  base: '/'
}))

// 使用插件构造函数
microCore.use(CommunicationPlugin, {
  enableGlobalState: true,
  enableEventBus: true
})
```

#### on

监听事件。

```typescript
on(event: string, listener: (...args: any[]) => void): void
```

##### 参数

- `event` - 事件名称
- `listener` - 事件监听器

##### 示例

```typescript
// 监听应用挂载事件
microCore.on('app-mount', (app) => {
  console.log(`应用 ${app.name} 已挂载`)
})

// 监听应用卸载事件
microCore.on('app-unmount', (app) => {
  console.log(`应用 ${app.name} 已卸载`)
})

// 监听路由变化事件
microCore.on('route-change', (route) => {
  console.log('路由变化:', route)
})
```

#### off

移除事件监听器。

```typescript
off(event: string, listener?: (...args: any[]) => void): void
```

##### 参数

- `event` - 事件名称
- `listener` - 可选的事件监听器，如果不提供则移除所有监听器

##### 示例

```typescript
const handleAppMount = (app) => {
  console.log(`应用 ${app.name} 已挂载`)
}

// 添加监听器
microCore.on('app-mount', handleAppMount)

// 移除特定监听器
microCore.off('app-mount', handleAppMount)

// 移除所有监听器
microCore.off('app-mount')
```

#### emit

触发事件。

```typescript
emit(event: string, ...args: any[]): void
```

##### 参数

- `event` - 事件名称
- `args` - 事件参数

##### 示例

```typescript
// 触发自定义事件
microCore.emit('custom-event', { data: 'hello world' })

// 触发应用事件
microCore.emit('app-ready', app)
```

### 属性

#### version

获取 Micro-Core 版本。

```typescript
readonly version: string
```

##### 示例

```typescript
console.log('Micro-Core 版本:', microCore.version)
```

#### status

获取系统状态。

```typescript
readonly status: 'not-started' | 'starting' | 'started' | 'stopped'
```

##### 示例

```typescript
console.log('系统状态:', microCore.status)
```

## 生命周期函数

微应用需要导出标准的生命周期函数：

### bootstrap

应用启动时调用，用于初始化应用。

```typescript
export async function bootstrap(props: any): Promise<void> {
  console.log('应用启动', props)
  // 初始化逻辑
}
```

### mount

应用挂载时调用，用于渲染应用到 DOM。

```typescript
export async function mount(props: any): Promise<void> {
  console.log('应用挂载', props)
  
  const container = props.container
  // 渲染应用到容器
  ReactDOM.render(<App {...props} />, container)
}
```

### unmount

应用卸载时调用，用于清理资源。

```typescript
export async function unmount(props: any): Promise<void> {
  console.log('应用卸载', props)
  
  const container = props.container
  // 清理 DOM
  ReactDOM.unmountComponentAtNode(container)
}
```

### update

应用更新时调用，用于处理属性变化。

```typescript
export async function update(props: any): Promise<void> {
  console.log('应用更新', props)
  // 处理属性更新
}
```

## 类型定义

### AppEntry

```typescript
type AppEntry = string | {
  scripts?: string[]
  styles?: string[]
  html?: string
}
```

### SandboxConfig

```typescript
interface SandboxConfig {
  // 沙箱类型
  type?: 'proxy' | 'snapshot' | 'iframe'
  
  // CSS 隔离
  css?: boolean | {
    enabled: boolean
    prefix?: string
  }
  
  // JavaScript 隔离
  js?: boolean | {
    enabled: boolean
    strict?: boolean
  }
  
  // 全局变量白名单
  globalWhitelist?: string[]
  
  // 全局变量黑名单
  globalBlacklist?: string[]
}
```

### ErrorBoundaryConfig

```typescript
interface ErrorBoundaryConfig {
  // 错误回退内容
  fallback?: string | HTMLElement | (() => HTMLElement)
  
  // 错误处理函数
  onError?: (error: Error, errorInfo: any) => void
}
```

### ErrorHandlerConfig

```typescript
interface ErrorHandlerConfig {
  // JavaScript 错误处理
  onJSError?: (error: Error, app?: MicroApp) => void
  
  // 加载错误处理
  onLoadError?: (error: Error, app?: MicroApp) => void
  
  // 应用错误处理
  onAppError?: (error: Error, app: MicroApp) => void
}
```

### RouterConfig

```typescript
interface RouterConfig {
  // 路由模式
  mode?: 'hash' | 'history' | 'memory'
  
  // 基础路径
  base?: string
  
  // 链接激活类名
  linkActiveClass?: string
}
```

### PrefetchConfig

```typescript
interface PrefetchConfig {
  // 空闲时预加载
  idle?: string[]
  
  // 鼠标悬停预加载
  hover?: string[] | {
    apps: string[]
    delay?: number
  }
  
  // 视口内预加载
  viewport?: string[]
}
```

## 事件系统

Micro-Core 内置了完整的事件系统，支持以下事件：

### 系统事件

- `system-start` - 系统启动
- `system-stop` - 系统停止
- `route-change` - 路由变化

### 应用事件

- `app-register` - 应用注册
- `app-unregister` - 应用注销
- `app-load` - 应用加载
- `app-mount` - 应用挂载
- `app-unmount` - 应用卸载
- `app-update` - 应用更新
- `app-error` - 应用错误

### 自定义事件

您可以触发和监听自定义事件：

```typescript
// 触发自定义事件
microCore.emit('user-login', { userId: 123 })

// 监听自定义事件
microCore.on('user-login', (data) => {
  console.log('用户登录:', data.userId)
})
```

## 错误处理

Micro-Core 提供了多层次的错误处理机制：

### 全局错误处理

```typescript
const microCore = new MicroCore({
  errorHandler: {
    onJSError: (error, app) => {
      // JavaScript 错误处理
      console.error('JS错误:', error)
      
      // 错误上报
      reportError(error, app)
      
      // 显示错误提示
      showErrorMessage('系统发生错误，请稍后重试')
    },
    
    onLoadError: (error, app) => {
      // 加载错误处理
      console.error('加载错误:', error)
      
      // 显示降级UI
      showFallbackUI(app)
    },
    
    onAppError: (error, app) => {
      // 应用错误处理
      console.error('应用错误:', error)
      
      // 重启应用
      microCore.restartApp(app.name)
    }
  }
})
```

### 应用级错误处理

```typescript
await microCore.registerApp({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeRule: '/user',
  errorBoundary: {
    fallback: '<div class="error">用户中心暂时不可用</div>',
    onError: (error, errorInfo) => {
      console.error('用户应用错误:', error, errorInfo)
    }
  }
})
```

### 错误恢复

```typescript
// 监听错误事件
microCore.on('app-error', async (error, app) => {
  console.error(`应用 ${app.name} 发生错误:`, error)
  
  // 尝试重新加载应用
  try {
    await microCore.unloadApp(app.name)
    await microCore.loadApp(app.name)
    console.log(`应用 ${app.name} 重新加载成功`)
  } catch (reloadError) {
    console.error(`应用 ${app.name} 重新加载失败:`, reloadError)
  }
})
```

## 下一步

了解了核心 API 后，您可以：

1. 查看[应用管理 API](/api/app-management) 了解应用管理的详细接口
2. 学习[路由系统 API](/api/routing) 掌握路由相关的 API
3. 探索[通信系统 API](/api/communication) 了解应用间通信接口
4. 阅读[插件 API](/api/plugin-base) 学习如何开发自定义插件

如果您在使用过程中遇到问题，请查看[常见问题](/guide/faq)或在 GitHub 上提交 Issue。