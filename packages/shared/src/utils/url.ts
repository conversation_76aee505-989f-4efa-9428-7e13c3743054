/**
 * @fileoverview URL工具函数
 * @description 提供URL解析相关的工具函数
 * <AUTHOR> <<EMAIL>>
 */

/**
 * 解析URL
 */
export function parseUrl(url: string): {
    protocol?: string
    hostname?: string
    port?: string
    pathname?: string
    search?: string
    hash?: string
} {
    try {
        const urlObj = new URL(url, typeof window !== 'undefined' ? window.location.origin : 'http://localhost')
        return {
            protocol: urlObj.protocol,
            hostname: urlObj.hostname,
            port: urlObj.port,
            pathname: urlObj.pathname,
            search: urlObj.search,
            hash: urlObj.hash
        }
    } catch {
        // 处理相对URL
        const match = url.match(/^([^?#]*)(\?[^#]*)?(#.*)?$/)
        if (match) {
            return {
                pathname: match[1] || '/',
                search: match[2] || '',
                hash: match[3] || ''
            }
        }
        return {}
    }
}