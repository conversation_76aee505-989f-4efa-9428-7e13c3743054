# @micro-core/plugin-qiankun-compat

Micro-Core qiankun 兼容插件 - 提供与 qiankun 完全兼容的 API 接口，支持无缝迁移

## 特性

- 🔄 **完全兼容**: 提供与 qiankun 100% 兼容的 API 接口
- 🚀 **无缝迁移**: 现有 qiankun 应用可以零代码修改迁移
- 📦 **HTML Entry**: 支持 qiankun 的 HTML Entry 加载方式
- 🏖️ **沙箱隔离**: 自动使用 Micro-Core 的沙箱能力
- 🌐 **全局状态**: 完整支持 qiankun 的全局状态管理
- 🔧 **生命周期**: 完整的应用生命周期钩子支持

## 安装

```bash
npm install @micro-core/plugin-qiankun-compat
# 或
pnpm add @micro-core/plugin-qiankun-compat
```

## 使用方法

### 方式一：插件模式

```typescript
import { MicroCoreKernel } from '@micro-core/core';
import { QiankunCompatPlugin } from '@micro-core/plugin-qiankun-compat';

const kernel = new MicroCoreKernel();

// 注册 qiankun 兼容插件
const qiankunCompat = new QiankunCompatPlugin();
kernel.use(qiankunCompat);

// 启动后可以使用全局 qiankun API
window.qiankun.registerMicroApps([
  {
    name: 'reactApp',
    entry: '//localhost:3000',
    container: '#subapp-viewport',
    activeRule: '/react',
  }
]);

window.qiankun.start();
```

### 方式二：直接 API 模式

```typescript
import { MicroCoreKernel } from '@micro-core/core';
import { 
  initQiankunCompat,
  registerMicroApps, 
  start,
  initGlobalState 
} from '@micro-core/plugin-qiankun-compat';

const kernel = new MicroCoreKernel();

// 初始化 qiankun 兼容 API
initQiankunCompat(kernel);

// 使用 qiankun API（与原 qiankun 完全相同）
registerMicroApps([
  {
    name: 'reactApp',
    entry: '//localhost:3000',
    container: '#subapp-viewport',
    activeRule: '/react',
    props: {
      routerBase: '/react',
    }
  },
  {
    name: 'vueApp',
    entry: { scripts: ['//localhost:7100/main.js'] },
    container: '#subapp-viewport',
    activeRule: '/vue',
  }
]);

// 启动 qiankun
start({
  prefetch: true,
  sandbox: {
    strictStyleIsolation: true,
  }
});
```

### 方式三：完全兼容模式（推荐迁移使用）

如果你有现有的 qiankun 代码，可以直接替换导入：

```typescript
// 原来的 qiankun 代码
// import { registerMicroApps, start, initGlobalState } from 'qiankun';

// 替换为 Micro-Core 兼容插件
import { registerMicroApps, start, initGlobalState } from '@micro-core/plugin-qiankun-compat';
import { MicroCoreKernel } from '@micro-core/core';
import { initQiankunCompat } from '@micro-core/plugin-qiankun-compat';

// 初始化（只需要添加这两行）
const kernel = new MicroCoreKernel();
initQiankunCompat(kernel);

// 其余代码保持不变
registerMicroApps([
  {
    name: 'reactApp',
    entry: '//localhost:3000',
    container: '#subapp-viewport',
    activeRule: '/react',
  }
]);

start();
```

## API 兼容性

### registerMicroApps

注册微应用，与 qiankun 完全兼容

```typescript
registerMicroApps(
  [
    {
      name: 'app1',
      entry: '//localhost:3000',
      container: '#subapp-viewport',
      activeRule: '/app1',
      props: { /* 自定义属性 */ }
    }
  ],
  {
    beforeLoad: [async app => console.log('before load', app.name)],
    beforeMount: [async app => console.log('before mount', app.name)],
    afterMount: [async app => console.log('after mount', app.name)],
    beforeUnmount: [async app => console.log('before unmount', app.name)],
    afterUnmount: [async app => console.log('after unmount', app.name)],
  }
);
```

### start

启动 qiankun，与 qiankun 完全兼容

```typescript
start({
  prefetch: true, // 预加载
  sandbox: {
    strictStyleIsolation: true, // 严格样式隔离
    experimentalStyleIsolation: true, // 实验性样式隔离
  },
  singular: false, // 是否单例模式
  getPublicPath: (entry) => `${entry}/public/`,
  getTemplate: (tpl) => tpl,
  excludeAssetFilter: (assetUrl) => assetUrl.includes('exclude'),
});
```

### loadMicroApp

手动加载微应用

```typescript
const microApp = await loadMicroApp({
  name: 'manualApp',
  entry: '//localhost:4000',
  container: '#manual-container',
  activeRule: () => true,
});

// 手动控制应用
await microApp.mount();
await microApp.unmount();
```

### initGlobalState

初始化全局状态管理

```typescript
const actions = initGlobalState({
  user: 'qiankun',
  theme: 'dark'
});

// 设置全局状态
actions.setGlobalState({
  user: 'micro-core',
  theme: 'light'
});

// 监听全局状态变化
actions.onGlobalStateChange((state, prev) => {
  console.log('状态变化:', state, prev);
}, true);
```

## 迁移指南

### 从 qiankun 迁移到 Micro-Core

1. **安装依赖**
   ```bash
   npm uninstall qiankun
   npm install @micro-core/core @micro-core/plugin-qiankun-compat
   ```

2. **修改主应用代码**
   ```typescript
   // 原来
   import { registerMicroApps, start } from 'qiankun';
   
   // 现在
   import { registerMicroApps, start, initQiankunCompat } from '@micro-core/plugin-qiankun-compat';
   import { MicroCoreKernel } from '@micro-core/core';
   
   // 添加初始化代码
   const kernel = new MicroCoreKernel();
   initQiankunCompat(kernel);
   
   // 其余代码保持不变
   registerMicroApps([...]);
   start();
   ```

3. **子应用无需修改**
   
   子应用的生命周期函数保持不变：
   ```typescript
   export async function bootstrap() {}
   export async function mount(props) {}
   export async function unmount(props) {}
   ```

### 渐进式迁移

你可以逐步迁移到 Micro-Core 的原生 API：

```typescript
// 第一步：使用兼容插件
import { initQiankunCompat } from '@micro-core/plugin-qiankun-compat';

// 第二步：逐步使用 Micro-Core 原生 API
import { MicroCoreKernel } from '@micro-core/core';

const kernel = new MicroCoreKernel();

// 可以混合使用
kernel.registerApplication({
  name: 'newApp',
  entry: '//localhost:5000',
  container: '#new-container',
  activeWhen: '/new-app'
});
```

## 性能优势

相比原生 qiankun，使用 Micro-Core 兼容插件可以获得：

- 🚀 **更快的启动速度**: 微内核架构，按需加载
- 💾 **更小的包体积**: 核心库 < 15KB
- ⚡ **更好的性能**: 优化的沙箱和资源加载策略
- 🔧 **更强的扩展性**: 插件化架构，功能可定制

## 注意事项

1. **兼容性**: 支持 qiankun 的所有核心 API，部分高级特性可能有差异
2. **沙箱**: 默认使用 Micro-Core 的 Proxy 沙箱，性能更好
3. **样式隔离**: 支持 qiankun 的样式隔离配置
4. **生命周期**: 完整支持 qiankun 的生命周期钩子

## 许可证

MIT License