/**
 * 沙箱系统类型定义
 *
 * @description 定义微前端沙箱隔离的所有类型接口 - 从 sandbox 包迁移
 * <AUTHOR> <<EMAIL>>
 */
export type SandboxType = 'proxy' | 'defineproperty' | 'iframe' | 'webcomponent' | 'namespace' | 'federation';
export interface SandboxConfig {
    /** 沙箱类型 */
    type: SandboxType;
    /** 沙箱名称 */
    name: string;
    /** 是否启用严格模式 */
    strict: boolean;
    /** 隔离配置 */
    isolation: IsolationConfig;
    /** 性能配置 */
    performance: {
        /** 是否启用性能监控 */
        monitoring: boolean;
        /** 内存限制（MB） */
        memoryLimit: number;
        /** 执行超时时间（毫秒） */
        timeout: number;
    };
    /** 权限配置 */
    permissions: SandboxPermission[];
    /** 自定义属性 */
    customProperties?: Record<string, any>;
}
export interface IsolationConfig {
    /** JavaScript 隔离 */
    javascript: {
        /** 是否启用 */
        enabled: boolean;
        /** 全局变量白名单 */
        globalWhitelist: string[];
        /** 全局变量黑名单 */
        globalBlacklist: string[];
        /** 是否允许访问原生 API */
        allowNativeAPI: boolean;
    };
    /** CSS 隔离 */
    css: {
        /** 是否启用 */
        enabled: boolean;
        /** 隔离策略 */
        strategy: 'scoped' | 'shadow' | 'namespace';
        /** CSS 前缀 */
        prefix?: string;
        /** 是否隔离全局样式 */
        isolateGlobalStyles: boolean;
    };
    /** DOM 隔离 */
    dom: {
        /** 是否启用 */
        enabled: boolean;
        /** 是否使用 Shadow DOM */
        useShadowDOM: boolean;
        /** 容器选择器 */
        containerSelector?: string;
    };
}
export interface SandboxPermission {
    /** 权限类型 */
    type: 'api' | 'dom' | 'network' | 'storage' | 'custom';
    /** 权限名称 */
    name: string;
    /** 是否允许 */
    allowed: boolean;
    /** 权限级别 */
    level: 'read' | 'write' | 'execute' | 'admin';
    /** 权限描述 */
    description?: string;
}
export interface SandboxContext {
    /** 沙箱 ID */
    id: string;
    /** 沙箱名称 */
    name: string;
    /** 沙箱类型 */
    type: SandboxType;
    /** 全局对象代理 */
    globalProxy: any;
    /** 文档对象代理 */
    documentProxy?: Document;
    /** 窗口对象代理 */
    windowProxy?: Window;
    /** 沙箱状态 */
    state: 'inactive' | 'active' | 'destroyed';
    /** 创建时间 */
    createdAt: number;
    /** 最后活动时间 */
    lastActivity: number;
}
export interface SandboxStrategy {
    /** 策略类型 */
    readonly type: SandboxType;
    /** 策略名称 */
    readonly name: string;
    /** 创建沙箱 */
    create(config: SandboxConfig): Promise<SandboxContext>;
    /** 激活沙箱 */
    activate(context: SandboxContext): Promise<void>;
    /** 停用沙箱 */
    deactivate(context: SandboxContext): Promise<void>;
    /** 销毁沙箱 */
    destroy(context: SandboxContext): Promise<void>;
    /** 执行代码 */
    execute(context: SandboxContext, code: string): Promise<any>;
    /** 获取性能指标 */
    getMetrics(context: SandboxContext): SandboxPerformanceMetrics;
}
export interface SandboxPerformanceMetrics {
    /** 内存使用量（字节） */
    memoryUsage: number;
    /** CPU 使用率（百分比） */
    cpuUsage: number;
    /** 执行时间（毫秒） */
    executionTime: number;
    /** 创建时间（毫秒） */
    creationTime: number;
    /** 激活次数 */
    activationCount: number;
    /** 错误次数 */
    errorCount: number;
    /** 最后更新时间 */
    lastUpdated: number;
}
export interface SandboxEvent {
    /** 事件类型 */
    type: 'created' | 'activated' | 'deactivated' | 'destroyed' | 'error';
    /** 沙箱 ID */
    sandboxId: string;
    /** 事件数据 */
    data?: any;
    /** 时间戳 */
    timestamp: number;
}
export interface SandboxError extends Error {
    /** 错误代码 */
    code: string;
    /** 错误类型 */
    type: 'CREATION_FAILED' | 'ACTIVATION_FAILED' | 'EXECUTION_FAILED' | 'PERMISSION_DENIED' | 'TIMEOUT';
    /** 沙箱 ID */
    sandboxId?: string;
    /** 错误详情 */
    details?: Record<string, any>;
}
export interface Isolator {
    /** 隔离器名称 */
    readonly name: string;
    /** 是否启用 */
    enabled: boolean;
    /** 初始化隔离器 */
    initialize(config: IsolationConfig): Promise<void>;
    /** 应用隔离 */
    apply(context: SandboxContext): Promise<void>;
    /** 移除隔离 */
    remove(context: SandboxContext): Promise<void>;
    /** 清理资源 */
    cleanup(): Promise<void>;
}
export interface SandboxFactoryConfig {
    /** 默认沙箱类型 */
    defaultType: SandboxType;
    /** 策略优先级 */
    strategyPriority: SandboxType[];
    /** 自动选择策略 */
    autoSelect: boolean;
    /** 性能阈值 */
    performanceThreshold: {
        memory: number;
        cpu: number;
        executionTime: number;
    };
}
export interface SandboxManagerConfig {
    /** 最大沙箱数量 */
    maxSandboxes: number;
    /** 默认沙箱配置 */
    defaultConfig: Partial<SandboxConfig>;
    /** 是否启用性能监控 */
    performanceMonitoring: boolean;
    /** 清理间隔（毫秒） */
    cleanupInterval: number;
    /** 是否启用自动清理 */
    autoCleanup: boolean;
}
//# sourceMappingURL=sandbox-types.d.ts.map