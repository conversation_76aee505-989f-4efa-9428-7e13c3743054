/**
 * @fileoverview 重试工具函数
 * @description 提供函数重试相关的工具函数
 * <AUTHOR> <<EMAIL>>
 */
export interface RetryOptions {
    maxAttempts: number;
    delay?: number;
    backoff?: 'linear' | 'exponential';
}
/**
 * 创建重试函数
 */
export declare function createRetryFunction<T extends (...args: any[]) => any>(fn: T, options: RetryOptions): (...args: Parameters<T>) => Promise<ReturnType<T>>;
//# sourceMappingURL=retry.d.ts.map