# Rollup 集成

Micro-Core 提供了与 Rollup 的深度集成，支持 Rollup 4.x+ 版本，专注于构建优化、模块打包和库开发场景。

## 📋 目录

- [集成概述](#集成概述)
- [安装配置](#安装配置)
- [基础构建](#基础构建)
- [高级配置](#高级配置)
- [插件生态](#插件生态)
- [优化策略](#优化策略)
- [库开发](#库开发)
- [最佳实践](#最佳实践)

## 集成概述

### 核心特性

```typescript
// Rollup 集成特性
const rollupIntegrationFeatures = {
  // 构建特性
  build: [
    'ES 模块优先',
    'Tree Shaking',
    'Code Splitting',
    '多格式输出'
  ],
  
  // 优化特性
  optimization: [
    '体积优化',
    '依赖分析',
    '死代码消除',
    '作用域提升'
  ],
  
  // 微前端特性
  microFrontend: [
    '模块联邦',
    '动态导入',
    '共享依赖',
    '独立构建'
  ],
  
  // 输出格式
  formats: [
    'ES Modules (ESM)',
    'CommonJS (CJS)',
    'UMD',
    'IIFE'
  ]
}
```

### 架构设计

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Rollup + Micro-Core 架构                     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    输入阶段                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 源码解析     │  │ 依赖分析     │  │ 模块图构建           │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    转换阶段                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 插件处理     │  │ 代码转换     │  │ 优化处理             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    输出阶段                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 代码生成     │  │ 文件输出     │  │ 资源处理             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 安装配置

### 安装依赖

```bash
# 安装 Rollup 和相关插件
npm install rollup @rollup/plugin-node-resolve @rollup/plugin-commonjs
npm install @rollup/plugin-typescript @rollup/plugin-babel
npm install @micro-core/rollup-plugin

# 压缩和优化插件
npm install @rollup/plugin-terser rollup-plugin-filesize
npm install rollup-plugin-visualizer

# 开发依赖
npm install rollup-plugin-serve rollup-plugin-livereload
```

### 基础配置

```javascript
// rollup.config.js
import resolve from '@rollup/plugin-node-resolve'
import commonjs from '@rollup/plugin-commonjs'
import typescript from '@rollup/plugin-typescript'
import terser from '@rollup/plugin-terser'
import { microCore } from '@micro-core/rollup-plugin'

export default {
  input: 'src/main.ts',
  
  output: [
    {
      file: 'dist/bundle.esm.js',
      format: 'es',
      sourcemap: true
    },
    {
      file: 'dist/bundle.cjs.js',
      format: 'cjs',
      sourcemap: true
    },
    {
      file: 'dist/bundle.umd.js',
      format: 'umd',
      name: 'MicroApp',
      sourcemap: true
    }
  ],
  
  plugins: [
    resolve({
      browser: true,
      preferBuiltins: false
    }),
    commonjs(),
    typescript({
      tsconfig: './tsconfig.json'
    }),
    microCore({
      name: 'micro-app',
      type: 'remote',
      
      // 暴露的模块
      exposes: {
        './App': './src/App.ts',
        './utils': './src/utils/index.ts'
      },
      
      // 共享依赖
      shared: {
        'vue': {
          singleton: true,
          requiredVersion: '^3.0.0'
        }
      }
    }),
    terser()
  ],
  
  external: ['vue', 'react', 'lodash']
}
```

## 基础构建

### 单应用构建

```javascript
// rollup.config.single.js
import { defineConfig } from 'rollup'
import { microCore } from '@micro-core/rollup-plugin'

export default defineConfig({
  input: 'src/main.ts',
  
  output: {
    dir: 'dist',
    format: 'es',
    entryFileNames: '[name]-[hash].js',
    chunkFileNames: '[name]-[hash].js',
    assetFileNames: '[name]-[hash].[ext]'
  },
  
  plugins: [
    microCore({
      name: 'single-app',
      
      // 生命周期配置
      lifecycle: {
        bootstrap: './src/lifecycle/bootstrap.ts',
        mount: './src/lifecycle/mount.ts',
        unmount: './src/lifecycle/unmount.ts'
      },
      
      // 样式处理
      styles: {
        extract: true,
        minimize: true
      }
    })
  ]
})
```

### 多应用构建

```javascript
// rollup.config.multi.js
export default [
  // 主应用配置
  {
    input: 'src/host/main.ts',
    output: {
      dir: 'dist/host',
      format: 'es'
    },
    plugins: [
      microCore({
        name: 'host-app',
        type: 'host',
        
        remotes: {
          'vue-app': './remotes/vue-app',
          'react-app': './remotes/react-app'
        }
      })
    ]
  },
  
  // Vue 微应用配置
  {
    input: 'src/vue-app/main.ts',
    output: {
      dir: 'dist/vue-app',
      format: 'es'
    },
    plugins: [
      microCore({
        name: 'vue-app',
        type: 'remote',
        
        exposes: {
          './App': './src/vue-app/App.vue'
        }
      })
    ]
  },
  
  // React 微应用配置
  {
    input: 'src/react-app/main.tsx',
    output: {
      dir: 'dist/react-app',
      format: 'es'
    },
    plugins: [
      microCore({
        name: 'react-app',
        type: 'remote',
        
        exposes: {
          './App': './src/react-app/App.tsx'
        }
      })
    ]
  }
]
```

## 高级配置

### 代码分割配置

```javascript
// rollup.config.advanced.js
export default {
  input: {
    main: 'src/main.ts',
    vendor: 'src/vendor.ts',
    utils: 'src/utils/index.ts'
  },
  
  output: {
    dir: 'dist',
    format: 'es',
    
    // 手动分包
    manualChunks: {
      // 框架代码
      framework: ['vue', 'vue-router'],
      // 工具库
      utils: ['lodash', 'dayjs'],
      // UI 组件
      ui: ['element-plus']
    }
  },
  
  plugins: [
    microCore({
      name: 'advanced-app',
      
      // 高级优化
      optimization: {
        // 代码分割
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          maxSize: 244000,
          
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all'
            },
            common: {
              name: 'common',
              minChunks: 2,
              chunks: 'all'
            }
          }
        },
        
        // Tree Shaking
        treeshake: {
          moduleSideEffects: false,
          propertyReadSideEffects: false,
          unknownGlobalSideEffects: false
        }
      }
    })
  ]
}
```

### 环境配置

```javascript
// rollup.config.env.js
const isDev = process.env.NODE_ENV === 'development'
const isProd = process.env.NODE_ENV === 'production'

export default {
  input: 'src/main.ts',
  
  output: {
    dir: 'dist',
    format: 'es',
    sourcemap: isDev
  },
  
  plugins: [
    // 开发环境插件
    ...(isDev ? [
      serve({
        open: true,
        contentBase: 'dist',
        port: 3000
      }),
      livereload('dist')
    ] : []),
    
    // 生产环境插件
    ...(isProd ? [
      terser({
        compress: {
          drop_console: true,
          drop_debugger: true
        }
      }),
      filesize(),
      visualizer({
        filename: 'dist/stats.html',
        open: true
      })
    ] : []),
    
    microCore({
      name: 'env-app',
      
      // 环境特定配置
      ...(isDev && {
        dev: {
          hmr: true,
          overlay: true
        }
      }),
      
      ...(isProd && {
        optimization: {
          minify: true,
          treeshake: true
        }
      })
    })
  ]
}
```

## 插件生态

### 自定义插件

```javascript
// plugins/micro-core-plugin.js
export function microCorePlugin(options = {}) {
  return {
    name: 'micro-core-plugin',
    
    // 构建开始
    buildStart(opts) {
      console.log('Micro-Core build started')
    },
    
    // 解析模块
    resolveId(id, importer) {
      if (id.startsWith('micro:')) {
        return id
      }
    },
    
    // 加载模块
    load(id) {
      if (id === 'micro:config') {
        return `export default ${JSON.stringify(options)}`
      }
    },
    
    // 转换代码
    transform(code, id) {
      if (id.includes('main.ts')) {
        return {
          code: `
            import microConfig from 'micro:config'
            ${code}
            
            // 注入微前端配置
            window.__MICRO_CONFIG__ = microConfig
          `,
          map: null
        }
      }
    },
    
    // 生成 bundle
    generateBundle(options, bundle) {
      // 生成微前端清单
      this.emitFile({
        type: 'asset',
        fileName: 'micro-manifest.json',
        source: JSON.stringify({
          name: options.name,
          version: process.env.npm_package_version,
          entries: Object.keys(bundle)
        }, null, 2)
      })
    },
    
    // 构建结束
    buildEnd() {
      console.log('Micro-Core build completed')
    }
  }
}
```

### 常用插件集成

```javascript
// rollup.config.plugins.js
import resolve from '@rollup/plugin-node-resolve'
import commonjs from '@rollup/plugin-commonjs'
import typescript from '@rollup/plugin-typescript'
import babel from '@rollup/plugin-babel'
import replace from '@rollup/plugin-replace'
import json from '@rollup/plugin-json'
import alias from '@rollup/plugin-alias'
import { microCorePlugin } from './plugins/micro-core-plugin.js'

export default {
  plugins: [
    // 路径别名
    alias({
      entries: [
        { find: '@', replacement: path.resolve(__dirname, 'src') },
        { find: '@components', replacement: path.resolve(__dirname, 'src/components') }
      ]
    }),
    
    // 模块解析
    resolve({
      browser: true,
      preferBuiltins: false,
      extensions: ['.js', '.ts', '.vue', '.jsx', '.tsx']
    }),
    
    // CommonJS 转换
    commonjs({
      include: 'node_modules/**'
    }),
    
    // TypeScript 编译
    typescript({
      tsconfig: './tsconfig.json',
      declaration: true,
      declarationDir: 'dist/types'
    }),
    
    // Babel 转换
    babel({
      babelHelpers: 'bundled',
      exclude: 'node_modules/**',
      presets: [
        ['@babel/preset-env', {
          targets: {
            browsers: ['> 1%', 'last 2 versions']
          }
        }]
      ]
    }),
    
    // JSON 支持
    json(),
    
    // 环境变量替换
    replace({
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
      '__VERSION__': JSON.stringify(process.env.npm_package_version)
    }),
    
    // 微前端插件
    microCorePlugin({
      name: 'plugin-demo-app'
    })
  ]
}
```

## 优化策略

### 构建优化

```javascript
// rollup.config.optimization.js
export default {
  input: 'src/main.ts',
  
  output: {
    dir: 'dist',
    format: 'es',
    
    // 优化输出
    compact: true,
    generatedCode: 'es2015',
    
    // 文件命名优化
    entryFileNames: 'js/[name]-[hash].js',
    chunkFileNames: 'js/[name]-[hash].js',
    assetFileNames: 'assets/[name]-[hash].[ext]'
  },
  
  // Tree Shaking 优化
  treeshake: {
    moduleSideEffects: false,
    propertyReadSideEffects: false,
    unknownGlobalSideEffects: false,
    
    // 精确的副作用检测
    preset: 'smallest'
  },
  
  plugins: [
    microCore({
      name: 'optimized-app',
      
      // 构建优化
      optimization: {
        // 压缩配置
        minify: {
          terser: {
            compress: {
              drop_console: true,
              drop_debugger: true,
              pure_funcs: ['console.log', 'console.info']
            },
            mangle: {
              properties: {
                regex: /^_/
              }
            }
          }
        },
        
        // 代码分割优化
        splitChunks: {
          strategy: 'split-by-experience',
          maxSize: 200000
        }
      }
    })
  ]
}
```

### 性能监控

```javascript
// plugins/performance-plugin.js
export function performancePlugin() {
  let startTime
  
  return {
    name: 'performance-plugin',
    
    buildStart() {
      startTime = Date.now()
    },
    
    generateBundle(options, bundle) {
      const bundleInfo = Object.entries(bundle).map(([fileName, chunk]) => ({
        fileName,
        size: chunk.code ? chunk.code.length : chunk.source.length,
        type: chunk.type
      }))
      
      console.table(bundleInfo)
    },
    
    buildEnd() {
      const buildTime = Date.now() - startTime
      console.log(`Build completed in ${buildTime}ms`)
    }
  }
}
```

## 库开发

### 库构建配置

```javascript
// rollup.config.lib.js
export default {
  input: 'src/index.ts',
  
  output: [
    // ES 模块
    {
      file: 'dist/index.esm.js',
      format: 'es',
      exports: 'named'
    },
    
    // CommonJS
    {
      file: 'dist/index.cjs.js',
      format: 'cjs',
      exports: 'named'
    },
    
    // UMD
    {
      file: 'dist/index.umd.js',
      format: 'umd',
      name: 'MicroCoreLib',
      exports: 'named'
    }
  ],
  
  external: [
    'vue',
    'react',
    'react-dom'
  ],
  
  plugins: [
    typescript({
      declaration: true,
      declarationDir: 'dist/types',
      rootDir: 'src'
    }),
    
    microCore({
      name: 'micro-core-lib',
      type: 'library',
      
      // 库特定配置
      library: {
        exports: 'named',
        globals: {
          vue: 'Vue',
          react: 'React',
          'react-dom': 'ReactDOM'
        }
      }
    })
  ]
}
```

### 组件库构建

```javascript
// rollup.config.components.js
import vue from 'rollup-plugin-vue'
import postcss from 'rollup-plugin-postcss'

export default {
  input: 'src/components/index.ts',
  
  output: [
    {
      file: 'dist/components.esm.js',
      format: 'es'
    },
    {
      file: 'dist/components.cjs.js',
      format: 'cjs'
    }
  ],
  
  plugins: [
    vue({
      css: false
    }),
    
    postcss({
      extract: 'components.css',
      minimize: true
    }),
    
    microCore({
      name: 'component-lib',
      type: 'library',
      
      // 组件库配置
      components: {
        extractCSS: true,
        scopedCSS: true,
        themeSupport: true
      }
    })
  ],
  
  external: ['vue']
}
```

## 最佳实践

### 1. 配置管理

```javascript
// config/rollup.base.js
export const baseConfig = {
  plugins: [
    resolve(),
    commonjs(),
    typescript()
  ],
  
  external: [
    'vue',
    'react',
    'lodash'
  ]
}

// config/rollup.dev.js
import { baseConfig } from './rollup.base.js'

export const devConfig = {
  ...baseConfig,
  
  output: {
    sourcemap: true
  },
  
  plugins: [
    ...baseConfig.plugins,
    serve(),
    livereload()
  ]
}

// config/rollup.prod.js
import { baseConfig } from './rollup.base.js'

export const prodConfig = {
  ...baseConfig,
  
  plugins: [
    ...baseConfig.plugins,
    terser(),
    filesize()
  ]
}
```

### 2. 脚本配置

```json
{
  "scripts": {
    "build": "rollup -c",
    "build:dev": "rollup -c --environment NODE_ENV:development",
    "build:prod": "rollup -c --environment NODE_ENV:production",
    "build:lib": "rollup -c rollup.config.lib.js",
    "build:watch": "rollup -c -w",
    "build:analyze": "rollup -c --environment ANALYZE:true"
  }
}
```

### 3. 类型定义

```typescript
// types/rollup.d.ts
declare module '@micro-core/rollup-plugin' {
  interface MicroCoreOptions {
    name: string
    type: 'host' | 'remote' | 'library'
    exposes?: Record<string, string>
    remotes?: Record<string, string>
    shared?: Record<string, any>
  }
  
  export function microCore(options: MicroCoreOptions): Plugin
}
```

### 4. 调试配置

```javascript
// rollup.config.debug.js
export default {
  input: 'src/main.ts',
  
  output: {
    dir: 'dist',
    format: 'es',
    sourcemap: 'inline'
  },
  
  plugins: [
    microCore({
      name: 'debug-app',
      
      // 调试配置
      debug: {
        enabled: true,
        logLevel: 'verbose',
        showBundleInfo: true
      }
    })
  ],
  
  // 监听模式配置
  watch: {
    include: 'src/**',
    exclude: 'node_modules/**'
  }
}
```

## 总结

Rollup 集成为 Micro-Core 提供了专业的构建解决方案：

- ✅ **ES 模块优先** - 原生支持 ES 模块，完美的 Tree Shaking
- ✅ **体积优化** - 极致的构建体积优化
- ✅ **多格式输出** - 支持 ESM、CJS、UMD 等多种格式
- ✅ **插件生态** - 丰富的插件生态系统
- ✅ **库开发** - 专业的库和组件开发支持
- ✅ **性能监控** - 完整的构建性能分析

通过 Rollup 集成，您可以获得最优化的构建结果，特别适合库开发和对构建体积有严格要求的项目。
