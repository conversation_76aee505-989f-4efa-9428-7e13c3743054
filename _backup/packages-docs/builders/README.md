# @micro-core/builders

微前端构建工具适配器集合 - 支持Webpack、Vite、Rollup、ESBuild等主流构建工具的无缝集成。

## 📦 包含构建器

### 🚀 核心构建器

- **Webpack构建器** - 支持Webpack 5+，包含模块联邦、代码分割、热重载等特性
- **Vite构建器** - 支持Vite 4+，提供快速开发服务器和优化构建
- **Rollup构建器** - 支持Rollup 3+，专注于库构建和Tree Shaking
- **ESBuild构建器** - 支持ESBuild 0.17+，提供极速构建体验
- **Parcel构建器** - 支持Parcel 2+，零配置构建工具
- **Rspack构建器** - 支持Rspack，基于Rust的高性能构建工具
- **Turbopack构建器** - 支持Turbopack，增量构建优化

### 🔧 通用功能

- **基础构建器类** - 提供统一的构建器接口和生命周期管理
- **自动检测** - 智能检测项目使用的构建工具
- **配置标准化** - 统一的配置接口，支持多种构建工具
- **开发服务器** - 内置开发服务器支持
- **热重载** - 完整的热重载和实时更新
- **代码分割** - 智能代码分割和懒加载
- **微前端支持** - 专门的微前端构建优化

## 🚀 快速开始

### 安装

```bash
# 使用 pnpm
pnpm add @micro-core/builders

# 使用 npm
npm install @micro-core/builders

# 使用 yarn
yarn add @micro-core/builders
```

### 基本使用

```typescript
import { MicroCoreKernel } from '@micro-core/core';
import { createWebpackBuilder, createViteBuilder } from '@micro-core/builders';

// 创建内核实例
const kernel = new MicroCoreKernel();

// 注册使用Webpack构建的应用
await kernel.registerApp({
  name: 'webpack-app',
  entry: 'http://localhost:3001',
  container: '#webpack-container',
  activeWhen: '/webpack',
  builder: createWebpackBuilder({
    mode: 'development',
    entry: './src/index.ts',
    outDir: './dist',
    microfrontend: {
      name: 'webpack-app',
      exposes: {
        './Component': './src/Component'
      },
      shared: {
        react: { singleton: true },
        'react-dom': { singleton: true }
      }
    }
  })
});

// 注册使用Vite构建的应用
await kernel.registerApp({
  name: 'vite-app',
  entry: 'http://localhost:3002',
  container: '#vite-container',
  activeWhen: '/vite',
  builder: createViteBuilder({
    mode: 'development',
    entry: './src/main.ts',
    outDir: './dist',
    microfrontend: {
      name: 'vite-app',
      exposes: {
        './App': './src/App.vue'
      }
    }
  })
});

// 启动内核
await kernel.start();
```

## 📖 构建器详细说明

### Webpack构建器

支持Webpack 5+版本，提供完整的模块联邦和微前端支持。

```typescript
import { createWebpackBuilder } from '@micro-core/builders/webpack';

const webpackBuilder = createWebpackBuilder({
  // 基础配置
  mode: 'development',
  entry: './src/index.ts',
  outDir: './dist',
  sourcemap: true,
  minify: false,
  
  // 微前端配置
  microfrontend: {
    name: 'my-app',
    exposes: {
      './Component': './src/Component',
      './utils': './src/utils'
    },
    remotes: {
      'remote-app': 'remoteApp@http://localhost:3001/remoteEntry.js'
    },
    shared: {
      react: {
        singleton: true,
        requiredVersion: '^18.0.0'
      },
      'react-dom': {
        singleton: true,
        requiredVersion: '^18.0.0'
      }
    }
  },
  
  // 开发服务器
  devServer: {
    port: 3000,
    host: 'localhost',
    hot: true,
    open: true,
    proxy: {
      '/api': 'http://localhost:8080'
    }
  },
  
  // 自定义Webpack配置
  webpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    },
    plugins: [
      new DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV)
      })
    ]
  }
});
```

**特性支持：**
- ✅ 模块联邦 (Module Federation)
- ✅ 代码分割 (Code Splitting)
- ✅ 热重载 (Hot Module Replacement)
- ✅ 加载器 (Loaders)
- ✅ 插件系统 (Plugins)
- ✅ Tree Shaking
- ✅ 开发服务器
- ✅ 生产优化

### Vite构建器

支持Vite 4+版本，提供快速的开发体验和优化的生产构建。

```typescript
import { createViteBuilder } from '@micro-core/builders/vite';

const viteBuilder = createViteBuilder({
  // 基础配置
  mode: 'development',
  entry: './src/main.ts',
  outDir: './dist',
  sourcemap: true,
  minify: 'esbuild',
  
  // 微前端配置
  microfrontend: {
    name: 'vite-app',
    exposes: {
      './App': './src/App.vue',
      './store': './src/store'
    }
  },
  
  // 开发服务器
  devServer: {
    port: 3000,
    host: 'localhost',
    hot: true,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  },
  
  // 自定义Vite配置
  vite: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    },
    plugins: [
      vue(),
      vueJsx()
    ],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@import "@/styles/variables.scss";'
        }
      }
    }
  }
});
```

**特性支持：**
- ✅ 极速冷启动
- ✅ 即时热重载
- ✅ 原生ESM支持
- ✅ TypeScript支持
- ✅ CSS预处理器
- ✅ 插件生态
- ✅ 优化构建
- ✅ 开发服务器

### Rollup构建器

支持Rollup 3+版本，专注于库构建和Tree Shaking优化。

```typescript
import { createRollupBuilder } from '@micro-core/builders/rollup';

const rollupBuilder = createRollupBuilder({
  // 基础配置
  entry: './src/index.ts',
  outDir: './dist',
  format: ['esm', 'cjs', 'umd'],
  sourcemap: true,
  minify: true,
  
  // 外部依赖
  external: ['react', 'react-dom', 'lodash'],
  
  // 全局变量映射
  globals: {
    react: 'React',
    'react-dom': 'ReactDOM',
    lodash: '_'
  },
  
  // 自定义Rollup配置
  rollup: {
    plugins: [
      resolve(),
      commonjs(),
      typescript(),
      terser()
    ]
  }
});
```

**特性支持：**
- ✅ Tree Shaking
- ✅ 多格式输出
- ✅ 插件系统
- ✅ 代码分割
- ✅ 库构建优化
- ✅ TypeScript支持
- ✅ 压缩优化

### ESBuild构建器

支持ESBuild 0.17+版本，提供极速构建体验。

```typescript
import { createEsbuildBuilder } from '@micro-core/builders/esbuild';

const esbuildBuilder = createEsbuildBuilder({
  // 基础配置
  entry: './src/index.ts',
  outDir: './dist',
  format: 'esm',
  sourcemap: true,
  minify: true,
  target: 'es2020',
  
  // 外部依赖
  external: ['react', 'react-dom'],
  
  // 自定义ESBuild配置
  esbuild: {
    jsx: 'automatic',
    jsxImportSource: 'react',
    loader: {
      '.svg': 'dataurl',
      '.png': 'file'
    },
    define: {
      'process.env.NODE_ENV': '"production"'
    }
  }
});
```

**特性支持：**
- ✅ 极速构建
- ✅ TypeScript支持
- ✅ JSX支持
- ✅ 代码压缩
- ✅ 源码映射
- ✅ 资源处理
- ✅ 代码转换

## 🔧 高级用法

### 自动检测构建工具

```typescript
import { detectBuilder, createBuilder } from '@micro-core/builders';

// 自动检测项目使用的构建工具
const detection = detectBuilder('./my-project');
console.log('检测结果:', detection);
// {
//   type: 'vite',
//   confidence: 0.9,
//   version: '4.5.0',
//   configFile: 'vite.config.ts',
//   features: ['esm', 'hot-reload', 'dev-server', 'plugins']
// }

// 基于检测结果创建构建器
const builder = await createBuilder({
  type: detection.type,
  autoDetect: true,
  config: {
    mode: 'development',
    outDir: './dist'
  }
});
```

### 自定义构建器

```typescript
import { BaseBuilder, BuilderType, BuildResult } from '@micro-core/builders';
import type { AppInfo } from '@micro-core/core';

interface CustomBuilderConfig extends BaseBuilderConfig {
  customOption?: string;
}

class CustomBuilder extends BaseBuilder {
  public readonly name = 'custom';
  public readonly version = '1.0.0';
  public readonly type: BuilderType = 'webpack'; // 基于现有类型

  constructor(config: CustomBuilderConfig = {}) {
    super(config);
  }

  protected async doInitialize(): Promise<void> {
    // 实现初始化逻辑
    console.log(`初始化自定义构建器: ${this.name}`);
  }

  protected async doBuild(appInfo: AppInfo): Promise<BuildResult> {
    // 实现构建逻辑
    console.log(`构建应用: ${appInfo.name}`);
    
    return this.createBuildSuccess([
      {
        path: './dist/index.js',
        size: 1024,
        type: 'js',
        isEntry: true
      }
    ]);
  }

  protected async doWatch(appInfo: AppInfo, callback: (result: BuildResult) => void): Promise<() => void> {
    // 实现监听模式
    console.log(`监听模式构建: ${appInfo.name}`);
    
    return () => {
      console.log('停止监听');
    };
  }

  protected async doServe(appInfo: AppInfo): Promise<DevServerInstance> {
    // 实现开发服务器
    return {
      url: 'http://localhost:3000',
      port: 3000,
      close: async () => {},
      restart: async () => {}
    };
  }

  protected async doStop(): Promise<void> {
    console.log('停止构建器');
  }

  protected async doClean(): Promise<void> {
    console.log('清理构建产物');
  }

  protected async doValidateConfig(config: BaseBuilderConfig): Promise<ValidationResult> {
    return { valid: true, errors: [], warnings: [] };
  }

  protected checkAvailability(): boolean {
    return true;
  }

  protected getSupportedFeatures(): string[] {
    return ['custom-feature'];
  }

  protected getSupportedFormats(): string[] {
    return ['esm', 'cjs'];
  }

  protected getDependencies(): Record<string, string> {
    return {};
  }
}

export function createCustomBuilder(config?: CustomBuilderConfig): CustomBuilder {
  return new CustomBuilder(config);
}
```

### 构建器生命周期钩子

```typescript
import { createWebpackBuilder } from '@micro-core/builders/webpack';

const builder = createWebpackBuilder({
  mode: 'development',
  entry: './src/index.ts'
});

// 添加构建钩子
builder.addHook('beforeBuild', async (appInfo, config) => {
  console.log('准备构建应用:', appInfo.name);
  // 可以在这里做一些预处理
});

builder.addHook('afterBuild', async (appInfo, config) => {
  console.log('应用构建完成:', appInfo.name);
  // 可以在这里做一些后处理
});

// 添加事件监听器
builder.on('start', (event) => {
  console.log('构建开始:', event.data);
});

builder.on('success', (event) => {
  console.log('构建成功:', event.data.result);
});

builder.on('error', (event) => {
  console.error('构建失败:', event.data.error);
});
```

### 配置合并和验证

```typescript
import { mergeBuilderConfig, validateBuilderConfig } from '@micro-core/builders';

// 合并多个配置
const baseConfig = {
  mode: 'development',
  sourcemap: true
};

const projectConfig = {
  entry: './src/index.ts',
  outDir: './dist'
};

const envConfig = {
  define: {
    'process.env.API_URL': '"http://localhost:8080"'
  }
};

const mergedConfig = mergeBuilderConfig(baseConfig, projectConfig, envConfig);

// 验证配置
const validation = validateBuilderConfig(mergedConfig);
if (!validation.valid) {
  console.error('配置验证失败:', validation.errors);
} else {
  console.log('配置验证通过');
  if (validation.warnings.length > 0) {
    console.warn('配置警告:', validation.warnings);
  }
}
```

## 🛠️ 工具函数

### 构建统计

```typescript
import { createBuildStats, formatFileSize, formatBuildTime } from '@micro-core/builders';

const outputs = [
  { path: './dist/index.js', size: 102400, type: 'js', isEntry: true },
  { path: './dist/style.css', size: 20480, type: 'css' },
  { path: './dist/logo.png', size: 5120, type: 'asset' }
];

const stats = createBuildStats(outputs);
console.log('构建统计:', stats);

// 格式化文件大小
console.log('总大小:', formatFileSize(stats.totalSize)); // "125 KB"

// 格式化构建时间
console.log('构建时间:', formatBuildTime(2500)); // "2.5s"
```

### 端口管理

```typescript
import { isPortAvailable, findAvailablePort } from '@micro-core/builders';

// 检查端口是否可用
const available = await isPortAvailable(3000);
console.log('端口3000可用:', available);

// 查找可用端口
const port = await findAvailablePort(3000);
console.log('找到可用端口:', port);
```

## 🔍 调试和开发

### 开发模式

在开发模式下，构建器会提供额外的调试信息和热重载支持：

```typescript
const builder = createWebpackBuilder({
  mode: 'development',
  devServer: {
    port: 3000,
    hot: true,
    open: true
  }
});

// 启动开发服务器
const server = await builder.serve(appInfo);
console.log(`开发服务器启动: ${server.url}`);
```

### 错误处理

```typescript
const builder = createViteBuilder({
  mode: 'development'
});

builder.on('error', (event) => {
  const { error, appInfo } = event.data;
  
  // 发送错误到监控系统
  errorReporting.captureException(error, {
    tags: {
      app: appInfo.name,
      builder: 'vite'
    }
  });
});
```

## 📊 性能优化

### 构建缓存

```typescript
const builder = createWebpackBuilder({
  webpack: {
    cache: {
      type: 'filesystem',
      cacheDirectory: path.resolve(__dirname, '.webpack-cache')
    }
  }
});
```

### 并行构建

```typescript
const builder = createEsbuildBuilder({
  esbuild: {
    platform: 'node',
    target: 'node16',
    bundle: true,
    splitting: true
  }
});
```

### 代码分割

```typescript
const builder = createWebpackBuilder({
  webpack: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all'
          }
        }
      }
    }
  }
});
```

## 🤝 贡献指南

欢迎贡献新的构建器或改进现有构建器！

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/new-builder`)
3. 提交更改 (`git commit -am 'Add new builder'`)
4. 推送到分支 (`git push origin feature/new-builder`)
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](../../LICENSE) 文件。

## 🔗 相关链接

- [微前端核心文档](../core/README.md)
- [适配器系统文档](../adapters/README.md)
- [插件系统文档](../plugins/README.md)
- [示例应用](../../apps/examples/README.md)