/**
 * Wujie 适配器 - 将 Wujie API 适配到 Micro-Core
 */

import { MicroApp } from '@micro-core/core';
import { IframeBridge } from './iframe-bridge';
import { PropsBridge } from './props-bridge';
import { AppInstance, WujieAppConfig, WujieCompatOptions } from './types';

export class WujieAdapter {
    private options: WujieCompatOptions;
    private iframeBridge: IframeBridge;
    private propsBridge: PropsBridge;
    private appInstances: Map<string, AppInstance> = new Map();
    private preloadedApps: Map<string, WujieAppConfig> = new Map();

    constructor(options: WujieCompatOptions) {
        this.options = options;
        this.iframeBridge = new IframeBridge();
        this.propsBridge = new PropsBridge();
    }

    /**
     * 挂载应用
     */
    async mountApp(microApp: MicroApp, config: WujieAppConfig): Promise<void> {
        try {
            // 获取容器元素
            const container = this.getContainer(config.el);
            if (!container) {
                throw new Error(`Container not found: ${config.el}`);
            }

            // 执行 beforeLoad 钩子
            if (config.beforeLoad) {
                config.beforeLoad(window);
            }

            // 创建应用实例
            const appInstance = await this.createAppInstance(config, container);
            this.appInstances.set(config.name, appInstance);

            // 执行 beforeMount 钩子
            if (config.beforeMount && appInstance.window) {
                config.beforeMount(appInstance.window);
            }

            // 挂载应用
            await this.mountAppInstance(appInstance);

            // 执行 afterMount 钩子
            if (config.afterMount && appInstance.window) {
                config.afterMount(appInstance.window);
            }

            console.log(`Wujie app ${config.name} mounted successfully`);
        } catch (error) {
            console.error(`Failed to mount Wujie app ${config.name}:`, error);
            throw error;
        }
    }

    /**
     * 创建应用实例
     */
    private async createAppInstance(config: WujieAppConfig, container: HTMLElement): Promise<AppInstance> {
        let appWindow: Window;
        let appDocument: Document;

        if (this.options.enableIframeIsolation) {
            // 使用 iframe 沙箱
            const iframe = this.iframeBridge.createIframe(config.name, config.url, container);
            appWindow = iframe.contentWindow!;
            appDocument = iframe.contentDocument!;
        } else {
            // 使用当前窗口
            appWindow = window;
            appDocument = document;
        }

        const appInstance: AppInstance = {
            name: config.name,
            window: appWindow,
            document: appDocument,
            container,
            status: 'loading',
            config
        };

        return appInstance;
    }

    /**
     * 挂载应用实例
     */
    private async mountAppInstance(appInstance: AppInstance): Promise<void> {
        try {
            // 加载应用资源
            await this.loadAppResources(appInstance);

            // 设置应用状态为已挂载
            appInstance.status = 'mounted';

            // 设置应用实例到 iframe 桥接器
            this.iframeBridge.setAppInstance(appInstance.name, appInstance);

        } catch (error) {
            appInstance.status = 'error';
            throw error;
        }
    }

    /**
     * 加载应用资源
     */
    private async loadAppResources(appInstance: AppInstance): Promise<void> {
        const { config } = appInstance;

        try {
            // 使用自定义 fetch 函数或默认 fetch
            const fetchFn = config.fetch || fetch;

            // 获取应用 HTML
            const response = await fetchFn(config.url);
            const html = await response.text();

            // 解析和注入资源
            await this.injectResources(appInstance, html);

        } catch (error) {
            console.error(`Failed to load resources for app ${appInstance.name}:`, error);
            throw error;
        }
    }

    /**
     * 注入资源到应用
     */
    private async injectResources(appInstance: AppInstance, html: string): Promise<void> {
        const { window: appWindow, document: appDocument } = appInstance;

        // 创建临时 DOM 解析 HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        // 提取并注入样式
        const styles = tempDiv.querySelectorAll('style, link[rel="stylesheet"]');
        styles.forEach(style => {
            const clonedStyle = style.cloneNode(true) as HTMLElement;
            appDocument.head.appendChild(clonedStyle);
        });

        // 提取并注入脚本
        const scripts = tempDiv.querySelectorAll('script');
        for (const script of Array.from(scripts)) {
            await this.injectScript(appWindow, script);
        }

        // 注入应用主体内容
        const body = tempDiv.querySelector('body');
        if (body) {
            appDocument.body.innerHTML = body.innerHTML;
        }
    }

    /**
     * 注入脚本
     */
    private async injectScript(appWindow: Window, script: HTMLScriptElement): Promise<void> {
        return new Promise((resolve, reject) => {
            const newScript = appWindow.document.createElement('script');

            if (script.src) {
                newScript.src = script.src;
                newScript.onload = () => resolve();
                newScript.onerror = () => reject(new Error(`Failed to load script: ${script.src}`));
            } else {
                newScript.textContent = script.textContent;
                resolve();
            }

            // 复制其他属性
            Array.from(script.attributes).forEach(attr => {
                if (attr.name !== 'src') {
                    newScript.setAttribute(attr.name, attr.value);
                }
            });

            appWindow.document.head.appendChild(newScript);

            if (!script.src) {
                resolve();
            }
        });
    }

    /**
     * 获取容器元素
     */
    private getContainer(el: string | HTMLElement): HTMLElement | null {
        if (typeof el === 'string') {
            return document.querySelector(el);
        }
        return el;
    }

    /**
     * 预设置应用
     */
    async setupApp(config: WujieAppConfig): Promise<void> {
        this.preloadedApps.set(config.name, config);
        console.log(`Wujie app ${config.name} setup completed`);
    }

    /**
     * 销毁应用
     */
    async destroyApp(name: string): Promise<void> {
        const appInstance = this.appInstances.get(name);
        if (!appInstance) {
            console.warn(`App ${name} not found`);
            return;
        }

        try {
            // 执行 beforeUnmount 钩子
            if (appInstance.config.beforeUnmount && appInstance.window) {
                appInstance.config.beforeUnmount(appInstance.window);
            }

            // 销毁 iframe
            this.iframeBridge.destroyIframe(name);

            // 执行 afterUnmount 钩子
            if (appInstance.config.afterUnmount && appInstance.window) {
                appInstance.config.afterUnmount(appInstance.window);
            }

            // 更新状态
            appInstance.status = 'unmounted';
            this.appInstances.delete(name);

            console.log(`Wujie app ${name} destroyed successfully`);
        } catch (error) {
            console.error(`Failed to destroy Wujie app ${name}:`, error);
            throw error;
        }
    }

    /**
     * 预加载应用
     */
    async preloadApp(config: WujieAppConfig): Promise<void> {
        try {
            // 预加载资源但不挂载
            const fetchFn = config.fetch || fetch;
            await fetchFn(config.url);

            // 保存预加载配置
            this.preloadedApps.set(config.name, config);

            console.log(`Wujie app ${config.name} preloaded successfully`);
        } catch (error) {
            console.error(`Failed to preload Wujie app ${config.name}:`, error);
            throw error;
        }
    }

    /**
     * 获取应用实例
     */
    getAppInstance(name: string): AppInstance | undefined {
        return this.appInstances.get(name);
    }

    /**
     * 获取所有应用实例
     */
    getAllAppInstances(): Map<string, AppInstance> {
        return new Map(this.appInstances);
    }
}