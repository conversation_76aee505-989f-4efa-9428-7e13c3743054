/**
 * @fileoverview 验证工具函数
 * @description 提供配置验证相关的工具函数
 * <AUTHOR> <<EMAIL>>
 */

export interface ValidationSchema {
    [key: string]: {
        type: 'string' | 'number' | 'boolean' | 'object' | 'array'
        required?: boolean
    }
}

/**
 * 验证配置对象
 */
export function validateConfig(config: any, schema: ValidationSchema): void {
    for (const key in schema) {
        const rule = schema[key]
        const value = config[key]

        if (rule.required && (value === undefined || value === null)) {
            throw new Error(`配置项 ${key} 是必需的`)
        }

        if (value !== undefined && value !== null) {
            const actualType = Array.isArray(value) ? 'array' : typeof value
            if (actualType !== rule.type) {
                throw new Error(`配置项 ${key} 类型错误，期望 ${rule.type}，实际 ${actualType}`)
            }
        }
    }
}