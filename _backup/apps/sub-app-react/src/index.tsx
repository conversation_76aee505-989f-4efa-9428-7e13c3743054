/**
 * @fileoverview React 微前端子应用入口 - 标准生命周期实现
 * <AUTHOR> <<EMAIL>>
 */

import React from 'react'
import { createRoot, Root } from 'react-dom/client'
import { BrowserRouter } from 'react-router-dom'
import App from './App'

// 微前端应用属性接口
interface MicroAppProps {
  container?: HTMLElement | string
  basename?: string
  theme?: 'light' | 'dark'
  user?: any
  [key: string]: any
}

// 应用状态管理
let root: Root | null = null
let isBootstrapped = false
let isMounted = false

/**
 * 微前端应用启动函数
 * 在应用首次加载时调用，用于初始化应用资源
 */
export async function bootstrap(props: MicroAppProps = {}): Promise<void> {
  try {
    console.log('🚀 React 子应用开始启动', props)

    // 防止重复启动
    if (isBootstrapped) {
      console.warn('⚠️ React 子应用已经启动，跳过重复启动')
      return
    }

    // 初始化应用资源
    await initializeResources(props)

    // 设置全局错误处理
    setupErrorHandling()

    // 标记为已启动
    isBootstrapped = true

    console.log('✅ React 子应用启动完成')
  } catch (error) {
    console.error('❌ React 子应用启动失败:', error)
    throw error
  }
}

/**
 * 微前端应用挂载函数
 * 将应用挂载到指定容器
 */
export async function mount(props: MicroAppProps = {}): Promise<void> {
  try {
    console.log('🔄 React 子应用开始挂载', props)

    // 确保应用已启动
    if (!isBootstrapped) {
      await bootstrap(props)
    }

    // 防止重复挂载
    if (isMounted) {
      console.warn('⚠️ React 子应用已经挂载，先卸载再重新挂载')
      await unmount(props)
    }

    // 获取容器元素
    const container = getContainer(props.container)
    if (!container) {
      throw new Error('找不到挂载容器')
    }

    // 创建 React 根节点
    root = createRoot(container)

    // 渲染应用
    const basename = props.basename || '/react-app'
    root.render(
      <React.StrictMode>
        <BrowserRouter basename={basename}>
          <App {...props} />
        </BrowserRouter>
      </React.StrictMode>
    )

    // 标记为已挂载
    isMounted = true

    // 触发挂载完成事件
    dispatchLifecycleEvent('mounted', props)

    console.log('✅ React 子应用挂载完成')
  } catch (error) {
    console.error('❌ React 子应用挂载失败:', error)
    throw error
  }
}

/**
 * 微前端应用卸载函数
 * 清理应用资源和DOM
 */
export async function unmount(props: MicroAppProps = {}): Promise<void> {
  try {
    console.log('🔄 React 子应用开始卸载', props)

    if (!isMounted) {
      console.warn('⚠️ React 子应用未挂载，跳过卸载')
      return
    }

    // 卸载 React 应用
    if (root) {
      root.unmount()
      root = null
    }

    // 清理事件监听器
    cleanupEventListeners()

    // 清理定时器
    cleanupTimers()

    // 标记为未挂载
    isMounted = false

    // 触发卸载完成事件
    dispatchLifecycleEvent('unmounted', props)

    console.log('✅ React 子应用卸载完成')
  } catch (error) {
    console.error('❌ React 子应用卸载失败:', error)
    throw error
  }
}

/**
 * 微前端应用更新函数
 * 更新应用属性
 */
export async function update(props: MicroAppProps = {}): Promise<void> {
  try {
    console.log('🔄 React 子应用开始更新', props)

    if (!isMounted) {
      console.warn('⚠️ React 子应用未挂载，无法更新')
      return
    }

    // 重新挂载应用以应用新属性
    await unmount(props)
    await mount(props)

    console.log('✅ React 子应用更新完成')
  } catch (error) {
    console.error('❌ React 子应用更新失败:', error)
    throw error
  }
}

/**
 * 获取挂载容器
 */
function getContainer(container?: HTMLElement | string): HTMLElement | null {
  if (!container) {
    return document.getElementById('react-app-container') || document.getElementById('root')
  }

  if (typeof container === 'string') {
    return document.querySelector(container)
  }

  return container
}

/**
 * 初始化应用资源
 */
async function initializeResources(props: MicroAppProps): Promise<void> {
  // 预加载必要资源
  // 初始化主题
  if (props.theme) {
    document.documentElement.setAttribute('data-theme', props.theme)
  }

  // 初始化用户上下文
  if (props.user) {
    // 设置用户相关的全局状态
  }
}

/**
 * 设置全局错误处理
 */
function setupErrorHandling(): void {
  // React 错误边界会处理组件内的错误
  // 这里处理其他类型的错误
  window.addEventListener('unhandledrejection', handleUnhandledRejection)
}

/**
 * 处理未捕获的 Promise 拒绝
 */
function handleUnhandledRejection(event: PromiseRejectionEvent): void {
  console.error('React 子应用未捕获的 Promise 拒绝:', event.reason)
  // 可以发送到错误监控系统
}

/**
 * 清理事件监听器
 */
function cleanupEventListeners(): void {
  window.removeEventListener('unhandledrejection', handleUnhandledRejection)
}

/**
 * 清理定时器
 */
function cleanupTimers(): void {
  // 清理可能存在的定时器
  // 这里可以维护一个定时器列表进行统一清理
}

/**
 * 触发生命周期事件
 */
function dispatchLifecycleEvent(type: string, props: MicroAppProps): void {
  const event = new CustomEvent(`react-app:${type}`, {
    detail: { props, timestamp: Date.now() }
  })
  window.dispatchEvent(event)
}

/**
 * 获取应用状态
 */
export function getAppStatus() {
  return {
    name: 'sub-app-react',
    isBootstrapped,
    isMounted,
    framework: 'React',
    version: '18.2.0'
  }
}

// 独立运行模式
if (!window.__POWERED_BY_MICRO_CORE__) {
  console.log('🏃 React 应用以独立模式运行')

  // 等待 DOM 加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      mount({
        container: '#root',
        basename: '/',
        theme: 'light'
      })
    })
  } else {
    mount({
      container: '#root',
      basename: '/',
      theme: 'light'
    })
  }
}

// 开发模式下的热重载支持
if (import.meta.hot) {
  import.meta.hot.accept('./App', () => {
    console.log('🔄 热重载: App 组件已更新')
    if (isMounted) {
      update({})
    }
  })
}

// 暴露给微前端框架的接口
declare global {
  interface Window {
    __REACT_MICRO_APP__?: {
      bootstrap: typeof bootstrap
      mount: typeof mount
      unmount: typeof unmount
      update: typeof update
      getAppStatus: typeof getAppStatus
    }
  }
}

if (typeof window !== 'undefined') {
  window.__REACT_MICRO_APP__ = {
    bootstrap,
    mount,
    unmount,
    update,
    getAppStatus
  }
}

// 导出生命周期函数供微前端框架使用
export default {
  bootstrap,
  mount,
  unmount,
  update,
  getAppStatus
}