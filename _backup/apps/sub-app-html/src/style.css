/* HTML 子应用样式 */
.html-app {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
    color: #333;
}

.app-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
    font-weight: 300;
}

.app-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1em;
}

.app-main {
    margin-bottom: 30px;
}

.feature-section,
.demo-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #4CAF50;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.feature-section h2,
.demo-section h2 {
    color: #4CAF50;
    margin-top: 0;
    font-size: 1.5em;
}

.feature-section ul {
    list-style: none;
    padding: 0;
}

.feature-section li {
    padding: 8px 0;
    font-size: 1.1em;
    position: relative;
    padding-left: 20px;
}

.feature-section li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: #4CAF50;
    border-radius: 50%;
}

.demo-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.demo-content p {
    font-size: 1.1em;
    margin: 0;
}

#current-time {
    font-weight: bold;
    color: #4CAF50;
}

.demo-button {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1em;
    transition: all 0.3s ease;
    align-self: flex-start;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.demo-button:hover {
    background: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
}

.demo-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.counter {
    padding: 10px 15px;
    background: #e8f5e8;
    border-radius: 6px;
    font-size: 1.1em;
    align-self: flex-start;
}

#count {
    font-weight: bold;
    color: #4CAF50;
    font-size: 1.2em;
}

.app-footer {
    text-align: center;
    padding: 20px;
    color: #666;
    border-top: 1px solid #eee;
    margin-top: 30px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .html-app {
        padding: 10px;
    }

    .app-header h1 {
        font-size: 2em;
    }

    .feature-section,
    .demo-section {
        padding: 15px;
    }

    .demo-content {
        align-items: stretch;
    }

    .demo-button,
    .counter {
        align-self: stretch;
        text-align: center;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.html-app {
    animation: fadeIn 0.5s ease-out;
}