# 微前端迁移测试策略

本指南提供了微前端迁移过程中的全面测试策略，确保迁移的质量和稳定性。

## 📋 目录

- [测试策略概述](#测试策略概述)
- [单元测试](#单元测试)
- [集成测试](#集成测试)
- [端到端测试](#端到端测试)
- [兼容性测试](#兼容性测试)
- [性能测试](#性能测试)
- [安全测试](#安全测试)
- [回归测试](#回归测试)
- [测试自动化](#测试自动化)

## 测试策略概述

### 测试金字塔

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    微前端测试金字塔                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│                    ┌─────────────────┐                         │
│                    │   E2E 测试      │                         │
│                    │   • 用户流程    │                         │
│                    │   • 跨应用交互  │                         │
│                    └─────────────────┘                         │
│                                                                 │
│              ┌─────────────────────────────────┐               │
│              │        集成测试                 │               │
│              │   • 应用间通信                  │               │
│              │   • 路由协调                    │               │
│              │   • 状态共享                    │               │
│              └─────────────────────────────────┘               │
│                                                                 │
│        ┌─────────────────────────────────────────────┐         │
│        │              单元测试                       │         │
│        │   • 组件测试                                │         │
│        │   • 工具函数测试                            │         │
│        │   • API 测试                                │         │
│        └─────────────────────────────────────────────┘         │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                  静态分析                               │   │
│  │   • 代码质量检查                                        │   │
│  │   • 类型检查                                            │   │
│  │   • 安全扫描                                            │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 测试分层策略

```typescript
// 测试策略配置
const testingStrategy = {
  // 单元测试 - 70%
  unit: {
    coverage: 70,
    tools: ['Jest', 'Vitest', 'Testing Library'],
    scope: ['组件', '工具函数', 'API', '业务逻辑']
  },
  
  // 集成测试 - 20%
  integration: {
    coverage: 20,
    tools: ['Jest', 'Cypress', 'Playwright'],
    scope: ['应用间通信', '路由集成', '状态管理', '插件系统']
  },
  
  // E2E 测试 - 10%
  e2e: {
    coverage: 10,
    tools: ['Cypress', 'Playwright', 'Puppeteer'],
    scope: ['关键用户流程', '跨应用场景', '完整业务流程']
  }
}
```

## 单元测试

### 组件测试

```typescript
// React 组件测试示例
import { render, screen, fireEvent } from '@testing-library/react'
import { MicroApp } from '../src/components/MicroApp'

describe('MicroApp Component', () => {
  const defaultProps = {
    name: 'test-app',
    entry: 'http://localhost:3001',
    activeWhen: '/test'
  }
  
  beforeEach(() => {
    // 清理 DOM
    document.body.innerHTML = ''
  })
  
  it('应该正确渲染微应用容器', () => {
    render(<MicroApp {...defaultProps} />)
    
    const container = screen.getByTestId('micro-app-container')
    expect(container).toBeInTheDocument()
    expect(container).toHaveAttribute('data-name', 'test-app')
  })
  
  it('应该在激活时加载应用', async () => {
    const mockLoad = jest.fn()
    const props = {
      ...defaultProps,
      onLoad: mockLoad
    }
    
    render(<MicroApp {...props} />)
    
    // 模拟路由变化
    fireEvent(window, new PopStateEvent('popstate'))
    
    await waitFor(() => {
      expect(mockLoad).toHaveBeenCalledWith('test-app')
    })
  })
  
  it('应该处理加载错误', async () => {
    const mockError = jest.fn()
    const props = {
      ...defaultProps,
      entry: 'invalid-url',
      onError: mockError
    }
    
    render(<MicroApp {...props} />)
    
    await waitFor(() => {
      expect(mockError).toHaveBeenCalled()
    })
  })
})
```

### 核心功能测试

```typescript
// 应用管理器测试
import { AppManager } from '../src/core/AppManager'
import { createMockApp } from './mocks/app'

describe('AppManager', () => {
  let appManager: AppManager
  
  beforeEach(() => {
    appManager = new AppManager()
  })
  
  afterEach(() => {
    appManager.destroy()
  })
  
  describe('registerApp', () => {
    it('应该成功注册应用', () => {
      const app = createMockApp('test-app')
      
      appManager.registerApp(app)
      
      expect(appManager.getApp('test-app')).toBe(app)
      expect(appManager.getApps()).toHaveLength(1)
    })
    
    it('应该拒绝重复注册', () => {
      const app = createMockApp('test-app')
      
      appManager.registerApp(app)
      
      expect(() => {
        appManager.registerApp(app)
      }).toThrow('App test-app already registered')
    })
    
    it('应该验证应用配置', () => {
      expect(() => {
        appManager.registerApp({} as any)
      }).toThrow('Invalid app configuration')
    })
  })
  
  describe('loadApp', () => {
    it('应该成功加载应用', async () => {
      const app = createMockApp('test-app')
      appManager.registerApp(app)
      
      const result = await appManager.loadApp('test-app')
      
      expect(result.success).toBe(true)
      expect(app.status).toBe('loaded')
    })
    
    it('应该处理加载失败', async () => {
      const app = createMockApp('test-app', { shouldFail: true })
      appManager.registerApp(app)
      
      const result = await appManager.loadApp('test-app')
      
      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
    })
  })
})
```

### 工具函数测试

```typescript
// 工具函数测试
import { 
  isValidUrl, 
  parseAppEntry, 
  generateAppId,
  mergeConfigs 
} from '../src/utils'

describe('Utils', () => {
  describe('isValidUrl', () => {
    it('应该验证有效的 URL', () => {
      expect(isValidUrl('http://localhost:3000')).toBe(true)
      expect(isValidUrl('https://example.com')).toBe(true)
      expect(isValidUrl('//cdn.example.com/app.js')).toBe(true)
    })
    
    it('应该拒绝无效的 URL', () => {
      expect(isValidUrl('invalid-url')).toBe(false)
      expect(isValidUrl('')).toBe(false)
      expect(isValidUrl(null)).toBe(false)
    })
  })
  
  describe('parseAppEntry', () => {
    it('应该解析字符串入口', () => {
      const result = parseAppEntry('http://localhost:3000')
      
      expect(result).toEqual({
        scripts: ['http://localhost:3000'],
        styles: []
      })
    })
    
    it('应该解析对象入口', () => {
      const entry = {
        scripts: ['http://localhost:3000/main.js'],
        styles: ['http://localhost:3000/main.css']
      }
      
      const result = parseAppEntry(entry)
      
      expect(result).toEqual(entry)
    })
  })
  
  describe('generateAppId', () => {
    it('应该生成唯一的应用 ID', () => {
      const id1 = generateAppId('test-app')
      const id2 = generateAppId('test-app')
      
      expect(id1).not.toBe(id2)
      expect(id1).toMatch(/^test-app-\w+$/)
    })
  })
  
  describe('mergeConfigs', () => {
    it('应该合并配置对象', () => {
      const config1 = { a: 1, b: { c: 2 } }
      const config2 = { b: { d: 3 }, e: 4 }
      
      const result = mergeConfigs(config1, config2)
      
      expect(result).toEqual({
        a: 1,
        b: { c: 2, d: 3 },
        e: 4
      })
    })
  })
})
```

## 集成测试

### 应用间通信测试

```typescript
// 应用间通信集成测试
import { EventBus } from '../src/communication/EventBus'
import { createMockApp } from './mocks/app'

describe('App Communication Integration', () => {
  let eventBus: EventBus
  let app1: MockApp
  let app2: MockApp
  
  beforeEach(() => {
    eventBus = new EventBus()
    app1 = createMockApp('app1', { eventBus })
    app2 = createMockApp('app2', { eventBus })
  })
  
  it('应该支持应用间事件通信', async () => {
    const messageHandler = jest.fn()
    
    // App2 监听事件
    app2.on('test-message', messageHandler)
    
    // App1 发送事件
    app1.emit('test-message', { data: 'hello' })
    
    expect(messageHandler).toHaveBeenCalledWith({ data: 'hello' })
  })
  
  it('应该支持全局状态共享', async () => {
    const globalState = eventBus.getGlobalState()
    
    // App1 设置状态
    app1.setGlobalState({ user: { name: 'John' } })
    
    // App2 获取状态
    const state = app2.getGlobalState()
    
    expect(state.user.name).toBe('John')
  })
  
  it('应该支持状态变化监听', async () => {
    const stateChangeHandler = jest.fn()
    
    // App2 监听状态变化
    app2.onGlobalStateChange(stateChangeHandler)
    
    // App1 修改状态
    app1.setGlobalState({ count: 1 })
    
    expect(stateChangeHandler).toHaveBeenCalledWith(
      { count: 1 },
      {}
    )
  })
})
```

### 路由集成测试

```typescript
// 路由集成测试
import { Router } from '../src/routing/Router'
import { AppManager } from '../src/core/AppManager'

describe('Router Integration', () => {
  let router: Router
  let appManager: AppManager
  
  beforeEach(() => {
    appManager = new AppManager()
    router = new Router({ appManager })
    
    // 注册测试应用
    appManager.registerApp({
      name: 'app1',
      entry: 'http://localhost:3001',
      activeWhen: '/app1'
    })
    
    appManager.registerApp({
      name: 'app2',
      entry: 'http://localhost:3002',
      activeWhen: '/app2'
    })
  })
  
  it('应该根据路由激活对应应用', async () => {
    // 导航到 /app1
    await router.push('/app1')
    
    expect(appManager.getActiveApps()).toContain('app1')
    expect(appManager.getActiveApps()).not.toContain('app2')
  })
  
  it('应该支持嵌套路由', async () => {
    appManager.registerApp({
      name: 'nested-app',
      entry: 'http://localhost:3003',
      activeWhen: '/app1/nested'
    })
    
    await router.push('/app1/nested')
    
    const activeApps = appManager.getActiveApps()
    expect(activeApps).toContain('app1')
    expect(activeApps).toContain('nested-app')
  })
  
  it('应该处理路由守卫', async () => {
    const guard = jest.fn().mockResolvedValue(true)
    router.beforeEach(guard)
    
    await router.push('/app2')
    
    expect(guard).toHaveBeenCalledWith(
      { path: '/app2' },
      { path: '/' }
    )
  })
})
```

## 端到端测试

### Cypress E2E 测试

```typescript
// cypress/integration/micro-frontend.spec.ts
describe('微前端应用', () => {
  beforeEach(() => {
    cy.visit('/')
  })
  
  it('应该正确加载主应用', () => {
    cy.get('[data-testid="main-app"]').should('be.visible')
    cy.get('[data-testid="navigation"]').should('be.visible')
  })
  
  it('应该支持应用间导航', () => {
    // 点击导航到 App1
    cy.get('[data-testid="nav-app1"]').click()
    
    // 验证 URL 变化
    cy.url().should('include', '/app1')
    
    // 验证应用加载
    cy.get('[data-testid="app1-container"]').should('be.visible')
    cy.get('[data-testid="app1-content"]').should('contain', 'App1 Content')
  })
  
  it('应该支持应用间数据传递', () => {
    // 在 App1 中设置数据
    cy.get('[data-testid="nav-app1"]').click()
    cy.get('[data-testid="user-input"]').type('John Doe')
    cy.get('[data-testid="save-user"]').click()
    
    // 切换到 App2
    cy.get('[data-testid="nav-app2"]').click()
    
    // 验证数据传递
    cy.get('[data-testid="user-display"]').should('contain', 'John Doe')
  })
  
  it('应该处理应用加载失败', () => {
    // 模拟网络错误
    cy.intercept('GET', '**/app3/**', { forceNetworkError: true })
    
    // 尝试加载失败的应用
    cy.get('[data-testid="nav-app3"]').click()
    
    // 验证错误处理
    cy.get('[data-testid="error-message"]').should('be.visible')
    cy.get('[data-testid="error-message"]').should('contain', '应用加载失败')
  })
})
```

### Playwright E2E 测试

```typescript
// tests/e2e/micro-frontend.spec.ts
import { test, expect } from '@playwright/test'

test.describe('微前端应用 E2E 测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })
  
  test('完整的用户流程测试', async ({ page }) => {
    // 1. 验证主应用加载
    await expect(page.locator('[data-testid="main-app"]')).toBeVisible()
    
    // 2. 登录流程
    await page.click('[data-testid="login-btn"]')
    await page.fill('[data-testid="username"]', 'testuser')
    await page.fill('[data-testid="password"]', 'password123')
    await page.click('[data-testid="submit-login"]')
    
    // 3. 验证登录成功
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible()
    
    // 4. 导航到不同的微应用
    await page.click('[data-testid="nav-dashboard"]')
    await expect(page.locator('[data-testid="dashboard-app"]')).toBeVisible()
    
    await page.click('[data-testid="nav-profile"]')
    await expect(page.locator('[data-testid="profile-app"]')).toBeVisible()
    
    // 5. 跨应用数据传递测试
    await page.fill('[data-testid="profile-name"]', 'John Doe')
    await page.click('[data-testid="save-profile"]')
    
    await page.click('[data-testid="nav-dashboard"]')
    await expect(page.locator('[data-testid="welcome-message"]')).toContainText('John Doe')
  })
})
```

## 总结

微前端迁移测试策略需要：

1. **分层测试** - 单元测试、集成测试、E2E测试的合理分配
2. **全面覆盖** - 功能、性能、安全、兼容性的全方位测试
3. **自动化流程** - CI/CD集成，持续测试和反馈
4. **回归保护** - 确保新功能不会破坏现有功能
5. **质量监控** - 持续监控和改进测试质量

通过完善的测试策略，可以确保微前端迁移的质量和稳定性。

## 相关链接

- [兼容性处理](/migration/general/compatibility) - 兼容性测试方法
- [渐进式迁移](/migration/general/progressive) - 渐进式迁移测试
- [qiankun 迁移](/migration/qiankun/) - qiankun 迁移测试
- [wujie 迁移](/migration/wujie/) - wujie 迁移测试
    
    // 2. 登录流程
    await page.click('[data-testid="login-btn"]')
    await page.fill('[data-testid="username"]', 'testuser')
    await page.fill('[data-testid="password"]', 'password123')
    await page.click('[data-testid="submit-login"]')
    
    // 3. 验证登录成功
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible()
    
    // 4. 导航到不同的微应用
    await page.click('[data-testid="nav-dashboard"]')
    await expect(page.locator('[data-testid="dashboard-app"]')).toBeVisible()
    
    await page.click('[data-testid="nav-profile"]')
    await expect(page.locator('[data-testid="profile-app"]')).toBeVisible()
    
    // 5. 跨应用数据传递测试
    await page.fill('[data-testid="profile-name"]', 'John Doe')
    await page.click('[data-testid="save-profile"]')
    
    await page.click('[data-testid="nav-dashboard"]')
    await expect(page.locator('[data-testid="welcome-message"]')).toContainText('John Doe')
  })
  
  test('应用加载性能测试', async ({ page }) => {
    const startTime = Date.now()
    
    await page.goto('/')
    await page.waitForSelector('[data-testid="main-app"]')
    
    const loadTime = Date.now() - startTime
    expect(loadTime).toBeLessThan(3000) // 3秒内加载完成
  })
  
  test('多浏览器兼容性测试', async ({ page, browserName }) => {
    await page.goto('/')
    
    // 验证基本功能在不同浏览器中都能正常工作
    await expect(page.locator('[data-testid="main-app"]')).toBeVisible()
    
    // 浏览器特定的测试
    if (browserName === 'webkit') {
      // Safari 特定测试
      await page.evaluate(() => {
        // 检查 Safari 特有的行为
      })
    }
  })
})
```

## 兼容性测试

### 浏览器兼容性测试

```typescript
// 浏览器兼容性测试配置
const browserCompatibilityConfig = {
  browsers: [
    { name: 'chromium', version: 'latest' },
    { name: 'firefox', version: 'latest' },
    { name: 'webkit', version: 'latest' },
    { name: 'chrome', version: '88' }, // 最低支持版本
    { name: 'edge', version: '88' }
  ],
  
  testMatrix: [
    {
      feature: 'ES6 Modules',
      browsers: ['chromium', 'firefox', 'webkit'],
      fallback: 'UMD'
    },
    {
      feature: 'Web Components',
      browsers: ['chromium', 'firefox'],
      fallback: 'Polyfill'
    },
    {
      feature: 'Dynamic Import',
      browsers: ['chromium', 'firefox', 'webkit'],
      fallback: 'Static Import'
    }
  ]
}

// 兼容性测试套件
describe('浏览器兼容性测试', () => {
  browserCompatibilityConfig.browsers.forEach(browser => {
    describe(`${browser.name} ${browser.version}`, () => {
      let page: Page
      
      beforeAll(async () => {
        page = await browserContext.newPage()
      })
      
      afterAll(async () => {
        await page.close()
      })
      
      it('应该支持基本的微前端功能', async () => {
        await page.goto('/')
        
        // 验证应用加载
        await expect(page.locator('[data-testid="main-app"]')).toBeVisible()
        
        // 验证路由功能
        await page.click('[data-testid="nav-app1"]')
        await expect(page.locator('[data-testid="app1-container"]')).toBeVisible()
        
        // 验证应用间通信
        await page.click('[data-testid="send-message"]')
        await expect(page.locator('[data-testid="message-received"]')).toBeVisible()
      })
      
      it('应该正确处理不支持的特性', async () => {
        // 模拟不支持的特性
        await page.addInitScript(() => {
          // 删除某些现代特性
          delete window.customElements
        })
        
        await page.goto('/')
        
        // 验证降级处理
        await expect(page.locator('[data-testid="fallback-mode"]')).toBeVisible()
      })
    })
  })
})
```

### 设备兼容性测试

```typescript
// 设备兼容性测试
const deviceCompatibilityConfig = {
  devices: [
    { name: 'Desktop Chrome', viewport: { width: 1920, height: 1080 } },
    { name: 'iPad', viewport: { width: 768, height: 1024 } },
    { name: 'iPhone', viewport: { width: 375, height: 667 } },
    { name: 'Android', viewport: { width: 360, height: 640 } }
  ]
}

describe('设备兼容性测试', () => {
  deviceCompatibilityConfig.devices.forEach(device => {
    describe(device.name, () => {
      beforeEach(async () => {
        await page.setViewportSize(device.viewport)
      })
      
      it('应该在不同设备上正确显示', async () => {
        await page.goto('/')
        
        // 验证响应式布局
        const mainApp = page.locator('[data-testid="main-app"]')
        await expect(mainApp).toBeVisible()
        
        // 验证导航在移动设备上的表现
        if (device.viewport.width < 768) {
          await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
        } else {
          await expect(page.locator('[data-testid="desktop-nav"]')).toBeVisible()
        }
      })
      
      it('应该支持触摸交互', async () => {
        if (device.name.includes('iPad') || device.name.includes('iPhone')) {
          await page.goto('/')
          
          // 模拟触摸事件
          await page.tap('[data-testid="nav-app1"]')
          await expect(page.locator('[data-testid="app1-container"]')).toBeVisible()
        }
      })
    })
  })
})
```

## 性能测试

### 加载性能测试

```typescript
// 性能测试工具
class PerformanceTestSuite {
  private metrics: PerformanceMetrics = {
    loadTime: 0,
    firstContentfulPaint: 0,
    largestContentfulPaint: 0,
    cumulativeLayoutShift: 0,
    firstInputDelay: 0
  }
  
  // 测试应用加载性能
  async testLoadPerformance(url: string): Promise<PerformanceReport> {
    const page = await browser.newPage()
    
    // 开始性能监控
    await page.coverage.startJSCoverage()
    await page.coverage.startCSSCoverage()
    
    const startTime = Date.now()
    
    // 加载页面
    await page.goto(url, { waitUntil: 'networkidle0' })
    
    const loadTime = Date.now() - startTime
    
    // 获取 Web Vitals
    const webVitals = await this.getWebVitals(page)
    
    // 获取资源使用情况
    const coverage = await this.getCoverage(page)
    
    await page.close()
    
    return {
      loadTime,
      webVitals,
      coverage,
      recommendations: this.generatePerformanceRecommendations(webVitals)
    }
  }
  
  private async getWebVitals(page: Page): Promise<WebVitals> {
    return await page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals: WebVitals = {}
        
        // First Contentful Paint
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry) => {
            if (entry.name === 'first-contentful-paint') {
              vitals.firstContentfulPaint = entry.startTime
            }
          })
        }).observe({ entryTypes: ['paint'] })
        
        // Largest Contentful Paint
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          vitals.largestContentfulPaint = lastEntry.startTime
        }).observe({ entryTypes: ['largest-contentful-paint'] })
        
        // Cumulative Layout Shift
        new PerformanceObserver((list) => {
          let cls = 0
          list.getEntries().forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              cls += entry.value
            }
          })
          vitals.cumulativeLayoutShift = cls
        }).observe({ entryTypes: ['layout-shift'] })
        
        setTimeout(() => resolve(vitals), 5000)
      })
    })
  }
  
  private async getCoverage(page: Page): Promise<CoverageReport> {
    const [jsCoverage, cssCoverage] = await Promise.all([
      page.coverage.stopJSCoverage(),
      page.coverage.stopCSSCoverage()
    ])
    
    const jsUsed = jsCoverage.reduce((acc, entry) => {
      return acc + entry.ranges.reduce((sum, range) => {
        return sum + (range.end - range.start)
      }, 0)
    }, 0)
    
    const jsTotal = jsCoverage.reduce((acc, entry) => acc + entry.text.length, 0)
    
    const cssUsed = cssCoverage.reduce((acc, entry) => {
      return acc + entry.ranges.reduce((sum, range) => {
        return sum + (range.end - range.start)
      }, 0)
    }, 0)
    
    const cssTotal = cssCoverage.reduce((acc, entry) => acc + entry.text.length, 0)
    
    return {
      js: {
        used: jsUsed,
        total: jsTotal,
        percentage: (jsUsed / jsTotal) * 100
      },
      css: {
        used: cssUsed,
        total: cssTotal,
        percentage: (cssUsed / cssTotal) * 100
      }
    }
  }
  
  private generatePerformanceRecommendations(vitals: WebVitals): string[] {
    const recommendations: string[] = []
    
    if (vitals.firstContentfulPaint > 2000) {
      recommendations.push('首次内容绘制时间过长，建议优化关键资源加载')
    }
    
    if (vitals.largestContentfulPaint > 4000) {
      recommendations.push('最大内容绘制时间过长，建议优化图片和字体加载')
    }
    
    if (vitals.cumulativeLayoutShift > 0.1) {
      recommendations.push('累积布局偏移过大，建议为图片和广告预留空间')
    }
    
    return recommendations
  }
}

interface PerformanceMetrics {
  loadTime: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  cumulativeLayoutShift: number
  firstInputDelay: number
}

interface WebVitals {
  firstContentfulPaint?: number
  largestContentfulPaint?: number
  cumulativeLayoutShift?: number
  firstInputDelay?: number
}

interface CoverageReport {
  js: {
    used: number
    total: number
    percentage: number
  }
  css: {
    used: number
    total: number
    percentage: number
  }
}

interface PerformanceReport {
  loadTime: number
  webVitals: WebVitals
  coverage: CoverageReport
  recommendations: string[]
}
```

### 压力测试

```typescript
// 压力测试套件
class StressTestSuite {
  // 并发用户测试
  async testConcurrentUsers(userCount: number): Promise<StressTestResult> {
    const results: UserTestResult[] = []
    const promises: Promise<UserTestResult>[] = []
    
    for (let i = 0; i < userCount; i++) {
      promises.push(this.simulateUser(i))
    }
    
    const userResults = await Promise.all(promises)
    
    return {
      userCount,
      results: userResults,
      averageResponseTime: this.calculateAverageResponseTime(userResults),
      errorRate: this.calculateErrorRate(userResults),
      throughput: this.calculateThroughput(userResults)
    }
  }
  
  private async simulateUser(userId: number): Promise<UserTestResult> {
    const page = await browser.newPage()
    const startTime = Date.now()
    const errors: string[] = []
    
    try {
      // 模拟用户行为
      await page.goto('/')
      await page.waitForSelector('[data-testid="main-app"]')
      
      // 随机导航
      const navItems = await page.$$('[data-testid^="nav-"]')
      const randomNav = navItems[Math.floor(Math.random() * navItems.length)]
      await randomNav.click()
      
      // 等待应用加载
      await page.waitForTimeout(1000)
      
      // 执行一些操作
      await this.performRandomActions(page)
      
    } catch (error) {
      errors.push(error.message)
    } finally {
      await page.close()
    }
    
    const endTime = Date.now()
    
    return {
      userId,
      duration: endTime - startTime,
      errors,
      success: errors.length === 0
    }
  }
  
  private async performRandomActions(page: Page): Promise<void> {
    const actions = [
      () => page.click('[data-testid="button"]'),
      () => page.fill('[data-testid="input"]', 'test data'),
      () => page.selectOption('[data-testid="select"]', 'option1'),
      () => page.check('[data-testid="checkbox"]')
    ]
    
    // 随机执行一些操作
    for (let i = 0; i < 3; i++) {
      const randomAction = actions[Math.floor(Math.random() * actions.length)]
      try {
        await randomAction()
        await page.waitForTimeout(500)
      } catch (error) {
        // 忽略操作失败
      }
    }
  }
  
  private calculateAverageResponseTime(results: UserTestResult[]): number {
    const totalTime = results.reduce((sum, result) => sum + result.duration, 0)
    return totalTime / results.length
  }
  
  private calculateErrorRate(results: UserTestResult[]): number {
    const errorCount = results.filter(result => !result.success).length
    return (errorCount / results.length) * 100
  }
  
  private calculateThroughput(results: UserTestResult[]): number {
    const successfulRequests = results.filter(result => result.success).length
    const totalTime = Math.max(...results.map(result => result.duration))
    return (successfulRequests / totalTime) * 1000 // 每秒请求数
  }
}

interface UserTestResult {
  userId: number
  duration: number
  errors: string[]
  success: boolean
}

interface StressTestResult {
  userCount: number
  results: UserTestResult[]
  averageResponseTime: number
  errorRate: number
  throughput: number
}
```

## 安全测试

### 安全漏洞扫描

```typescript
// 安全测试套件
class SecurityTestSuite {
  // XSS 攻击测试
  async testXSSVulnerabilities(page: Page): Promise<SecurityTestResult> {
    const vulnerabilities: SecurityVulnerability[] = []
    
    const xssPayloads = [
      '<script>alert("XSS")</script>',
      '"><script>alert("XSS")</script>',
      'javascript:alert("XSS")',
      '<img src=x onerror=alert("XSS")>',
      '<svg onload=alert("XSS")>'
    ]
    
    // 测试输入字段
    const inputs = await page.$$('input[type="text"], textarea')
    
    for (const input of inputs) {
      for (const payload of xssPayloads) {
        try {
          await input.fill(payload)
          await page.keyboard.press('Enter')
          
          // 检查是否执行了脚本
          const alertHandled = await this.checkForAlert(page)
          
          if (alertHandled) {
            vulnerabilities.push({
              type: 'XSS',
              severity: 'high',
              description: `输入字段存在 XSS 漏洞: ${payload}`,
              element: await input.getAttribute('data-testid') || 'unknown'
            })
          }
        } catch (error) {
          // 输入被阻止，这是好事
        }
      }
    }
    
    return {
      vulnerabilities,
      passed: vulnerabilities.length === 0
    }
  }
  
  // CSRF 攻击测试
  async testCSRFProtection(page: Page): Promise<SecurityTestResult> {
    const vulnerabilities: SecurityVulnerability[] = []
    
    // 检查表单是否有 CSRF token
    const forms = await page.$$('form')
    
    for (const form of forms) {
      const csrfToken = await form.$('input[name*="csrf"], input[name*="token"]')
      
      if (!csrfToken) {
        vulnerabilities.push({
          type: 'CSRF',
          severity: 'medium',
          description: '表单缺少 CSRF 保护',
          element: await form.getAttribute('data-testid') || 'unknown'
        })
      }
    }
    
    return {
      vulnerabilities,
      passed: vulnerabilities.length === 0
    }
  }
  
  // 内容安全策略测试
  async testContentSecurityPolicy(page: Page): Promise<SecurityTestResult> {
    const vulnerabilities: SecurityVulnerability[] = []
    
    // 检查 CSP 头
    const response = await page.goto(page.url())
    const cspHeader = response?.headers()['content-security-policy']
    
    if (!cspHeader) {
      vulnerabilities.push({
        type: 'CSP',
        severity: 'medium',
        description: '缺少内容安全策略头',
        element: 'response-headers'
      })
    } else {
      // 检查 CSP 配置
      if (cspHeader.includes("'unsafe-inline'")) {
        vulnerabilities.push({
          type: 'CSP',
          severity: 'low',
          description: 'CSP 允许内联脚本，存在安全风险',
          element: 'csp-header'
        })
      }
      
      if (cspHeader.includes("'unsafe-eval'")) {
        vulnerabilities.push({
          type: 'CSP',
          severity: 'medium',
          description: 'CSP 允许 eval，存在安全风险',
          element: 'csp-header'
        })
      }
    }
    
    return {
      vulnerabilities,
      passed: vulnerabilities.length === 0
    }
  }
  
  private async checkForAlert(page: Page): Promise<boolean> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => resolve(false), 1000)
      
      page.once('dialog', async (dialog) => {
        clearTimeout(timeout)
        await dialog.dismiss()
        resolve(true)
      })
    })
  }
}

interface SecurityVulnerability {
  type: 'XSS' | 'CSRF' | 'CSP' | 'SQL_INJECTION'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  element: string
}

interface SecurityTestResult {
  vulnerabilities: SecurityVulnerability[]
  passed: boolean
}
```

## 回归测试

### 自动化回归测试

```typescript
// 回归测试管理器
class RegressionTestManager {
  private baselineResults: Map<string, TestResult> = new Map()
  
  // 建立基线
  async establishBaseline(): Promise<void> {
    const testSuites = [
      'unit-tests',
      'integration-tests',
      'e2e-tests',
      'performance-tests'
    ]
    
    for (const suite of testSuites) {
      const result = await this.runTestSuite(suite)
      this.baselineResults.set(suite, result)
    }
    
    // 保存基线到文件
    await this.saveBaseline()
  }
  
  // 运行回归测试
  async runRegressionTests(): Promise<RegressionReport> {
    const currentResults: Map<string, TestResult> = new Map()
    const regressions: Regression[] = []
    
    // 运行所有测试套件
    for (const [suite, baseline] of this.baselineResults) {
      const current = await this.runTestSuite(suite)
      currentResults.set(suite, current)
      
      // 比较结果
      const regression = this.compareResults(suite, baseline, current)
      if (regression) {
        regressions.push(regression)
      }
    }
    
    return {
      regressions,
      passed: regressions.length === 0,
      summary: this.generateSummary(currentResults)
    }
  }
  
  private async runTestSuite(suite: string): Promise<TestResult> {
    // 根据测试套件类型运行相应的测试
    switch (suite) {
      case 'unit-tests':
        return await this.runUnitTests()
      case 'integration-tests':
        return await this.runIntegrationTests()
      case 'e2e-tests':
        return await this.runE2ETests()
      case 'performance-tests':
        return await this.runPerformanceTests()
      default:
        throw new Error(`Unknown test suite: ${suite}`)
    }
  }
  
  private compareResults(suite: string, baseline: TestResult, current: TestResult): Regression | null {
    const issues: string[] = []
    
    // 检查测试通过率
    if (current.passRate < baseline.passRate) {
      issues.push(`测试通过率下降: ${baseline.passRate}% -> ${current.passRate}%`)
    }
    
    // 检查性能回归
    if (current.averageTime > baseline.averageTime * 1.2) {
      issues.push(`性能回归: ${baseline.averageTime}ms -> ${current.averageTime}ms`)
    }
    
    // 检查新增失败的测试
    const newFailures = current.failures.filter(
      failure => !baseline.failures.includes(failure)
    )
    
    if (newFailures.length > 0) {
      issues.push(`新增失败测试: ${newFailures.join(', ')}`)
    }
    
    if (issues.length > 0) {
      return {
        suite,
        issues,
        severity: this.calculateSeverity(issues)
      }
    }
    
    return null
  }
  
  private calculateSeverity(issues: string[]): 'low' | 'medium' | 'high' {
    if (issues.some(issue => issue.includes('性能回归'))) {
      return 'high'
    }
    if (issues.some(issue => issue.includes('通过率下降'))) {
      return 'medium'
    }
    return 'low'
  }
  
  private async saveBaseline(): Promise<void> {
    const baseline = Object.fromEntries(this.baselineResults)
    await fs.writeFile('test-baseline.json', JSON.stringify(baseline, null, 2))
  }
  
  private async loadBaseline(): Promise<void> {
    try {
      const data = await fs.readFile('test-baseline.json', 'utf-8')
      const baseline = JSON.parse(data)
      this.baselineResults = new Map(Object.entries(baseline))
    } catch (error) {
      console.warn('无法加载测试基线，将建立新的基线')
    }
  }
}

interface Regression {
  suite: string
  issues: string[]
  severity: 'low' | 'medium' | 'high'
}

interface RegressionReport {
  regressions: Regression[]
  passed: boolean
  summary: TestSummary
}

interface TestSummary {
  totalTests: number
  passedTests: number
  failedTests: number
  averageTime: number
}
```

## 测试自动化

### CI/CD 集成

```yaml
# .github/workflows/test.yml
name: 微前端测试流水线

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: 设置 Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - name: 安装依赖
        run: pnpm install
      
      - name: 运行单元测试
        run: pnpm test:unit --coverage
      
      - name: 上传覆盖率报告
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info

  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - uses: actions/checkout@v3
      
      - name: 设置 Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - name: 安装依赖
        run: pnpm install
      
      - name: 启动测试服务
        run: |
          pnpm build
          pnpm start:test &
          sleep 10
      
      - name: 运行集成测试
        run: pnpm test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
    steps:
      - uses: actions/checkout@v3
      
      - name: 设置 Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - name: 安装依赖
        run: pnpm install
      
      - name: 安装 Playwright
        run: pnpm exec playwright install ${{ matrix.browser }}
      
      - name: 启动应用
        run: |
          pnpm build
          pnpm start &
          sleep 15
      
      - name: 运行 E2E 测试
        run: pnpm test:e2e --project=${{ matrix.browser }}
      
      - name: 上传测试报告
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report-${{ matrix.browser }}
          path: playwright-report/

  performance-tests:
    runs-on: ubuntu-latest
    needs: e2e-tests
    steps:
      - uses: actions/checkout@v3
      
      - name: 设置 Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - name: 安装依赖
        run: pnpm install
      
      - name: 构建应用
        run: pnpm build
      
      - name: 运行性能测试
        run: pnpm test:performance
      
      - name: 生成性能报告
        run: pnpm generate:performance-report
      
      - name: 上传性能报告
        uses: actions/upload-artifact@v3
        with:
          name: performance-report
          path: performance-report/

  security-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: 运行安全扫描
        uses: securecodewarrior/github-action-add-sarif@v1
        with:
          sarif-file: security-scan-results.sarif
      
      - name: 运行依赖安全检查
        run: |
          npm audit --audit-level high
          pnpm audit --audit-level high

  regression-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      - name: 设置 Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - name: 安装依赖
        run: pnpm install
      
      - name: 运行回归测试
        run: pnpm test:regression
      
      -
# 微前端迁移测试策略

本指南提供了微前端迁移过程中的全面测试策略，确保迁移的质量和稳定性。

## 📋 目录

- [测试策略概述](#测试策略概述)
- [单元测试](#单元测试)
- [集成测试](#集成测试)
- [端到端测试](#端到端测试)
- [兼容性测试](#兼容性测试)
- [性能测试](#性能测试)
- [安全测试](#安全测试)
- [回归测试](#回归测试)
- [测试自动化](#测试自动化)

## 测试策略概述

### 测试金字塔

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    微前端测试金字塔                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│                    ┌─────────────────┐                         │
│                    │   E2E 测试      │                         │
│                    │   • 用户流程    │                         │
│                    │   • 跨应用交互  │                         │
│                    └─────────────────┘                         │
│                                                                 │
│              ┌─────────────────────────────────┐               │
│              │        集成测试                 │               │
│              │   • 应用间通信                  │               │
│              │   • 路由协调                    │               │
│              │   • 状态共享                    │               │
│              └─────────────────────────────────┘               │
│                                                                 │
│        ┌─────────────────────────────────────────────┐         │
│        │              单元测试                       │         │
│        │   • 组件测试                                │         │
│        │   • 工具函数测试                            │         │
│        │   • API 测试                                │         │
│        └─────────────────────────────────────────────┘         │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                  静态分析                               │   │
│  │   • 代码质量检查                                        │   │
│  │   • 类型检查                                            │   │
│  │   • 安全扫描                                            │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 测试分层策略

```typescript
// 测试策略配置
const testingStrategy = {
  // 单元测试 - 70%
  unit: {
    coverage: 70,
    tools: ['Jest', 'Vitest', 'Testing Library'],
    scope: ['组件', '工具函数', 'API', '业务逻辑']
  },
  
  // 集成测试 - 20%
  integration: {
    coverage: 20,
    tools: ['Jest', 'Cypress', 'Playwright'],
    scope: ['应用间通信', '路由集成', '状态管理', '插件系统']
  },
  
  // E2E 测试 - 10%
  e2e: {
    coverage: 10,
    tools: ['Cypress', 'Playwright', 'Puppeteer'],
    scope: ['关键用户流程', '跨应用场景', '完整业务流程']
  }
}
```

## 单元测试

### 组件测试

```typescript
// React 组件测试示例
import { render, screen, fireEvent } from '@testing-library/react'
import { MicroApp } from '../src/components/MicroApp'

describe('MicroApp Component', () => {
  const defaultProps = {
    name: 'test-app',
    entry: 'http://localhost:3001',
    activeWhen: '/test'
  }
  
  beforeEach(() => {
    // 清理 DOM
    document.body.innerHTML = ''
  })
  
  it('应该正确渲染微应用容器', () => {
    render(<MicroApp {...defaultProps} />)
    
    const container = screen.getByTestId('micro-app-container')
    expect(container).toBeInTheDocument()
    expect(container).toHaveAttribute('data-name', 'test-app')
  })
  
  it('应该在激活时加载应用', async () => {
    const mockLoad = jest.fn()
    const props = {
      ...defaultProps,
      onLoad: mockLoad
    }
    
    render(<MicroApp {...props} />)
    
    // 模拟路由变化
    fireEvent(window, new PopStateEvent('popstate'))
    
    await waitFor(() => {
      expect(mockLoad).toHaveBeenCalledWith('test-app')
    })
  })
  
  it('应该处理加载错误', async () => {
    const mockError = jest.fn()
    const props = {
      ...defaultProps,
      entry: 'invalid-url',
      onError: mockError
    }
    
    render(<MicroApp {...props} />)
    
    await waitFor(() => {
      expect(mockError).toHaveBeenCalled()
    })
  })
})
```

### 核心功能测试

```typescript
// 应用管理器测试
import { AppManager } from '../src/core/AppManager'
import { createMockApp } from './mocks/app'

describe('AppManager', () => {
  let appManager: AppManager
  
  beforeEach(() => {
    appManager = new AppManager()
  })
  
  afterEach(() => {
    appManager.destroy()
  })
  
  describe('registerApp', () => {
    it('应该成功注册应用', () => {
      const app = createMockApp('test-app')
      
      appManager.registerApp(app)
      
      expect(appManager.getApp('test-app')).toBe(app)
      expect(appManager.getApps()).toHaveLength(1)
    })
    
    it('应该拒绝重复注册', () => {
      const app = createMockApp('test-app')
      
      appManager.registerApp(app)
      
      expect(() => {
        appManager.registerApp(app)
      }).toThrow('App test-app already registered')
    })
    
    it('应该验证应用配置', () => {
      expect(() => {
        appManager.registerApp({} as any)
      }).toThrow('Invalid app configuration')
    })
  })
  
  describe('loadApp', () => {
    it('应该成功加载应用', async () => {
      const app = createMockApp('test-app')
      appManager.registerApp(app)
      
      const result = await appManager.loadApp('test-app')
      
      expect(result.success).toBe(true)
      expect(app.status).toBe('loaded')
    })
    
    it('应该处理加载失败', async () => {
      const app = createMockApp('test-app', { shouldFail: true })
      appManager.registerApp(app)
      
      const result = await appManager.loadApp('test-app')
      
      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
    })
  })
})
```

### 工具函数测试

```typescript
// 工具函数测试
import { 
  isValidUrl, 
  parseAppEntry, 
  generateAppId,
  mergeConfigs 
} from '../src/utils'

describe('Utils', () => {
  describe('isValidUrl', () => {
    it('应该验证有效的 URL', () => {
      expect(isValidUrl('http://localhost:3000')).toBe(true)
      expect(isValidUrl('https://example.com')).toBe(true)
      expect(isValidUrl('//cdn.example.com/app.js')).toBe(true)
    })
    
    it('应该拒绝无效的 URL', () => {
      expect(isValidUrl('invalid-url')).toBe(false)
      expect(isValidUrl('')).toBe(false)
      expect(isValidUrl(null)).toBe(false)
    })
  })
  
  describe('parseAppEntry', () => {
    it('应该解析字符串入口', () => {
      const result = parseAppEntry('http://localhost:3000')
      
      expect(result).toEqual({
        scripts: ['http://localhost:3000'],
        styles: []
      })
    })
    
    it('应该解析对象入口', () => {
      const entry = {
        scripts: ['http://localhost:3000/main.js'],
        styles: ['http://localhost:3000/main.css']
      }
      
      const result = parseAppEntry(entry)
      
      expect(result).toEqual(entry)
    })
  })
  
  describe('generateAppId', () => {
    it('应该生成唯一的应用 ID', () => {
      const id1 = generateAppId('test-app')
      const id2 = generateAppId('test-app')
      
      expect(id1).not.toBe(id2)
      expect(id1).toMatch(/^test-app-\w+$/)
    })
  })
  
  describe('mergeConfigs', () => {
    it('应该合并配置对象', () => {
      const config1 = { a: 1, b: { c: 2 } }
      const config2 = { b: { d: 3 }, e: 4 }
      
      const result = mergeConfigs(config1, config2)
      
      expect(result).toEqual({
        a: 1,
        b: { c: 2, d: 3 },
        e: 4
      })
    })
  })
})
```

## 集成测试

### 应用间通信测试

```typescript
// 应用间通信集成测试
import { EventBus } from '../src/communication/EventBus'
import { createMockApp } from './mocks/app'

describe('App Communication Integration', () => {
  let eventBus: EventBus
  let app1: MockApp
  let app2: MockApp
  
  beforeEach(() => {
    eventBus = new EventBus()
    app1 = createMockApp('app1', { eventBus })
    app2 = createMockApp('app2', { eventBus })
  })
  
  it('应该支持应用间事件通信', async () => {
    const messageHandler = jest.fn()
    
    // App2 监听事件
    app2.on('test-message', messageHandler)
    
    // App1 发送事件
    app1.emit('test-message', { data: 'hello' })
    
    expect(messageHandler).toHaveBeenCalledWith({ data: 'hello' })
  })
  
  it('应该支持全局状态共享', async () => {
    const globalState = eventBus.getGlobalState()
    
    // App1 设置状态
    app1.setGlobalState({ user: { name: 'John' } })
    
    // App2 获取状态
    const state = app2.getGlobalState()
    
    expect(state.user.name).toBe('John')
  })
  
  it('应该支持状态变化监听', async () => {
    const stateChangeHandler = jest.fn()
    
    // App2 监听状态变化
    app2.onGlobalStateChange(stateChangeHandler)
    
    // App1 修改状态
    app1.setGlobalState({ count: 1 })
    
    expect(stateChangeHandler).toHaveBeenCalledWith(
      { count: 1 },
      {}
    )
  })
})
```

### 路由集成测试

```typescript
// 路由集成测试
import { Router } from '../src/routing/Router'
import { AppManager } from '../src/core/AppManager'

describe('Router Integration', () => {
  let router: Router
  let appManager: AppManager
  
  beforeEach(() => {
    appManager = new AppManager()
    router = new Router({ appManager })
    
    // 注册测试应用
    appManager.registerApp({
      name: 'app1',
      entry: 'http://localhost:3001',
      activeWhen: '/app1'
    })
    
    appManager.registerApp({
      name: 'app2',
      entry: 'http://localhost:3002',
      activeWhen: '/app2'
    })
  })
  
  it('应该根据路由激活对应应用', async () => {
    // 导航到 /app1
    await router.push('/app1')
    
    expect(appManager.getActiveApps()).toContain('app1')
    expect(appManager.getActiveApps()).not.toContain('app2')
  })
  
  it('应该支持嵌套路由', async () => {
    appManager.registerApp({
      name: 'nested-app',
      entry: 'http://localhost:3003',
      activeWhen: '/app1/nested'
    })
    
    await router.push('/app1/nested')
    
    const activeApps = appManager.getActiveApps()
    expect(activeApps).toContain('app1')
    expect(activeApps).toContain('nested-app')
  })
  
  it('应该处理路由守卫', async () => {
    const guard = jest.fn().mockResolvedValue(true)
    router.beforeEach(guard)
    
    await router.push('/app2')
    
    expect(guard).toHaveBeenCalledWith(
      { path: '/app2' },
      { path: '/' }
    )
  })
})
```

## 端到端测试

### Cypress E2E 测试

```typescript
// cypress/integration/micro-frontend.spec.ts
describe('微前端应用', () => {
  beforeEach(() => {
    cy.visit('/')
  })
  
  it('应该正确加载主应用', () => {
    cy.get('[data-testid="main-app"]').should('be.visible')
    cy.get('[data-testid="navigation"]').should('be.visible')
  })
  
  it('应该支持应用间导航', () => {
    // 点击导航到 App1
    cy.get('[data-testid="nav-app1"]').click()
    
    // 验证 URL 变化
    cy.url().should('include', '/app1')
    
    // 验证应用加载
    cy.get('[data-testid="app1-container"]').should('be.visible')
    cy.get('[data-testid="app1-content"]').should('contain', 'App1 Content')
  })
  
  it('应该支持应用间数据传递', () => {
    // 在 App1 中设置数据
    cy.get('[data-testid="nav-app1"]').click()
    cy.get('[data-testid="user-input"]').type('John Doe')
    cy.get('[data-testid="save-user"]').click()
    
    // 切换到 App2
    cy.get('[data-testid="nav-app2"]').click()
    
    // 验证数据传递
    cy.get('[data-testid="user-display"]').should('contain', 'John Doe')
  })
  
  it('应该处理应用加载失败', () => {
    // 模拟网络错误
    cy.intercept('GET', '**/app3/**', { forceNetworkError: true })
    
    // 尝试加载失败的应用
    cy.get('[data-testid="nav-app3"]').click()
    
    // 验证错误处理
    cy.get('[data-testid="error-message"]').should('be.visible')
    cy.get('[data-testid="error-message"]').should('contain', '应用加载失败')
  })
})
```

### Playwright E2E 测试

```typescript
// tests/e2e/micro-frontend.spec.ts
import { test, expect } from '@playwright/test'

test.describe('微前端应用 E2E 测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })
  
  test('完整的用户流程测试', async ({ page }) => {
    // 1. 验证主应用加载
    await expect(page.locator('[data-testid="main-app"]')).toBeVisible()