/**
 * 通信系统类型定义
 *
 * @description 定义微前端应用间通信的所有类型接口 - 从 communication 包迁移
 * <AUTHOR> <<EMAIL>>
 */
export interface CommunicationConfig {
    /** 是否启用通信系统 */
    enabled: boolean;
    /** 通信协议版本 */
    protocolVersion: string;
    /** 最大消息大小（字节） */
    maxMessageSize: number;
    /** 消息超时时间（毫秒） */
    timeout: number;
    /** 是否启用消息持久化 */
    persistence: boolean;
    /** 权限配置 */
    permissions: CommunicationPermission[];
}
export interface EventBusConfig {
    /** 事件总线名称 */
    name: string;
    /** 最大监听器数量 */
    maxListeners: number;
    /** 是否启用通配符事件 */
    wildcardEvents: boolean;
    /** 事件历史记录大小 */
    historySize: number;
    /** 是否启用异步事件处理 */
    async: boolean;
}
export interface MessageChannelConfig {
    /** 通道名称 */
    name: string;
    /** 通道类型 */
    type: 'broadcast' | 'direct' | 'multicast';
    /** 缓冲区大小 */
    bufferSize: number;
    /** 是否启用消息确认 */
    acknowledgment: boolean;
    /** 重试配置 */
    retry: {
        enabled: boolean;
        maxAttempts: number;
        backoff: number;
    };
}
export interface CommunicationEvent<T = any> {
    /** 事件ID */
    id: string;
    /** 事件类型 */
    type: string;
    /** 事件数据 */
    data: T;
    /** 发送者应用ID */
    source: string;
    /** 目标应用ID（可选，为空表示广播） */
    target?: string;
    /** 时间戳 */
    timestamp: number;
    /** 事件优先级 */
    priority: 'low' | 'normal' | 'high' | 'critical';
    /** 是否需要确认 */
    requiresAck: boolean;
}
export interface MessagePayload<T = any> {
    /** 消息ID */
    id: string;
    /** 消息类型 */
    type: string;
    /** 消息数据 */
    data: T;
    /** 消息元数据 */
    metadata: {
        /** 发送者信息 */
        sender: {
            appId: string;
            instanceId: string;
            version: string;
        };
        /** 接收者信息 */
        receiver?: {
            appId: string;
            instanceId?: string;
        };
        /** 消息创建时间 */
        createdAt: number;
        /** 消息过期时间 */
        expiresAt?: number;
        /** 消息优先级 */
        priority: number;
        /** 消息标签 */
        tags: string[];
    };
}
export interface StateChangeEvent<T = any> {
    /** 状态键 */
    key: string;
    /** 旧值 */
    oldValue: T;
    /** 新值 */
    newValue: T;
    /** 变更类型 */
    changeType: 'create' | 'update' | 'delete';
    /** 变更来源应用 */
    source: string;
    /** 变更时间戳 */
    timestamp: number;
    /** 变更路径（用于嵌套对象） */
    path?: string[];
}
export interface CommunicationPermission {
    /** 应用ID */
    appId: string;
    /** 允许的操作 */
    actions: ('send' | 'receive' | 'broadcast' | 'subscribe')[];
    /** 允许的事件类型 */
    eventTypes: string[];
    /** 允许的目标应用 */
    targets: string[];
    /** 权限级别 */
    level: 'read' | 'write' | 'admin';
}
export type EventListener<T = any> = (event: CommunicationEvent<T>) => void | Promise<void>;
export type MessageHandler<T = any> = (message: MessagePayload<T>) => void | Promise<void>;
export type StateListener<T = any> = (event: StateChangeEvent<T>) => void | Promise<void>;
export interface CommunicationStats {
    /** 发送的消息数量 */
    messagesSent: number;
    /** 接收的消息数量 */
    messagesReceived: number;
    /** 失败的消息数量 */
    messagesFailed: number;
    /** 活跃的监听器数量 */
    activeListeners: number;
    /** 平均消息处理时间 */
    avgProcessingTime: number;
    /** 最后活动时间 */
    lastActivity: number;
}
export interface CommunicationError extends Error {
    /** 错误代码 */
    code: string;
    /** 错误类型 */
    type: 'TIMEOUT' | 'PERMISSION_DENIED' | 'MESSAGE_TOO_LARGE' | 'INVALID_TARGET' | 'SERIALIZATION_ERROR';
    /** 相关的消息或事件ID */
    relatedId?: string;
    /** 错误详情 */
    details?: Record<string, any>;
}
export interface ExtendedCommunicationConfig extends CommunicationConfig {
    /** 是否启用事件总线 */
    enableEventBus?: boolean;
    /** 是否启用消息通道 */
    enableMessageChannel?: boolean;
    /** 是否启用性能指标 */
    enableMetrics?: boolean;
    /** 是否启用持久化 */
    enablePersistence?: boolean;
    /** 是否启用加密 */
    enableEncryption?: boolean;
    /** 事件总线配置 */
    eventBus?: EventBusConfig;
    /** 消息通道配置 */
    messageChannel?: MessageChannelConfig;
}
export interface CommunicationManagerOptions {
    /** 调试模式 */
    debug?: boolean;
    /** 自动初始化 */
    autoInit?: boolean;
    /** 性能监控 */
    enableMetrics?: boolean;
}
export interface CommunicationState {
    /** 是否已初始化 */
    initialized: boolean;
    /** 事件总线是否激活 */
    eventBusActive: boolean;
    /** 消息通道是否激活 */
    messageChannelActive: boolean;
    /** 总订阅数 */
    totalSubscriptions: number;
    /** 总通道数 */
    totalChannels: number;
    /** 最后活动时间 */
    lastActivity: number;
}
export interface CommunicationMetrics {
    /** 事件总线指标 */
    eventBus: any;
    /** 消息通道指标 */
    messageChannel: any;
    /** 总订阅数 */
    totalSubscriptions: number;
    /** 总通道数 */
    totalChannels: number;
    /** 最后活动时间 */
    lastActivity: number;
}
export type EventHandler<T = any> = (data: T, context?: any) => void | Promise<void>;
//# sourceMappingURL=communication-types.d.ts.map