{"name": "@micro-core/core", "version": "0.1.0", "description": "微前端框架核心包 - 微内核架构设计，核心运行时 <15KB", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["微前端", "micro-frontend", "微内核", "插件系统", "生命周期"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/core"}, "dependencies": {"@micro-core/shared": "workspace:*"}, "devDependencies": {"@types/node": "^20.10.5", "rimraf": "^5.0.5", "typescript": "^5.7.2", "vite": "^7.0.6", "vite-plugin-dts": "^4.5.4", "vitest": "^3.2.4"}, "peerDependencies": {}, "sideEffects": false}