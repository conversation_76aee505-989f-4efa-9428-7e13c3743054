/**
 * 共享工具包 - 事件处理工具函数
 *
 * @description 提供事件处理相关的工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * 事件监听器接口
 */
export interface EventListener<T = any> {
    (event: T): void;
}
/**
 * 事件监听器选项
 */
export interface EventListenerOptions {
    /** 是否只执行一次 */
    once?: boolean;
    /** 优先级（数字越大优先级越高） */
    priority?: number;
}
/**
 * 事件总线配置
 */
export interface EventBusConfig {
    /** 最大监听器数量 */
    maxListeners?: number;
    /** 是否启用调试 */
    enableDebug?: boolean;
    /** 是否启用异步处理 */
    enableAsync?: boolean;
}
/**
 * 增强版事件总线类
 */
export declare class EventBus {
    private events;
    private config;
    private listenerIdCounter;
    constructor(config?: EventBusConfig);
    /**
     * 注册事件监听器
     */
    on<T = any>(event: string, listener: EventListener<T>, options?: EventListenerOptions): () => void;
    /**
     * 注册一次性事件监听器
     */
    once<T = any>(event: string, listener: EventListener<T>, options?: Omit<EventListenerOptions, 'once'>): () => void;
    /**
     * 移除事件监听器
     */
    off<T = any>(event: string, listener?: EventListener<T>): void;
    /**
     * 触发事件
     */
    emit<T = any>(event: string, ...args: T[]): boolean;
    /**
     * 等待事件触发
     */
    waitFor<T = any>(event: string, timeout?: number): Promise<T[]>;
    /**
     * 获取事件监听器数量
     */
    listenerCount(event: string): number;
    /**
     * 获取所有事件名称
     */
    eventNames(): string[];
    /**
     * 获取指定事件的所有监听器
     */
    listeners<T = any>(event: string): EventListener<T>[];
    /**
     * 检查是否有监听器
     */
    hasListeners(event: string): boolean;
    /**
     * 清除所有事件监听器
     */
    clear(): void;
    /**
     * 清除指定事件的所有监听器
     */
    removeAllListeners(event?: string): void;
    /**
     * 获取统计信息
     */
    getStats(): {
        totalEvents: number;
        totalListeners: number;
        eventNames: string[];
        eventDetails: Array<{
            event: string;
            listenerCount: number;
            onceListeners: number;
        }>;
    };
    /**
     * 销毁事件总线
     */
    destroy(): void;
}
/**
 * 创建事件总线实例
 */
export declare function createEventBus(config?: EventBusConfig): EventBus;
/**
 * 全局事件总线实例
 */
export declare const globalEventBus: EventBus;
/**
 * 添加DOM事件监听器
 */
export declare function addEventListener<K extends keyof HTMLElementEventMap>(element: HTMLElement | Window | Document, type: K, listener: (event: HTMLElementEventMap[K]) => void, options?: boolean | AddEventListenerOptions): () => void;
/**
 * 移除DOM事件监听器
 */
export declare function removeEventListener<K extends keyof HTMLElementEventMap>(element: HTMLElement | Window | Document, type: K, listener: (event: HTMLElementEventMap[K]) => void, options?: boolean | EventListenerOptions): void;
/**
 * 一次性DOM事件监听器
 */
export declare function addEventListenerOnce<K extends keyof HTMLElementEventMap>(element: HTMLElement | Window | Document, type: K, listener: (event: HTMLElementEventMap[K]) => void, options?: boolean | AddEventListenerOptions): void;
/**
 * 委托事件监听器
 */
export declare function delegateEventListener<K extends keyof HTMLElementEventMap>(container: HTMLElement, selector: string, type: K, listener: (event: HTMLElementEventMap[K], target: HTMLElement) => void, options?: boolean | AddEventListenerOptions): () => void;
/**
 * 触发自定义事件
 */
export declare function dispatchCustomEvent(element: HTMLElement, eventName: string, detail?: any, options?: CustomEventInit): boolean;
/**
 * 等待事件触发
 */
export declare function waitForEvent<K extends keyof HTMLElementEventMap>(element: HTMLElement | Window | Document, type: K, timeout?: number): Promise<HTMLElementEventMap[K]>;
/**
 * 事件防抖
 */
export declare function debounceEvent<K extends keyof HTMLElementEventMap>(element: HTMLElement | Window | Document, type: K, listener: (event: HTMLElementEventMap[K]) => void, delay: number, options?: boolean | AddEventListenerOptions): () => void;
/**
 * 事件节流
 */
export declare function throttleEvent<K extends keyof HTMLElementEventMap>(element: HTMLElement | Window | Document, type: K, listener: (event: HTMLElementEventMap[K]) => void, delay: number, options?: boolean | AddEventListenerOptions): () => void;
/**
 * 键盘事件工具
 */
export declare class KeyboardEventUtils {
    /**
     * 检查是否按下了指定键
     */
    static isKeyPressed(event: KeyboardEvent, key: string): boolean;
    /**
     * 检查是否按下了组合键
     */
    static isComboPressed(event: KeyboardEvent, combo: {
        key: string;
        ctrl?: boolean;
        alt?: boolean;
        shift?: boolean;
        meta?: boolean;
    }): boolean;
    /**
     * 阻止默认行为
     */
    static preventDefault(event: KeyboardEvent): void;
    /**
     * 停止事件传播
     */
    static stopPropagation(event: KeyboardEvent): void;
}
/**
 * 鼠标事件工具
 */
export declare class MouseEventUtils {
    /**
     * 获取鼠标相对于元素的位置
     */
    static getRelativePosition(event: MouseEvent, element: HTMLElement): {
        x: number;
        y: number;
    };
    /**
     * 检查是否点击了指定按钮
     */
    static isButtonPressed(event: MouseEvent, button: 'left' | 'middle' | 'right'): boolean;
    /**
     * 检查是否在元素内部点击
     */
    static isClickInside(event: MouseEvent, element: HTMLElement): boolean;
}
/**
 * 触摸事件工具
 */
export declare class TouchEventUtils {
    /**
     * 获取触摸点相对于元素的位置
     */
    static getRelativePosition(touch: Touch, element: HTMLElement): {
        x: number;
        y: number;
    };
    /**
     * 计算两个触摸点之间的距离
     */
    static getDistance(touch1: Touch, touch2: Touch): number;
    /**
     * 计算触摸点的中心位置
     */
    static getCenter(touches: TouchList): {
        x: number;
        y: number;
    };
}
//# sourceMappingURL=event-utils.d.ts.map