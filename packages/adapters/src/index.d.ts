/**
 * @micro-core/adapters
 * 微前端适配器系统
 *
 * @description 提供统一的多框架适配器管理
 * @version 0.1.0
 * <AUTHOR> <<EMAIL>>
 */
export { AdapterManager } from './adapter-manager';
export type { AdapterConfig, AdapterInstance, AdapterStatus, FrameworkType } from '@micro-core/shared';
/**
 * 创建适配器管理器实例
 */
export declare function createAdapterManager(): AdapterManager;
/**
 * 默认适配器管理器实例
 */
export declare const defaultAdapterManager: AdapterManager;
/**
 * 版本信息
 */
export declare const VERSION = "0.1.0";
/**
 * 默认导出
 */
declare const _default: {
    VERSION: string;
    AdapterManager: any;
    createAdapterManager: typeof createAdapterManager;
    defaultAdapterManager: AdapterManager;
};
export default _default;
//# sourceMappingURL=index.d.ts.map