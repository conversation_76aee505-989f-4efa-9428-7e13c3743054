/**
 * @fileoverview 对象工具函数
 * @description 提供对象操作相关的工具函数
 * <AUTHOR> <<EMAIL>>
 */

/**
 * 深度克隆对象
 */
export function deepClone<T>(obj: T, visited = new WeakMap()): T {
    if (obj === null || typeof obj !== 'object') {
        return obj
    }

    if (visited.has(obj as any)) {
        return visited.get(obj as any)
    }

    if (obj instanceof Date) {
        return new Date(obj.getTime()) as T
    }

    if (obj instanceof RegExp) {
        return new RegExp(obj.source, obj.flags) as T
    }

    if (Array.isArray(obj)) {
        const cloned: any[] = []
        visited.set(obj as any, cloned)
        for (let i = 0; i < obj.length; i++) {
            cloned[i] = deepClone(obj[i], visited)
        }
        return cloned as T
    }

    const cloned = {} as T
    visited.set(obj as any, cloned)

    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            ; (cloned as any)[key] = deepClone((obj as any)[key], visited)
        }
    }

    return cloned
}

/**
 * 深度合并对象
 */
export function deepMerge<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T {
    if (!sources.length) return target

    const source = sources.shift()
    if (!source) return target

    if (isPlainObject(target) && isPlainObject(source)) {
        for (const key in source) {
            if (key && isPlainObject(source[key])) {
                if (!target[key]) Object.assign(target, { [key]: {} })
                deepMerge(target[key], source[key])
            } else if (key) {
                Object.assign(target, { [key]: source[key] })
            }
        }
    }

    return deepMerge(target, ...sources)
}

/**
 * 判断是否为普通对象
 */
export function isPlainObject(obj: any): obj is Record<string, any> {
    if (typeof obj !== 'object' || obj === null) {
        return false
    }

    if (Object.prototype.toString.call(obj) !== '[object Object]') {
        return false
    }

    if (Object.getPrototypeOf(obj) === null) {
        return true
    }

    let proto = obj
    while (Object.getPrototypeOf(proto) !== null) {
        proto = Object.getPrototypeOf(proto)
    }

    return Object.getPrototypeOf(obj) === proto
}