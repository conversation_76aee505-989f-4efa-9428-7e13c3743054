/**
 * 路由管理器测试用例
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { EventBus } from '../src/communication/event-bus';
import { Router } from '../src/router/index';

// 模拟依赖
vi.mock('../src/communication/event-bus');

describe('Router', () => {
    let router: Router;
    let mockEventBus: EventBus;
    let mockWindow: Window;

    beforeEach(() => {
        // 创建模拟对象
        mockEventBus = {
            on: vi.fn(),
            off: vi.fn(),
            emit: vi.fn(),
            once: vi.fn()
        } as unknown as EventBus;

        // 模拟 window 对象
        mockWindow = {
            location: {
                pathname: '/',
                search: '',
                hash: '',
                href: 'http://localhost/',
                assign: vi.fn(),
                replace: vi.fn()
            },
            history: {
                pushState: vi.fn(),
                replaceState: vi.fn(),
                back: vi.fn(),
                forward: vi.fn(),
                go: vi.fn(),
                state: null,
                length: 1
            },
            addEventListener: vi.fn(),
            removeEventListener: vi.fn()
        } as unknown as Window;

        // 重置模拟
        vi.resetAllMocks();

        // 创建路由实例
        router = new Router({
            eventBus: mockEventBus,
            window: mockWindow
        });
    });

    describe('路由初始化', () => {
        it('应该正确初始化路由', () => {
            expect(router).toBeDefined();
            expect(router.currentRoute).toEqual({
                path: '/',
                query: {},
                params: {},
                hash: ''
            });
        });

        it('应该在初始化时监听 popstate 事件', () => {
            expect(mockWindow.addEventListener).toHaveBeenCalledWith('popstate', expect.any(Function));
        });
    });

    describe('路由注册', () => {
        it('应该能够注册路由', () => {
            const handler = vi.fn();
            router.register('/test', handler);

            expect(router.hasRoute('/test')).toBe(true);
        });

        it('应该能够注册带参数的路由', () => {
            const handler = vi.fn();
            router.register('/users/:id', handler);

            expect(router.hasRoute('/users/:id')).toBe(true);
        });

        it('应该能够注册带通配符的路由', () => {
            const handler = vi.fn();
            router.register('/posts/*', handler);

            expect(router.hasRoute('/posts/*')).toBe(true);
        });

        it('应该能够注册带查询参数的路由', () => {
            const handler = vi.fn();
            router.register('/search', handler);

            expect(router.hasRoute('/search')).toBe(true);
        });
    });

    describe('路由导航', () => {
        it('应该能够导航到指定路由', () => {
            const handler = vi.fn();
            router.register('/test', handler);

            router.navigate('/test');

            expect(mockWindow.history.pushState).toHaveBeenCalled();
            expect(handler).toHaveBeenCalled();
            expect(router.currentRoute.path).toBe('/test');
        });

        it('应该能够导航到带参数的路由', () => {
            const handler = vi.fn();
            router.register('/users/:id', handler);

            router.navigate('/users/123');

            expect(mockWindow.history.pushState).toHaveBeenCalled();
            expect(handler).toHaveBeenCalledWith(expect.objectContaining({
                params: { id: '123' }
            }));
            expect(router.currentRoute.path).toBe('/users/123');
            expect(router.currentRoute.params).toEqual({ id: '123' });
        });

        it('应该能够导航到带查询参数的路由', () => {
            const handler = vi.fn();
            router.register('/search', handler);

            router.navigate('/search?q=test&page=1');

            expect(mockWindow.history.pushState).toHaveBeenCalled();
            expect(handler).toHaveBeenCalledWith(expect.objectContaining({
                query: { q: 'test', page: '1' }
            }));
            expect(router.currentRoute.path).toBe('/search');
            expect(router.currentRoute.query).toEqual({ q: 'test', page: '1' });
        });

        it('应该能够使用 replace 模式导航', () => {
            const handler = vi.fn();
            router.register('/test', handler);

            router.navigate('/test', { replace: true });

            expect(mockWindow.history.replaceState).toHaveBeenCalled();
            expect(mockWindow.history.pushState).not.toHaveBeenCalled();
        });

        it('应该在导航时触发事件', () => {
            const handler = vi.fn();
            router.register('/test', handler);

            router.navigate('/test');

            expect(mockEventBus.emit).toHaveBeenCalledWith('router:before-navigate', expect.any(Object));
            expect(mockEventBus.emit).toHaveBeenCalledWith('router:navigated', expect.any(Object));
        });
    });

    describe('路由参数解析', () => {
        it('应该能够解析路径参数', () => {
            const handler = vi.fn();
            router.register('/users/:id/posts/:postId', handler);

            router.navigate('/users/123/posts/456');

            expect(handler).toHaveBeenCalledWith(expect.objectContaining({
                params: { id: '123', postId: '456' }
            }));
            expect(router.currentRoute.params).toEqual({ id: '123', postId: '456' });
        });

        it('应该能够解析查询参数', () => {
            const handler = vi.fn();
            router.register('/search', handler);

            router.navigate('/search?q=test&filters=active&filters=recent');

            expect(handler).toHaveBeenCalledWith(expect.objectContaining({
                query: { q: 'test', filters: ['active', 'recent'] }
            }));
            expect(router.currentRoute.query).toEqual({ q: 'test', filters: ['active', 'recent'] });
        });

        it('应该能够解析哈希片段', () => {
            const handler = vi.fn();
            router.register('/page', handler);

            router.navigate('/page#section1');

            expect(handler).toHaveBeenCalledWith(expect.objectContaining({
                hash: 'section1'
            }));
            expect(router.currentRoute.hash).toBe('section1');
        });
    });

    describe('路由守卫', () => {
        it('应该支持全局前置守卫', () => {
            const guard = vi.fn().mockReturnValue(true);
            const handler = vi.fn();

            router.beforeEach(guard);
            router.register('/protected', handler);

            router.navigate('/protected');

            expect(guard).toHaveBeenCalled();
            expect(handler).toHaveBeenCalled();
        });

        it('应该在守卫返回 false 时阻止导航', () => {
            const guard = vi.fn().mockReturnValue(false);
            const handler = vi.fn();

            router.beforeEach(guard);
            router.register('/protected', handler);

            router.navigate('/protected');

            expect(guard).toHaveBeenCalled();
            expect(handler).not.toHaveBeenCalled();
            expect(mockWindow.history.pushState).not.toHaveBeenCalled();
        });

        it('应该支持全局后置钩子', () => {
            const hook = vi.fn();
            const handler = vi.fn();

            router.afterEach(hook);
            router.register('/test', handler);

            router.navigate('/test');

            expect(handler).toHaveBeenCalled();
            expect(hook).toHaveBeenCalled();
        });
    });

    describe('路由匹配', () => {
        it('应该能够匹配精确路径', () => {
            const handler = vi.fn();
            router.register('/exact-path', handler);

            router.navigate('/exact-path');

            expect(handler).toHaveBeenCalled();
        });

        it('应该能够匹配带参数的路径', () => {
            const handler = vi.fn();
            router.register('/users/:id', handler);

            router.navigate('/users/123');

            expect(handler).toHaveBeenCalled();
        });

        it('应该能够匹配带通配符的路径', () => {
            const handler = vi.fn();
            router.register('/posts/*', handler);

            router.navigate('/posts/2023/01/01/title');

            expect(handler).toHaveBeenCalled();
        });

        it('应该在没有匹配的路由时触发 notFound 处理器', () => {
            const notFoundHandler = vi.fn();
            router.notFound(notFoundHandler);

            router.navigate('/non-existent-path');

            expect(notFoundHandler).toHaveBeenCalled();
        });
    });

    describe('路由历史', () => {
        it('应该能够返回上一页', () => {
            router.back();

            expect(mockWindow.history.back).toHaveBeenCalled();
        });

        it('应该能够前进到下一页', () => {
            router.forward();

            expect(mockWindow.history.forward).toHaveBeenCalled();
        });

        it('应该能够跳转到指定历史位置', () => {
            router.go(-2);

            expect(mockWindow.history.go).toHaveBeenCalledWith(-2);
        });
    });

    describe('路由销毁', () => {
        it('应该能够销毁路由实例', () => {
            router.destroy();

            expect(mockWindow.removeEventListener).toHaveBeenCalledWith('popstate', expect.any(Function));
        });
    });
});