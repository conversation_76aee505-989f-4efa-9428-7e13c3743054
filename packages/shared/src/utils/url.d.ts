/**
 * @fileoverview URL处理工具函数
 * @description 提供各种URL操作和解析功能
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 解析URL参数
 */
export declare function parseQuery(url: string): Record<string, string>;
/**
 * 构建查询字符串
 */
export declare function buildQuery(params: Record<string, any>): string;
/**
 * 添加查询参数到URL
 */
export declare function addQuery(url: string, params: Record<string, any>): string;
/**
 * 移除URL中的查询参数
 */
export declare function removeQuery(url: string, keys: string[]): string;
/**
 * 获取URL的基础路径
 */
export declare function getBasePath(url: string): string;
/**
 * 检查是否为绝对URL
 */
export declare function isAbsoluteUrl(url: string): boolean;
/**
 * 规范化URL路径
 */
export declare function normalizePath(path: string): string;
/**
 * 连接URL路径
 */
export declare function joinPaths(...paths: string[]): string;
//# sourceMappingURL=url.d.ts.map