# @micro-core/builder-esbuild

A high-performance ESBuild builder implementation for the Micro-Core ecosystem, providing lightning-fast JavaScript and TypeScript bundling.

## 🚀 Features

- **Ultra-Fast Builds**: Leverages ESBuild's native Go implementation for maximum speed
- **TypeScript Support**: Built-in TypeScript compilation without additional setup
- **Tree Shaking**: Automatic dead code elimination
- **Code Splitting**: Dynamic import support for optimal loading
- **Development Server**: Fast rebuild and hot reload capabilities
- **Source Maps**: Full source map support for debugging
- **Plugin System**: Extensible plugin architecture
- **Bundle Analysis**: Built-in bundle size analysis

## 📦 Installation

```bash
pnpm add @micro-core/builder-esbuild
# or
npm install @micro-core/builder-esbuild
# or
yarn add @micro-core/builder-esbuild
```

## 🔧 Quick Start

```typescript
import { EsbuildBuilder } from '@micro-core/builder-esbuild';

// Create builder instance
const builder = new EsbuildBuilder({
  mode: 'development',
  entry: './src/index.ts',
  outDir: './dist'
});

// Build project
const result = await builder.build({
  entry: './src/index.ts',
  outDir: './dist',
  mode: 'production'
});

console.log('Build completed:', result.success);
```

## ⚙️ Configuration

### Basic Configuration

```typescript
const builder = new EsbuildBuilder({
  mode: 'development',
  entry: './src/index.ts',
  outDir: './dist',
  target: 'es2020',
  format: 'esm'
});
```

### Advanced Configuration

```typescript
const builder = new EsbuildBuilder({
  mode: 'production',
  entry: './src/index.ts',
  outDir: './dist',
  esbuildConfig: {
    target: 'es2020',
    format: 'esm',
    platform: 'browser',
    bundle: true,
    minify: true,
    sourcemap: true,
    splitting: true,
    loader: {
      '.png': 'file',
      '.svg': 'text'
    },
    define: {
      'process.env.NODE_ENV': '"production"'
    },
    external: ['react', 'react-dom']
  }
});
```

## 🔌 API Reference

### EsbuildBuilder Class

#### Constructor

```typescript
constructor(options: EsbuildBuilderOptions)
```

**Parameters:**
- `options.mode`: Build mode ('development' | 'production')
- `options.entry`: Entry point(s) for the build
- `options.outDir`: Output directory
- `options.target`: Target environment (e.g., 'es2020', 'node14')
- `options.format`: Output format ('esm' | 'cjs' | 'iife')
- `options.esbuildConfig`: Custom ESBuild configuration

#### Methods

##### `build(config: EsbuildBuilderConfig): Promise<BuildResult>`

Execute the build process.

```typescript
const result = await builder.build({
  entry: './src/index.ts',
  outDir: './dist',
  mode: 'production'
});
```

##### `serve(config: EsbuildBuilderConfig, devServerConfig?: DevServerConfig): Promise<any>`

Start the development server.

```typescript
const server = await builder.serve({
  entry: './src/index.ts',
  mode: 'development'
}, {
  port: 3000,
  host: 'localhost'
});
```

##### `stop(): Promise<void>`

Stop the development server.

```typescript
await builder.stop();
```

### Configuration Types

#### EsbuildBuilderConfig

```typescript
interface EsbuildBuilderConfig extends BaseBuilderConfig {
  target?: string | string[];
  format?: 'esm' | 'cjs' | 'iife';
  platform?: 'browser' | 'node' | 'neutral';
  esbuildConfig?: BuildOptions;
  plugins?: Plugin[];
  loader?: Record<string, Loader>;
  external?: string[];
}
```

## 🏗️ Advanced Usage

### Custom Plugins

```typescript
import { Plugin } from 'esbuild';

const customPlugin: Plugin = {
  name: 'custom-plugin',
  setup(build) {
    build.onResolve({ filter: /^custom:/ }, args => {
      return { path: args.path, namespace: 'custom' };
    });
    
    build.onLoad({ filter: /.*/, namespace: 'custom' }, args => {
      return {
        contents: 'export default "custom content"',
        loader: 'js'
      };
    });
  }
};

const builder = new EsbuildBuilder({
  esbuildConfig: {
    plugins: [customPlugin]
  }
});
```

### Environment Variables

```typescript
const builder = new EsbuildBuilder({
  esbuildConfig: {
    define: {
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
      'process.env.API_URL': JSON.stringify(process.env.API_URL),
      __VERSION__: JSON.stringify(require('./package.json').version)
    }
  }
});
```

### Code Splitting

```typescript
const builder = new EsbuildBuilder({
  entry: {
    main: './src/index.ts',
    admin: './src/admin.ts'
  },
  esbuildConfig: {
    splitting: true,
    format: 'esm',
    outdir: './dist'
  }
});
```

## 🧪 Testing

```bash
# Run tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Run integration tests
pnpm test:integration
```

## 📊 Performance Tips

1. **Use Native Loaders**: Leverage ESBuild's built-in loaders for better performance
2. **Enable Splitting**: Use code splitting for better caching
3. **Minimize External Dependencies**: Keep externals to a minimum
4. **Use Target Optimization**: Set appropriate target for your environment

```typescript
const builder = new EsbuildBuilder({
  esbuildConfig: {
    target: 'es2020', // Modern target for better optimization
    treeShaking: true,
    minify: true,
    splitting: true
  }
});
```

## 🔍 Troubleshooting

### Common Issues

**TypeScript compilation errors:**
```typescript
// Ensure proper TypeScript configuration
esbuildConfig: {
  loader: {
    '.ts': 'ts',
    '.tsx': 'tsx'
  },
  target: 'es2020'
}
```

**Import resolution issues:**
```typescript
// Configure path mapping
esbuildConfig: {
  alias: {
    '@': './src',
    '~': './node_modules'
  }
}
```

**Bundle size issues:**
```typescript
// Enable tree shaking and minification
esbuildConfig: {
  treeShaking: true,
  minify: true,
  bundle: true
}
```

## 🔄 Migration Guide

### From ESBuild CLI

```bash
# Before
esbuild src/index.ts --bundle --outfile=dist/bundle.js

# After (Micro-Core)
```

```typescript
const builder = new EsbuildBuilder({
  entry: './src/index.ts',
  outDir: './dist'
});
```

## 📄 License

MIT © 2025 Micro-Core

## 🤝 Contributing

Contributions are welcome! Please read our contributing guidelines and submit pull requests to our repository.

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/echo008/micro-core/issues)
- 📖 Documentation: [Micro-Core Docs](https://micro-core.dev)
