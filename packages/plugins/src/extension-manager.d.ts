/**
 * 插件扩展点管理器
 *
 * @description 管理插件的扩展点和扩展实现
 * <AUTHOR> <<EMAIL>>
 */
import type { PluginExtension, PluginExtensionPoint } from './types';
/**
 * 插件扩展点管理器
 */
export declare class ExtensionManager extends EventEmitter {
    private readonly logger;
    private readonly extensionPoints;
    private readonly extensions;
    private readonly providerCache;
    constructor();
    /**
     * 注册扩展点
     */
    registerExtensionPoint(extensionPoint: PluginExtensionPoint, registeredBy: string): void;
    /**
     * 注销扩展点
     */
    unregisterExtensionPoint(extensionPointId: string): boolean;
    /**
     * 注册扩展实现
     */
    registerExtension(extension: PluginExtension, registeredBy: string): void;
    /**
     * 注销扩展实现
     */
    unregisterExtension(extensionId: string): boolean;
    /**
     * 获取扩展点定义
     */
    getExtensionPoint(extensionPointId: string): PluginExtensionPoint | undefined;
    /**
     * 获取扩展实现
     */
    getExtension(extensionId: string): PluginExtension | undefined;
    /**
     * 获取扩展点的所有扩展实现
     */
    getExtensions(extensionPointId: string): PluginExtension[];
    /**
     * 获取提供者实现（缓存版本）
     */
    getProviders<T = any>(extensionPointId: string): T[];
    /**
     * 获取单个提供者实现
     */
    getProvider<T = any>(extensionPointId: string): T | undefined;
    /**
     * 执行钩子扩展点
     */
    executeHook(extensionPointId: string, ...args: any[]): Promise<any[]>;
    /**
     * 检查扩展点是否存在
     */
    hasExtensionPoint(extensionPointId: string): boolean;
    /**
     * 检查扩展是否存在
     */
    hasExtension(extensionId: string): boolean;
    /**
     * 激活扩展
     */
    activateExtension(extensionId: string): boolean;
    /**
     * 停用扩展
     */
    deactivateExtension(extensionId: string): boolean;
    /**
     * 获取所有扩展点
     */
    getAllExtensionPoints(): PluginExtensionPoint[];
    /**
     * 获取所有扩展实现
     */
    getAllExtensions(): PluginExtension[];
    /**
     * 获取插件的扩展点
     */
    getPluginExtensionPoints(pluginId: string): PluginExtensionPoint[];
    /**
     * 获取插件的扩展实现
     */
    getPluginExtensions(pluginId: string): PluginExtension[];
    /**
     * 清理插件的所有扩展点和扩展
     */
    cleanupPlugin(pluginId: string): void;
    /**
     * 获取统计信息
     */
    getStats(): {
        totalExtensionPoints: number;
        totalExtensions: number;
        activeExtensions: number;
        extensionPointsByType: Record<string, number>;
        averageExtensionsPerPoint: number;
    };
    /**
     * 清空所有扩展点和扩展
     */
    clear(): void;
    /**
     * 验证扩展点定义
     */
    private validateExtensionPoint;
    /**
     * 验证扩展实现
     */
    private validateExtension;
    /**
     * 验证扩展实现与扩展点的兼容性
     */
    private validateExtensionImplementation;
}
//# sourceMappingURL=extension-manager.d.ts.map