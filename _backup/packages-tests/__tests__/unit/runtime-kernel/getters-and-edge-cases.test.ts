/**
 * @fileoverview MicroCoreKernel 获取器方法和边界情况测试
 * @description 测试内核的获取器方法、边界情况和错误处理
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { MicroCoreError } from '../../../src/errors';
import { MicroCoreKernel } from '../../../src/runtime/kernel';

// Mock 依赖模块
vi.mock('../../../src/communication/event-bus');
vi.mock('../../../src/runtime/app-registry');
vi.mock('../../../src/runtime/lifecycle-manager');
vi.mock('../../../src/utils');

describe('MicroCoreKernel 获取器方法和边界情况', () => {
    let kernel: MicroCoreKernel;
    let mockEventBus: any;
    let mockAppRegistry: any;
    let mockLifecycleManager: any;
    let mockLogger: any;

    beforeEach(() => {
        // 创建模拟对象
        mockEventBus = {
            emit: vi.fn(),
            on: vi.fn(),
            off: vi.fn(),
            clear: vi.fn()
        };

        mockAppRegistry = {
            register: vi.fn(),
            unregister: vi.fn(),
            get: vi.fn(),
            getAll: vi.fn(),
            getByStatus: vi.fn(),
            updateStatus: vi.fn(),
            setError: vi.fn(),
            clear: vi.fn()
        };

        mockLifecycleManager = {
            bootstrap: vi.fn(),
            mount: vi.fn(),
            unmount: vi.fn(),
            clearHooks: vi.fn()
        };

        mockLogger = {
            debug: vi.fn(),
            info: vi.fn(),
            warn: vi.fn(),
            error: vi.fn()
        };

        // Mock 构造函数
        const { EventBus } = require('../../../src/communication/event-bus');
        const { AppRegistry } = require('../../../src/runtime/app-registry');
        const { LifecycleManager } = require('../../../src/runtime/lifecycle-manager');
        const { createLogger } = require('../../../src/utils');

        EventBus.mockImplementation(() => mockEventBus);
        AppRegistry.mockImplementation(() => mockAppRegistry);
        LifecycleManager.mockImplementation(() => mockLifecycleManager);
        createLogger.mockReturnValue(mockLogger);

        kernel = new MicroCoreKernel();
    });

    afterEach(() => {
        vi.clearAllMocks();
        vi.resetModules();
    });

    describe('获取器方法', () => {
        describe('getEventBus', () => {
            it('应该返回事件总线实例', () => {
                const result = kernel.getEventBus();
                expect(result).toBe(mockEventBus);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                expect(() => {
                    kernel.getEventBus();
                }).toThrow(MicroCoreError);
            });

            it('应该返回相同的实例', () => {
                const eventBus1 = kernel.getEventBus();
                const eventBus2 = kernel.getEventBus();
                expect(eventBus1).toBe(eventBus2);
            });
        });

        describe('getAppRegistry', () => {
            it('应该返回应用注册中心实例', () => {
                const result = kernel.getAppRegistry();
                expect(result).toBe(mockAppRegistry);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                expect(() => {
                    kernel.getAppRegistry();
                }).toThrow(MicroCoreError);
            });

            it('应该返回相同的实例', () => {
                const registry1 = kernel.getAppRegistry();
                const registry2 = kernel.getAppRegistry();
                expect(registry1).toBe(registry2);
            });
        });

        describe('getLifecycleManager', () => {
            it('应该返回生命周期管理器实例', () => {
                const result = kernel.getLifecycleManager();
                expect(result).toBe(mockLifecycleManager);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                expect(() => {
                    kernel.getLifecycleManager();
                }).toThrow(MicroCoreError);
            });

            it('应该返回相同的实例', () => {
                const manager1 = kernel.getLifecycleManager();
                const manager2 = kernel.getLifecycleManager();
                expect(manager1).toBe(manager2);
            });
        });
    });

    describe('边界情况和错误处理', () => {
        describe('错误类型处理', () => {
            it('应该处理字符串类型的错误', async () => {
                const mockApp: any = {
                    name: 'test-app',
                    status: 'NOT_LOADED'
                };

                mockAppRegistry.get.mockReturnValue(mockApp);
                mockAppRegistry.updateStatus.mockImplementation(() => {
                    throw 'string error';
                });

                await expect(kernel.loadApplication('test-app')).rejects.toThrow();
                expect(mockAppRegistry.setError).toHaveBeenCalledWith('test-app', expect.any(Error));
            });

            it('应该处理null错误', async () => {
                const mockApp: any = {
                    name: 'test-app',
                    status: 'NOT_LOADED'
                };

                mockAppRegistry.get.mockReturnValue(mockApp);
                mockAppRegistry.updateStatus.mockImplementation(() => {
                    throw null;
                });

                await expect(kernel.loadApplication('test-app')).rejects.toThrow();
                expect(mockAppRegistry.setError).toHaveBeenCalledWith('test-app', expect.any(Error));
            });

            it('应该处理undefined错误', async () => {
                const mockApp: any = {
                    name: 'test-app',
                    status: 'NOT_LOADED'
                };

                mockAppRegistry.get.mockReturnValue(mockApp);
                mockAppRegistry.updateStatus.mockImplementation(() => {
                    throw undefined;
                });

                await expect(kernel.loadApplication('test-app')).rejects.toThrow();
                expect(mockAppRegistry.setError).toHaveBeenCalledWith('test-app', expect.any(Error));
            });

            it('应该处理对象类型的错误', async () => {
                const mockApp: any = {
                    name: 'test-app',
                    status: 'NOT_LOADED'
                };

                mockAppRegistry.get.mockReturnValue(mockApp);
                mockAppRegistry.updateStatus.mockImplementation(() => {
                    throw { message: 'object error', code: 500 };
                });

                await expect(kernel.loadApplication('test-app')).rejects.toThrow();
                expect(mockAppRegistry.setError).toHaveBeenCalledWith('test-app', expect.any(Error));
            });
        });

        describe('参数验证', () => {
            it('应该处理null参数', () => {
                expect(() => {
                    kernel.registerApplication(null as any);
                }).toThrow(MicroCoreError);

                expect(() => {
                    kernel.unregisterApplication(null as any);
                }).toThrow();

                expect(() => {
                    kernel.use(null as any);
                }).toThrow(MicroCoreError);
            });

            it('应该处理undefined参数', () => {
                expect(() => {
                    kernel.registerApplication(undefined as any);
                }).toThrow(MicroCoreError);

                expect(() => {
                    kernel.unregisterApplication(undefined as any);
                }).toThrow();

                expect(() => {
                    kernel.use(undefined as any);
                }).toThrow(MicroCoreError);
            });

            it('应该处理空对象参数', () => {
                expect(() => {
                    kernel.registerApplication({} as any);
                }).toThrow(MicroCoreError);

                expect(() => {
                    kernel.use({} as any);
                }).toThrow(MicroCoreError);
            });

            it('应该处理无效的应用配置', () => {
                const invalidConfigs = [
                    { name: '' }, // 空名称
                    { name: 'test' }, // 缺少必需字段
                    { name: 'test', entry: '' }, // 空入口
                    { name: 'test', entry: 'invalid-url' }, // 无效URL
                ];

                invalidConfigs.forEach(config => {
                    expect(() => {
                        kernel.registerApplication(config as any);
                    }).toThrow();
                });
            });
        });

        describe('状态一致性', () => {
            it('应该在操作失败后保持状态一致性', async () => {
                const mockApp: any = {
                    name: 'test-app',
                    status: 'NOT_LOADED',
                    config: {
                        name: 'test-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                };

                mockAppRegistry.get.mockReturnValue(mockApp);
                mockLifecycleManager.mount.mockRejectedValue(new Error('挂载失败'));

                // 尝试挂载失败
                await expect(kernel.mountApplication('test-app')).rejects.toThrow();

                // 内核状态应该保持一致
                expect(kernel.isKernelStarted()).toBe(false);
                expect(kernel.isKernelDestroyed()).toBe(false);
            });

            it('应该在并发操作时保持状态一致性', async () => {
                await kernel.start();

                const operations = [
                    kernel.stop(),
                    kernel.stop(),
                    kernel.start()
                ];

                await Promise.allSettled(operations);

                // 最终状态应该是一致的
                const finalState = kernel.isKernelStarted();
                expect(typeof finalState).toBe('boolean');
            });
        });

        describe('内存和资源管理', () => {
            it('应该正确处理大量操作', async () => {
                const initialMemory = process.memoryUsage?.()?.heapUsed || 0;

                // 执行大量操作
                for (let i = 0; i < 1000; i++) {
                    const appConfig: any = {
                        name: `app-${i}`,
                        entry: `http://localhost:${3000 + i}`,
                        container: `#app-${i}`,
                        activeWhen: `/app-${i}`
                    };

                    kernel.registerApplication(appConfig);

                    const plugin: any = {
                        name: `plugin-${i}`,
                        version: '1.0.0'
                    };

                    kernel.use(plugin);
                }

                const afterOperationsMemory = process.memoryUsage?.()?.heapUsed || 0;

                // 销毁内核
                await kernel.destroy();

                // 强制垃圾回收（如果可用）
                if (global.gc) {
                    global.gc();
                }

                const afterDestroyMemory = process.memoryUsage?.()?.heapUsed || 0;

                // 内存使用应该在合理范围内
                const memoryIncrease = afterOperationsMemory - initialMemory;
                const memoryAfterDestroy = afterDestroyMemory - initialMemory;

                expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024); // 小于100MB
                expect(memoryAfterDestroy).toBeLessThan(memoryIncrease); // 销毁后内存应该减少
            });

            it('应该处理循环引用', () => {
                const circularPlugin: any = {
                    name: 'circular-plugin',
                    version: '1.0.0'
                };

                // 创建循环引用
                circularPlugin.self = circularPlugin;

                expect(() => {
                    kernel.use(circularPlugin);
                }).not.toThrow();

                expect(kernel.hasPlugin('circular-plugin')).toBe(true);
            });
        });

        describe('异步操作处理', () => {
            it('应该正确处理Promise rejection', async () => {
                const mockApp: any = {
                    name: 'test-app',
                    status: 'NOT_MOUNTED',
                    config: {
                        name: 'test-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                };

                mockAppRegistry.get.mockReturnValue(mockApp);
                mockLifecycleManager.mount.mockRejectedValue(new Error('异步错误'));

                await expect(kernel.mountApplication('test-app')).rejects.toThrow('异步错误');
            });

            it('应该处理超时情况', async () => {
                const mockApp: any = {
                    name: 'test-app',
                    status: 'NOT_MOUNTED',
                    config: {
                        name: 'test-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                };

                mockAppRegistry.get.mockReturnValue(mockApp);

                // 模拟超时
                mockLifecycleManager.mount.mockImplementation(() => {
                    return new Promise((_, reject) => {
                        setTimeout(() => reject(new Error('操作超时')), 100);
                    });
                });

                await expect(kernel.mountApplication('test-app')).rejects.toThrow('操作超时');
            });
        });

        describe('环境兼容性', () => {
            it('应该在不同环境下正常工作', () => {
                // 模拟不同的全局环境
                const originalWindow = global.window;
                const originalProcess = global.process;

                try {
                    // 浏览器环境
                    global.window = {} as any;
                    delete global.process;

                    const browserKernel = new MicroCoreKernel();
                    expect(browserKernel).toBeInstanceOf(MicroCoreKernel);

                    // Node.js 环境
                    delete global.window;
                    global.process = { env: {} } as any;

                    const nodeKernel = new MicroCoreKernel();
                    expect(nodeKernel).toBeInstanceOf(MicroCoreKernel);

                } finally {
                    // 恢复原始环境
                    global.window = originalWindow;
                    global.process = originalProcess;
                }
            });

            it('应该处理缺失的API', () => {
                const originalConsole = global.console;

                try {
                    // 模拟缺失console API
                    delete global.console;

                    expect(() => {
                        new MicroCoreKernel();
                    }).not.toThrow();

                } finally {
                    global.console = originalConsole;
                }
            });
        });

        describe('错误恢复', () => {
            it('应该能从错误状态恢复', async () => {
                // 模拟启动失败
                mockEventBus.emit.mockImplementationOnce((event: string) => {
                    if (event === 'kernel:starting') {
                        throw new Error('启动失败');
                    }
                });

                await expect(kernel.start()).rejects.toThrow();

                // 修复问题后应该能正常启动
                mockEventBus.emit.mockImplementation(() => { });

                await expect(kernel.start()).resolves.toBeUndefined();
                expect(kernel.isKernelStarted()).toBe(true);
            });

            it('应该处理部分失败的批量操作', async () => {
                const apps = [
                    { name: 'app1', status: 'NOT_LOADED' },
                    { name: 'app2', status: 'NOT_LOADED' },
                    { name: 'app3', status: 'NOT_LOADED' }
                ];

                mockAppRegistry.get.mockImplementation((name: string) =>
                    apps.find(app => app.name === name)
                );

                // 模拟第二个应用加载失败
                mockAppRegistry.updateStatus.mockImplementation((name: string) => {
                    if (name === 'app2') {
                        throw new Error('加载失败');
                    }
                });

                const results = await Promise.allSettled([
                    kernel.loadApplication('app1'),
                    kernel.loadApplication('app2'),
                    kernel.loadApplication('app3')
                ]);

                expect(results[0].status).toBe('fulfilled');
                expect(results[1].status).toBe('rejected');
                expect(results[2].status).toBe('fulfilled');
            });
        });
    });
});