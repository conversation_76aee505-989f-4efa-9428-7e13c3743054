/**
 * Namespace 沙箱策略实现
 *
 * @description 使用命名空间实现的轻量级沙箱隔离策略
 * <AUTHOR> <<EMAIL>>
 */
import type { SandboxConfig, SandboxContext, SandboxPerformanceMetrics, SandboxStrategy } from '../types';
/**
 * Namespace 沙箱策略
 * 使用命名空间实现轻量级沙箱隔离
 */
export declare class NamespaceSandbox implements SandboxStrategy {
    readonly type: "namespace";
    readonly name = "Namespace\u6C99\u7BB1\u7B56\u7565";
    private readonly logger;
    private readonly contexts;
    private readonly globalNamespace;
    constructor();
    /**
     * 创建沙箱
     */
    create(config: SandboxConfig): Promise<SandboxContext>;
    /**
     * 激活沙箱
     */
    activate(context: SandboxContext): Promise<void>;
    /**
     * 停用沙箱
     */
    deactivate(context: SandboxContext): Promise<void>;
    /**
     * 销毁沙箱
     */
    destroy(context: SandboxContext): Promise<void>;
    /**
     * 执行代码
     */
    execute(context: SandboxContext, code: string): Promise<any>;
    /**
     * 获取性能指标
     */
    getMetrics(context: SandboxContext): SandboxPerformanceMetrics;
    /**
     * 初始化全局命名空间
     */
    private initializeGlobalNamespace;
    /**
     * 生成命名空间名称
     */
    private generateNamespace;
    /**
     * 创建命名空间对象
     */
    private createNamespaceObject;
    /**
     * 注册命名空间
     */
    private registerNamespace;
    /**
     * 注销命名空间
     */
    private unregisterNamespace;
    /**
     * 获取命名空间
     */
    private getNamespace;
    /**
     * 创建沙箱全局对象
     */
    private createSandboxGlobal;
    /**
     * 激活命名空间
     */
    private activateNamespace;
    /**
     * 停用命名空间
     */
    private deactivateNamespace;
    /**
     * 应用命名空间隔离
     */
    private applyNamespaceIsolation;
    /**
     * 移除命名空间隔离
     */
    private removeNamespaceIsolation;
    /**
     * 设置模块隔离
     */
    private setupModuleIsolation;
    /**
     * 清理模块隔离
     */
    private cleanupModuleIsolation;
    /**
     * 在命名空间环境中执行代码
     */
    private executeInNamespace;
    /**
     * 计算内存使用量
     */
    private calculateMemoryUsage;
    /**
     * 估算值的大小
     */
    private estimateValueSize;
    /**
     * 获取内部上下文
     */
    private getInternalContext;
    /**
     * 生成沙箱ID
     */
    private generateSandboxId;
    /**
     * 创建初始性能指标
     */
    private createInitialMetrics;
}
//# sourceMappingURL=namespace-sandbox.d.ts.map