/**
 * @fileoverview 存储工具函数
 * @description 提供localStorage和sessionStorage的封装功能
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 存储类型
 */
export type StorageType = 'localStorage' | 'sessionStorage';
/**
 * 设置存储项
 */
export declare function setItem(key: string, value: any, type?: StorageType): boolean;
/**
 * 获取存储项
 */
export declare function getItem<T = any>(key: string, defaultValue?: T, type?: StorageType): T | undefined;
/**
 * 移除存储项
 */
export declare function removeItem(key: string, type?: StorageType): boolean;
/**
 * 清空存储
 */
export declare function clear(type?: StorageType): boolean;
/**
 * 检查存储项是否存在
 */
export declare function hasItem(key: string, type?: StorageType): boolean;
/**
 * 获取所有存储键
 */
export declare function getKeys(type?: StorageType): string[];
/**
 * 获取存储大小（字节）
 */
export declare function getSize(type?: StorageType): number;
/**
 * 带过期时间的存储
 */
export declare function setItemWithExpiry(key: string, value: any, expiryMs: number, type?: StorageType): boolean;
/**
 * 获取带过期时间的存储项
 */
export declare function getItemWithExpiry<T = any>(key: string, defaultValue?: T, type?: StorageType): T | undefined;
//# sourceMappingURL=storage.d.ts.map