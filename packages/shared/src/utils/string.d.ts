/**
 * @fileoverview 字符串处理工具函数
 * @description 提供各种字符串操作和转换功能
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 转换为驼峰命名
 */
export declare function camelCase(str: string): string;
/**
 * 转换为短横线命名
 */
export declare function kebabCase(str: string): string;
/**
 * 转换为帕斯卡命名
 */
export declare function pascalCase(str: string): string;
/**
 * 转换为下划线命名
 */
export declare function snakeCase(str: string): string;
/**
 * 首字母大写
 */
export declare function capitalize(str: string): string;
/**
 * 首字母小写
 */
export declare function uncapitalize(str: string): string;
/**
 * 截断字符串
 */
export declare function truncate(str: string, length: number, suffix?: string): string;
/**
 * 左侧填充
 */
export declare function padStart(str: string, length: number, fillString?: string): string;
/**
 * 右侧填充
 */
export declare function padEnd(str: string, length: number, fillString?: string): string;
/**
 * 移除空白字符
 */
export declare function removeWhitespace(str: string): string;
/**
 * HTML转义
 */
export declare function escapeHtml(str: string): string;
/**
 * HTML反转义
 */
export declare function unescapeHtml(str: string): string;
/**
 * 生成随机ID
 */
export declare function generateId(prefix?: string, length?: number): string;
/**
 * 字符串哈希
 */
export declare function hashString(str: string): number;
/**
 * 版本号比较
 */
export declare function compareVersions(version1: string, version2: string): number;
/**
 * 解析版本号
 */
export declare function parseVersion(version: string): {
    major: number;
    minor: number;
    patch: number;
};
/**
 * 格式化字节大小
 */
export declare function formatBytes(bytes: number, decimals?: number): string;
/**
 * 生成URL友好的slug
 */
export declare function slugify(str: string): string;
/**
 * 提取域名
 */
export declare function extractDomain(url: string): string;
/**
 * 验证邮箱格式
 */
export declare function isValidEmail(email: string): boolean;
/**
 * 掩码字符串
 */
export declare function maskString(str: string, start?: number, end?: number, mask?: string): string;
/**
 * 生成随机字符串
 */
export declare function randomString(length?: number, chars?: string): string;
/**
 * 模板字符串替换
 */
export declare function template(str: string, data: Record<string, any>): string;
/**
 * 单词复数化
 */
export declare function pluralize(word: string, count?: number): string;
/**
 * 单词单数化
 */
export declare function singularize(word: string): string;
//# sourceMappingURL=string.d.ts.map