/**
 * @fileoverview 生命周期管理器
 * @description 管理微应用的生命周期状态和转换
 * <AUTHOR> <<EMAIL>>
 */

import { createLogger } from '@micro-core/shared'

const logger = createLogger('LifecycleManager')

export enum ApplicationStatus {
    NOT_LOADED = 'NOT_LOADED',
    LOADING = 'LOADING',
    NOT_BOOTSTRAPPED = 'NOT_BOOTSTRAPPED',
    BOOTSTRAPPING = 'BOOTSTRAPPING',
    NOT_MOUNTED = 'NOT_MOUNTED',
    MOUNTING = 'MOUNTING',
    MOUNTED = 'MOUNTED',
    UNMOUNTING = 'UNMOUNTING',
    UNLOADING = 'UNLOADING',
    LOAD_ERROR = 'LOAD_ERROR',
    SKIP_BECAUSE_BROKEN = 'SKIP_BECAUSE_BROKEN'
}

export interface ApplicationLifecycle {
    bootstrap?: () => Promise<void>
    mount?: () => Promise<void>
    unmount?: () => Promise<void>
    unload?: () => Promise<void>
}

export interface ApplicationInfo {
    name: string
    status: ApplicationStatus
    lifecycle: ApplicationLifecycle
    loadPromise?: Promise<void>
    bootstrapPromise?: Promise<void>
    mountPromise?: Promise<void>
    unmountPromise?: Promise<void>
    unloadPromise?: Promise<void>
}

/**
 * 生命周期管理器
 * 负责管理微应用的生命周期状态转换
 */
export class LifecycleManager {
    private applications = new Map<string, ApplicationInfo>()

    /**
     * 注册应用
     */
    registerApplication(name: string, lifecycle: ApplicationLifecycle): void {
        if (this.applications.has(name)) {
            logger.warn(`应用 ${name} 已经注册`)
            return
        }

        this.applications.set(name, {
            name,
            status: ApplicationStatus.NOT_LOADED,
            lifecycle
        })

        logger.info(`应用 ${name} 注册成功`)
    }

    /**
     * 获取应用信息
     */
    getApplication(name: string): ApplicationInfo | undefined {
        return this.applications.get(name)
    }

    /**
     * 获取应用状态
     */
    getApplicationStatus(name: string): ApplicationStatus | undefined {
        return this.applications.get(name)?.status
    }

    /**
     * 设置应用状态
     */
    setApplicationStatus(name: string, status: ApplicationStatus): void {
        const app = this.applications.get(name)
        if (app) {
            app.status = status
            logger.debug(`应用 ${name} 状态变更为 ${status}`)
        }
    }

    /**
     * 加载应用
     */
    async loadApplication(name: string): Promise<void> {
        const app = this.applications.get(name)
        if (!app) {
            throw new Error(`应用 ${name} 未注册`)
        }

        if (app.status !== ApplicationStatus.NOT_LOADED) {
            return
        }

        if (app.loadPromise) {
            return app.loadPromise
        }

        this.setApplicationStatus(name, ApplicationStatus.LOADING)

        app.loadPromise = this.performLoad(app)

        try {
            await app.loadPromise
            this.setApplicationStatus(name, ApplicationStatus.NOT_BOOTSTRAPPED)
        } catch (error) {
            this.setApplicationStatus(name, ApplicationStatus.LOAD_ERROR)
            throw error
        }
    }

    /**
     * 引导应用
     */
    async bootstrapApplication(name: string): Promise<void> {
        const app = this.applications.get(name)
        if (!app) {
            throw new Error(`应用 ${name} 未注册`)
        }

        if (app.status !== ApplicationStatus.NOT_BOOTSTRAPPED) {
            return
        }

        if (app.bootstrapPromise) {
            return app.bootstrapPromise
        }

        this.setApplicationStatus(name, ApplicationStatus.BOOTSTRAPPING)

        app.bootstrapPromise = this.performBootstrap(app)

        try {
            await app.bootstrapPromise
            this.setApplicationStatus(name, ApplicationStatus.NOT_MOUNTED)
        } catch (error) {
            this.setApplicationStatus(name, ApplicationStatus.SKIP_BECAUSE_BROKEN)
            throw error
        }
    }

    /**
     * 挂载应用
     */
    async mountApplication(name: string): Promise<void> {
        const app = this.applications.get(name)
        if (!app) {
            throw new Error(`应用 ${name} 未注册`)
        }

        if (app.status !== ApplicationStatus.NOT_MOUNTED) {
            return
        }

        if (app.mountPromise) {
            return app.mountPromise
        }

        this.setApplicationStatus(name, ApplicationStatus.MOUNTING)

        app.mountPromise = this.performMount(app)

        try {
            await app.mountPromise
            this.setApplicationStatus(name, ApplicationStatus.MOUNTED)
        } catch (error) {
            this.setApplicationStatus(name, ApplicationStatus.SKIP_BECAUSE_BROKEN)
            throw error
        }
    }

    /**
     * 卸载应用
     */
    async unmountApplication(name: string): Promise<void> {
        const app = this.applications.get(name)
        if (!app) {
            throw new Error(`应用 ${name} 未注册`)
        }

        if (app.status !== ApplicationStatus.MOUNTED) {
            return
        }

        if (app.unmountPromise) {
            return app.unmountPromise
        }

        this.setApplicationStatus(name, ApplicationStatus.UNMOUNTING)

        app.unmountPromise = this.performUnmount(app)

        try {
            await app.unmountPromise
            this.setApplicationStatus(name, ApplicationStatus.NOT_MOUNTED)
        } catch (error) {
            this.setApplicationStatus(name, ApplicationStatus.SKIP_BECAUSE_BROKEN)
            throw error
        }
    }

    /**
     * 卸载应用
     */
    async unloadApplication(name: string): Promise<void> {
        const app = this.applications.get(name)
        if (!app) {
            throw new Error(`应用 ${name} 未注册`)
        }

        if (app.status === ApplicationStatus.NOT_LOADED) {
            return
        }

        if (app.unloadPromise) {
            return app.unloadPromise
        }

        this.setApplicationStatus(name, ApplicationStatus.UNLOADING)

        app.unloadPromise = this.performUnload(app)

        try {
            await app.unloadPromise
            this.setApplicationStatus(name, ApplicationStatus.NOT_LOADED)
        } catch (error) {
            this.setApplicationStatus(name, ApplicationStatus.SKIP_BECAUSE_BROKEN)
            throw error
        }
    }

    /**
     * 获取所有应用
     */
    getAllApplications(): ApplicationInfo[] {
        return Array.from(this.applications.values())
    }

    /**
     * 清理所有应用
     */
    clear(): void {
        this.applications.clear()
    }

    private async performLoad(app: ApplicationInfo): Promise<void> {
        // 加载逻辑
        logger.debug(`加载应用 ${app.name}`)
    }

    private async performBootstrap(app: ApplicationInfo): Promise<void> {
        if (app.lifecycle.bootstrap) {
            await app.lifecycle.bootstrap()
        }
        logger.debug(`引导应用 ${app.name}`)
    }

    private async performMount(app: ApplicationInfo): Promise<void> {
        if (app.lifecycle.mount) {
            await app.lifecycle.mount()
        }
        logger.debug(`挂载应用 ${app.name}`)
    }

    private async performUnmount(app: ApplicationInfo): Promise<void> {
        if (app.lifecycle.unmount) {
            await app.lifecycle.unmount()
        }
        logger.debug(`卸载应用 ${app.name}`)
    }

    private async performUnload(app: ApplicationInfo): Promise<void> {
        if (app.lifecycle.unload) {
            await app.lifecycle.unload()
        }
        logger.debug(`卸载应用 ${app.name}`)
    }
}