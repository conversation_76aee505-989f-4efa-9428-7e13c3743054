/**
 * 共享工具包 - 错误处理模块
 *
 * @description 提供统一的错误处理机制和错误类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import type { ErrorCode } from '../constants';
/**
 * Micro-Core 基础错误类
 */
export declare class MicroCoreError extends Error {
    /** 错误码 */
    readonly code: ErrorCode;
    /** 错误数据 */
    readonly data?: Record<string, any>;
    /** 原始错误 */
    readonly cause?: Error;
    /** 错误时间戳 */
    readonly timestamp: number;
    constructor(code: ErrorCode, message: string, data?: Record<string, any>, cause?: Error);
    /**
     * 创建系统错误
     */
    static system(message: string, data?: Record<string, any>, cause?: Error): MicroCoreError;
    /**
     * 创建配置错误
     */
    static config(message: string, data?: Record<string, any>, cause?: Error): MicroCoreError;
    /**
     * 创建应用错误
     */
    static app(message: string, data?: Record<string, any>, cause?: Error): MicroCoreError;
    /**
     * 创建沙箱错误
     */
    static sandbox(message: string, data?: Record<string, any>, cause?: Error): MicroCoreError;
    /**
     * 创建插件错误
     */
    static plugin(message: string, data?: Record<string, any>, cause?: Error): MicroCoreError;
    /**
     * 创建通信错误
     */
    static communication(message: string, data?: Record<string, any>, cause?: Error): MicroCoreError;
    /**
     * 创建资源错误
     */
    static resource(message: string, data?: Record<string, any>, cause?: Error): MicroCoreError;
    /**
     * 转换为JSON对象
     */
    toJSON(): Record<string, any>;
    /**
     * 转换为字符串
     */
    toString(): string;
}
/**
 * 创建通用错误
 */
export declare function createError(code: ErrorCode, message?: string, data?: Record<string, any>, cause?: Error): MicroCoreError;
/**
 * 创建系统错误
 */
export declare function createSystemError(code: ErrorCode, message?: string, data?: Record<string, any>, cause?: Error): MicroCoreError;
/**
 * 创建应用错误
 */
export declare function createAppError(code: ErrorCode, appName?: string, message?: string, data?: Record<string, any>, cause?: Error): MicroCoreError;
/**
 * 错误处理器类
 */
export declare class ErrorHandler {
    private handlers;
    private globalHandlers;
    /**
     * 注册错误处理器
     */
    register(category: string | 'global', handler: (error: MicroCoreError) => void): void;
    /**
     * 处理错误
     */
    handle(error: MicroCoreError): void;
    /**
     * 清除所有处理器
     */
    clear(): void;
}
export declare const globalErrorHandler: ErrorHandler;
//# sourceMappingURL=index.d.ts.map