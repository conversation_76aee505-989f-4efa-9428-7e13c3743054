/**
 * Props 桥接器 - 处理应用间属性和事件通信
 */

import { EventBusMessage } from './types';

export class PropsBridge {
    private eventBus: EventTarget = new EventTarget();
    private globalState: Map<string, any> = new Map();
    private subscribers: Map<string, Set<(data: any) => void>> = new Map();

    /**
     * 初始化 props 桥接器
     */
    initialize(): void {
        // 创建全局事件总线
        this.setupGlobalEventBus();
    }

    /**
     * 设置全局事件总线
     */
    private setupGlobalEventBus(): void {
        // 在 window 上暴露事件总线 API
        (window as any).wujieBus = {
            on: this.on.bind(this),
            off: this.off.bind(this),
            emit: this.emit.bind(this),
            once: this.once.bind(this)
        };
    }

    /**
     * 监听事件
     */
    on(eventType: string, callback: (data: any) => void): void {
        if (!this.subscribers.has(eventType)) {
            this.subscribers.set(eventType, new Set());
        }
        this.subscribers.get(eventType)!.add(callback);

        // 同时在 EventTarget 上监听
        this.eventBus.addEventListener(eventType, (event: any) => {
            callback(event.detail);
        });
    }

    /**
     * 取消监听事件
     */
    off(eventType: string, callback: (data: any) => void): void {
        const subscribers = this.subscribers.get(eventType);
        if (subscribers) {
            subscribers.delete(callback);
            if (subscribers.size === 0) {
                this.subscribers.delete(eventType);
            }
        }

        // 从 EventTarget 移除监听器
        this.eventBus.removeEventListener(eventType, callback as EventListener);
    }

    /**
     * 触发事件
     */
    emit(eventType: string, data: any): void {
        // 通过 EventTarget 分发事件
        const event = new CustomEvent(eventType, { detail: data });
        this.eventBus.dispatchEvent(event);

        // 通知订阅者
        const subscribers = this.subscribers.get(eventType);
        if (subscribers) {
            subscribers.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event callback for ${eventType}:`, error);
                }
            });
        }
    }

    /**
     * 监听一次事件
     */
    once(eventType: string, callback: (data: any) => void): void {
        const onceCallback = (data: any) => {
            callback(data);
            this.off(eventType, onceCallback);
        };
        this.on(eventType, onceCallback);
    }

    /**
     * 设置全局状态
     */
    setGlobalState(key: string, value: any): void {
        const oldValue = this.globalState.get(key);
        this.globalState.set(key, value);

        // 触发状态变更事件
        this.emit('globalStateChange', {
            key,
            value,
            oldValue,
            timestamp: Date.now()
        });
    }

    /**
     * 获取全局状态
     */
    getGlobalState(key: string): any {
        return this.globalState.get(key);
    }

    /**
     * 获取所有全局状态
     */
    getAllGlobalState(): Record<string, any> {
        const state: Record<string, any> = {};
        this.globalState.forEach((value, key) => {
            state[key] = value;
        });
        return state;
    }

    /**
     * 监听全局状态变更
     */
    onGlobalStateChange(callback: (change: { key: string; value: any; oldValue: any; timestamp: number }) => void): void {
        this.on('globalStateChange', callback);
    }

    /**
     * 发送消息到指定应用
     */
    sendToApp(appName: string, message: any): void {
        const eventMessage: EventBusMessage = {
            type: 'app-message',
            data: message,
            from: 'main-app',
            to: appName,
            timestamp: Date.now()
        };

        this.emit(`app-message-${appName}`, eventMessage);
    }

    /**
     * 监听来自指定应用的消息
     */
    onAppMessage(appName: string, callback: (message: any) => void): void {
        this.on(`app-message-from-${appName}`, callback);
    }

    /**
     * 广播消息到所有应用
     */
    broadcast(message: any): void {
        const eventMessage: EventBusMessage = {
            type: 'broadcast',
            data: message,
            from: 'main-app',
            timestamp: Date.now()
        };

        this.emit('broadcast', eventMessage);
    }

    /**
     * 获取事件总线实例
     */
    getEventBus(): EventTarget {
        return this.eventBus;
    }

    /**
     * 销毁 props 桥接器
     */
    destroy(): void {
        // 清空所有订阅者
        this.subscribers.clear();

        // 清空全局状态
        this.globalState.clear();

        // 移除全局事件总线
        delete (window as any).wujieBus;
    }
}