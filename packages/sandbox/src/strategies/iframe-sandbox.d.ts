/**
 * iframe 沙箱策略实现
 *
 * @description 使用 iframe 实现的完全隔离沙箱策略
 * <AUTHOR> <<EMAIL>>
 */
import type { SandboxConfig, SandboxContext, SandboxPerformanceMetrics, SandboxStrategy } from '../types';
/**
 * iframe 沙箱策略
 * 使用 iframe 实现完全隔离的沙箱环境
 */
export declare class IframeSandbox implements SandboxStrategy {
    readonly type: "iframe";
    readonly name = "iframe\u6C99\u7BB1\u7B56\u7565";
    private readonly logger;
    private readonly contexts;
    constructor();
    /**
     * 创建沙箱
     */
    create(config: SandboxConfig): Promise<SandboxContext>;
    /**
     * 激活沙箱
     */
    activate(context: SandboxContext): Promise<void>;
    /**
     * 停用沙箱
     */
    deactivate(context: SandboxContext): Promise<void>;
    /**
     * 销毁沙箱
     */
    destroy(context: SandboxContext): Promise<void>;
    /**
     * 执行代码
     */
    execute(context: SandboxContext, code: string): Promise<any>;
    /**
     * 获取性能指标
     */
    getMetrics(context: SandboxContext): SandboxPerformanceMetrics;
    /**
     * 创建 iframe 元素
     */
    private createIframe;
    /**
     * 等待 iframe 加载完成
     */
    private waitForIframeLoad;
    /**
     * 设置沙箱环境
     */
    private setupSandboxEnvironment;
    /**
     * 注入基础 API
     */
    private injectBasicAPIs;
    /**
     * 设置全局变量限制
     */
    private setupGlobalVariableRestrictions;
    /**
     * 设置通信接口
     */
    private setupCommunicationInterface;
    /**
     * 设置消息监听
     */
    private setupMessageListeners;
    /**
     * 清理消息监听
     */
    private cleanupMessageListeners;
    /**
     * 处理 iframe 消息
     */
    private handleIframeMessage;
    /**
     * 在 iframe 中执行代码
     */
    private executeInIframe;
    /**
     * 显示 iframe
     */
    private showIframe;
    /**
     * 隐藏 iframe
     */
    private hideIframe;
    /**
     * 移除 iframe
     */
    private removeIframe;
    /**
     * 计算内存使用量
     */
    private calculateMemoryUsage;
    /**
     * 获取内部上下文
     */
    private getInternalContext;
    /**
     * 生成沙箱ID
     */
    private generateSandboxId;
    /**
     * 创建初始性能指标
     */
    private createInitialMetrics;
}
//# sourceMappingURL=iframe-sandbox.d.ts.map