/**
 * @fileoverview 事件工具函数
 * @description 提供事件发射器相关的工具函数
 * <AUTHOR> <<EMAIL>>
 */

export interface EventEmitter {
    on(event: string, handler: (data?: any) => void): void
    off(event: string, handler?: (data?: any) => void): void
    once(event: string, handler: (data?: any) => void): void
    emit(event: string, data?: any): void
    clear(): void
}

/**
 * 创建事件发射器
 */
export function createEventEmitter(): EventEmitter {
    const listeners = new Map<string, Set<Function>>()

    return {
        on(event: string, handler: (data?: any) => void): void {
            if (!listeners.has(event)) {
                listeners.set(event, new Set())
            }
            listeners.get(event)!.add(handler)
        },

        off(event: string, handler?: (data?: any) => void): void {
            if (!handler) {
                listeners.delete(event)
                return
            }
            const handlers = listeners.get(event)
            if (handlers) {
                handlers.delete(handler)
                if (handlers.size === 0) {
                    listeners.delete(event)
                }
            }
        },

        once(event: string, handler: (data?: any) => void): void {
            const onceHandler = (data?: any) => {
                handler(data)
                this.off(event, onceHandler)
            }
            this.on(event, onceHandler)
        },

        emit(event: string, data?: any): void {
            const handlers = listeners.get(event)
            if (handlers) {
                handlers.forEach(handler => {
                    try {
                        handler(data)
                    } catch (error) {
                        console.error(`事件处理器错误 [${event}]:`, error)
                    }
                })
            }
        },

        clear(): void {
            listeners.clear()
        }
    }
}