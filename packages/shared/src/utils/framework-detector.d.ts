/**
 * 共享工具包 - 框架检测工具
 *
 * @description 自动检测当前页面使用的前端框架
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import type { FrameworkType } from '../constants';
/**
 * 框架检测结果
 */
export interface FrameworkDetectionResult {
    framework: FrameworkType;
    version?: string;
    confidence: number;
    evidence: string[];
}
/**
 * 框架检测器
 */
export declare class FrameworkDetector {
    private detectionCache;
    private cacheExpiry;
    private cacheTime;
    /**
     * 检测框架
     */
    detect(forceRefresh?: boolean): FrameworkType;
    /**
     * 检测框架（带详细信息）
     */
    detectWithDetails(forceRefresh?: boolean): FrameworkDetectionResult;
    /**
     * 检测React
     */
    private detectReact;
    /**
     * 检测Vue
     */
    private detectVue;
    /**
     * 检测Angular
     */
    private detectAngular;
    /**
     * 检测Svelte
     */
    private detectSvelte;
    /**
     * 检测Solid
     */
    private detectSolid;
    /**
     * 检测原生JavaScript
     */
    private detectVanilla;
    /**
     * 清除缓存
     */
    clearCache(): void;
    /**
     * 获取支持的框架列表
     */
    getSupportedFrameworks(): FrameworkType[];
}
/**
 * 创建框架检测器
 */
export declare function createFrameworkDetector(): FrameworkDetector;
/**
 * 全局框架检测器实例
 */
export declare const globalFrameworkDetector: FrameworkDetector;
export default FrameworkDetector;
//# sourceMappingURL=framework-detector.d.ts.map