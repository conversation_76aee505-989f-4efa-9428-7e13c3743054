# 边车模式

边车模式（Sidecar Pattern）是 Micro-Core 的核心特性之一，它允许传统应用以零侵入的方式接入微前端架构，实现渐进式升级。

## 📋 目录

- [模式概述](#模式概述)
- [工作原理](#工作原理)
- [快速接入](#快速接入)
- [配置选项](#配置选项)
- [使用场景](#使用场景)
- [最佳实践](#最佳实践)

## 模式概述

### 🎯 什么是边车模式

边车模式是一种架构模式，通过在现有应用旁边部署一个"边车"代理，来扩展应用的功能而不修改原有代码。在微前端场景中，边车充当传统应用与微前端系统之间的桥梁。

### 🏗️ 架构设计

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    边车模式架构图                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    传统应用层                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ jQuery App  │  │ Legacy JS   │  │ Vanilla HTML        │ │ │
│  │  │ (无需修改)   │  │ (无需修改)   │  │ (无需修改)           │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    边车代理层                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 协议转换器   │  │ 生命周期适配 │  │ 事件桥接器           │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 样式隔离器   │  │ 路由适配器   │  │ 状态同步器           │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Micro-Core 核心                          │ │
│  │  • 应用管理    • 路由协调    • 状态管理                    │ │
│  │  • 事件总线    • 沙箱隔离    • 性能监控                    │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### ✨ 核心优势

- **🚫 零侵入**: 无需修改现有应用代码
- **🔄 渐进式**: 支持逐步迁移和升级
- **🛡️ 风险可控**: 保持原有应用稳定性
- **⚡ 快速接入**: 一行代码启用微前端
- **🔧 灵活配置**: 支持多种接入方式

## 工作原理

### 🔄 生命周期适配

```typescript
// 边车代理自动为传统应用生成标准生命周期
class SidecarAdapter {
  // 自动生成 bootstrap 函数
  async bootstrap() {
    // 检测应用类型
    const appType = this.detectAppType();
    
    // 初始化适配器
    await this.initializeAdapter(appType);
    
    // 设置事件桥接
    this.setupEventBridge();
  }
  
  // 自动生成 mount 函数
  async mount(props) {
    // 创建容器
    const container = this.createContainer(props.container);
    
    // 加载应用资源
    await this.loadAppResources();
    
    // 启动应用
    await this.startLegacyApp(container, props);
    
    // 建立通信桥梁
    this.establishCommunication();
  }
  
  // 自动生成 unmount 函数
  async unmount() {
    // 清理应用状态
    this.cleanupAppState();
    
    // 移除事件监听
    this.removeEventListeners();
    
    // 销毁容器
    this.destroyContainer();
  }
}
```

### 🌉 协议转换

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    协议转换流程                                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  传统应用事件 ──┐                                               │
│                │                                               │
│  ┌─────────────▼─────────────┐    ┌─────────────────────────────┐│
│  │      协议转换器            │───▶│    Micro-Core 事件总线      ││
│  │                           │    │                             ││
│  │ • jQuery 事件 → 标准事件   │    │ • 标准化事件格式            ││
│  │ • 全局变量 → 状态管理      │    │ • 类型安全                  ││
│  │ • DOM 操作 → 生命周期      │    │ • 错误处理                  ││
│  └───────────────────────────┘    └─────────────────────────────┘│
│                │                                               │
│                └──▶ 其他微应用                                  │
└─────────────────────────────────────────────────────────────────┘
```

## 快速接入

### 1. 一行代码接入

```html
<!-- 在传统应用的 HTML 中添加一行代码 -->
<script src="https://unpkg.com/@micro-core/sidecar/dist/sidecar.min.js"></script>
<script>
  // 自动启用微前端能力
  MicroCoreSidecar.enable();
</script>
```

### 2. 配置式接入

```javascript
// 更详细的配置
MicroCoreSidecar.enable({
  // 应用信息
  name: 'legacy-app',
  version: '1.0.0',
  
  // 自动检测配置
  autoDetect: {
    framework: true,    // 自动检测框架类型
    routes: true,       // 自动检测路由
    events: true        // 自动检测事件
  },
  
  // 通信配置
  communication: {
    eventBus: true,     // 启用事件总线
    globalState: true,  // 启用全局状态
    directCall: false   // 禁用直接调用
  },
  
  // 样式隔离
  styleIsolation: {
    enabled: true,
    mode: 'scoped',     // 'scoped' | 'shadow' | 'none'
    prefix: 'legacy-'
  },
  
  // 路由适配
  routing: {
    mode: 'hash',       // 'hash' | 'history' | 'memory'
    base: '/legacy',
    autoRegister: true
  }
});
```

### 3. 编程式接入

```typescript
import { SidecarAdapter } from '@micro-core/sidecar';

// 创建边车适配器
const sidecar = new SidecarAdapter({
  name: 'my-legacy-app',
  
  // 自定义生命周期
  lifecycle: {
    bootstrap: async () => {
      console.log('Legacy app bootstrapping...');
      // 自定义初始化逻辑
    },
    
    mount: async (props) => {
      console.log('Legacy app mounting...', props);
      // 自定义挂载逻辑
    },
    
    unmount: async () => {
      console.log('Legacy app unmounting...');
      // 自定义卸载逻辑
    }
  },
  
  // 事件映射
  eventMapping: {
    'legacy-event': 'standard-event',
    'custom-action': 'app-action'
  }
});

// 启用边车模式
sidecar.enable();
```

## 配置选项

### SidecarConfig 接口

```typescript
interface SidecarConfig {
  /** 应用名称 */
  name: string;
  
  /** 应用版本 */
  version?: string;
  
  /** 自动检测配置 */
  autoDetect?: {
    /** 自动检测框架类型 */
    framework?: boolean;
    /** 自动检测路由 */
    routes?: boolean;
    /** 自动检测事件 */
    events?: boolean;
    /** 自动检测全局变量 */
    globals?: boolean;
  };
  
  /** 生命周期配置 */
  lifecycle?: {
    bootstrap?: () => Promise<void>;
    mount?: (props: any) => Promise<void>;
    unmount?: () => Promise<void>;
    update?: (props: any) => Promise<void>;
  };
  
  /** 通信配置 */
  communication?: {
    /** 启用事件总线 */
    eventBus?: boolean;
    /** 启用全局状态 */
    globalState?: boolean;
    /** 启用直接调用 */
    directCall?: boolean;
    /** 事件映射 */
    eventMapping?: Record<string, string>;
  };
  
  /** 样式隔离配置 */
  styleIsolation?: {
    /** 是否启用 */
    enabled?: boolean;
    /** 隔离模式 */
    mode?: 'scoped' | 'shadow' | 'none';
    /** CSS 前缀 */
    prefix?: string;
    /** 排除选择器 */
    exclude?: string[];
  };
  
  /** 路由配置 */
  routing?: {
    /** 路由模式 */
    mode?: 'hash' | 'history' | 'memory';
    /** 基础路径 */
    base?: string;
    /** 自动注册路由 */
    autoRegister?: boolean;
    /** 路由映射 */
    routes?: Record<string, string>;
  };
  
  /** 沙箱配置 */
  sandbox?: {
    /** 沙箱类型 */
    type?: 'proxy' | 'snapshot' | 'none';
    /** 全局变量白名单 */
    globals?: string[];
    /** 严格模式 */
    strict?: boolean;
  };
}
```

### 框架特定配置

#### jQuery 应用

```javascript
MicroCoreSidecar.enable({
  name: 'jquery-app',
  
  // jQuery 特定配置
  jquery: {
    version: '3.6.0',
    noConflict: true,
    
    // 事件映射
    eventMapping: {
      'click': 'app:click',
      'submit': 'app:submit',
      'ready': 'app:ready'
    },
    
    // DOM 操作拦截
    domInterception: {
      enabled: true,
      methods: ['append', 'prepend', 'html', 'text']
    }
  }
});
```

#### 原生 JavaScript 应用

```javascript
MicroCoreSidecar.enable({
  name: 'vanilla-app',
  
  // 原生 JS 特定配置
  vanilla: {
    // 全局变量检测
    globalDetection: {
      enabled: true,
      whitelist: ['myApp', 'config', 'utils'],
      blacklist: ['eval', 'Function']
    },
    
    // DOM 事件自动绑定
    autoEventBinding: {
      enabled: true,
      events: ['click', 'submit', 'change'],
      selector: '[data-micro-event]'
    }
  }
});
```

## 使用场景

### 1. 遗留系统改造

```typescript
// 场景：将老旧的 jQuery 管理后台接入微前端
MicroCoreSidecar.enable({
  name: 'admin-legacy',
  
  // 保持原有功能不变
  preserveGlobals: ['$', 'jQuery', 'AdminConfig'],
  
  // 渐进式迁移
  migration: {
    strategy: 'progressive',
    modules: [
      { name: 'user-management', priority: 1 },
      { name: 'order-system', priority: 2 },
      { name: 'report-center', priority: 3 }
    ]
  },
  
  // 兼容性处理
  compatibility: {
    ie11: true,
    polyfills: ['Promise', 'fetch']
  }
});
```

### 2. 多技术栈整合

```typescript
// 场景：整合不同时期开发的多个系统
const apps = [
  {
    name: 'user-center',
    framework: 'jquery',
    entry: '/legacy/user/',
    activeRule: '/user'
  },
  {
    name: 'product-catalog',
    framework: 'vanilla',
    entry: '/legacy/product/',
    activeRule: '/product'
  },
  {
    name: 'order-system',
    framework: 'backbone',
    entry: '/legacy/order/',
    activeRule: '/order'
  }
];

apps.forEach(app => {
  MicroCoreSidecar.enableForApp(app);
});
```

### 3. 第三方系统集成

```typescript
// 场景：集成第三方供应商的系统
MicroCoreSidecar.enable({
  name: 'third-party-crm',
  
  // 第三方系统配置
  thirdParty: {
    vendor: 'SalesForce',
    version: '2.0',
    
    // API 代理
    apiProxy: {
      enabled: true,
      baseUrl: '/api/crm',
      headers: {
        'Authorization': 'Bearer ${token}'
      }
    },
    
    // 样式覆盖
    styleOverride: {
      enabled: true,
      customCSS: '/assets/crm-custom.css'
    }
  }
});
```

## 最佳实践

### 1. 渐进式迁移策略

```typescript
// 制定迁移计划
const migrationPlan = {
  phase1: {
    duration: '2 weeks',
    scope: '接入边车模式，保持现有功能',
    tasks: [
      '添加边车脚本',
      '配置基础通信',
      '验证功能完整性'
    ]
  },
  
  phase2: {
    duration: '4 weeks', 
    scope: '逐步替换核心模块',
    tasks: [
      '用户管理模块重构',
      '权限系统升级',
      '数据接口标准化'
    ]
  },
  
  phase3: {
    duration: '6 weeks',
    scope: '完整微前端化',
    tasks: [
      '移除边车依赖',
      '原生微前端改造',
      '性能优化'
    ]
  }
};

// 实施迁移
MicroCoreSidecar.migrate(migrationPlan);
```

### 2. 兼容性测试

```typescript
// 自动化兼容性测试
const compatibilityTests = {
  browsers: ['Chrome', 'Firefox', 'Safari', 'Edge', 'IE11'],
  
  testCases: [
    {
      name: '基础功能测试',
      tests: [
        'app-loading',
        'event-communication', 
        'style-isolation',
        'routing-navigation'
      ]
    },
    {
      name: '性能测试',
      tests: [
        'memory-usage',
        'loading-time',
        'interaction-response'
      ]
    }
  ],
  
  // 自动回滚机制
  rollback: {
    enabled: true,
    conditions: [
      'error-rate > 5%',
      'performance-degradation > 20%'
    ]
  }
};

MicroCoreSidecar.runCompatibilityTests(compatibilityTests);
```

### 3. 监控和调试

```typescript
// 边车模式专用监控
MicroCoreSidecar.enableMonitoring({
  // 性能监控
  performance: {
    enabled: true,
    metrics: ['loading', 'rendering', 'interaction'],
    threshold: {
      loading: 2000,    // 2秒
      rendering: 100,   // 100ms
      interaction: 50   // 50ms
    }
  },
  
  // 错误监控
  errorTracking: {
    enabled: true,
    level: 'warn',
    reportUrl: '/api/error-report'
  },
  
  // 调试模式
  debug: {
    enabled: process.env.NODE_ENV === 'development',
    console: true,
    visualIndicator: true
  }
});
```

### 4. 安全考虑

```typescript
// 安全配置
MicroCoreSidecar.enable({
  name: 'secure-legacy-app',
  
  security: {
    // CSP 配置
    contentSecurityPolicy: {
      enabled: true,
      directives: {
        'script-src': ["'self'", "'unsafe-inline'"],
        'style-src': ["'self'", "'unsafe-inline'"]
      }
    },
    
    // XSS 防护
    xssProtection: {
      enabled: true,
      sanitizeHTML: true,
      escapeOutput: true
    },
    
    // 权限控制
    permissions: {
      globalAccess: false,
      allowedAPIs: ['fetch', 'localStorage'],
      restrictedAPIs: ['eval', 'Function']
    }
  }
});
```

## 🔧 故障排除

### 常见问题

#### Q1: 边车模式下样式冲突？

**A:** 启用样式隔离：

```javascript
MicroCoreSidecar.enable({
  styleIsolation: {
    enabled: true,
    mode: 'scoped',
    prefix: 'legacy-app-'
  }
});
```

#### Q2: 全局变量冲突？

**A:** 配置变量隔离：

```javascript
MicroCoreSidecar.enable({
  sandbox: {
    type: 'proxy',
    globals: ['$', 'jQuery'], // 保留必要的全局变量
    strict: true
  }
});
```

#### Q3: 事件通信不生效？

**A:** 检查事件映射配置：

```javascript
MicroCoreSidecar.enable({
  communication: {
    eventBus: true,
    eventMapping: {
      'legacy-event': 'micro-event'
    }
  }
});
```

---

边车模式为传统应用提供了一条平滑的微前端升级路径，让你能够在不破坏现有系统的前提下，逐步享受微前端架构的优势。

## 🔗 相关资源

- **[快速开始](/guide/getting-started)** - 了解基础概念
- **[应用管理](/guide/features/app-management)** - 学习应用管理
- **[迁移指南](/migration/)** - 查看详细迁移方案
- **[最佳实践](/guide/best-practices/)** - 了解最佳实践