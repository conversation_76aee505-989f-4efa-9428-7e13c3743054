{"name": "sub-app-angular", "version": "0.1.0", "description": "Micro-Core Angular 子应用示例", "scripts": {"dev": "ng serve --port 4201", "build": "ng build", "serve": "ng serve --port 4201", "test": "ng test", "test:coverage": "ng test --code-coverage", "lint": "ng lint", "lint:fix": "ng lint --fix"}, "dependencies": {"@angular/animations": "^16.0.0", "@angular/common": "^16.0.0", "@angular/compiler": "^16.0.0", "@angular/core": "^16.0.0", "@angular/forms": "^16.0.0", "@angular/platform-browser": "^16.0.0", "@angular/platform-browser-dynamic": "^16.0.0", "@angular/router": "^16.0.0", "@micro-core/core": "workspace:*", "@micro-core/adapter-angular": "workspace:*", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.0.0", "@angular/cli": "~16.0.0", "@angular/compiler-cli": "^16.0.0", "@types/node": "^20.0.0", "typescript": "~5.3.0"}, "keywords": ["micro-frontend", "sub-app", "angular", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}