{"version": 3, "file": "state-utils.d.ts", "sourceRoot": "", "sources": ["state-utils.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,UAAU,CAAC;AAGtD;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAC7B,GAAG,EAAE,MAAM,CAAC;IACZ,QAAQ,EAAE,GAAG,CAAC;IACd,QAAQ,EAAE,GAAG,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;CACrB;AAED;;GAEG;AACH,cAAM,kBAAmB,YAAW,WAAW;IAC3C,OAAO,CAAC,KAAK,CAA0B;IACvC,OAAO,CAAC,QAAQ,CAAW;;IAM3B;;;;OAIG;IACH,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,IAAI;IAgBlC;;;;OAIG;IACH,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,CAAC,GAAG,CAAC;IAI9C;;;OAGG;IACH,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IAIzB;;;OAGG;IACH,MAAM,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IAuB5B;;OAEG;IACH,KAAK,IAAI,IAAI;IAkBb;;OAEG;IACH,IAAI,IAAI,MAAM,EAAE;IAIhB;;OAEG;IACH,MAAM,IAAI,GAAG,EAAE;IAIf;;OAEG;IACH,OAAO,IAAI,KAAK,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAI/B;;OAEG;IACH,IAAI,IAAI,MAAM;IAId;;;OAGG;IACH,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI;IAM9C;;;OAGG;IACH,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAUhD;;;OAGG;IACH,QAAQ,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,gBAAgB,KAAK,IAAI,GAAG,MAAM,IAAI;IAKlE;;;;OAIG;IACH,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,gBAAgB,KAAK,IAAI,GAAG,MAAM,IAAI;IAMlF;;OAEG;IACH,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAQlC;;;OAGG;IACH,mBAAmB,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI;IAKxD;;OAEG;IACH,WAAW,IAAI,QAAQ;CAG1B;AAED;;GAEG;AACH,wBAAgB,iBAAiB,IAAI,WAAW,CAE/C;AAED;;GAEG;AACH,qBAAa,gBAAgB;IACzB,OAAO,CAAC,OAAO,CAAU;IACzB,OAAO,CAAC,MAAM,CAAS;gBAEX,OAAO,GAAE,OAAsB,EAAE,MAAM,SAAsB;IAKzE;;;;OAIG;IACH,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,IAAI;IASnC;;;;OAIG;IACH,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,CAAC,GAAG,CAAC;IAa/C;;;OAGG;IACH,MAAM,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAIzB;;OAEG;IACH,KAAK,IAAI,IAAI;IASb;;OAEG;IACH,IAAI,IAAI,MAAM,EAAE;IAOhB;;;;OAIG;IACH,aAAa,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,IAAI;IAU9D;;;;OAIG;IACH,eAAe,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,IAAI;CAenE;AAED;;;;GAIG;AACH,wBAAgB,sBAAsB,CAClC,OAAO,GAAE,OAAsB,EAC/B,MAAM,SAAsB,GAC7B,gBAAgB,CAElB;AAED;;GAEG;AACH,MAAM,MAAM,eAAe,GAAG,CAC1B,GAAG,EAAE,MAAM,EACX,KAAK,EAAE,GAAG,EACV,QAAQ,EAAE,GAAG,EACb,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI,KACtC,IAAI,CAAC;AAEV;;GAEG;AACH,qBAAa,0BAA2B,SAAQ,kBAAkB;IAC9D,OAAO,CAAC,WAAW,CAAyB;IAE5C;;;OAGG;IACH,GAAG,CAAC,UAAU,EAAE,eAAe,GAAG,IAAI;IAItC;;;;OAIG;IACH,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,IAAI;IASlC;;OAEG;IACH,OAAO,CAAC,kBAAkB;CAsB7B;AAED;;GAEG;AACH,wBAAgB,gCAAgC,IAAI,0BAA0B,CAE7E"}