/**
 * @fileoverview Micro-Core 类型定义
 * @description 集中管理所有项目类型定义，确保类型一致性
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
export * from './communication-types';
export * from './core-manager-types';
export * from './core-types';
export * from './plugins';
export * from './sandbox-types';
export * from './sidecar-types';
export type { AppStatus, BuilderType, ErrorCode, EventType, FrameworkType, LogLevel, PluginType, ResourceType, SandboxStatus, SandboxType } from '../constants';
/**
 * 基础配置接口
 */
export interface BaseConfig {
    /** 名称 */
    name: string;
    /** 版本 */
    version?: string;
    /** 描述 */
    description?: string;
    /** 作者 */
    author?: string;
    /** 是否启用 */
    enabled?: boolean;
    /** 日志记录器 */
    logger?: SharedLogger;
}
/**
 * 日志记录器接口
 */
export interface SharedLogger {
    debug(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
}
/**
 * 生命周期钩子函数类型
 */
export type LifecycleHookFn<T = any> = (context: T) => Promise<void> | void;
/**
 * 事件监听器函数类型
 */
export type SharedEventListener<T = any> = (data: T) => Promise<void> | void;
/**
 * 错误处理函数类型
 */
export type SharedErrorHandler = (error: Error, context?: any) => void;
export interface SharedEventBus {
    /** 发送事件 */
    emit(event: string, data?: any): void;
    /** 监听事件 */
    on(event: string, handler: (data?: any) => void): void;
    /** 取消监听 */
    off(event: string, handler?: (data?: any) => void): void;
}
export interface SharedPluginManager {
    /** 获取插件 */
    getPlugin(name: string): SharedPlugin | null;
    /** 安装插件 */
    install(plugin: SharedPlugin): Promise<void>;
    /** 卸载插件 */
    uninstall(name: string): Promise<void>;
}
export interface PluginDependency {
    /** 依赖插件名称 */
    name: string;
    /** 依赖版本 */
    version: string;
    /** 是否为可选依赖 */
    optional?: boolean;
}
export interface PluginMetadata {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 插件描述 */
    description?: string;
    /** 插件作者 */
    author?: string;
    /** 插件依赖 */
    dependencies?: PluginDependency[];
    /** 插件类型 */
    type?: PluginType;
}
export interface PluginManagerOptions {
    /** 是否启用依赖检查 */
    enableDependencyCheck?: boolean;
    /** 是否启用循环依赖检查 */
    enableCircularDependencyCheck?: boolean;
    /** 插件加载超时时间 */
    loadTimeout?: number;
    /** 日志记录器 */
    logger?: SharedLogger;
}
export type PluginState = 'unregistered' | 'registered' | 'loaded' | 'activated' | 'deactivated' | 'unloaded' | 'error';
export type PluginLifecycleHook = 'beforeLoad' | 'afterLoad' | 'beforeActivate' | 'afterActivate' | 'beforeDeactivate' | 'afterDeactivate' | 'beforeUnload' | 'afterUnload';
export interface SharedPlugin {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 安装插件 */
    install(kernel: any): Promise<void>;
    /** 卸载插件 */
    uninstall(): Promise<void>;
    /** 启用插件 */
    enable?(): Promise<void>;
    /** 禁用插件 */
    disable?(): Promise<void>;
    /** 获取插件状态 */
    getStatus(): string;
    /** 检查是否已安装 */
    isInstalled(): boolean;
    /** 检查是否已启用 */
    isEnabled(): boolean;
}
export interface SharedSandboxInstance {
    /** 沙箱名称 */
    name: string;
    /** 沙箱类型 */
    type?: SandboxType;
    /** 是否已激活 */
    isActivated(): boolean;
    /** 激活沙箱 */
    activate(app: any): Promise<void>;
    /** 停用沙箱 */
    deactivate(): Promise<void>;
    /** 执行脚本 */
    execScript(code: string, filename?: string): Promise<any>;
    /** 销毁沙箱 */
    destroy(): Promise<void>;
}
export interface SharedResourceInfo {
    /** 资源类型 */
    type: ResourceType;
    /** 资源URL */
    url: string | null;
    /** 内联内容 */
    content: string | null;
    /** 是否为内联资源 */
    inline: boolean;
    /** 加载状态 */
    status: 'loading' | 'loaded' | 'error';
    /** 错误信息 */
    error?: Error;
    /** 加载时间 */
    loadTime?: number;
    /** 资源大小 */
    size?: number;
}
export interface SharedPerformanceMetric {
    /** 指标名称 */
    name: string;
    /** 指标值 */
    value: number;
    /** 指标单位 */
    unit: string;
    /** 时间戳 */
    timestamp: number;
    /** 指标标签 */
    labels?: Record<string, string>;
}
export interface ErrorHandlingConfig {
    /** 是否启用全局错误处理 */
    global?: boolean;
    /** 错误重试次数 */
    retryCount?: number;
    /** 错误重试延迟 */
    retryDelay?: number;
    /** 自定义错误处理器 */
    handler?: SharedErrorHandler;
}
export interface SharedCacheConfig {
    /** 缓存大小限制 */
    maxSize?: number;
    /** 缓存过期时间 */
    ttl?: number;
    /** 是否启用LRU策略 */
    lru?: boolean;
    /** 缓存键前缀 */
    prefix?: string;
}
export interface SharedSecurityConfig {
    /** CSP策略 */
    csp?: string;
    /** 是否启用沙箱 */
    sandbox?: boolean;
    /** 允许的域名列表 */
    allowedDomains?: string[];
    /** 是否启用HTTPS */
    https?: boolean;
}
//# sourceMappingURL=index.d.ts.map