/**
 * @fileoverview 生命周期管理器测试
 * @description 测试应用生命周期管理功能
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi } from 'vitest'
import { LifecycleManager } from '../lifecycle/lifecycle-manager'

describe('LifecycleManager', () => {
    let lifecycleManager: LifecycleManager

    beforeEach(() => {
        lifecycleManager = new LifecycleManager()
    })

    describe('应用注册', () => {
        it('应该能够注册应用', () => {
            const appConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app'
            }

            lifecycleManager.registerApplication(appConfig)
            const app = lifecycleManager.getApplication('test-app')

            expect(app).toBeDefined()
            expect(app?.name).toBe('test-app')
            expect(app?.status).toBe(ApplicationStatus.NOT_LOADED)
        })

        it('应该拒绝重复注册同名应用', () => {
            const appConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app'
            }

            lifecycleManager.registerApplication(appConfig)

            expect(() => {
                lifecycleManager.registerApplication(appConfig)
            }).toThrow('应用 test-app 已存在')
        })
    })

    describe('应用生命周期', () => {
        beforeEach(() => {
            const appConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app'
            }
            lifecycleManager.registerApplication(appConfig)
        })

        it('应该能够加载应用', async () => {
            await lifecycleManager.loadApplication('test-app')
            const app = lifecycleManager.getApplication('test-app')

            expect(app?.status).toBe(ApplicationStatus.LOADED)
        })

        it('应该能够启动应用', async () => {
            await lifecycleManager.loadApplication('test-app')
            await lifecycleManager.mountApplication('test-app')

            const app = lifecycleManager.getApplication('test-app')
            expect(app?.status).toBe(ApplicationStatus.MOUNTED)
        })

        it('应该能够卸载应用', async () => {
            await lifecycleManager.loadApplication('test-app')
            await lifecycleManager.mountApplication('test-app')
            await lifecycleManager.unmountApplication('test-app')

            const app = lifecycleManager.getApplication('test-app')
            expect(app?.status).toBe(ApplicationStatus.UNMOUNTED)
        })

        it('应该能够销毁应用', async () => {
            await lifecycleManager.loadApplication('test-app')
            await lifecycleManager.unloadApplication('test-app')

            const app = lifecycleManager.getApplication('test-app')
            expect(app?.status).toBe(ApplicationStatus.NOT_LOADED)
        })
    })

    describe('应用查询', () => {
        it('应该能够获取所有应用', () => {
            const appConfig1 = {
                name: 'app1',
                entry: 'http://localhost:3001',
                container: '#app1'
            }
            const appConfig2 = {
                name: 'app2',
                entry: 'http://localhost:3002',
                container: '#app2'
            }

            lifecycleManager.registerApplication(appConfig1)
            lifecycleManager.registerApplication(appConfig2)

            const apps = lifecycleManager.getAllApplications()
            expect(apps.size).toBe(2)
            expect(apps.has('app1')).toBe(true)
            expect(apps.has('app2')).toBe(true)
        })

        it('应该能够获取指定状态的应用', async () => {
            const appConfig1 = {
                name: 'app1',
                entry: 'http://localhost:3001',
                container: '#app1'
            }
            const appConfig2 = {
                name: 'app2',
                entry: 'http://localhost:3002',
                container: '#app2'
            }

            lifecycleManager.registerApplication(appConfig1)
            lifecycleManager.registerApplication(appConfig2)

            await lifecycleManager.loadApplication('app1')

            const loadedApps = lifecycleManager.getApplicationsByStatus(ApplicationStatus.LOADED)
            const notLoadedApps = lifecycleManager.getApplicationsByStatus(ApplicationStatus.NOT_LOADED)

            expect(loadedApps).toHaveLength(1)
            expect(loadedApps[0].name).toBe('app1')
            expect(notLoadedApps).toHaveLength(1)
            expect(notLoadedApps[0].name).toBe('app2')
        })
    })

    describe('错误处理', () => {
        it('应该处理不存在的应用', () => {
            expect(() => {
                lifecycleManager.getApplication('non-existent')
            }).not.toThrow()

            expect(lifecycleManager.getApplication('non-existent')).toBeUndefined()
        })

        it('应该处理加载失败的情况', async () => {
            const appConfig = {
                name: 'test-app',
                entry: 'http://invalid-url',
                container: '#app'
            }

            lifecycleManager.registerApplication(appConfig)

            await expect(lifecycleManager.loadApplication('test-app')).rejects.toThrow()
        })
    })

    describe('事件系统', () => {
        it('应该发送应用注册事件', () => {
            const eventSpy = vi.fn()
            lifecycleManager.on('application:registered', eventSpy)

            const appConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app'
            }

            lifecycleManager.registerApplication(appConfig)

            expect(eventSpy).toHaveBeenCalledWith({
                name: 'test-app',
                status: ApplicationStatus.NOT_LOADED
            })
        })

        it('应该发送应用状态变更事件', async () => {
            const eventSpy = vi.fn()
            lifecycleManager.on('application:status-changed', eventSpy)

            const appConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app'
            }

            lifecycleManager.registerApplication(appConfig)
            await lifecycleManager.loadApplication('test-app')

            expect(eventSpy).toHaveBeenCalledWith({
                name: 'test-app',
                from: ApplicationStatus.NOT_LOADED,
                to: ApplicationStatus.LOADED
            })
        })
    })
})