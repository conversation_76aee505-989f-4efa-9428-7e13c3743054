/**
 * @fileoverview Proxy沙箱策略
 * @description 基于Proxy的JavaScript沙箱隔离实现
 * <AUTHOR> <<EMAIL>>
 */

import { createLogger } from '@micro-core/shared'
import { SandboxContext, SandboxOptions, SandboxStrategy } from '../types'

const logger = createLogger('ProxySandbox')

/**
 * Proxy沙箱策略实现
 * 通过Proxy代理全局对象，实现JavaScript运行时隔离
 */
export class ProxySandbox implements SandboxStrategy {
    private readonly name: string
    private readonly options: SandboxOptions
    private proxyWindow: WindowProxy | null = null
    private fakeWindow: Record<string, any> = {}
    private addedPropsMapInSandbox = new Map<string, any>()
    private modifiedPropsOriginalValueMapInSandbox = new Map<string, any>()
    private currentUpdatedPropsValueMap = new Map<string, any>()
    private active = false

    constructor(name: string, options: SandboxOptions = {}) {
        this.name = name
        this.options = options
        this.createProxyWindow()
    }

    private createProxyWindow(): void {
        const { fakeWindow } = this
        const { modifiedPropsOriginalValueMapInSandbox, addedPropsMapInSandbox, currentUpdatedPropsValueMap } = this

        const rawWindow = window
        const descriptorTargetMap = new Map<string, 'target' | 'rawWindow'>()

        this.proxyWindow = new Proxy(fakeWindow, {
            set: (target: Record<string, any>, prop: string | symbol, value: any): boolean => {
                if (this.active) {
                    const key = String(prop)
                    
                    if (!rawWindow.hasOwnProperty(key)) {
                        addedPropsMapInSandbox.set(key, value)
                    } else if (!modifiedPropsOriginalValueMapInSandbox.has(key)) {
                        const originalValue = (rawWindow as any)[key]
                        modifiedPropsOriginalValueMapInSandbox.set(key, originalValue)
                    }

                    currentUpdatedPropsValueMap.set(key, value)
                    
                    if (typeof prop === 'string' && this.isPropertyConfigurable(rawWindow, prop)) {
                        ;(rawWindow as any)[prop] = value
                    }

                    target[key] = value
                    return true
                }

                logger.warn(`尝试在非激活状态下设置属性: ${String(prop)}`)
                return true
            },

            get: (target: Record<string, any>, prop: string | symbol): any => {
                const key = String(prop)

                if (prop === Symbol.unscopables) return undefined
                if (prop === 'window' || prop === 'self' || prop === 'globalThis') {
                    return this.proxyWindow
                }

                if (
                    prop === 'top' ||
                    prop === 'parent' ||
                    prop === 'frames' ||
                    prop === 'frameElement'
                ) {
                    return rawWindow
                }

                if (target.hasOwnProperty(key)) {
                    return target[key]
                }

                const value = (rawWindow as any)[prop]
                const valueType = typeof value
                
                if (valueType === 'function') {
                    const boundValue = Function.prototype.bind.call(value, rawWindow)
                    
                    for (const key in value) {
                        boundValue[key] = value[key]
                    }
                    
                    if (value.hasOwnProperty('prototype') && !boundValue.hasOwnProperty('prototype')) {
                        boundValue.prototype = value.prototype
                    }
                    
                    return boundValue
                }

                return value
            },

            has: (target: Record<string, any>, prop: string | symbol): boolean => {
                const key = String(prop)
                return key in target || key in rawWindow
            },

            getOwnPropertyDescriptor: (target: Record<string, any>, prop: string | symbol): PropertyDescriptor | undefined => {
                const key = String(prop)
                
                if (target.hasOwnProperty(key)) {
                    const descriptor = Object.getOwnPropertyDescriptor(target, key)
                    descriptorTargetMap.set(key, 'target')
                    return descriptor
                }

                if (rawWindow.hasOwnProperty(key)) {
                    const descriptor = Object.getOwnPropertyDescriptor(rawWindow, key)
                    descriptorTargetMap.set(key, 'rawWindow')
                    
                    if (descriptor && !descriptor.configurable) {
                        descriptor.configurable = true
                    }
                    return descriptor
                }

                return undefined
            },

            defineProperty: (target: Record<string, any>, prop: string | symbol, descriptor: PropertyDescriptor): boolean => {
                const key = String(prop)
                
                if (this.active) {
                    if (!rawWindow.hasOwnProperty(key)) {
                        addedPropsMapInSandbox.set(key, descriptor.value)
                    } else if (!modifiedPropsOriginalValueMapInSandbox.has(key)) {
                        const originalDescriptor = Object.getOwnPropertyDescriptor(rawWindow, key)
                        modifiedPropsOriginalValueMapInSandbox.set(key, originalDescriptor?.value)
                    }

                    currentUpdatedPropsValueMap.set(key, descriptor.value)
                }

                return Reflect.defineProperty(target, prop, descriptor)
            },

            deleteProperty: (target: Record<string, any>, prop: string | symbol): boolean => {
                const key = String(prop)
                
                if (this.active) {
                    if (target.hasOwnProperty(key)) {
                        delete target[key]
                        currentUpdatedPropsValueMap.delete(key)
                    }
                }

                return true
            },

            ownKeys: (target: Record<string, any>): ArrayLike<string | symbol> => {
                return Array.from(new Set([...Reflect.ownKeys(rawWindow), ...Reflect.ownKeys(target)]))
            }
        })
    }

    private isPropertyConfigurable(obj: any, prop: string): boolean {
        const descriptor = Object.getOwnPropertyDescriptor(obj, prop)
        return descriptor ? descriptor.configurable !== false : true
    }

    getName(): string {
        return this.name
    }

    getType(): string {
        return 'proxy'
    }

    isActive(): boolean {
        return this.active
    }

    activate(): void {
        if (!this.active) {
            this.active = true
            logger.debug(`Proxy沙箱 ${this.name} 已激活`)
        }
    }

    deactivate(): void {
        if (this.active) {
            this.active = false
            this.restoreGlobalProps()
            logger.debug(`Proxy沙箱 ${this.name} 已停用`)
        }
    }

    private restoreGlobalProps(): void {
        const { modifiedPropsOriginalValueMapInSandbox, addedPropsMapInSandbox } = this

        // 恢复修改的属性
        modifiedPropsOriginalValueMapInSandbox.forEach((value, prop) => {
            if (typeof prop === 'string' && this.isPropertyConfigurable(window, prop)) {
                ;(window as any)[prop] = value
            }
        })

        // 删除新增的属性
        addedPropsMapInSandbox.forEach((_, prop) => {
            if (typeof prop === 'string' && this.isPropertyConfigurable(window, prop)) {
                delete (window as any)[prop]
            }
        })
    }

    getContext(): SandboxContext {
        return {
            window: this.proxyWindow || window,
            document: window.document,
            location: window.location,
            history: window.history,
            navigator: window.navigator
        }
    }

    execScript(script: string): any {
        if (!this.active) {
            throw new Error(`沙箱 ${this.name} 未激活`)
        }

        try {
            const func = new Function('window', 'self', 'globalThis', `
                with (window) {
                    ${script}
                }
            `)
            
            return func.call(this.proxyWindow, this.proxyWindow, this.proxyWindow, this.proxyWindow)
        } catch (error) {
            logger.error(`执行脚本失败:`, error)
            throw error
        }
    }

    destroy(): void {
        this.deactivate()
        this.fakeWindow = {}
        this.addedPropsMapInSandbox.clear()
        this.modifiedPropsOriginalValueMapInSandbox.clear()
        this.currentUpdatedPropsValueMap.clear()
        this.proxyWindow = null
        logger.debug(`Proxy沙箱 ${this.name} 已销毁`)
    }
}