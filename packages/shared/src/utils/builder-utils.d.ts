/**
 * 共享工具包 - 构建工具相关工具函数
 *
 * @description 提供构建工具相关的通用工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import type { BuilderType } from '../constants';
/**
 * 构建配置接口
 */
export interface BuildConfig {
    /** 构建器类型 */
    type: BuilderType;
    /** 入口文件 */
    entry: string;
    /** 输出目录 */
    output: string;
    /** 构建模式 */
    mode: 'development' | 'production' | 'test';
    /** 构建选项 */
    options?: Record<string, any>;
}
/**
 * 基础构建器选项
 */
export interface BaseBuilderOptions {
    /** 应用名称 */
    appName?: string;
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 自定义配置 */
    customConfig?: Record<string, any>;
}
/**
 * 基础构建器配置
 */
export interface BaseBuilderConfig extends BuildConfig {
    /** 微应用配置 */
    microApp?: {
        /** 应用名称 */
        name: string;
        /** 入口点 */
        entry: string;
        /** 导出模块 */
        exposes?: Record<string, string>;
        /** 共享依赖 */
        shared?: Record<string, any>;
    };
}
/**
 * 构建结果接口
 */
export interface BuildResult {
    /** 构建是否成功 */
    success: boolean;
    /** 构建输出文件 */
    outputs: BuildOutput[];
    /** 构建统计信息 */
    stats: BuildStats;
    /** 错误信息 */
    errors?: string[];
    /** 警告信息 */
    warnings?: string[];
}
/**
 * 构建输出文件接口
 */
export interface BuildOutput {
    /** 文件路径 */
    path: string;
    /** 文件大小 */
    size: number;
    /** 文件类型 */
    type: 'js' | 'css' | 'html' | 'asset';
    /** 是否为入口文件 */
    isEntry?: boolean;
}
/**
 * 构建统计信息接口
 */
export interface BuildStats {
    /** 构建持续时间 */
    duration: number;
    /** 总文件大小 */
    totalSize: number;
    /** 文件数量 */
    fileCount: number;
    /** 构建时间戳 */
    timestamp: number;
}
/**
 * 微应用清单接口
 */
export interface MicroAppManifest {
    /** 应用名称 */
    name: string;
    /** 应用版本 */
    version: string;
    /** 入口文件 */
    entry: string;
    /** 静态资源 */
    assets: string[];
    /** 导出模块 */
    exposes?: Record<string, string>;
    /** 共享依赖 */
    shared?: Record<string, any>;
    /** 应用元数据 */
    metadata?: Record<string, any>;
}
/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小
 */
export declare function formatFileSize(bytes: number): string;
/**
 * 验证构建配置
 * @param config 构建配置
 * @throws {Error} 配置无效时抛出错误
 */
export declare function validateBuildConfig(config: BuildConfig): void;
/**
 * 生成微应用清单
 * @param options 生成选项
 * @returns 微应用清单
 */
export declare function generateManifest(options: {
    appName: string;
    version?: string;
    entry: string;
    assets?: string[];
    exposes?: Record<string, string>;
    shared?: Record<string, any>;
    metadata?: Record<string, any>;
}): MicroAppManifest;
/**
 * 合并构建配置
 * @param baseConfig 基础配置
 * @param overrideConfig 覆盖配置
 * @returns 合并后的配置
 */
export declare function mergeBuildConfig<T extends BuildConfig>(baseConfig: T, overrideConfig: Partial<T>): T;
/**
 * 检查构建工具是否可用
 * @param builderType 构建工具类型
 * @returns 是否可用
 */
export declare function isBuilderAvailable(builderType: BuilderType): boolean;
/**
 * 获取构建工具版本
 * @param builderType 构建工具类型
 * @returns 版本号或null
 */
export declare function getBuilderVersion(builderType: BuilderType): string | null;
/**
 * 创建默认构建配置
 * @param builderType 构建工具类型
 * @param appName 应用名称
 * @returns 默认构建配置
 */
export declare function createDefaultBuildConfig(builderType: BuilderType, appName: string): BaseBuilderConfig;
/**
 * 提取构建信息
 * @param buildResult 构建结果
 * @returns 构建信息
 */
export declare function extractBuildInfo(buildResult: any): BuildResult;
//# sourceMappingURL=builder-utils.d.ts.map