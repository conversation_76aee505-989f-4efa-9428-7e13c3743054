<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Micro-Core 示例主应用</title>
</head>

<body>
    <div id="app">
        <header style="padding: 20px; background: #f0f0f0; border-bottom: 1px solid #ccc;">
            <h1>Micro-Core 微前端架构演示</h1>
            <nav>
                <button onclick="loadReactApp()">加载 React 应用</button>
                <button onclick="loadVueApp()">加载 Vue 应用</button>
                <button onclick="unloadAll()">卸载所有应用</button>
            </nav>
        </header>

        <main style="display: flex; min-height: 500px;">
            <aside style="width: 200px; padding: 20px; background: #f9f9f9;">
                <h3>应用状态</h3>
                <div id="app-status"></div>
            </aside>

            <section style="flex: 1; padding: 20px;">
                <div id="react-container" style="margin-bottom: 20px;"></div>
                <div id="vue-container"></div>
            </section>
        </main>
    </div>

    <script type="module" src="/src/main.ts"></script>
</body>

</html>