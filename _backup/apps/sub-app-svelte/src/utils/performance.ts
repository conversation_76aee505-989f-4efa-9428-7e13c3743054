/**
 * @fileoverview Svelte 性能优化工具
 * <AUTHOR> <<EMAIL>>
 */

export interface PerformanceMetrics {
    mountTime: number
    renderTime: number
    updateTime: number
    memoryUsage?: number
}

export class PerformanceMonitor {
    private static instance: PerformanceMonitor
    private metrics: PerformanceMetrics = {
        mountTime: 0,
        renderTime: 0,
        updateTime: 0
    }
    private startTimes: Map<string, number> = new Map()

    static getInstance(): PerformanceMonitor {
        if (!PerformanceMonitor.instance) {
            PerformanceMonitor.instance = new PerformanceMonitor()
        }
        return PerformanceMonitor.instance
    }

    public startTiming(operation: string): void {
        this.startTimes.set(operation, performance.now())
    }

    public endTiming(operation: string): number {
        const startTime = this.startTimes.get(operation)
        if (!startTime) return 0

        const duration = performance.now() - startTime
        this.startTimes.delete(operation)

        switch (operation) {
            case 'mount':
                this.metrics.mountTime = duration
                break
            case 'render':
                this.metrics.renderTime = duration
                break
            case 'update':
                this.metrics.updateTime = duration
                break
        }

        return duration
    }

    public getMetrics(): PerformanceMetrics {
        return { ...this.metrics }
    }

    public logMetrics(): void {
        console.log('Svelte 应用性能指标:', this.metrics)
    }

    public measureMemoryUsage(): void {
        if ('memory' in performance) {
            const memory = (performance as any).memory
            this.metrics.memoryUsage = memory.usedJSHeapSize
        }
    }
}

export const performanceMonitor = PerformanceMonitor.getInstance()

// 资源预加载工具
export class ResourcePreloader {
    private static preloadedResources = new Set<string>()

    static preloadScript(src: string): Promise<void> {
        if (this.preloadedResources.has(src)) {
            return Promise.resolve()
        }

        return new Promise((resolve, reject) => {
            const link = document.createElement('link')
            link.rel = 'preload'
            link.as = 'script'
            link.href = src
            link.onload = () => {
                this.preloadedResources.add(src)
                resolve()
            }
            link.onerror = reject
            document.head.appendChild(link)
        })
    }

    static preloadStyle(href: string): Promise<void> {
        if (this.preloadedResources.has(href)) {
            return Promise.resolve()
        }

        return new Promise((resolve, reject) => {
            const link = document.createElement('link')
            link.rel = 'preload'
            link.as = 'style'
            link.href = href
            link.onload = () => {
                this.preloadedResources.add(href)
                resolve()
            }
            link.onerror = reject
            document.head.appendChild(link)
        })
    }
}

// 懒加载工具
export class LazyLoader {
    private static observer: IntersectionObserver | null = null

    static init(): void {
        if (!this.observer && 'IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target as HTMLElement
                        const src = element.dataset['src']
                        if (src) {
                            if (element.tagName === 'IMG') {
                                (element as HTMLImageElement).src = src
                            }
                            element.removeAttribute('data-src')
                            this.observer?.unobserve(element)
                        }
                    }
                })
            })
        }
    }

    static observe(element: HTMLElement): void {
        if (this.observer) {
            this.observer.observe(element)
        }
    }

    static disconnect(): void {
        if (this.observer) {
            this.observer.disconnect()
            this.observer = null
        }
    }
}