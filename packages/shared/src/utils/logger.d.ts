/**
 * @fileoverview 日志工具
 * @description 提供统一的日志记录功能
 * <AUTHOR> <<EMAIL>>
 */
export interface Logger {
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
    debug(message: string, ...args: any[]): void;
}
/**
 * 创建日志记录器
 */
export declare function createLogger(namespace: string): Logger;
//# sourceMappingURL=logger.d.ts.map