/**
 * 适配器管理器
 *
 * @description 统一管理所有框架适配器
 * <AUTHOR> <<EMAIL>>
 */
import { BaseAdapter } from '../shared/src/base-adapter';
import type { AdapterConfig, AdapterFactory, AdapterState, FrameworkType } from '../shared/src/types';
/**
 * 简单的事件发射器
 */
declare class EventEmitter {
    private listeners;
    emit(event: string, data?: any): void;
    on(event: string, listener: Function): void;
    off(event: string, listener: Function): void;
}
/**
 * 适配器管理器
 */
export declare class AdapterManager extends EventEmitter {
    private readonly logger;
    private readonly adapters;
    private readonly factories;
    private readonly registry;
    constructor();
    /**
     * 注册适配器工厂
     */
    registerFactory(frameworkType: FrameworkType, factory: AdapterFactory): void;
    /**
     * 注销适配器工厂
     */
    unregisterFactory(frameworkType: FrameworkType): boolean;
    /**
     * 创建适配器实例
     */
    createAdapter(adapterId: string, frameworkType: FrameworkType, config?: AdapterConfig): Promise<BaseAdapter>;
    /**
     * 初始化适配器
     */
    initializeAdapter(adapterId: string): Promise<void>;
    /**
     * 挂载适配器
     */
    mountAdapter(adapterId: string, container: HTMLElement, props?: any): Promise<void>;
    /**
     * 卸载适配器
     */
    unmountAdapter(adapterId: string): Promise<void>;
    /**
     * 销毁适配器
     */
    destroyAdapter(adapterId: string): Promise<void>;
    /**
     * 获取适配器实例
     */
    getAdapter(adapterId: string): BaseAdapter | undefined;
    /**
     * 获取适配器状态
     */
    getAdapterState(adapterId: string): AdapterState | undefined;
    /**
     * 获取适配器配置
     */
    getAdapterConfig(adapterId: string): AdapterConfig | undefined;
    /**
     * 检查适配器是否存在
     */
    hasAdapter(adapterId: string): boolean;
    /**
     * 获取所有适配器ID
     */
    getAdapterIds(): string[];
    /**
     * 获取指定状态的适配器
     */
    getAdaptersByState(state: AdapterState): string[];
    /**
     * 获取支持的框架类型
     */
    getSupportedFrameworks(): FrameworkType[];
    /**
     * 检查框架是否支持
     */
    isFrameworkSupported(frameworkType: FrameworkType): boolean;
    /**
     * 自动检测框架类型
     */
    detectFramework(container: HTMLElement): FrameworkType | null;
    /**
     * 批量创建适配器
     */
    createAdapters(configs: Array<{
        id: string;
        frameworkType: FrameworkType;
        config?: AdapterConfig;
    }>): Promise<BaseAdapter[]>;
    /**
     * 获取适配器统计信息
     */
    getStats(): {
        totalAdapters: number;
        adaptersByState: Record<AdapterState, number>;
        adaptersByFramework: Record<FrameworkType, number>;
        supportedFrameworks: number;
        averageLifetime: number;
    };
    /**
     * 清理所有适配器
     */
    cleanup(): Promise<void>;
    /**
     * 获取实例信息
     */
    private getInstanceInfo;
    /**
     * 更新适配器状态
     */
    private updateState;
    /**
     * 处理适配器错误
     */
    private handleAdapterError;
    /**
     * 验证适配器工厂
     */
    private validateFactory;
    /**
     * 初始化内置适配器
     */
    private initializeBuiltinAdapters;
    /**
     * 检测 React
     */
    private detectReact;
    /**
     * 检测 Vue
     */
    private detectVue;
    /**
     * 检测 Angular
     */
    private detectAngular;
    /**
     * 检测 Svelte
     */
    private detectSvelte;
    /**
     * 检测 Solid
     */
    private detectSolid;
}
export {};
//# sourceMappingURL=adapter-manager.d.ts.map