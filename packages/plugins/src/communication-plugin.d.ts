/**
 * @fileoverview 通信插件
 * @description 提供微前端应用间的通信功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import { type PluginConfig } from '@micro-core/shared';
import { BasePlugin } from './base-plugin';
interface CommunicationMessage {
    id: string;
    type: string;
    data: any;
    sender: string;
    target?: string;
    timestamp: number;
    encrypted?: boolean;
    compressed?: boolean;
    priority?: number;
    timeout?: number;
}
interface MessageHandler {
    (message: CommunicationMessage): Promise<void> | void;
}
interface MessageFilter {
    (message: CommunicationMessage): boolean;
}
interface MicroCoreKernel {
    registerAPI(name: string, api: any): void;
    getEventBus(): any;
}
/**
 * 通信插件配置接口
 */
interface CommunicationPluginConfig extends PluginConfig {
    /** 消息超时时间（毫秒） */
    timeout?: number;
    /** 最大消息队列长度 */
    maxQueueSize?: number;
    /** 是否启用消息持久化 */
    enablePersistence?: boolean;
    /** 是否启用消息加密 */
    enableEncryption?: boolean;
    /** 加密密钥 */
    encryptionKey?: string;
    /** 是否启用消息压缩 */
    enableCompression?: boolean;
    /** 消息重试次数 */
    retryCount?: number;
    /** 重试间隔（毫秒） */
    retryInterval?: number;
}
/**
 * 消息记录接口
 */
interface MessageRecord {
    /** 消息ID */
    id: string;
    /** 消息内容 */
    message: CommunicationMessage;
    /** 发送时间 */
    timestamp: number;
    /** 发送者 */
    sender: string;
    /** 接收者 */
    receiver?: string;
    /** 消息状态 */
    status: 'pending' | 'sent' | 'delivered' | 'failed';
    /** 重试次数 */
    retryCount: number;
    /** 错误信息 */
    error?: string;
}
/**
 * 通信插件类
 * 提供微前端应用间的消息传递、事件通信和数据共享功能
 */
export declare class CommunicationPlugin extends BasePlugin {
    /** 内核实例 */
    protected kernel?: MicroCoreKernel;
    /** 通信插件配置 */
    private commConfig;
    /** 消息处理器注册表 */
    private handlers;
    /** 消息队列 */
    private messageQueue;
    /** 通信通道 */
    private channels;
    /** 消息历史 */
    private messageHistory;
    /** 待确认的消息 */
    private pendingMessages;
    /** 消息过滤器 */
    private globalFilters;
    constructor(config?: CommunicationPluginConfig);
    /**
     * 执行安装逻辑
     */
    protected doInstall(kernel: MicroCoreKernel): Promise<void>;
    /**
     * 执行卸载逻辑
     */
    protected doUninstall(): Promise<void>;
    /**
     * 发送消息
     */
    sendMessage(type: string, data: any, target?: string, options?: {
        timeout?: number;
        priority?: number;
        persistent?: boolean;
        encrypted?: boolean;
    }): Promise<string>;
    /**
     * 广播消息
     */
    broadcastMessage(type: string, data: any, options?: any): Promise<string>;
    /**
     * 添加消息处理器
     */
    addMessageHandler(type: string, handler: MessageHandler, options?: {
        appName?: string;
        filter?: MessageFilter;
        priority?: number;
    }): string;
    /**
     * 移除消息处理器
     */
    removeMessageHandler(handlerId: string): boolean;
    /**
     * 添加一次性处理器
     */
    addOnceHandler(type: string, handler: MessageHandler, options?: any): string;
    /**
     * 创建通信通道
     */
    createChannel(name: string, type?: 'broadcast' | 'unicast' | 'multicast'): string;
    /**
     * 加入通道
     */
    joinChannel(channelName: string, appName?: string): boolean;
    /**
     * 离开通道
     */
    leaveChannel(channelName: string, appName?: string): boolean;
    /**
     * 获取所有通道
     */
    getChannels(): Array<{
        name: string;
        type: string;
        participantCount: number;
        createdAt: number;
    }>;
    /**
     * 获取消息历史
     */
    getMessageHistory(limit?: number): MessageRecord[];
    /**
     * 清理消息历史
     */
    clearHistory(): void;
    /**
     * 添加全局过滤器
     */
    addGlobalFilter(filter: MessageFilter): void;
    /**
     * 移除全局过滤器
     */
    removeGlobalFilter(filter: MessageFilter): boolean;
    /**
     * 开始消息处理循环
     */
    private startMessageProcessing;
    /**
     * 停止消息处理
     */
    private stopMessageProcessing;
    /**
     * 处理消息队列
     */
    private processMessageQueue;
    /**
     * 处理单个消息
     */
    private processMessage;
    /**
     * 添加到消息队列
     */
    private addToQueue;
    /**
     * 添加到历史记录
     */
    private addToHistory;
    /**
     * 等待消息投递完成
     */
    private waitForMessageDelivery;
    /**
     * 应用全局过滤器
     */
    private applyGlobalFilters;
    /**
     * 加密数据
     */
    private encryptData;
    /**
     * 解密数据
     */
    private decryptData;
    /**
     * 压缩数据
     */
    private compressData;
    /**
     * 解压数据
     */
    private decompressData;
    /**
     * 获取当前应用名称
     */
    private getCurrentAppName;
    /**
     * 恢复消息历史
     */
    private restoreMessageHistory;
    /**
     * 保存消息历史
     */
    private saveMessageHistory;
    /**
     * 获取插件统计信息
     */
    getStats(): Record<string, any>;
}
export {};
//# sourceMappingURL=communication-plugin.d.ts.map