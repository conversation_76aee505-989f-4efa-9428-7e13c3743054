/**
 * 共享工具包 - 统一错误处理工具
 *
 * @description 提供微前端项目的统一错误处理和恢复机制
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * 错误类型枚举
 */
export declare enum ErrorType {
    INITIALIZATION = "INITIALIZATION",
    CONFIGURATION = "CONFIGURATION",
    NETWORK = "NETWORK",
    COMPATIBILITY = "COMPATIBILITY",
    SECURITY = "SECURITY",
    PERFORMANCE = "PERFORMANCE",
    UNKNOWN = "UNKNOWN"
}
/**
 * 错误严重程度枚举
 */
export declare enum ErrorSeverity {
    LOW = "LOW",
    MEDIUM = "MEDIUM",
    HIGH = "HIGH",
    CRITICAL = "CRITICAL"
}
/**
 * 错误上下文接口
 */
export interface ErrorContext {
    /** 错误发生的组件 */
    component?: string;
    /** 错误发生的方法 */
    method?: string;
    /** 用户代理信息 */
    userAgent?: string;
    /** 时间戳 */
    timestamp?: number;
    /** 额外的上下文数据 */
    data?: Record<string, any>;
}
/**
 * 错误恢复策略接口
 */
export interface ErrorRecoveryStrategy {
    /** 策略名称 */
    name: string;
    /** 是否可以恢复 */
    canRecover: (error: Error, context?: ErrorContext) => boolean;
    /** 恢复操作 */
    recover: (error: Error, context?: ErrorContext) => Promise<boolean>;
    /** 恢复优先级 */
    priority: number;
}
/**
 * 增强的微前端错误类
 */
export declare class MicroCoreError extends Error {
    readonly code: number;
    readonly type: ErrorType;
    readonly severity: ErrorSeverity;
    readonly context: ErrorContext;
    readonly timestamp: number;
    readonly recoverable: boolean;
    constructor(code: number, message: string, type?: ErrorType, severity?: ErrorSeverity, context?: ErrorContext, recoverable?: boolean);
    /**
     * 转换为JSON格式
     */
    toJSON(): Record<string, any>;
    /**
     * 创建系统错误
     */
    static system(message: string, context?: ErrorContext): MicroCoreError;
    /**
     * 创建应用错误
     */
    static app(message: string, context?: ErrorContext): MicroCoreError;
    /**
     * 创建沙箱错误
     */
    static sandbox(message: string, context?: ErrorContext): MicroCoreError;
    /**
     * 创建初始化错误
     */
    static initialization(message: string, context?: ErrorContext): MicroCoreError;
    /**
     * 创建配置错误
     */
    static configuration(message: string, context?: ErrorContext): MicroCoreError;
    /**
     * 创建网络错误
     */
    static network(message: string, context?: ErrorContext): MicroCoreError;
    /**
     * 创建兼容性错误
     */
    static compatibility(message: string, context?: ErrorContext): MicroCoreError;
    /**
     * 创建安全错误
     */
    static security(message: string, context?: ErrorContext): MicroCoreError;
    /**
     * 创建性能错误
     */
    static performance(message: string, context?: ErrorContext): MicroCoreError;
}
/**
 * 错误处理器类
 */
export declare class ErrorHandler {
    private strategies;
    private errorHistory;
    private maxHistorySize;
    private onError?;
    private onRecovery?;
    constructor();
    /**
     * 设置错误回调
     */
    setErrorCallback(callback: (error: MicroCoreError) => void): void;
    /**
     * 设置恢复回调
     */
    setRecoveryCallback(callback: (error: MicroCoreError, strategy: string) => void): void;
    /**
     * 注册恢复策略
     */
    registerStrategy(strategy: ErrorRecoveryStrategy): void;
    /**
     * 处理错误
     */
    handleError(error: Error | MicroCoreError, context?: ErrorContext): Promise<boolean>;
    /**
     * 尝试错误恢复
     */
    private attemptRecovery;
    /**
     * 记录错误
     */
    private recordError;
    /**
     * 获取错误历史
     */
    getErrorHistory(): MicroCoreError[];
    /**
     * 获取错误统计
     */
    getErrorStats(): Record<string, any>;
    /**
     * 清理错误历史
     */
    clearHistory(): void;
    /**
     * 设置默认恢复策略
     */
    private setupDefaultStrategies;
    /**
     * 设置全局错误处理
     */
    private setupGlobalErrorHandling;
}
/**
 * 全局错误处理器实例
 */
export declare const globalErrorHandler: ErrorHandler;
/**
 * 创建错误处理器
 */
export declare function createErrorHandler(): ErrorHandler;
/**
 * 错误处理装饰器
 */
export declare function handleErrors(errorType?: ErrorType, severity?: ErrorSeverity): (target: any, propertyName: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
/**
 * 简单错误创建函数（向后兼容）
 */
export declare function createMicroCoreError(code: string, message: string, context?: any): Error;
export default ErrorHandler;
//# sourceMappingURL=error-handler.d.ts.map