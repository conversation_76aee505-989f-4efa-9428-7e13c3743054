# @micro-core/communication

微前端应用间通信系统 - 提供事件总线、消息通道、状态同步等通信能力

## 🚀 特性

- **高性能事件总线** - 支持同步/异步事件处理，通配符事件匹配
- **消息通道系统** - 支持广播、直连、多播等多种通信模式
- **状态同步机制** - 实现应用间状态共享和同步
- **通信协议标准化** - 统一的消息格式和协议验证
- **完整的 TypeScript 支持** - 类型安全的 API 设计

## 📦 安装

```bash
pnpm add @micro-core/communication
```

## 🔧 基础用法

### 事件总线

```typescript
import { createEventBus } from '@micro-core/communication';

// 创建事件总线
const eventBus = createEventBus({
    name: 'main-bus',
    maxListeners: 100,
    wildcardEvents: true
});

// 订阅事件
const unsubscribe = eventBus.subscribe('user:login', (event) => {
    console.log('用户登录:', event.data);
});

// 发布事件
await eventBus.publish({
    type: 'user:login',
    data: { userId: '123', username: 'admin' },
    source: 'auth-app',
    priority: 'normal',
    requiresAck: false
});

// 取消订阅
unsubscribe();
```

### 消息通道

```typescript
import { createMessageChannel } from '@micro-core/communication';

// 创建消息通道
const channel = createMessageChannel({
    name: 'app-channel',
    type: 'broadcast',
    bufferSize: 1000
});

// 订阅消息
const unsubscribe = channel.subscribe('data:update', (message) => {
    console.log('收到数据更新:', message.data);
});

// 发送消息
await channel.send({
    type: 'data:update',
    data: { table: 'users', action: 'create' }
});
```

### 状态同步

```typescript
import { createStateSync } from '@micro-core/communication';

// 创建状态同步实例
const stateSync = createStateSync();

// 监听状态变化
const unsubscribe = stateSync.subscribe('user', (event) => {
    console.log('用户状态变化:', event);
});

// 设置状态
stateSync.setState('user', { id: '123', name: 'admin' }, 'auth-app');

// 获取状态
const user = stateSync.getState('user');
```

### 通信管理器

```typescript
import { createCommunicationManager } from '@micro-core/communication';

// 创建通信管理器
const manager = createCommunicationManager({
    enabled: true,
    maxMessageSize: 1024 * 1024,
    timeout: 5000
});

// 获取事件总线
const eventBus = manager.getEventBus();

// 创建消息通道
const channel = manager.createMessageChannel('app-channel');

// 获取统计信息
const stats = manager.getStats();
console.log('通信统计:', stats);
```

## 📚 API 文档

### EventBus

#### 方法

- `publish<T>(event)` - 发布事件
- `subscribe<T>(eventType, listener)` - 订阅事件
- `once<T>(eventType, listener)` - 一次性订阅事件
- `getEventHistory(filter?)` - 获取事件历史
- `getStats()` - 获取统计信息
- `destroy()` - 销毁事件总线

### MessageChannel

#### 方法

- `send<T>(message)` - 发送消息
- `subscribe<T>(messageType, handler)` - 订阅消息
- `destroy()` - 销毁消息通道

### StateSync

#### 方法

- `setState<T>(key, value, source)` - 设置状态
- `getState<T>(key)` - 获取状态
- `subscribe<T>(key, listener)` - 监听状态变化
- `destroy()` - 销毁状态同步

### CommunicationManager

#### 方法

- `getEventBus()` - 获取事件总线
- `createMessageChannel(name)` - 创建消息通道
- `getStats()` - 获取统计信息
- `destroy()` - 销毁管理器

## 🔧 配置选项

### EventBusConfig

```typescript
interface EventBusConfig {
    name: string;              // 事件总线名称
    maxListeners: number;      // 最大监听器数量
    wildcardEvents: boolean;   // 是否启用通配符事件
    historySize: number;       // 事件历史记录大小
    async: boolean;            // 是否启用异步事件处理
}
```

### MessageChannelConfig

```typescript
interface MessageChannelConfig {
    name: string;              // 通道名称
    type: 'broadcast' | 'direct' | 'multicast';  // 通道类型
    bufferSize: number;        // 缓冲区大小
    acknowledgment: boolean;   // 是否启用消息确认
    retry: {                   // 重试配置
        enabled: boolean;
        maxAttempts: number;
        backoff: number;
    };
}
```

### CommunicationConfig

```typescript
interface CommunicationConfig {
    enabled: boolean;          // 是否启用通信系统
    protocolVersion: string;   // 通信协议版本
    maxMessageSize: number;    // 最大消息大小（字节）
    timeout: number;           // 消息超时时间（毫秒）
    persistence: boolean;      // 是否启用消息持久化
    permissions: CommunicationPermission[];  // 权限配置
}
```

## 🎯 最佳实践

### 事件命名规范

```typescript
// 推荐的事件命名格式：模块:动作
'user:login'
'order:created'
'payment:failed'
'app:mounted'

// 使用通配符监听
eventBus.subscribe('user:*', handler);  // 监听所有用户相关事件
eventBus.subscribe('*:error', handler); // 监听所有错误事件
```

### 错误处理

```typescript
// 监听通信错误
eventBus.on('error', (error) => {
    console.error('通信错误:', error);
});

// 监听监听器错误
eventBus.on('listenerError', (error, event) => {
    console.error('监听器执行错误:', error, event);
});
```

### 性能优化

```typescript
// 使用异步事件处理提高性能
const eventBus = createEventBus({
    async: true,
    maxListeners: 200
});

// 合理设置历史记录大小
const eventBus = createEventBus({
    historySize: 500  // 根据实际需求调整
});
```

## 📄 许可证

MIT License