# API 参考

欢迎来到 Micro-Core API 参考文档。本文档提供了 Micro-Core 框架的完整 API 说明，包括核心模块、插件系统、适配器等所有功能的详细介绍。

## 快速导航

### 核心 API

- **[核心模块](./core.md)** - MicroCore 主类和基础功能
- **[应用管理](./app-management.md)** - 应用注册、生命周期管理
- **[路由系统](./routing.md)** - 路由配置和导航控制
- **[事件总线](./event-bus.md)** - 应用间事件通信
- **[状态管理](./state-management.md)** - 全局状态管理
- **[沙箱系统](./sandbox.md)** - 应用隔离和安全
- **[加载器](./loader.md)** - 资源加载和管理
- **[通信系统](./communication.md)** - 跨应用通信协议

### 插件 API

- **[插件基类](./plugins/base.md)** - 插件开发基础
- **[路由插件](./plugins/router.md)** - 路由功能扩展
- **[通信插件](./plugins/communication.md)** - 通信功能扩展
- **[认证插件](./plugins/auth.md)** - 身份认证功能

### 适配器 API

- **[适配器基类](./adapters/base.md)** - 框架适配器基础
- **[React 适配器](./adapters/react.md)** - React 框架支持
- **[Vue 适配器](./adapters/vue.md)** - Vue 框架支持
- **[Angular 适配器](./adapters/angular.md)** - Angular 框架支持

## 核心概念

### MicroCore 主类

`MicroCore` 是框架的核心类，负责管理整个微前端系统：

```typescript
import { MicroCore } from '@micro-core/core';

const microCore = new MicroCore({
  container: '#app',
  router: { mode: 'history' },
  sandbox: { type: 'proxy' }
});
```

### 应用配置

每个微应用都需要通过配置对象进行注册：

```typescript
interface AppConfig {
  name: string;                    // 应用名称
  entry: string | AppEntry;        // 应用入口
  container?: string | Element;    // 容器元素
  activeRule: ActiveRule;          // 激活规则
  props?: Record<string, any>;     // 应用属性
  sandbox?: SandboxConfig;         // 沙箱配置
  // ... 更多配置选项
}
```

### 生命周期钩子

应用生命周期提供了丰富的钩子函数：

```typescript
interface LifecycleHooks {
  beforeBootstrap?: (app: MicroApp) => Promise<void>;
  afterBootstrap?: (app: MicroApp) => Promise<void>;
  beforeMount?: (app: MicroApp) => Promise<void>;
  afterMount?: (app: MicroApp) => Promise<void>;
  beforeUnmount?: (app: MicroApp) => Promise<void>;
  afterUnmount?: (app: MicroApp) => Promise<void>;
  beforeUpdate?: (app: MicroApp) => Promise<void>;
  afterUpdate?: (app: MicroApp) => Promise<void>;
}
```

## 类型定义

### 基础类型

```typescript
// 应用状态
type AppStatus = 'NOT_LOADED' | 'LOADING' | 'LOADED' | 'BOOTSTRAPPING' | 
                 'NOT_MOUNTED' | 'MOUNTING' | 'MOUNTED' | 'UNMOUNTING' | 
                 'UNLOADING' | 'SKIP_BECAUSE_BROKEN';

// 激活规则
type ActiveRule = string | RegExp | Array<string | RegExp> | 
                  ((location: Location) => boolean);

// 应用入口
interface AppEntry {
  scripts?: string[];
  styles?: string[];
  html?: string;
}
```

### 配置类型

```typescript
// 主配置
interface MicroCoreConfig {
  container?: string | Element;
  router?: RouterConfig;
  sandbox?: SandboxConfig;
  prefetch?: PrefetchConfig;
  errorHandler?: ErrorHandlerConfig;
  plugins?: Plugin[];
  adapters?: Record<string, FrameworkAdapter>;
}

// 路由配置
interface RouterConfig {
  mode?: 'hash' | 'history' | 'memory';
  base?: string;
  linkActiveClass?: string;
  linkExactActiveClass?: string;
}

// 沙箱配置
interface SandboxConfig {
  type?: 'proxy' | 'iframe' | 'web-components';
  css?: boolean | CSSIsolationConfig;
  js?: boolean | JSIsolationConfig;
  globalWhitelist?: string[];
  globalBlacklist?: string[];
}
```

## 使用示例

### 基础使用

```typescript
import { MicroCore } from '@micro-core/core';

// 创建实例
const microCore = new MicroCore({
  container: '#micro-app-container',
  router: {
    mode: 'history',
    base: '/'
  }
});

// 注册应用
microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001',
  activeRule: '/react-app',
  container: '#react-container'
});

// 启动框架
microCore.start();
```

### 高级配置

```typescript
import { MicroCore } from '@micro-core/core';
import { ReactAdapter } from '@micro-core/adapter-react';
import { RouterPlugin } from '@micro-core/plugin-router';

const microCore = new MicroCore({
  // 容器配置
  container: document.getElementById('app'),
  
  // 路由配置
  router: {
    mode: 'history',
    base: '/micro-apps/',
    linkActiveClass: 'active',
    linkExactActiveClass: 'exact-active'
  },
  
  // 沙箱配置
  sandbox: {
    type: 'proxy',
    css: {
      enabled: true,
      prefix: 'micro-app-'
    },
    js: {
      enabled: true,
      strict: true
    },
    globalWhitelist: ['console', 'location'],
    globalBlacklist: ['eval', 'Function']
  },
  
  // 预加载配置
  prefetch: {
    idle: ['app1', 'app2'],
    hover: ['app3']
  },
  
  // 错误处理
  errorHandler: {
    onJSError: (error, app) => {
      console.error('JS Error:', error);
    },
    onLoadError: (error, app) => {
      console.error('Load Error:', error);
    }
  }
});

// 注册适配器
microCore.registerAdapter('react', new ReactAdapter());

// 使用插件
microCore.use(RouterPlugin, {
  guards: {
    beforeEach: (to, from, next) => {
      console.log('Route change:', from.path, '->', to.path);
      next();
    }
  }
});

// 注册多个应用
microCore.registerApps([
  {
    name: 'header',
    entry: 'http://localhost:3001',
    activeRule: () => true, // 始终激活
    container: '#header'
  },
  {
    name: 'main-app',
    entry: 'http://localhost:3002',
    activeRule: '/main',
    container: '#main',
    props: {
      userId: '123',
      theme: 'dark'
    }
  },
  {
    name: 'admin-panel',
    entry: 'http://localhost:3003',
    activeRule: ['/admin', '/admin/*'],
    container: '#admin',
    beforeMount: async (app) => {
      // 权限检查
      const hasPermission = await checkAdminPermission();
      if (!hasPermission) {
        throw new Error('No admin permission');
      }
    }
  }
]);

// 启动框架
microCore.start().then(() => {
  console.log('Micro-Core started successfully');
});
```

### 应用间通信

```typescript
// 事件通信
microCore.eventBus.emit('user-login', {
  userId: '123',
  username: 'john'
});

microCore.eventBus.on('user-login', (data) => {
  console.log('User logged in:', data);
});

// 全局状态
microCore.globalState.set('currentUser', {
  id: '123',
  name: 'John Doe'
});

const user = microCore.globalState.get('currentUser');

microCore.globalState.subscribe('currentUser', (newUser, oldUser) => {
  console.log('User changed:', oldUser, '->', newUser);
});

// 共享依赖
microCore.shared.register('utils', {
  formatDate: (date) => date.toISOString(),
  request: axios.create({ baseURL: '/api' })
});

const utils = microCore.shared.get('utils');
```

## 错误处理

### 常见错误类型

```typescript
// 应用加载错误
class AppLoadError extends Error {
  constructor(appName: string, originalError: Error) {
    super(`Failed to load app: ${appName}`);
    this.name = 'AppLoadError';
    this.cause = originalError;
  }
}

// 应用挂载错误
class AppMountError extends Error {
  constructor(appName: string, originalError: Error) {
    super(`Failed to mount app: ${appName}`);
    this.name = 'AppMountError';
    this.cause = originalError;
  }
}

// 路由错误
class RouteError extends Error {
  constructor(path: string, originalError: Error) {
    super(`Route error for path: ${path}`);
    this.name = 'RouteError';
    this.cause = originalError;
  }
}
```

### 错误处理最佳实践

```typescript
const microCore = new MicroCore({
  errorHandler: {
    // JavaScript 运行时错误
    onJSError: (error, app) => {
      // 错误上报
      reportError({
        type: 'js-error',
        app: app?.name,
        error: error.message,
        stack: error.stack,
        timestamp: Date.now()
      });
      
      // 错误恢复
      if (app && error.name === 'ChunkLoadError') {
        // 重新加载应用
        microCore.reloadApp(app.name);
      }
    },
    
    // 资源加载错误
    onLoadError: (error, app) => {
      console.error(`Failed to load resources for app: ${app?.name}`, error);
      
      // 显示降级UI
      showFallbackUI(app);
      
      // 尝试备用资源
      if (app && app.entry.includes('cdn.example.com')) {
        app.entry = app.entry.replace('cdn.example.com', 'backup-cdn.example.com');
        microCore.reloadApp(app.name);
      }
    },
    
    // 应用级错误
    onAppError: (error, app) => {
      console.error(`App error in ${app.name}:`, error);
      
      // 应用隔离：错误不影响其他应用
      if (error.name === 'CriticalError') {
        microCore.unloadApp(app.name);
        showErrorBoundary(app.name, error);
      }
    }
  }
});
```

## 性能优化

### 预加载策略

```typescript
const microCore = new MicroCore({
  prefetch: {
    // 空闲时预加载
    idle: {
      apps: ['frequently-used-app'],
      timeout: 5000
    },
    
    // 鼠标悬停预加载
    hover: {
      apps: ['on-demand-app'],
      delay: 200,
      selector: '[data-prefetch]'
    },
    
    // 视口内预加载
    viewport: {
      apps: ['below-fold-app'],
      threshold: 0.1,
      rootMargin: '50px'
    }
  }
});
```

### 缓存配置

```typescript
const microCore = new MicroCore({
  cache: {
    // 应用缓存
    apps: {
      enabled: true,
      maxAge: 5 * 60 * 1000,    // 5分钟
      maxSize: 10,              // 最多缓存10个应用
      strategy: 'lru'           // LRU策略
    },
    
    // 资源缓存
    assets: {
      enabled: true,
      maxAge: 24 * 60 * 60 * 1000, // 24小时
      types: ['js', 'css', 'json'],
      compression: true
    }
  }
});
```

## 调试和监控

### 开发工具

```typescript
// 开发环境启用调试工具
if (process.env.NODE_ENV === 'development') {
  import('@micro-core/dev-tools').then(({ DevTools }) => {
    microCore.use(DevTools, {
      position: 'bottom-right',
      theme: 'dark',
      features: {
        appInspector: true,      // 应用检查器
        eventMonitor: true,      // 事件监控
        performanceMonitor: true, // 性能监控
        stateInspector: true,    // 状态检查器
        networkMonitor: true,    // 网络监控
        consoleIntegration: true // 控制台集成
      }
    });
  });
}
```

### 性能监控

```typescript
// 性能监控插件
microCore.use(PerformancePlugin, {
  // 监控指标
  metrics: {
    appLoadTime: true,        // 应用加载时间
    appMountTime: true,       // 应用挂载时间
    routeChangeTime: true,    // 路由切换时间
    memoryUsage: true,        // 内存使用情况
    bundleSize: true,         // 包大小
    errorRate: true           // 错误率
  },
  
  // 性能阈值
  thresholds: {
    appLoadTime: 3000,        // 3秒
    appMountTime: 1000,       // 1秒
    memoryUsage: 100 * 1024 * 1024 // 100MB
  },
  
  // 上报配置
  report: {
    url: '/api/performance',
    interval: 30000,          // 30秒上报一次
    batch: true,              // 批量上报
    compress: true            // 压缩数据
  }
});
```

## 版本兼容性

### API 版本

| 版本 | 状态 | 说明 |
|------|------|------|
| v3.x | 当前版本 | 最新稳定版本，推荐使用 |
| v2.x | 维护中 | 仅修复关键bug，建议升级 |
| v1.x | 已停止支持 | 不再维护，请尽快升级 |

### 迁移指南

如果你正在使用旧版本的 Micro-Core，请参考：

- **[从 v2.x 升级到 v3.x](../migration/v2-to-v3.md)**
- **[从 v1.x 升级到 v3.x](../migration/v1-to-v3.md)**
- **[破坏性变更说明](../migration/breaking-changes.md)**

## 获取帮助

如果你在使用 API 时遇到问题，可以通过以下方式获取帮助：

- **[GitHub Issues](https://github.com/micro-core/micro-core/issues)** - 报告bug或提出功能请求
- **[GitHub Discussions](https://github.com/micro-core/micro-core/discussions)** - 社区讨论和问答
- **[官方文档](https://micro-core.dev)** - 完整的使用指南
- **[示例项目](https://github.com/micro-core/examples)** - 实际使用案例

## 贡献指南

如果你想为 Micro-Core 的 API 文档做出贡献：

1. **Fork** 项目仓库
2. **创建** 功能分支
3. **编写** 或改进文档
4. **提交** Pull Request

详细的贡献指南请参考 [CONTRIBUTING.md](../CONTRIBUTING.md)。

---

**注意**: 本文档会随着 Micro-Core 的更新而持续更新。建议定期查看最新版本的文档以获取最新的 API 信息。