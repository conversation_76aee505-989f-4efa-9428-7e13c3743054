/**
 * Proxy 沙箱策略实现
 *
 * @description 使用 Proxy 实现的高性能沙箱隔离策略
 * <AUTHOR> <<EMAIL>>
 */
import type { SandboxConfig, SandboxContext, SandboxPerformanceMetrics, SandboxStrategy } from '../types';
/**
 * Proxy 沙箱策略
 * 使用 ES6 Proxy 实现全局变量隔离
 */
export declare class ProxySandbox implements SandboxStrategy {
    readonly type: "proxy";
    readonly name = "Proxy\u6C99\u7BB1\u7B56\u7565";
    private readonly logger;
    private readonly contexts;
    constructor();
    /**
     * 创建沙箱
     */
    create(config: SandboxConfig): Promise<SandboxContext>;
    /**
     * 激活沙箱
     */
    activate(context: SandboxContext): Promise<void>;
    /**
     * 停用沙箱
     */
    deactivate(context: SandboxContext): Promise<void>;
    /**
     * 销毁沙箱
     */
    destroy(context: SandboxContext): Promise<void>;
    /**
     * 执行代码
     */
    execute(context: SandboxContext, code: string): Promise<any>;
    /**
     * 获取性能指标
     */
    getMetrics(context: SandboxContext): SandboxPerformanceMetrics;
    /**
     * 创建沙箱全局对象
     */
    private createSandboxGlobal;
    /**
     * 创建全局代理
     */
    private createGlobalProxy;
    /**
     * 应用沙箱环境
     */
    private applySandboxEnvironment;
    /**
     * 恢复原始环境
     */
    private restoreOriginalEnvironment;
    /**
     * 在沙箱环境中执行代码
     */
    private executeInSandboxEnvironment;
    /**
     * 计算内存使用量
     */
    private calculateMemoryUsage;
    /**
     * 估算值的大小
     */
    private estimateValueSize;
    /**
     * 获取内部上下文
     */
    private getInternalContext;
    /**
     * 生成沙箱ID
     */
    private generateSandboxId;
    /**
     * 创建初始性能指标
     */
    private createInitialMetrics;
}
//# sourceMappingURL=proxy-sandbox.d.ts.map