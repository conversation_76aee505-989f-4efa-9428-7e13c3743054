import { defineConfig } from 'vite'

/**
 * 通用构建预设
 */
export const presets = {
    /**
     * 核心包构建配置
     */
    core: (options: { external?: string[] } = {}) => defineConfig({
        build: {
            lib: {
                entry: 'src/index.ts',
                name: 'MicroCore',
                formats: ['es', 'cjs'],
                fileName: (format) => `index.${format === 'es' ? 'mjs' : 'js'}`
            },
            rollupOptions: {
                external: options.external || [],
                output: {
                    globals: {
                        '@micro-core/shared': 'MicroCoreShared'
                    }
                }
            },
            sourcemap: true,
            minify: false
        },
        plugins: []
    }),

    /**
     * 共享包构建配置
     */
    shared: () => defineConfig({
        build: {
            lib: {
                entry: 'src/index.ts',
                name: 'MicroCoreShared',
                formats: ['es', 'cjs'],
                fileName: (format) => `index.${format === 'es' ? 'mjs' : 'js'}`
            },
            sourcemap: true,
            minify: false
        },
        plugins: []
    }),

    /**
     * 插件包构建配置
     */
    plugin: (options: { external?: string[] } = {}) => defineConfig({
        build: {
            lib: {
                entry: 'src/index.ts',
                name: 'MicroCorePlugin',
                formats: ['es', 'cjs'],
                fileName: (format) => `index.${format === 'es' ? 'mjs' : 'js'}`
            },
            rollupOptions: {
                external: options.external || ['@micro-core/core', '@micro-core/shared'],
                output: {
                    globals: {
                        '@micro-core/core': 'MicroCore',
                        '@micro-core/shared': 'MicroCoreShared'
                    }
                }
            },
            sourcemap: true,
            minify: false
        },
        plugins: []
    }),

    /**
     * 适配器包构建配置
     */
    adapter: (options: { external?: string[] } = {}) => defineConfig({
        build: {
            lib: {
                entry: 'src/index.ts',
                name: 'MicroCoreAdapter',
                formats: ['es', 'cjs'],
                fileName: (format) => `index.${format === 'es' ? 'mjs' : 'js'}`
            },
            rollupOptions: {
                external: options.external || ['@micro-core/core', '@micro-core/shared'],
                output: {
                    globals: {
                        '@micro-core/core': 'MicroCore',
                        '@micro-core/shared': 'MicroCoreShared'
                    }
                }
            },
            sourcemap: true,
            minify: false
        },
        plugins: []
    }),

    /**
     * 沙箱包构建配置
     */
    sandbox: (options: { external?: string[] } = {}) => defineConfig({
        build: {
            lib: {
                entry: 'src/index.ts',
                name: 'MicroCoreSandbox',
                formats: ['es', 'cjs'],
                fileName: (format) => `index.${format === 'es' ? 'mjs' : 'js'}`
            },
            rollupOptions: {
                external: options.external || ['@micro-core/core', '@micro-core/shared'],
                output: {
                    globals: {
                        '@micro-core/core': 'MicroCore',
                        '@micro-core/shared': 'MicroCoreShared'
                    }
                }
            },
            sourcemap: true,
            minify: false
        },
        plugins: []
    }),

    /**
     * 通信包构建配置
     */
    communication: (options: { external?: string[] } = {}) => defineConfig({
        build: {
            lib: {
                entry: 'src/index.ts',
                name: 'MicroCoreCommunication',
                formats: ['es', 'cjs'],
                fileName: (format) => `index.${format === 'es' ? 'mjs' : 'js'}`
            },
            rollupOptions: {
                external: options.external || ['@micro-core/core', '@micro-core/shared'],
                output: {
                    globals: {
                        '@micro-core/core': 'MicroCore',
                        '@micro-core/shared': 'MicroCoreShared'
                    }
                }
            },
            sourcemap: true,
            minify: false
        },
        plugins: []
    }),

    /**
     * 构建器包构建配置
     */
    builder: (options: { external?: string[] } = {}) => defineConfig({
        build: {
            lib: {
                entry: 'src/index.ts',
                name: 'MicroCoreBuilder',
                formats: ['es', 'cjs'],
                fileName: (format) => `index.${format === 'es' ? 'mjs' : 'js'}`
            },
            rollupOptions: {
                external: options.external || ['@micro-core/core', '@micro-core/shared'],
                output: {
                    globals: {
                        '@micro-core/core': 'MicroCore',
                        '@micro-core/shared': 'MicroCoreShared'
                    }
                }
            },
            sourcemap: true,
            minify: false
        },
        plugins: []
    }),

    /**
     * Sidecar包构建配置
     */
    sidecar: (options: { external?: string[] } = {}) => defineConfig({
        build: {
            lib: {
                entry: 'src/index.ts',
                name: 'MicroCoreSidecar',
                formats: ['es', 'cjs'],
                fileName: (format) => `index.${format === 'es' ? 'mjs' : 'js'}`
            },
            rollupOptions: {
                external: options.external || ['@micro-core/core', '@micro-core/shared'],
                output: {
                    globals: {
                        '@micro-core/core': 'MicroCore',
                        '@micro-core/shared': 'MicroCoreShared'
                    }
                }
            },
            sourcemap: true,
            minify: false
        },
        plugins: []
    })
}

export default presets