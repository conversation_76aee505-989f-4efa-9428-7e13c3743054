/**
 * 沙箱系统工厂函数
 *
 * @description 提供创建沙箱组件的工厂函数
 * <AUTHOR> <<EMAIL>>
 */
import { SandboxFactory } from './sandbox-factory';
import { SandboxManager } from './sandbox-manager';
import { ProxySandbox } from './strategies/proxy-sandbox';
import type { SandboxConfig, SandboxFactoryConfig, SandboxManagerConfig, SandboxType } from './types';
/**
 * 创建沙箱实例
 */
export declare function createSandbox(config?: Partial<SandboxConfig>): ProxySandbox;
/**
 * 创建沙箱管理器实例
 */
export declare function createSandboxManager(config?: Partial<SandboxManagerConfig>): SandboxManager;
/**
 * 创建沙箱工厂实例
 */
export declare function createSandboxFactory(config?: Partial<SandboxFactoryConfig>): SandboxFactory;
/**
 * 智能选择沙箱类型
 */
export declare function selectOptimalSandboxType(): SandboxType;
/**
 * 获取沙箱类型的兼容性信息
 */
export declare function getSandboxCompatibility(): Record<SandboxType, boolean>;
/**
 * 创建默认沙箱配置
 */
export declare function createDefaultSandboxConfig(overrides?: Partial<SandboxConfig>): SandboxConfig;
//# sourceMappingURL=factories.d.ts.map