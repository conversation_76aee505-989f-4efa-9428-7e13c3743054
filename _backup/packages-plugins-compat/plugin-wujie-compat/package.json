{"name": "@micro-core/plugin-wujie-compat", "version": "0.1.0", "description": "Wujie compatibility plugin for Micro-Core micro-frontend architecture", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "vite build && tsc --emitDeclarationOnly", "dev": "vite build --watch", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "test:watch": "vitest"}, "keywords": ["micro-frontend", "wujie", "compatibility", "plugin", "micro-core"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins/plugin-wujie-compat"}, "bugs": {"url": "https://github.com/echo008/micro-core/issues"}, "homepage": "https://micro-core.dev", "peerDependencies": {"@micro-core/core": "workspace:*"}, "dependencies": {"@micro-core/core": "workspace:*"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.3.3", "vite": "^7.0.6", "vitest": "^3.2.4"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}}