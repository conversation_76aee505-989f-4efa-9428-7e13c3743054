# qiankun 配置迁移指南

本指南详细说明如何将现有的 qiankun 微前端项目配置迁移到 Micro-Core，包括主应用配置、子应用配置、路由配置等。

## 📋 目录

- [迁移概述](#迁移概述)
- [主应用配置迁移](#主应用配置迁移)
- [子应用配置迁移](#子应用配置迁移)
- [路由配置迁移](#路由配置迁移)
- [构建配置迁移](#构建配置迁移)
- [环境配置迁移](#环境配置迁移)
- [完整示例](#完整示例)

## 迁移概述

Micro-Core 提供了完整的 qiankun 兼容层，使得迁移过程更加平滑。大部分配置可以直接迁移，少数需要进行适配调整。

### 🔄 配置对比图

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    qiankun → Micro-Core 配置迁移                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────┐              ┌─────────────────────────┐│
│  │ qiankun 配置        │              │ Micro-Core 配置         ││
│  │                     │              │                         ││
│  │ • registerMicroApps │    ────────▶ │ • microCore.registerApp ││
│  │ • start()           │              │ • microCore.start()     ││
│  │ • setDefaultMountApp│              │ • defaultApp            ││
│  │ • addGlobalUncaught │              │ • errorHandler          ││
│  │ • initGlobalState   │              │ • globalState           ││
│  └─────────────────────┘              └─────────────────────────┘│
│                                                                 │
│  ┌─────────────────────┐              ┌─────────────────────────┐│
│  │ 子应用生命周期       │              │ Micro-Core 生命周期     ││
│  │                     │              │                         ││
│  │ • bootstrap         │    ────────▶ │ • bootstrap             ││
│  │ • mount             │              │ • mount                 ││
│  │ • unmount           │              │ • unmount               ││
│  │ • update            │              │ • update (新增)         ││
│  └─────────────────────┘              └─────────────────────────┘│
│                                                                 │
│  ┌─────────────────────┐              ┌─────────────────────────┐│
│  │ 通信机制            │              │ 增强通信机制             ││
│  │                     │              │                         ││
│  │ • initGlobalState   │    ────────▶ │ • EventBus              ││
│  │ • onGlobalStateChange│              │ • GlobalState           ││
│  │ • setGlobalState    │              │ • DirectChannel         ││
│  │                     │              │ • 中间件支持             ││
│  └─────────────────────┘              └─────────────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

## 主应用配置迁移

### qiankun 主应用配置

```javascript
// qiankun 原始配置
import { registerMicroApps, start, setDefaultMountApp, initGlobalState } from 'qiankun'

// 注册微应用
registerMicroApps([
  {
    name: 'react-app',
    entry: '//localhost:3001',
    container: '#subapp-viewport',
    activeRule: '/react-app',
    props: {
      routerBase: '/react-app'
    }
  },
  {
    name: 'vue-app',
    entry: '//localhost:3002',
    container: '#subapp-viewport',
    activeRule: '/vue-app',
    props: {
      routerBase: '/vue-app'
    }
  }
])

// 初始化全局状态
const { onGlobalStateChange, setGlobalState } = initGlobalState({
  user: null,
  theme: 'light'
})

// 设置默认应用
setDefaultMountApp('/react-app')

// 启动 qiankun
start({
  sandbox: {
    css: true,
    js: true
  },
  prefetch: true,
  singular: false
})
```

### Micro-Core 迁移后配置

```typescript
// Micro-Core 迁移后配置
import { MicroCore } from '@micro-core/core'
import { QiankunCompatPlugin } from '@micro-core/plugin-qiankun-compat'

// 创建 Micro-Core 实例
const microCore = new MicroCore({
  // 使用 qiankun 兼容插件
  plugins: [
    new QiankunCompatPlugin({
      // 保持 qiankun API 兼容性
      enableLegacyAPI: true
    })
  ],
  
  // 应用配置
  apps: [
    {
      name: 'react-app',
      entry: 'http://localhost:3001',
      container: '#subapp-viewport',
      activeWhen: '/react-app',
      props: {
        routerBase: '/react-app'
      }
    },
    {
      name: 'vue-app',
      entry: 'http://localhost:3002',
      container: '#subapp-viewport',
      activeWhen: '/vue-app',
      props: {
        routerBase: '/vue-app'
      }
    }
  ],
  
  // 全局配置
  globalConfig: {
    defaultApp: '/react-app',
    sandbox: {
      css: true,
      js: true
    },
    prefetch: true,
    singular: false
  },
  
  // 全局状态初始化
  globalState: {
    user: null,
    theme: 'light'
  }
})

// 启动应用
microCore.start()

// 兼容 qiankun API（可选）
window.qiankun = {
  registerMicroApps: microCore.registerApps.bind(microCore),
  start: microCore.start.bind(microCore),
  setDefaultMountApp: microCore.setDefaultApp.bind(microCore),
  initGlobalState: microCore.initGlobalState.bind(microCore)
}
```

### 配置对照表

| qiankun 配置 | Micro-Core 配置 | 说明 |
|-------------|----------------|------|
| `registerMicroApps()` | `microCore.registerApps()` | 应用注册方法 |
| `start()` | `microCore.start()` | 启动方法 |
| `setDefaultMountApp()` | `globalConfig.defaultApp` | 默认应用设置 |
| `initGlobalState()` | `globalState` | 全局状态初始化 |
| `addGlobalUncaughtErrorHandler()` | `errorHandler` | 全局错误处理 |
| `addErrorHandler()` | `plugins.errorHandler` | 错误处理插件 |

## 子应用配置迁移

### qiankun 子应用配置

```javascript
// qiankun React 子应用配置
import React from 'react'
import ReactDOM from 'react-dom'
import App from './App'

let root = null

// 导出生命周期函数
export async function bootstrap() {
  console.log('React app bootstraped')
}

export async function mount(props) {
  console.log('React app mount', props)
  
  const { container } = props
  root = ReactDOM.createRoot(
    container ? container.querySelector('#root') : document.querySelector('#root')
  )
  root.render(<App />)
}

export async function unmount(props) {
  console.log('React app unmount', props)
  if (root) {
    root.unmount()
    root = null
  }
}

// 独立运行时
if (!window.__POWERED_BY_QIANKUN__) {
  mount({})
}
```

### Micro-Core 迁移后配置

```typescript
// Micro-Core React 子应用配置
import React from 'react'
import ReactDOM from 'react-dom/client'
import { MicroCoreReactAdapter } from '@micro-core/adapter-react'
import App from './App'

let root: ReactDOM.Root | null = null

// 创建适配器
const adapter = new MicroCoreReactAdapter({
  name: 'react-app',
  
  // 生命周期函数
  async bootstrap() {
    console.log('React app bootstraped')
  },

  async mount(props) {
    console.log('React app mount', props)
    
    const { container } = props
    const mountElement = container ? container.querySelector('#root') : document.querySelector('#root')
    
    root = ReactDOM.createRoot(mountElement!)
    root.render(<App {...props} />)
  },

  async unmount(props) {
    console.log('React app unmount', props)
    if (root) {
      root.unmount()
      root = null
    }
  },

  // 新增：更新生命周期
  async update(props) {
    console.log('React app update', props)
    // 处理属性更新
  }
})

// 注册适配器
adapter.register()

// 独立运行时（兼容 qiankun 检查）
if (!window.__POWERED_BY_QIANKUN__ && !window.__MICRO_CORE__) {
  adapter.mount({})
}

// 导出生命周期函数（保持兼容性）
export const { bootstrap, mount, unmount, update } = adapter.getLifecycles()
```

### Vue 子应用迁移

```javascript
// qiankun Vue 子应用原始配置
import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import routes from './router'

let instance = null
let router = null

function render(props = {}) {
  const { container } = props
  
  router = createRouter({
    history: createWebHistory(window.__POWERED_BY_QIANKUN__ ? '/vue-app' : '/'),
    routes
  })

  instance = createApp(App)
  instance.use(router)
  instance.mount(container ? container.querySelector('#app') : '#app')
}

export async function bootstrap() {
  console.log('Vue app bootstraped')
}

export async function mount(props) {
  console.log('Vue app mount', props)
  render(props)
}

export async function unmount() {
  console.log('Vue app unmount')
  if (instance) {
    instance.unmount()
    instance = null
    router = null
  }
}

if (!window.__POWERED_BY_QIANKUN__) {
  render()
}
```

```typescript
// Micro-Core Vue 子应用迁移后配置
import { createApp, App as VueApp } from 'vue'
import { createRouter, createWebHistory, Router } from 'vue-router'
import { MicroCoreVueAdapter } from '@micro-core/adapter-vue'
import App from './App.vue'
import routes from './router'

let instance: VueApp | null = null
let router: Router | null = null

// 创建适配器
const adapter = new MicroCoreVueAdapter({
  name: 'vue-app',
  
  async bootstrap() {
    console.log('Vue app bootstraped')
  },

  async mount(props) {
    console.log('Vue app mount', props)
    
    const { container, routerBase } = props
    
    // 创建路由
    router = createRouter({
      history: createWebHistory(routerBase || '/vue-app'),
      routes
    })

    // 创建应用实例
    instance = createApp(App)
    instance.use(router)
    
    // 注入 Micro-Core 服务
    instance.provide('microCore', adapter.getMicroCoreService())
    
    // 挂载应用
    const mountElement = container ? container.querySelector('#app') : '#app'
    instance.mount(mountElement)
  },

  async unmount() {
    console.log('Vue app unmount')
    if (instance) {
      instance.unmount()
      instance = null
      router = null
    }
  },

  async update(props) {
    console.log('Vue app update', props)
    // 处理路由基础路径更新
    if (props.routerBase && router) {
      router.push('/')
    }
  }
})

// 注册适配器
adapter.register()

// 独立运行时
if (!window.__POWERED_BY_QIANKUN__ && !window.__MICRO_CORE__) {
  adapter.mount({})
}

export const { bootstrap, mount, unmount, update } = adapter.getLifecycles()
```

## 路由配置迁移

### qiankun 路由配置

```javascript
// qiankun 主应用路由配置
import { BrowserRouter as Router, Route, Switch } from 'react-router-dom'

function App() {
  return (
    <Router>
      <div className="main-container">
        <nav>
          <Link to="/react-app">React App</Link>
          <Link to="/vue-app">Vue App</Link>
        </nav>
        
        <Switch>
          <Route path="/react-app" />
          <Route path="/vue-app" />
          <Route path="/" exact>
            <div>主应用首页</div>
          </Route>
        </Switch>
        
        <div id="subapp-viewport"></div>
      </div>
    </Router>
  )
}
```

### Micro-Core 路由配置

```typescript
// Micro-Core 主应用路由配置
import { BrowserRouter as Router, Route, Routes, Link } from 'react-router-dom'
import { useMicroCore } from '@micro-core/adapter-react'

function App() {
  const microCore = useMicroCore()

  useEffect(() => {
    // 设置路由监听
    microCore.onRouteChange((route) => {
      console.log('路由变化:', route)
    })
  }, [])

  return (
    <Router>
      <div className="main-container">
        <nav>
          <Link to="/react-app">React App</Link>
          <Link to="/vue-app">Vue App</Link>
        </nav>
        
        <Routes>
          <Route path="/react-app/*" element={<div id="react-app-container" />} />
          <Route path="/vue-app/*" element={<div id="vue-app-container" />} />
          <Route path="/" element={<div>主应用首页</div>} />
        </Routes>
        
        {/* 微应用容器 */}
        <div id="subapp-viewport"></div>
      </div>
    </Router>
  )
}
```

### 路由守卫迁移

```javascript
// qiankun 路由守卫（需要在各个应用中单独实现）
// 主应用
function checkAuth() {
  return localStorage.getItem('token') !== null
}

// 子应用中需要重复实现
```

```typescript
// Micro-Core 统一路由守卫
const microCore = new MicroCore({
  // 全局路由守卫
  routeGuards: [
    {
      name: 'auth-guard',
      guard: async (to, from) => {
        const token = localStorage.getItem('token')
        if (!token && to.path !== '/login') {
          return '/login'
        }
        return true
      }
    },
    {
      name: 'permission-guard',
      guard: async (to, from) => {
        const user = microCore.globalState.get('user')
        if (to.meta?.requiresPermission && user) {
          return user.permissions.includes(to.meta.requiresPermission)
        }
        return true
      }
    }
  ]
})
```

## 构建配置迁移

### qiankun Webpack 配置

```javascript
// qiankun 子应用 webpack 配置
const { name } = require('./package.json')

module.exports = {
  output: {
    library: `${name}-[name]`,
    libraryTarget: 'umd',
    jsonpFunction: `webpackJsonp_${name}`,
    globalObject: 'window'
  },
  
  devServer: {
    port: 3001,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': '*',
      'Access-Control-Allow-Headers': '*'
    }
  }
}
```

### Micro-Core Webpack 配置

```javascript
// Micro-Core 子应用 webpack 配置
const { MicroCoreWebpackPlugin } = require('@micro-core/webpack-plugin')
const { name } = require('./package.json')

module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      name,
      // 自动配置 UMD 输出
      autoUMD: true,
      // 自动配置 CORS
      autoCORS: true,
      // 生成应用清单
      generateManifest: true
    })
  ],
  
  // 插件会自动配置以下内容，无需手动设置
  // output: {
  //   library: `${name}-[name]`,
  //   libraryTarget: 'umd',
  //   chunkLoadingGlobal: `webpackJsonp_${name}`,
  //   globalObject: 'window'
  // },
  
  devServer: {
    port: 3001,
    // 插件会自动配置 CORS 头
  }
}
```

### Vite 配置迁移

```javascript
// qiankun Vite 配置
import { defineConfig } from 'vite'
import { name } from './package.json'

export default defineConfig({
  base: process.env.NODE_ENV === 'production' ? `/${name}/` : '/',
  
  build: {
    lib: {
      entry: 'src/main.ts',
      name: name,
      fileName: 'index',
      formats: ['umd']
    },
    rollupOptions: {
      external: ['vue'],
      output: {
        globals: {
          vue: 'Vue'
        }
      }
    }
  },
  
  server: {
    port: 3002,
    cors: true
  }
})
```

```typescript
// Micro-Core Vite 配置
import { defineConfig } from 'vite'
import { microCoreVite } from '@micro-core/vite-plugin'
import { name } from './package.json'

export default defineConfig({
  plugins: [
    microCoreVite({
      name,
      // 自动配置微前端构建
      autoConfig: true,
      // 生成应用清单
      generateManifest: true,
      // 外部依赖配置
      externals: ['vue', 'vue-router']
    })
  ],
  
  // 插件会自动配置构建选项
  server: {
    port: 3002
    // 插件会自动配置 CORS
  }
})
```

## 环境配置迁移

### 环境变量配置

```bash
# qiankun 环境变量
# .env.development
REACT_APP_PUBLIC_PATH=//localhost:3001/
REACT_APP_API_BASE_URL=http://localhost:8080/api

# .env.production  
REACT_APP_PUBLIC_PATH=https://cdn.example.com/react-app/
REACT_APP_API_BASE_URL=https://api.example.com
```

```bash
# Micro-Core 环境变量
# .env.development
MICRO_CORE_APP_NAME=react-app
MICRO_CORE_PUBLIC_PATH=//localhost:3001/
MICRO_CORE_API_BASE_URL=http://localhost:8080/api
MICRO_CORE_MAIN_APP_URL=http://localhost:3000

# .env.production
MICRO_CORE_APP_NAME=react-app
MICRO_CORE_PUBLIC_PATH=https://cdn.example.com/react-app/
MICRO_CORE_API_BASE_URL=https://api.example.com
MICRO_CORE_MAIN_APP_URL=https://app.example.com
```

### 部署配置迁移

```nginx
# qiankun Nginx 配置
server {
    listen 80;
    server_name app.example.com;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /react-app {
        proxy_pass http://react-app-server;
    }
    
    location /vue-app {
        proxy_pass http://vue-app-server;
    }
}
```

```nginx
# Micro-Core Nginx 配置
server {
    listen 80;
    server_name app.example.com;
    
    # 主应用
    location / {
        try_files $uri $uri/ /index.html;
        
        # 添加 Micro-Core 头部
        add_header X-Micro-Core-Version "1.0.0";
        add_header X-Frame-Options "SAMEORIGIN";
    }
    
    # 微应用代理
    location /micro-apps/ {
        proxy_pass http://micro-apps-server/;
        
        # 微应用专用头部
        proxy_set_header X-Micro-Core-App "true";
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 完整示例

### 迁移前后对比

```javascript
// ===== qiankun 完整示例 =====

// 主应用 main.js
import { registerMicroApps, start, initGlobalState } from 'qiankun'

registerMicroApps([
  {
    name: 'react-app',
    entry: '//localhost:3001',
    container: '#subapp-viewport',
    activeRule: '/react-app'
  }
])

const { onGlobalStateChange, setGlobalState } = initGlobalState({
  user: null
})

start()

// 子应用 main.js
let instance = null

export async function bootstrap() {}

export async function mount(props) {
  const { container } = props
  instance = new Vue({
    render: h => h(App)
  }).$mount(container ? container.querySelector('#app') : '#app')
}

export async function unmount() {
  if (instance) {
    instance.$destroy()
    instance = null
  }
}

if (!window.__POWERED_BY_QIANKUN__) {
  mount({})
}
```

```typescript
// ===== Micro-Core 迁移后示例 =====

// 主应用 main.ts
import { MicroCore } from '@micro-core/core'
import { QiankunCompatPlugin } from '@micro-core/plugin-qiankun-compat'

const microCore = new MicroCore({
  plugins: [new QiankunCompatPlugin()],
  
  apps: [
    {
      name: 'react-app',
      entry: 'http://localhost:3001',
      container: '#subapp-viewport',
      activeWhen: '/react-app'
    }
  ],
  
  globalState: {
    user: null
  }
})

microCore.start()

// 子应用 main.ts
import { MicroCoreVueAdapter } from '@micro-core/adapter-vue'

let instance: any = null

const adapter = new MicroCoreVueAdapter({
  name: 'vue-app',
  
  async bootstrap() {},

  async mount(props) {
    const { container } = props
    instance = new Vue({
      render: h => h(App)
    }).$mount(container ? container.querySelector('#app') : '#app')
  },

  async unmount() {
    if (instance) {
      instance.$destroy()
      instance = null
    }
  }
})

adapter.register()

if (!window.__POWERED_BY_QIANKUN__ && !window.__MICRO_CORE__) {
  adapter.mount({})
}

export const { bootstrap, mount, unmount } = adapter.getLifecycles()
```

### 迁移检查清单

- [ ] 主应用配置迁移完成
- [ ] 子应用生命周期函数迁移完成
- [ ] 路由配置更新完成
- [ ] 构建配置调整完成
- [ ] 环境变量配置更新
- [ ] 全局状态管理迁移完成
- [ ] 应用间通信功能验证
- [ ] 部署配置更新完成
- [ ] 性能测试通过
- [ ] 兼容性测试通过

## 相关链接

- [qiankun 生命周期迁移](/migration/qiankun/lifecycle-migration)
- [qiankun 通信迁移](/migration/qiankun/communication-migration)
- [qiankun API 对照表](/migration/qiankun/api-mapping)
- [完整迁移示例](/migration/qiankun/complete-example)

---

*最后更新: 2024-07-27*
