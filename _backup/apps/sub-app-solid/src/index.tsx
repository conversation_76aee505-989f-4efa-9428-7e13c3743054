import { render } from 'solid-js/web';
import App from './App';

let dispose: (() => void) | undefined;

// 微前端生命周期函数
export async function bootstrap(props: any) {
    console.log('Solid.js 子应用启动', props);
}

export async function mount(props: any) {
    console.log('Solid.js 子应用挂载', props);
    const container = props.container.querySelector('#sub-app-solid-root');

    if (container) {
        dispose = render(() => <App />, container);
    }
}

export async function unmount(props: any) {
    console.log('Solid.js 子应用卸载', props);
    if (dispose) {
        dispose();
        dispose = undefined;
    }
}

// 独立运行时的启动逻辑
if (!(window as any).__POWERED_BY_MICRO_CORE__) {
    const root = document.getElementById('root');
    if (root) {
        render(() => <App />, root);
    }
}