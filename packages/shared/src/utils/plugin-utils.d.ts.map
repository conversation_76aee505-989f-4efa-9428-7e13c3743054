{"version": 3, "file": "plugin-utils.d.ts", "sourceRoot": "", "sources": ["plugin-utils.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AAElF;;GAEG;AACH,MAAM,WAAW,cAAc;IAC3B,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW;IACX,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,WAAW;IACX,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,WAAW;IACX,IAAI,EAAE,UAAU,CAAC;IACjB,WAAW;IACX,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;IACxB,WAAW;IACX,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;IAChB,WAAW;IACX,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,WAAW;IACX,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,YAAY;IACZ,OAAO,CAAC,EAAE,MAAM,CAAC;CACpB;AAED;;GAEG;AACH,oBAAY,YAAY;IACpB,UAAU;IACV,aAAa,kBAAkB;IAC/B,UAAU;IACV,SAAS,cAAc;IACvB,UAAU;IACV,OAAO,YAAY;IACnB,UAAU;IACV,QAAQ,aAAa;IACrB,UAAU;IACV,UAAU,eAAe;IACzB,UAAU;IACV,YAAY,iBAAiB;IAC7B,WAAW;IACX,KAAK,UAAU;CAClB;AAED;;GAEG;AACH,MAAM,WAAW,WAAW;IACxB,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,MAAM,EAAE,YAAY,CAAC;IACrB,WAAW;IACX,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,WAAW;IACX,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,WAAW;IACX,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW;IACX,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW;IACX,SAAS,CAAC,EAAE,GAAG,CAAC;IAChB,WAAW;IACX,WAAW,CAAC,EAAE;QACV,WAAW,EAAE,MAAM,CAAC;QACpB,oBAAoB,EAAE,MAAM,CAAC;QAC7B,kBAAkB,EAAE,MAAM,CAAC;KAC9B,CAAC;CACL;AAED;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACjC,eAAe;IACf,SAAS,EAAE,OAAO,CAAC;IACnB,YAAY;IACZ,OAAO,EAAE,MAAM,EAAE,CAAC;IAClB,cAAc;IACd,SAAS,EAAE,KAAK,CAAC;QACb,IAAI,EAAE,MAAM,CAAC;QACb,QAAQ,EAAE,MAAM,CAAC;QACjB,MAAM,EAAE,MAAM,CAAC;KAClB,CAAC,CAAC;IACH,UAAU;IACV,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;CACnC;AAED;;;;GAIG;AACH,wBAAgB,oBAAoB,CAAC,MAAM,EAAE,YAAY,GAAG,IAAI,CA+B/D;AAED;;;;GAIG;AACH,wBAAgB,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,GAAG,YAAY,CAiBjF;AAED;;;;GAIG;AACH,wBAAgB,yBAAyB,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,oBAAoB,CAgDjF;AAED;;;;;GAKG;AACH,wBAAgB,yBAAyB,CAAC,eAAe,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,OAAO,CAgCjG;AAED;;;;GAIG;AACH,wBAAgB,kBAAkB,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,CA+C9D;AAED;;;;;;GAMG;AACH,wBAAgB,oBAAoB,CAChC,QAAQ,EAAE,cAAc,EACxB,SAAS,EAAE,CAAC,MAAM,EAAE,eAAe,KAAK,OAAO,CAAC,IAAI,CAAC,EACrD,WAAW,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,GAClC,MAAM,CAgHR;AAED;;;;GAIG;AACH,wBAAgB,0BAA0B,CAAC,MAAM,EAAE,YAAY,GAAG,MAAM,CAYvE;AAED;;;;GAIG;AACH,wBAAgB,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAGpD;AAED;;;;GAIG;AACH,wBAAgB,kBAAkB,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC,CAelF;AAED;;;;;GAKG;AACH,wBAAgB,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,CAWxE;AAED;;;;GAIG;AACH,wBAAgB,0BAA0B,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG;IAC3D,YAAY,EAAE,MAAM,CAAC;IACrB,cAAc,EAAE,MAAM,CAAC;IACvB,kBAAkB,EAAE,MAAM,CAAC;IAC3B,WAAW,EAAE,MAAM,CAAC;IACpB,iBAAiB,EAAE,MAAM,CAAC,UAAU,EAAE;QAClC,KAAK,EAAE,MAAM,CAAC;QACd,kBAAkB,EAAE,MAAM,CAAC;QAC3B,SAAS,EAAE,MAAM,CAAC;KACrB,CAAC,CAAC;CACN,CAuDA"}