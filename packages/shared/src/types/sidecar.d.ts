/**
 * @fileoverview Sidecar 边车模式类型定义
 * @description 定义边车模式相关的接口和类型
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * Sidecar 配置接口
 */
export interface SidecarConfig {
    /** 应用名称 */
    appName: string;
    /** 隔离配置 */
    isolation?: IsolationConfig;
    /** 代理配置 */
    proxy?: ProxyConfig;
    /** 桥接配置 */
    bridge?: BridgeConfig;
}
/**
 * 隔离配置接口
 */
export interface IsolationConfig {
    /** 是否启用隔离 */
    enabled: boolean;
    /** 隔离类型 */
    type: 'iframe' | 'shadow-dom' | 'web-component';
    /** 是否启用沙箱 */
    sandbox?: boolean;
    /** 沙箱选项 */
    sandboxOptions?: string[];
}
/**
 * 代理配置接口
 */
export interface ProxyConfig {
    /** 是否启用代理 */
    enabled: boolean;
    /** 是否启用缓存 */
    cache?: boolean;
    /** 超时时间 */
    timeout?: number;
    /** 代理规则 */
    rules?: ProxyRule[];
}
/**
 * 代理规则接口
 */
export interface ProxyRule {
    /** 匹配模式 */
    pattern: string | RegExp;
    /** 目标地址 */
    target: string;
    /** 是否重写路径 */
    rewrite?: boolean;
}
/**
 * 桥接配置接口
 */
export interface BridgeConfig {
    /** 是否启用桥接 */
    enabled: boolean;
    /** 通信协议 */
    protocol: 'postMessage' | 'websocket' | 'custom';
    /** 超时时间 */
    timeout?: number;
    /** 自定义处理器 */
    handler?: (message: any) => void;
}
//# sourceMappingURL=sidecar.d.ts.map