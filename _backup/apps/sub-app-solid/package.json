{"name": "sub-app-solid", "version": "0.1.0", "description": "Micro-Core Solid.js 子应用示例", "type": "module", "scripts": {"dev": "vite --port 3005", "build": "tsc && vite build", "preview": "vite preview", "serve": "vite preview --port 3005", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/adapter-solid": "workspace:*", "@micro-core/builder-vite": "workspace:*", "solid-js": "^1.8.0"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-plugin-solid": "^0.13.0", "typescript": "^5.3.0", "vite": "^5.4.0", "vite-plugin-solid": "^2.8.0", "vitest": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "@solidjs/testing-library": "^0.8.0", "jsdom": "^23.0.0"}, "keywords": ["micro-frontend", "sub-app", "solid", "solidjs", "vite", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}