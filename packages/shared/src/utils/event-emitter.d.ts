/**
 * @fileoverview 简单事件发射器
 * @description 提供基础的事件发射和监听功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * 事件处理函数类型
 */
export type SharedEventHandler = (...args: any[]) => void;
/**
 * 简单事件发射器类
 */
export declare class EventEmitter {
    private events;
    /**
     * 监听事件
     */
    on(event: string, handler: SharedEventHandler): this;
    /**
     * 监听一次事件
     */
    once(event: string, handler: SharedEventHandler): this;
    /**
     * 移除事件监听
     */
    off(event: string, handler?: SharedEventHandler): this;
    /**
     * 发射事件
     */
    emit(event: string, ...args: any[]): boolean;
    /**
     * 获取事件监听器数量
     */
    listenerCount(event: string): number;
    /**
     * 获取所有事件名称
     */
    eventNames(): string[];
    /**
     * 移除所有事件监听器
     */
    removeAllListeners(event?: string): this;
}
//# sourceMappingURL=event-emitter.d.ts.map