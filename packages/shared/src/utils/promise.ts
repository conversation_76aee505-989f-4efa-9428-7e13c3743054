/**
 * @fileoverview Promise工具函数
 * @description 提供Promise相关的工具函数
 * <AUTHOR> <<EMAIL>>
 */

export interface PromiseWithResolvers<T> {
    promise: Promise<T>
    resolve: (value: T | PromiseLike<T>) => void
    reject: (reason?: any) => void
}

/**
 * 创建带解析器的Promise
 */
export function createPromiseWithResolvers<T>(): PromiseWithResolvers<T> {
    let resolve: (value: T | PromiseLike<T>) => void
    let reject: (reason?: any) => void

    const promise = new Promise<T>((res, rej) => {
        resolve = res
        reject = rej
    })

    return {
        promise,
        resolve: resolve!,
        reject: reject!
    }
}