/**
 * 共享工具包 - 公共基础设施层
 *
 * @description 提供微前端项目的公共工具、类型定义、常量和错误处理机制
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import type { EventBus, GlobalState } from '../types';
/**
 * 状态变化事件
 */
export interface StateChangeEvent {
    key: string;
    oldValue: any;
    newValue: any;
    timestamp: number;
}
/**
 * 全局状态管理器实现
 */
declare class GlobalStateManager implements GlobalState {
    private state;
    private eventBus;
    constructor();
    /**
     * 设置状态值
     * @param key 键名
     * @param value 值
     */
    set(key: string, value: any): void;
    /**
     * 获取状态值
     * @param key 键名
     * @param defaultValue 默认值
     */
    get<T = any>(key: string, defaultValue?: T): T;
    /**
     * 检查是否存在指定键
     * @param key 键名
     */
    has(key: string): boolean;
    /**
     * 删除状态值
     * @param key 键名
     */
    delete(key: string): boolean;
    /**
     * 清空所有状态
     */
    clear(): void;
    /**
     * 获取所有键
     */
    keys(): string[];
    /**
     * 获取所有值
     */
    values(): any[];
    /**
     * 获取所有键值对
     */
    entries(): Array<[string, any]>;
    /**
     * 获取状态大小
     */
    size(): number;
    /**
     * 批量设置状态
     * @param states 状态对象
     */
    setMultiple(states: Record<string, any>): void;
    /**
     * 批量获取状态
     * @param keys 键名数组
     */
    getMultiple(keys: string[]): Record<string, any>;
    /**
     * 监听状态变化
     * @param callback 回调函数
     */
    onChange(callback: (change: StateChangeEvent) => void): () => void;
    /**
     * 监听特定键的状态变化
     * @param key 键名
     * @param callback 回调函数
     */
    onKeyChange(key: string, callback: (change: StateChangeEvent) => void): () => void;
    /**
     * 获取状态快照
     */
    getSnapshot(): Record<string, any>;
    /**
     * 从快照恢复状态
     * @param snapshot 状态快照
     */
    restoreFromSnapshot(snapshot: Record<string, any>): void;
    /**
     * 获取事件总线（用于高级用法）
     */
    getEventBus(): EventBus;
}
/**
 * 创建全局状态管理器
 */
export declare function createGlobalState(): GlobalState;
/**
 * 状态持久化工具
 */
export declare class StatePersistence {
    private storage;
    private prefix;
    constructor(storage?: Storage, prefix?: string);
    /**
     * 保存状态到存储
     * @param key 键名
     * @param value 值
     */
    save(key: string, value: any): void;
    /**
     * 从存储加载状态
     * @param key 键名
     * @param defaultValue 默认值
     */
    load<T = any>(key: string, defaultValue?: T): T;
    /**
     * 删除存储的状态
     * @param key 键名
     */
    remove(key: string): void;
    /**
     * 清空所有存储的状态
     */
    clear(): void;
    /**
     * 获取所有存储的键
     */
    keys(): string[];
    /**
     * 同步全局状态到存储
     * @param globalState 全局状态管理器
     * @param keys 要同步的键（可选，不传则同步所有）
     */
    syncToStorage(globalState: GlobalState, keys?: string[]): void;
    /**
     * 从存储同步到全局状态
     * @param globalState 全局状态管理器
     * @param keys 要同步的键（可选，不传则同步所有）
     */
    syncFromStorage(globalState: GlobalState, keys?: string[]): void;
}
/**
 * 创建状态持久化工具
 * @param storage 存储对象
 * @param prefix 键前缀
 */
export declare function createStatePersistence(storage?: Storage, prefix?: string): StatePersistence;
/**
 * 状态中间件类型
 */
export type StateMiddleware = (key: string, value: any, oldValue: any, next: (key: string, value: any) => void) => void;
/**
 * 带中间件的状态管理器
 */
export declare class StateManagerWithMiddleware extends GlobalStateManager {
    private middlewares;
    /**
     * 添加中间件
     * @param middleware 中间件函数
     */
    use(middleware: StateMiddleware): void;
    /**
     * 设置状态值（带中间件支持）
     * @param key 键名
     * @param value 值
     */
    set(key: string, value: any): void;
    /**
     * 执行中间件链
     */
    private executeMiddlewares;
}
/**
 * 创建带中间件的状态管理器
 */
export declare function createStateManagerWithMiddleware(): StateManagerWithMiddleware;
export {};
//# sourceMappingURL=state-utils.d.ts.map