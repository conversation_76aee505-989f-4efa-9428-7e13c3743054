/**
 * @fileoverview HTML 应用性能优化工具
 * <AUTHOR> <<EMAIL>>
 */

export interface PerformanceMetrics {
    mountTime: number
    renderTime: number
    updateTime: number
    memoryUsage?: number
}

export class PerformanceMonitor {
    private static instance: PerformanceMonitor
    private metrics: PerformanceMetrics = {
        mountTime: 0,
        renderTime: 0,
        updateTime: 0
    }
    private startTimes: Map<string, number> = new Map()

    static getInstance(): PerformanceMonitor {
        if (!PerformanceMonitor.instance) {
            PerformanceMonitor.instance = new PerformanceMonitor()
        }
        return PerformanceMonitor.instance
    }

    public startTiming(operation: string): void {
        this.startTimes.set(operation, performance.now())
    }

    public endTiming(operation: string): number {
        const startTime = this.startTimes.get(operation)
        if (!startTime) return 0

        const duration = performance.now() - startTime
        this.startTimes.delete(operation)

        switch (operation) {
            case 'mount':
                this.metrics.mountTime = duration
                break
            case 'render':
                this.metrics.renderTime = duration
                break
            case 'update':
                this.metrics.updateTime = duration
                break
        }

        return duration
    }

    public getMetrics(): PerformanceMetrics {
        return { ...this.metrics }
    }

    public logMetrics(): void {
        console.log('HTML 应用性能指标:', this.metrics)
    }

    public measureMemoryUsage(): void {
        if ('memory' in performance) {
            const memory = (performance as any).memory
            this.metrics.memoryUsage = memory.usedJSHeapSize
        }
    }
}

export const performanceMonitor = PerformanceMonitor.getInstance()

// DOM 操作优化工具
export class DOMOptimizer {
    private static documentFragment: DocumentFragment | null = null

    // 批量 DOM 操作
    static batchDOMOperations(operations: () => void): void {
        this.documentFragment = document.createDocumentFragment()
        operations()
        this.documentFragment = null
    }

    // 防抖函数
    static debounce<T extends (...args: any[]) => any>(
        func: T,
        wait: number
    ): (...args: Parameters<T>) => void {
        let timeout: number | undefined
        return (...args: Parameters<T>) => {
            clearTimeout(timeout)
            timeout = window.setTimeout(() => func(...args), wait)
        }
    }

    // 节流函数
    static throttle<T extends (...args: any[]) => any>(
        func: T,
        limit: number
    ): (...args: Parameters<T>) => void {
        let inThrottle: boolean
        return (...args: Parameters<T>) => {
            if (!inThrottle) {
                func(...args)
                inThrottle = true
                setTimeout(() => inThrottle = false, limit)
            }
        }
    }

    // 虚拟滚动实现
    static createVirtualScroll(
        container: HTMLElement,
        items: any[],
        itemHeight: number,
        renderItem: (item: any, index: number) => HTMLElement
    ): {
        update: () => void
        destroy: () => void
    } {
        const viewport = container
        const content = document.createElement('div')
        content.style.height = `${items.length * itemHeight}px`
        content.style.position = 'relative'
        viewport.appendChild(content)

        let startIndex = 0
        let endIndex = 0
        const renderedItems = new Map<number, HTMLElement>()

        const update = () => {
            const scrollTop = viewport.scrollTop
            const viewportHeight = viewport.clientHeight

            const newStartIndex = Math.floor(scrollTop / itemHeight)
            const newEndIndex = Math.min(
                newStartIndex + Math.ceil(viewportHeight / itemHeight) + 1,
                items.length
            )

            // 移除不在视口内的元素
            for (let i = startIndex; i < newStartIndex; i++) {
                const element = renderedItems.get(i)
                if (element) {
                    content.removeChild(element)
                    renderedItems.delete(i)
                }
            }

            for (let i = newEndIndex; i <= endIndex; i++) {
                const element = renderedItems.get(i)
                if (element) {
                    content.removeChild(element)
                    renderedItems.delete(i)
                }
            }

            // 添加新的元素
            for (let i = newStartIndex; i < newEndIndex; i++) {
                if (!renderedItems.has(i) && items[i]) {
                    const element = renderItem(items[i], i)
                    element.style.position = 'absolute'
                    element.style.top = `${i * itemHeight}px`
                    element.style.height = `${itemHeight}px`
                    content.appendChild(element)
                    renderedItems.set(i, element)
                }
            }

            startIndex = newStartIndex
            endIndex = newEndIndex
        }

        const throttledUpdate = this.throttle(update, 16) // 60fps
        viewport.addEventListener('scroll', throttledUpdate)

        // 初始渲染
        update()

        return {
            update,
            destroy: () => {
                viewport.removeEventListener('scroll', throttledUpdate)
                content.remove()
                renderedItems.clear()
            }
        }
    }
}

// 资源预加载工具
export class ResourcePreloader {
    private static preloadedResources = new Set<string>()

    static preloadScript(src: string): Promise<void> {
        if (this.preloadedResources.has(src)) {
            return Promise.resolve()
        }

        return new Promise((resolve, reject) => {
            const link = document.createElement('link')
            link.rel = 'preload'
            link.as = 'script'
            link.href = src
            link.onload = () => {
                this.preloadedResources.add(src)
                resolve()
            }
            link.onerror = reject
            document.head.appendChild(link)
        })
    }

    static preloadStyle(href: string): Promise<void> {
        if (this.preloadedResources.has(href)) {
            return Promise.resolve()
        }

        return new Promise((resolve, reject) => {
            const link = document.createElement('link')
            link.rel = 'preload'
            link.as = 'style'
            link.href = href
            link.onload = () => {
                this.preloadedResources.add(href)
                resolve()
            }
            link.onerror = reject
            document.head.appendChild(link)
        })
    }

    static preloadImage(src: string): Promise<void> {
        if (this.preloadedResources.has(src)) {
            return Promise.resolve()
        }

        return new Promise((resolve, reject) => {
            const img = new Image()
            img.onload = () => {
                this.preloadedResources.add(src)
                resolve()
            }
            img.onerror = reject
            img.src = src
        })
    }
}

// 懒加载工具
export class LazyLoader {
    private static observer: IntersectionObserver | null = null

    static init(): void {
        if (!this.observer && 'IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target as HTMLElement
                        const src = element.dataset.src
                        if (src) {
                            if (element.tagName === 'IMG') {
                                (element as HTMLImageElement).src = src
                            }
                            element.removeAttribute('data-src')
                            this.observer?.unobserve(element)
                        }
                    }
                })
            })
        }
    }

    static observe(element: HTMLElement): void {
        if (this.observer) {
            this.observer.observe(element)
        }
    }

    static disconnect(): void {
        if (this.observer) {
            this.observer.disconnect()
            this.observer = null
        }
    }
}