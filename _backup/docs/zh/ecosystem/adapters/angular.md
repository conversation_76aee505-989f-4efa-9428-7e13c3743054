# Angular 适配器

Micro-Core Angular 适配器提供了与 Angular 应用的深度集成，支持 Angular 12+ 版本，包含完整的依赖注入、RxJS、路由守卫等 Angular 特性支持。

## 📋 目录

- [适配器概述](#适配器概述)
- [安装配置](#安装配置)
- [基础用法](#基础用法)
- [依赖注入](#依赖注入)
- [服务集成](#服务集成)
- [路由集成](#路由集成)
- [状态管理](#状态管理)
- [最佳实践](#最佳实践)

## 适配器概述

### 核心特性

```typescript
// Angular 适配器特性
const angularAdapterFeatures = {
  // Angular 版本支持
  versions: [
    'Angular 12+',   // Ivy 渲染引擎
    'Angular 13+',   // 独立组件
    'Angular 14+',   // 可选注入器
    'Angular 15+',   // 独立 API
    'Angular 16+',   // Signals
    'Angular 17+'    // 新控制流
  ],
  
  // 集成功能
  integration: [
    '依赖注入系统',
    'RxJS 响应式编程',
    'Angular Router',
    'HttpClient',
    'Forms (Template/Reactive)',
    'Standalone Components'
  ],
  
  // 微前端特性
  microFrontend: [
    '应用生命周期',
    '服务共享',
    '事件通信',
    '路由集成',
    '模块隔离'
  ]
}
```

### 架构设计

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Angular 适配器架构                            │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Angular 应用层                           │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 组件树       │  │ 服务层       │  │ 路由系统             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    适配器层                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 生命周期适配 │  │ 服务适配     │  │ 事件适配             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Micro-Core 核心                          │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 应用管理     │  │ 通信系统     │  │ 路由系统             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 安装配置

### 安装适配器

```bash
# 安装 Angular 适配器
npm install @micro-core/adapter-angular

# 安装 Angular 依赖 (如果还没有)
npm install @angular/core @angular/common @angular/platform-browser
npm install @angular/router @angular/forms @angular/common/http

# 开发依赖
npm install @angular/cli @angular-devkit/build-angular
```

### 基础配置

```typescript
// main.ts - 主应用配置
import { bootstrapApplication } from '@angular/platform-browser'
import { MicroCore } from '@micro-core/core'
import { AngularAdapter } from '@micro-core/adapter-angular'
import { AppComponent } from './app/app.component'

// 创建 Angular 适配器
const angularAdapter = new AngularAdapter({
  // Angular 版本配置
  version: '17',
  
  // 启动配置
  bootstrap: {
    mode: 'standalone', // 'standalone' | 'module'
    enableIvy: true
  },
  
  // 依赖注入配置
  providers: [
    // 全局服务提供者
  ],
  
  // 错误处理配置
  errorHandler: {
    enabled: true,
    handler: (error: any) => {
      console.error('Angular Error:', error)
    }
  }
})

// 创建 Micro-Core 实例
const microCore = new MicroCore({
  apps: [
    {
      name: 'angular-app',
      entry: 'http://localhost:4201',
      activeWhen: '/angular-app',
      adapter: angularAdapter
    }
  ]
})

// 启动微前端
microCore.start()

// 启动主应用
bootstrapApplication(AppComponent, {
  providers: [
    // 应用级提供者
  ]
})
```

## 基础用法

### 独立组件微应用 (Angular 14+)

```typescript
// main.ts
import { bootstrapApplication } from '@angular/platform-browser'
import { provideRouter } from '@angular/router'
import { provideHttpClient } from '@angular/common/http'
import { AppComponent } from './app/app.component'
import { routes } from './app/app.routes'

let appRef: any = null

// 微应用生命周期
export async function bootstrap() {
  console.log('Angular 微应用启动中...')
}

export async function mount(props: any) {
  console.log('Angular 微应用挂载中...', props)
  
  // 动态配置路由基础路径
  const baseHref = props.basePath || '/angular-app'
  
  appRef = await bootstrapApplication(AppComponent, {
    providers: [
      provideRouter(routes),
      provideHttpClient(),
      // 注入微前端属性
      { provide: 'MICRO_PROPS', useValue: props },
      { provide: 'MICRO_CORE', useValue: props.microCore }
    ]
  })
}

export async function unmount() {
  console.log('Angular 微应用卸载中...')
  
  if (appRef) {
    appRef.destroy()
    appRef = null
  }
}

// 独立运行模式
if (!window.__MICRO_CORE__) {
  mount({
    container: '#app'
  })
}
```

### 模块化微应用 (传统方式)

```typescript
// app.module.ts
import { NgModule } from '@angular/core'
import { BrowserModule } from '@angular/platform-browser'
import { RouterModule } from '@angular/router'
import { HttpClientModule } from '@angular/common/http'

import { AppComponent } from './app.component'
import { routes } from './app.routes'

@NgModule({
  declarations: [
    AppComponent
  ],
  imports: [
    BrowserModule,
    HttpClientModule,
    RouterModule.forRoot(routes)
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }

// main.ts
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic'
import { AppModule } from './app/app.module'

let platformRef: any = null

export async function bootstrap() {
  console.log('Angular 模块微应用启动中...')
}

export async function mount(props: any) {
  console.log('Angular 模块微应用挂载中...', props)
  
  platformRef = platformBrowserDynamic([
    { provide: 'MICRO_PROPS', useValue: props },
    { provide: 'MICRO_CORE', useValue: props.microCore }
  ])
  
  await platformRef.bootstrapModule(AppModule)
}

export async function unmount() {
  console.log('Angular 模块微应用卸载中...')
  
  if (platformRef) {
    platformRef.destroy()
    platformRef = null
  }
}
```

## 依赖注入

### 微前端服务注入

```typescript
// services/micro-core.service.ts
import { Injectable, Inject, Optional } from '@angular/core'
import { BehaviorSubject, Observable } from 'rxjs'

@Injectable({
  providedIn: 'root'
})
export class MicroCoreService {
  private microCore: any
  private microProps: any
  
  constructor(
    @Optional() @Inject('MICRO_CORE') microCore: any,
    @Optional() @Inject('MICRO_PROPS') microProps: any
  ) {
    this.microCore = microCore
    this.microProps = microProps
  }
  
  // 获取微前端实例
  getMicroCore() {
    return this.microCore
  }
  
  // 获取微前端属性
  getMicroProps() {
    return this.microProps
  }
  
  // 事件发送
  emit(event: string, data?: any): void {
    this.microCore?.eventBus?.emit(event, data)
  }
  
  // 事件监听
  on(event: string): Observable<any> {
    return new Observable(observer => {
      const handler = (data: any) => observer.next(data)
      this.microCore?.eventBus?.on(event, handler)
      
      return () => {
        this.microCore?.eventBus?.off(event, handler)
      }
    })
  }
  
  // 全局状态获取
  getGlobalState(key: string): any {
    return this.microCore?.globalState?.get(key)
  }
  
  // 全局状态设置
  setGlobalState(key: string, value: any): void {
    this.microCore?.globalState?.set(key, value)
  }
  
  // 全局状态监听
  watchGlobalState(key: string): Observable<any> {
    return new Observable(observer => {
      const unwatch = this.microCore?.globalState?.watch(key, (newValue: any) => {
        observer.next(newValue)
      })
      
      return () => {
        unwatch?.()
      }
    })
  }
}
```

### 状态管理服务

```typescript
// services/state.service.ts
import { Injectable } from '@angular/core'
import { BehaviorSubject, Observable, combineLatest } from 'rxjs'
import { map, distinctUntilChanged } from 'rxjs/operators'
import { MicroCoreService } from './micro-core.service'

interface AppState {
  user: any
  theme: string
  notifications: any[]
}

@Injectable({
  providedIn: 'root'
})
export class StateService {
  private localState$ = new BehaviorSubject<AppState>({
    user: null,
    theme: 'light',
    notifications: []
  })
  
  constructor(private microCoreService: MicroCoreService) {
    this.initializeGlobalStateSync()
  }
  
  // 获取本地状态
  getState(): Observable<AppState> {
    return this.localState$.asObservable()
  }
  
  // 获取特定状态
  getStateSlice<K extends keyof AppState>(key: K): Observable<AppState[K]> {
    return this.localState$.pipe(
      map(state => state[key]),
      distinctUntilChanged()
    )
  }
  
  // 更新本地状态
  updateState(updates: Partial<AppState>): void {
    const currentState = this.localState$.value
    const newState = { ...currentState, ...updates }
    this.localState$.next(newState)
    
    // 同步到全局状态
    Object.keys(updates).forEach(key => {
      this.microCoreService.setGlobalState(`angular-app:${key}`, updates[key as keyof AppState])
    })
  }
  
  // 初始化全局状态同步
  private initializeGlobalStateSync(): void {
    // 监听全局用户状态
    this.microCoreService.watchGlobalState('currentUser').subscribe(user => {
      if (user) {
        this.updateLocalState({ user })
      }
    })
    
    // 监听全局主题状态
    this.microCoreService.watchGlobalState('theme').subscribe(theme => {
      if (theme) {
        this.updateLocalState({ theme })
      }
    })
  }
  
  // 仅更新本地状态（不同步到全局）
  private updateLocalState(updates: Partial<AppState>): void {
    const currentState = this.localState$.value
    const newState = { ...currentState, ...updates }
    this.localState$.next(newState)
  }
}
```

## 服务集成

### HTTP 拦截器

```typescript
// interceptors/micro-core.interceptor.ts
import { Injectable } from '@angular/core'
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http'
import { Observable } from 'rxjs'
import { tap, catchError } from 'rxjs/operators'
import { MicroCoreService } from '../services/micro-core.service'

@Injectable()
export class MicroCoreInterceptor implements HttpInterceptor {
  constructor(private microCoreService: MicroCoreService) {}
  
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // 添加微前端标识头
    const modifiedReq = req.clone({
      setHeaders: {
        'X-Micro-App': 'angular-app',
        'X-Micro-Core-Version': '1.0.0'
      }
    })
    
    return next.handle(modifiedReq).pipe(
      tap(event => {
        // 记录成功请求
        this.microCoreService.emit('http:success', {
          url: req.url,
          method: req.method,
          app: 'angular-app'
        })
      }),
      catchError(error => {
        // 记录失败请求
        this.microCoreService.emit('http:error', {
          url: req.url,
          method: req.method,
          error: error.message,
          app: 'angular-app'
        })
        throw error
      })
    )
  }
}
```

### 通信服务

```typescript
// services/communication.service.ts
import { Injectable } from '@angular/core'
import { Observable, Subject } from 'rxjs'
import { filter, map } from 'rxjs/operators'
import { MicroCoreService } from './micro-core.service'

interface Message {
  type: string
  payload: any
  from: string
  to?: string
  timestamp: number
}

@Injectable({
  providedIn: 'root'
})
export class CommunicationService {
  private messageSubject = new Subject<Message>()
  
  constructor(private microCoreService: MicroCoreService) {
    this.initializeMessageHandling()
  }
  
  // 发送消息到其他应用
  sendMessage(type: string, payload: any, to?: string): void {
    const message: Message = {
      type,
      payload,
      from: 'angular-app',
      to,
      timestamp: Date.now()
    }
    
    this.microCoreService.emit('message:send', message)
  }
  
  // 监听特定类型的消息
  onMessage(type: string): Observable<Message> {
    return this.messageSubject.pipe(
      filter(message => message.type === type)
    )
  }
  
  // 监听来自特定应用的消息
  onMessageFrom(from: string): Observable<Message> {
    return this.messageSubject.pipe(
      filter(message => message.from === from)
    )
  }
  
  // 广播消息
  broadcast(type: string, payload: any): void {
    this.sendMessage(type, payload)
  }
  
  // 请求-响应模式
  request(type: string, payload: any, timeout = 5000): Observable<any> {
    const requestId = this.generateRequestId()
    const responseType = `${type}:response:${requestId}`
    
    // 发送请求
    this.sendMessage(type, { ...payload, requestId })
    
    // 等待响应
    return new Observable(observer => {
      const subscription = this.onMessage(responseType).subscribe(
        response => {
          observer.next(response.payload)
          observer.complete()
        },
        error => observer.error(error)
      )
      
      // 超时处理
      const timeoutId = setTimeout(() => {
        subscription.unsubscribe()
        observer.error(new Error('Request timeout'))
      }, timeout)
      
      return () => {
        clearTimeout(timeoutId)
        subscription.unsubscribe()
      }
    })
  }
  
  // 初始化消息处理
  private initializeMessageHandling(): void {
    this.microCoreService.on('message:receive').subscribe((message: Message) => {
      this.messageSubject.next(message)
    })
  }
  
  // 生成请求ID
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}
```

## 路由集成

### 路由配置

```typescript
// app.routes.ts
import { Routes } from '@angular/router'
import { MicroGuard } from './guards/micro.guard'

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/home',
    pathMatch: 'full'
  },
  {
    path: 'home',
    loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent),
    canActivate: [MicroGuard]
  },
  {
    path: 'profile',
    loadComponent: () => import('./pages/profile/profile.component').then(m => m.ProfileComponent),
    canActivate: [MicroGuard],
    data: { requiresAuth: true }
  },
  {
    path: 'settings',
    loadChildren: () => import('./modules/settings/settings.routes').then(m => m.settingsRoutes)
  }
]
```

### 路由守卫

```typescript
// guards/micro.guard.ts
import { Injectable } from '@angular/core'
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router'
import { Observable, of } from 'rxjs'
import { map, catchError } from 'rxjs/operators'
import { MicroCoreService } from '../services/micro-core.service'
import { StateService } from '../services/state.service'

@Injectable({
  providedIn: 'root'
})
export class MicroGuard implements CanActivate {
  constructor(
    private microCoreService: MicroCoreService,
    private stateService: StateService,
    private router: Router
  ) {}
  
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    // 通知路由变化
    this.microCoreService.emit('route:change', {
      from: this.router.url,
      to: state.url,
      app: 'angular-app'
    })
    
    // 检查认证要求
    if (route.data?.['requiresAuth']) {
      return this.stateService.getStateSlice('user').pipe(
        map(user => {
          if (!user) {
            // 重定向到登录页
            this.microCoreService.emit('auth:required', {
              redirectUrl: state.url,
              app: 'angular-app'
            })
            return false
          }
          return true
        }),
        catchError(() => of(false))
      )
    }
    
    return of(true)
  }
}
```

### 路由同步服务

```typescript
// services/route-sync.service.ts
import { Injectable } from '@angular/core'
import { Router, NavigationEnd } from '@angular/router'
import { filter } from 'rxjs/operators'
import { MicroCoreService } from './micro-core.service'

@Injectable({
  providedIn: 'root'
})
export class RouteSyncService {
  constructor(
    private router: Router,
    private microCoreService: MicroCoreService
  ) {
    this.initializeRouteSync()
  }
  
  // 初始化路由同步
  private initializeRouteSync(): void {
    // 监听本地路由变化
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      this.microCoreService.emit('route:navigate', {
        url: event.url,
        app: 'angular-app'
      })
    })
    
    // 监听外部路由变化
    this.microCoreService.on('route:external').subscribe((data: any) => {
      if (data.app !== 'angular-app' && data.path.startsWith('/angular-app')) {
        const localPath = data.path.replace('/angular-app', '') || '/'
        this.router.navigateByUrl(localPath)
      }
    })
  }
}
```

## 状态管理

### NgRx 集成

```typescript
// store/app.state.ts
export interface AppState {
  micro: MicroState
  user: UserState
  ui: UIState
}

export interface MicroState {
  globalData: any
  communications: Message[]
  connectedApps: string[]
}

// store/micro/micro.reducer.ts
import { createReducer, on } from '@ngrx/store'
import * as MicroActions from './micro.actions'

const initialState: MicroState = {
  globalData: {},
  communications: [],
  connectedApps: []
}

export const microReducer = createReducer(
  initialState,
  on(MicroActions.setGlobalData, (state, { key, value }) => ({
    ...state,
    globalData: { ...state.globalData, [key]: value }
  })),
  on(MicroActions.addMessage, (state, { message }) => ({
    ...state,
    communications: [...state.communications, message]
  })),
  on(MicroActions.updateConnectedApps, (state, { apps }) => ({
    ...state,
    connectedApps: apps
  }))
)

// store/micro/micro.effects.ts
import { Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { tap, map, switchMap } from 'rxjs/operators'
import { MicroCoreService } from '../../services/micro-core.service'
import * as MicroActions from './micro.actions'

@Injectable()
export class MicroEffects {
  constructor(
    private actions$: Actions,
    private microCoreService: MicroCoreService
  ) {}
  
  // 同步全局状态到微前端
  syncGlobalState$ = createEffect(() =>
    this.actions$.pipe(
      ofType(MicroActions.setGlobalData),
      tap(({ key, value }) => {
        this.microCoreService.setGlobalState(key, value)
      })
    ), { dispatch: false }
  )
  
  // 监听微前端消息
  listenMessages$ = createEffect(() =>
    this.microCoreService.on('message:receive').pipe(
      map(message => MicroActions.addMessage({ message }))
    )
  )
}
```

## 最佳实践

### 1. 组件设计

```typescript
// components/micro-component.component.ts
import { Component, OnInit, OnDestroy, ChangeDetectionStrategy } from '@angular/core'
import { CommonModule } from '@angular/common'
import { Observable, Subject } from 'rxjs'
import { takeUntil } from 'rxjs/operators'
import { MicroCoreService } from '../services/micro-core.service'
import { StateService } from '../services/state.service'

@Component({
  selector: 'app-micro-component',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="micro-component" [class]="themeClass">
      <h2>Angular 微前端组件</h2>
      <p>当前用户: {{ (user$ | async)?.name }}</p>
      <p>主题: {{ theme$ | async }}</p>
      
      <button (click)="sendMessage()">发送消息</button>
      <button (click)="updateTheme()">切换主题</button>
    </div>
  `,
  styleUrls: ['./micro-component.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MicroComponentComponent implements OnInit, OnDestroy {
  user$ = this.stateService.getStateSlice('user')
  theme$ = this.stateService.getStateSlice('theme')
  themeClass = ''
  
  private destroy$ = new Subject<void>()
  
  constructor(
    private microCoreService: MicroCoreService,
    private stateService: StateService
  ) {}
  
  ngOnInit(): void {
    // 监听主题变化
    this.theme$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(theme => {
      this.themeClass = `theme-${theme}`
      document.body.className = this.themeClass
    })
    
    // 监听消息
    this.microCoreService.on('message:receive').pipe(
      takeUntil(this.destroy$)
    ).subscribe(message => {
      console.log('收到消息:', message)
    })
  }
  
  ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
  }
  
  sendMessage(): void {
    this.microCoreService.emit('message:send', {
      from: 'angular-app',
      content: 'Hello from Angular!',
      timestamp: Date.now()
    })
  }
  
  updateTheme(): void {
    const currentTheme = this.stateService.getState().pipe(
      map(state => state.theme)
    )
    
    currentTheme.subscribe(theme => {
      const newTheme = theme === 'light' ? 'dark' : 'light'
      this.stateService.updateState({ theme: newTheme })
    })
  }
}
```

### 2. 性能优化

```typescript
// 使用 OnPush 变更检测策略
@Component({
  changeDetection: ChangeDetectionStrategy.OnPush
})

// 使用 TrackBy 函数优化 *ngFor
trackByFn(index: number, item: any): any {
  return item.id || index
}

// 懒加载模块
const routes: Routes = [
  {
    path: 'feature',
    loadChildren: () => import('./feature/feature.module').then(m => m.FeatureModule)
  }
]

// 使用 Async Pipe 自动订阅管理
template: `
  <div *ngIf="data$ | async as data">
    {{ data.name }}
  </div>
`
```

### 3. 错误处理

```typescript
// global-error-handler.ts
import { ErrorHandler, Injectable } from '@angular/core'
import { MicroCoreService } from './services/micro-core.service'

@Injectable()
export class GlobalErrorHandler implements ErrorHandler {
  constructor(private microCoreService: MicroCoreService) {}
  
  handleError(error: any): void {
    console.error('Angular Error:', error)
    
    // 上报错误到微前端系统
    this.microCoreService.emit('error:report', {
      error: error.message,
      stack: error.stack,
      app: 'angular-app',
      timestamp: Date.now()
    })
  }
}

// 在 providers 中注册
providers: [
  { provide: ErrorHandler, useClass: GlobalErrorHandler }
]
```

### 4. 测试策略

```typescript
// micro-core.service.spec.ts
import { TestBed } from '@angular/core/testing'
import { MicroCoreService } from './micro-core.service'

describe('MicroCoreService', () => {
  let service: MicroCoreService
  let mockMicroCore: any
  
  beforeEach(() => {
    mockMicroCore = {
      eventBus: {
        emit: jasmine.createSpy('emit'),
        on: jasmine.createSpy('on'),
        off: jasmine.createSpy('off')
      },
      globalState: {
        get: jasmine.createSpy('get'),
        set: jasmine.createSpy('set'),
        watch: jasmine.createSpy('watch')
      }
    }
    
    TestBed.configureTestingModule({
      providers: [
        { provide: 'MICRO_CORE', useValue: mockMicroCore },
        { provide: 'MICRO_PROPS', useValue: {} }
      ]
    })
    
    service = TestBed.inject(MicroCoreService)
  })
  
  it('should emit events', () => {
    service.emit('test-event', { data: 'test' })
    expect(mockMicroCore.eventBus.emit).toHaveBeenCalledWith('test-event', { data: 'test' })
  })
  
  it('should set global state', () => {
    service.setGlobalState('key', 'value')
    expect(mockMicroCore.globalState.set).toHaveBeenCalledWith('key', 'value')
  })
})
```

### 5. 内存管理

```typescript
// 使用 takeUntil 模式管理订阅
export class ComponentBase implements OnDestroy {
  protected destroy$ = new Subject<void>()
  
  ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
  }
}

// 在组件中使用
export class MyComponent extends ComponentBase implements OnInit {
  ngOnInit(): void {
    this.someObservable$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(data => {
      // 处理数据
    })
  }
}
```

## 总结

Angular 适配器为 Angular 应用提供了完整的微前端集成方案：

- ✅ **版本支持** - Angular 12+ 全版本支持，包括最新的独立组件
- ✅ **依赖注入** - 完整的 DI 系统集成，支持服务共享
- ✅ **RxJS 集成** - 响应式编程模式，完美适配 Angular 生态
- ✅ **路由集成** - Angular Router 深度集成，支持路由守卫
- ✅ **状态管理** - 支持 NgRx 等状态管理库
- ✅ **性能优化** - OnPush 策略、懒加载、TrackBy 等优化
- ✅ **测试支持** - 完整的单元测试和集成测试支持

通过 Angular 适配器，您可以将现有的 Angular 应用无缝集成到微前端架构中，充分利用 Angular 的企业级特性和微前端的架构优势。
