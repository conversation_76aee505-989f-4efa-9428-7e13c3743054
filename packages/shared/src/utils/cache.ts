/**
 * @fileoverview 缓存工具函数
 * @description 提供缓存管理相关的工具函数
 * <AUTHOR> <<EMAIL>>
 */

export interface CacheManager {
    set(key: string, value: any, ttl?: number): void
    get(key: string): any
    has(key: string): boolean
    delete(key: string): boolean
    clear(): void
}

interface CacheItem {
    value: any
    expireTime?: number
}

/**
 * 创建缓存管理器
 */
export function createCacheManager(): CacheManager {
    const cache = new Map<string, CacheItem>()

    const isExpired = (item: CacheItem): boolean => {
        return item.expireTime !== undefined && Date.now() > item.expireTime
    }

    return {
        set(key: string, value: any, ttl?: number): void {
            const item: CacheItem = { value }
            if (ttl && ttl > 0) {
                item.expireTime = Date.now() + ttl
            }
            cache.set(key, item)
        },

        get(key: string): any {
            const item = cache.get(key)
            if (!item) return undefined

            if (isExpired(item)) {
                cache.delete(key)
                return undefined
            }

            return item.value
        },

        has(key: string): boolean {
            const item = cache.get(key)
            if (!item) return false

            if (isExpired(item)) {
                cache.delete(key)
                return false
            }

            return true
        },

        delete(key: string): boolean {
            return cache.delete(key)
        },

        clear(): void {
            cache.clear()
        }
    }
}