{"version": 3, "file": "event-utils.d.ts", "sourceRoot": "", "sources": ["event-utils.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH;;GAEG;AACH,MAAM,WAAW,aAAa,CAAC,CAAC,GAAG,GAAG;IAClC,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;CACpB;AAED;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACjC,cAAc;IACd,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,qBAAqB;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,cAAc;IAC3B,cAAc;IACd,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,aAAa;IACb,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,eAAe;IACf,WAAW,CAAC,EAAE,OAAO,CAAC;CACzB;AAYD;;GAEG;AACH,qBAAa,QAAQ;IACjB,OAAO,CAAC,MAAM,CAA6C;IAC3D,OAAO,CAAC,MAAM,CAA2B;IACzC,OAAO,CAAC,iBAAiB,CAAK;gBAElB,MAAM,GAAE,cAAmB;IASvC;;OAEG;IACH,EAAE,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,oBAAoB,GAAG,MAAM,IAAI;IAiClG;;OAEG;IACH,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,oBAAoB,EAAE,MAAM,CAAC,GAAG,MAAM,IAAI;IAIlH;;OAEG;IACH,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI;IA0B9D;;OAEG;IACH,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,OAAO;IA4CnD;;OAEG;IACH,OAAO,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC;IAkB/D;;OAEG;IACH,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM;IAIpC;;OAEG;IACH,UAAU,IAAI,MAAM,EAAE;IAItB;;OAEG;IACH,SAAS,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE;IAKrD;;OAEG;IACH,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO;IAIpC;;OAEG;IACH,KAAK,IAAI,IAAI;IAOb;;OAEG;IACH,kBAAkB,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI;IAWxC;;OAEG;IACH,QAAQ,IAAI;QACR,WAAW,EAAE,MAAM,CAAC;QACpB,cAAc,EAAE,MAAM,CAAC;QACvB,UAAU,EAAE,MAAM,EAAE,CAAC;QACrB,YAAY,EAAE,KAAK,CAAC;YAChB,KAAK,EAAE,MAAM,CAAC;YACd,aAAa,EAAE,MAAM,CAAC;YACtB,aAAa,EAAE,MAAM,CAAC;SACzB,CAAC,CAAC;KACN;IAuBD;;OAEG;IACH,OAAO,IAAI,IAAI;CAMlB;AAED;;GAEG;AACH,wBAAgB,cAAc,CAAC,MAAM,CAAC,EAAE,cAAc,GAAG,QAAQ,CAEhE;AAED;;GAEG;AACH,eAAO,MAAM,cAAc,UAGzB,CAAC;AAEH;;GAEG;AACH,wBAAgB,gBAAgB,CAAC,CAAC,SAAS,MAAM,mBAAmB,EAChE,OAAO,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ,EACxC,IAAI,EAAE,CAAC,EACP,QAAQ,EAAE,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC,CAAC,KAAK,IAAI,EACjD,OAAO,CAAC,EAAE,OAAO,GAAG,uBAAuB,GAC5C,MAAM,IAAI,CAMZ;AAED;;GAEG;AACH,wBAAgB,mBAAmB,CAAC,CAAC,SAAS,MAAM,mBAAmB,EACnE,OAAO,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ,EACxC,IAAI,EAAE,CAAC,EACP,QAAQ,EAAE,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC,CAAC,KAAK,IAAI,EACjD,OAAO,CAAC,EAAE,OAAO,GAAG,oBAAoB,GACzC,IAAI,CAEN;AAED;;GAEG;AACH,wBAAgB,oBAAoB,CAAC,CAAC,SAAS,MAAM,mBAAmB,EACpE,OAAO,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ,EACxC,IAAI,EAAE,CAAC,EACP,QAAQ,EAAE,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC,CAAC,KAAK,IAAI,EACjD,OAAO,CAAC,EAAE,OAAO,GAAG,uBAAuB,GAC5C,IAAI,CAON;AAED;;GAEG;AACH,wBAAgB,qBAAqB,CAAC,CAAC,SAAS,MAAM,mBAAmB,EACrE,SAAS,EAAE,WAAW,EACtB,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,CAAC,EACP,QAAQ,EAAE,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,KAAK,IAAI,EACtE,OAAO,CAAC,EAAE,OAAO,GAAG,uBAAuB,GAC5C,MAAM,IAAI,CAeZ;AAED;;GAEG;AACH,wBAAgB,mBAAmB,CAC/B,OAAO,EAAE,WAAW,EACpB,SAAS,EAAE,MAAM,EACjB,MAAM,CAAC,EAAE,GAAG,EACZ,OAAO,CAAC,EAAE,eAAe,GAC1B,OAAO,CAST;AAED;;GAEG;AACH,wBAAgB,YAAY,CAAC,CAAC,SAAS,MAAM,mBAAmB,EAC5D,OAAO,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ,EACxC,IAAI,EAAE,CAAC,EACP,OAAO,CAAC,EAAE,MAAM,GACjB,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAmBjC;AAED;;GAEG;AACH,wBAAgB,aAAa,CAAC,CAAC,SAAS,MAAM,mBAAmB,EAC7D,OAAO,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ,EACxC,IAAI,EAAE,CAAC,EACP,QAAQ,EAAE,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC,CAAC,KAAK,IAAI,EACjD,KAAK,EAAE,MAAM,EACb,OAAO,CAAC,EAAE,OAAO,GAAG,uBAAuB,GAC5C,MAAM,IAAI,CAiBZ;AAED;;GAEG;AACH,wBAAgB,aAAa,CAAC,CAAC,SAAS,MAAM,mBAAmB,EAC7D,OAAO,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ,EACxC,IAAI,EAAE,CAAC,EACP,QAAQ,EAAE,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC,CAAC,KAAK,IAAI,EACjD,KAAK,EAAE,MAAM,EACb,OAAO,CAAC,EAAE,OAAO,GAAG,uBAAuB,GAC5C,MAAM,IAAI,CAiBZ;AAED;;GAEG;AACH,qBAAa,kBAAkB;IAC3B;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,aAAa,EAAE,GAAG,EAAE,MAAM,GAAG,OAAO;IAI/D;;OAEG;IACH,MAAM,CAAC,cAAc,CACjB,KAAK,EAAE,aAAa,EACpB,KAAK,EAAE;QACH,GAAG,EAAE,MAAM,CAAC;QACZ,IAAI,CAAC,EAAE,OAAO,CAAC;QACf,GAAG,CAAC,EAAE,OAAO,CAAC;QACd,KAAK,CAAC,EAAE,OAAO,CAAC;QAChB,IAAI,CAAC,EAAE,OAAO,CAAC;KAClB,GACF,OAAO;IAUV;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,aAAa,GAAG,IAAI;IAIjD;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,KAAK,EAAE,aAAa,GAAG,IAAI;CAGrD;AAED;;GAEG;AACH,qBAAa,eAAe;IACxB;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,GAAG;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE;IAQ7F;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,GAAG,QAAQ,GAAG,OAAO,GAAG,OAAO;IAKvF;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,GAAG,OAAO;CAGzE;AAED;;GAEG;AACH,qBAAa,eAAe;IACxB;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,GAAG;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE;IAQxF;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,GAAG,MAAM;IAMxD;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,GAAG;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE;CAcjE"}