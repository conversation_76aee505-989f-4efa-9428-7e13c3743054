/**
 * @fileoverview Micro-Core 常量定义
 * @description 集中管理所有项目常量，确保一致性
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
export declare const APP_STATUS: {
    readonly NOT_LOADED: "NOT_LOADED";
    readonly LOADING_SOURCE_CODE: "LOADING_SOURCE_CODE";
    readonly NOT_BOOTSTRAPPED: "NOT_BOOTSTRAPPED";
    readonly BOOTSTRAPPING: "BOOTSTRAPPING";
    readonly NOT_MOUNTED: "NOT_MOUNTED";
    readonly MOUNTING: "MOUNTING";
    readonly MOUNTED: "MOUNTED";
    readonly UPDATING: "UPDATING";
    readonly UNMOUNTING: "UNMOUNTING";
    readonly UNLOADING: "UNLOADING";
    readonly LOAD_ERROR: "LOAD_ERROR";
    readonly SKIP_BECAUSE_BROKEN: "SKIP_BECAUSE_BROKEN";
};
export declare const SANDBOX_TYPES: {
    readonly PROXY: "proxy";
    readonly DEFINE_PROPERTY: "defineProperty";
    readonly WEB_COMPONENT: "webComponent";
    readonly IFRAME: "iframe";
    readonly NAMESPACE: "namespace";
    readonly FEDERATION: "federation";
};
export declare const SANDBOX_STATUS: {
    readonly CREATED: "CREATED";
    readonly ACTIVATING: "ACTIVATING";
    readonly ACTIVE: "ACTIVE";
    readonly DEACTIVATING: "DEACTIVATING";
    readonly INACTIVE: "INACTIVE";
    readonly ERROR: "ERROR";
    readonly DESTROYED: "DESTROYED";
};
export declare const LIFECYCLE_HOOKS: {
    readonly BEFORE_LOAD: "beforeLoad";
    readonly LOADED: "loaded";
    readonly BEFORE_BOOTSTRAP: "beforeBootstrap";
    readonly BOOTSTRAPPED: "bootstrapped";
    readonly BEFORE_MOUNT: "beforeMount";
    readonly MOUNTED: "mounted";
    readonly BEFORE_UPDATE: "beforeUpdate";
    readonly UPDATED: "updated";
    readonly BEFORE_UNMOUNT: "beforeUnmount";
    readonly UNMOUNTED: "unmounted";
    readonly BEFORE_UNLOAD: "beforeUnload";
    readonly UNLOADED: "unloaded";
    readonly ERROR: "error";
};
export declare const EVENT_TYPES: {
    readonly APP_LOADED: "app:loaded";
    readonly APP_MOUNTED: "app:mounted";
    readonly APP_UNMOUNTED: "app:unmounted";
    readonly APP_ERROR: "app:error";
    readonly ROUTE_CHANGED: "route:changed";
    readonly ROUTE_BEFORE_CHANGE: "route:beforeChange";
    readonly ROUTE_ERROR: "route:error";
    readonly MESSAGE_SENT: "message:sent";
    readonly MESSAGE_RECEIVED: "message:received";
    readonly MESSAGE_ERROR: "message:error";
    readonly SYSTEM_READY: "system:ready";
    readonly SYSTEM_ERROR: "system:error";
    readonly SYSTEM_SHUTDOWN: "system:shutdown";
};
export declare const ERROR_CODES: {
    readonly SYSTEM_INIT_FAILED: 1001;
    readonly SYSTEM_NOT_READY: 1002;
    readonly SYSTEM_ALREADY_STARTED: 1003;
    readonly SYSTEM_SHUTDOWN_FAILED: 1004;
    readonly APP_NOT_FOUND: 2001;
    readonly APP_LOAD_FAILED: 2002;
    readonly APP_BOOTSTRAP_FAILED: 2003;
    readonly APP_MOUNT_FAILED: 2004;
    readonly APP_UNMOUNT_FAILED: 2005;
    readonly APP_UPDATE_FAILED: 2006;
    readonly APP_ALREADY_REGISTERED: 2007;
    readonly APP_INVALID_CONFIG: 2008;
    readonly SANDBOX_CREATE_FAILED: 3001;
    readonly SANDBOX_ACTIVATE_FAILED: 3002;
    readonly SANDBOX_DEACTIVATE_FAILED: 3003;
    readonly SANDBOX_EXEC_FAILED: 3004;
    readonly SANDBOX_TYPE_NOT_SUPPORTED: 3005;
    readonly SANDBOX_CREATION_FAILED: 3006;
    readonly SANDBOX_ACTIVATION_FAILED: 3007;
    readonly SANDBOX_DEACTIVATION_FAILED: 3008;
    readonly SANDBOX_DESTRUCTION_FAILED: 3009;
    readonly SANDBOX_EXECUTION_FAILED: 3010;
    readonly STRATEGY_NOT_FOUND: 3011;
    readonly STRATEGY_INITIALIZATION_FAILED: 3012;
    readonly SANDBOX_LIMIT_EXCEEDED: 3013;
    readonly SANDBOX_NOT_FOUND: 3014;
    readonly PLUGIN_NOT_FOUND: 4001;
    readonly PLUGIN_INSTALL_FAILED: 4002;
    readonly PLUGIN_UNINSTALL_FAILED: 4003;
    readonly PLUGIN_INVALID_CONFIG: 4004;
    readonly PLUGIN_DEPENDENCY_MISSING: 4005;
    readonly PLUGIN_MANAGER_INIT_FAILED: 4006;
    readonly PLUGIN_ALREADY_REGISTERED: 4007;
    readonly PLUGIN_REGISTRATION_FAILED: 4008;
    readonly PLUGIN_LOAD_FAILED: 4009;
    readonly PLUGIN_ACTIVATION_FAILED: 4010;
    readonly PLUGIN_HAS_ACTIVE_DEPENDENTS: 4011;
    readonly PLUGIN_DEACTIVATION_FAILED: 4012;
    readonly PLUGIN_UNLOAD_FAILED: 4013;
    readonly PLUGIN_UNREGISTRATION_FAILED: 4014;
    readonly BATCH_PLUGIN_LOAD_FAILED: 4015;
    readonly BATCH_PLUGIN_ACTIVATION_FAILED: 4016;
    readonly PLUGIN_MANAGER_DESTROY_FAILED: 4017;
    readonly INVALID_PLUGIN_ID: 4018;
    readonly INVALID_PLUGIN_METADATA: 4019;
    readonly DEPENDENCY_NOT_FOUND: 4020;
    readonly DEPENDENCY_VERSION_MISMATCH: 4021;
    readonly CIRCULAR_DEPENDENCY: 4022;
    readonly ROUTE_NOT_FOUND: 5001;
    readonly ROUTE_NAVIGATE_FAILED: 5002;
    readonly ROUTE_INVALID_CONFIG: 5003;
    readonly ROUTE_GUARD_REJECTED: 5004;
    readonly COMMUNICATION_SEND_FAILED: 6001;
    readonly COMMUNICATION_RECEIVE_FAILED: 6002;
    readonly COMMUNICATION_TIMEOUT: 6003;
    readonly COMMUNICATION_INVALID_MESSAGE: 6004;
    readonly HANDLER_REGISTRATION_FAILED: 6005;
    readonly MESSAGE_CREATION_FAILED: 6006;
    readonly MESSAGE_SERIALIZATION_FAILED: 6007;
    readonly MESSAGE_DESERIALIZATION_FAILED: 6008;
    readonly INVALID_MESSAGE_TYPE: 6009;
    readonly INVALID_PROTOCOL_HANDLER: 6010;
    readonly INVALID_MESSAGE_FORMAT: 6011;
    readonly MESSAGE_TOO_LARGE: 6012;
    readonly CHECKSUM_MISMATCH: 6013;
    readonly MESSAGE_TIMEOUT: 6014;
    readonly RESOURCE_LOAD_FAILED: 7001;
    readonly RESOURCE_NOT_FOUND: 7002;
    readonly RESOURCE_INVALID_TYPE: 7003;
    readonly RESOURCE_CACHE_FAILED: 7004;
};
export declare const RESOURCE_TYPES: {
    readonly SCRIPT: "script";
    readonly STYLE: "style";
    readonly HTML: "html";
    readonly JSON: "json";
    readonly IMAGE: "image";
    readonly FONT: "font";
    readonly OTHER: "other";
};
export declare const PLUGIN_TYPES: {
    readonly CORE: "core";
    readonly SANDBOX: "sandbox";
    readonly ROUTER: "router";
    readonly COMMUNICATION: "communication";
    readonly AUTH: "auth";
    readonly LOGGER: "logger";
    readonly METRICS: "metrics";
    readonly DEVTOOLS: "devtools";
    readonly LOADER: "loader";
    readonly PREFETCH: "prefetch";
    readonly COMPAT: "compat";
};
export declare const FRAMEWORK_TYPES: {
    readonly REACT: "react";
    readonly VUE2: "vue2";
    readonly VUE3: "vue3";
    readonly ANGULAR: "angular";
    readonly SVELTE: "svelte";
    readonly SOLID: "solid";
    readonly HTML: "html";
    readonly VANILLA: "vanilla";
};
export declare const BUILDER_TYPES: {
    readonly VITE: "vite";
    readonly WEBPACK: "webpack";
    readonly ROLLUP: "rollup";
    readonly ESBUILD: "esbuild";
    readonly PARCEL: "parcel";
    readonly RSPACK: "rspack";
    readonly TURBOPACK: "turbopack";
};
export declare const PERFORMANCE_THRESHOLDS: {
    readonly APP_LOAD_TIME: 500;
    readonly APP_MOUNT_TIME: 100;
    readonly ROUTE_CHANGE_TIME: 50;
    readonly MEMORY_USAGE_LIMIT: number;
    readonly BUNDLE_SIZE_LIMIT: number;
};
export declare const CACHE_CONFIG: {
    readonly DEFAULT_TTL: number;
    readonly MAX_SIZE: 100;
    readonly STORAGE_KEY_PREFIX: "micro-core:";
};
export declare const DEV_MODE: {
    readonly DEVELOPMENT: "development";
    readonly PRODUCTION: "production";
    readonly TEST: "test";
};
export declare const LOG_LEVELS: {
    readonly ERROR: 0;
    readonly WARN: 1;
    readonly INFO: 2;
    readonly DEBUG: 3;
    readonly TRACE: 4;
};
export type AppStatus = typeof APP_STATUS[keyof typeof APP_STATUS];
export type SandboxType = typeof SANDBOX_TYPES[keyof typeof SANDBOX_TYPES];
export type SandboxStatus = typeof SANDBOX_STATUS[keyof typeof SANDBOX_STATUS];
export type LifecycleHook = typeof LIFECYCLE_HOOKS[keyof typeof LIFECYCLE_HOOKS];
export type EventType = typeof EVENT_TYPES[keyof typeof EVENT_TYPES];
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];
export type ResourceType = typeof RESOURCE_TYPES[keyof typeof RESOURCE_TYPES];
export type PluginType = typeof PLUGIN_TYPES[keyof typeof PLUGIN_TYPES];
export type FrameworkType = typeof FRAMEWORK_TYPES[keyof typeof FRAMEWORK_TYPES];
export type BuilderType = typeof BUILDER_TYPES[keyof typeof BUILDER_TYPES];
export type LogLevel = typeof LOG_LEVELS[keyof typeof LOG_LEVELS];
//# sourceMappingURL=index.d.ts.map