/**
 * @fileoverview 事件总线测试
 * @description 测试事件总线的功能
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi } from 'vitest'
import { EventBus } from '../event-bus'

describe('EventBus', () => {
    let eventBus: EventBus

    beforeEach(() => {
        eventBus = new EventBus()
    })

    describe('事件监听和触发', () => {
        it('应该能够监听和触发事件', () => {
            const handler = vi.fn()

            eventBus.on('test-event', handler)
            eventBus.emit('test-event', 'test-data')

            expect(handler).toHaveBeenCalledWith('test-data')
        })

        it('应该能够监听多个事件处理器', () => {
            const handler1 = vi.fn()
            const handler2 = vi.fn()

            eventBus.on('test-event', handler1)
            eventBus.on('test-event', handler2)
            eventBus.emit('test-event', 'test-data')

            expect(handler1).toHaveBeenCalledWith('test-data')
            expect(handler2).toHaveBeenCalledWith('test-data')
        })

        it('应该能够一次性监听事件', () => {
            const handler = vi.fn()

            eventBus.once('test-event', handler)
            eventBus.emit('test-event', 'data1')
            eventBus.emit('test-event', 'data2')

            expect(handler).toHaveBeenCalledTimes(1)
            expect(handler).toHaveBeenCalledWith('data1')
        })

        it('应该能够取消事件监听', () => {
            const handler = vi.fn()

            eventBus.on('test-event', handler)
            eventBus.off('test-event', handler)
            eventBus.emit('test-event', 'test-data')

            expect(handler).not.toHaveBeenCalled()
        })

        it('应该能够取消所有事件监听', () => {
            const handler1 = vi.fn()
            const handler2 = vi.fn()

            eventBus.on('test-event', handler1)
            eventBus.on('test-event', handler2)
            eventBus.off('test-event')
            eventBus.emit('test-event', 'test-data')

            expect(handler1).not.toHaveBeenCalled()
            expect(handler2).not.toHaveBeenCalled()
        })
    })

    describe('通配符事件', () => {
        it('应该支持通配符事件监听', () => {
            const handler = vi.fn()

            eventBus.on('app.*', handler)
            eventBus.emit('app.loaded', 'data1')
            eventBus.emit('app.mounted', 'data2')

            expect(handler).toHaveBeenCalledTimes(2)
            expect(handler).toHaveBeenNthCalledWith(1, 'data1')
            expect(handler).toHaveBeenNthCalledWith(2, 'data2')
        })

        it('应该支持全局事件监听', () => {
            const handler = vi.fn()

            eventBus.on('*', handler)
            eventBus.emit('any-event', 'data')

            expect(handler).toHaveBeenCalledWith('data')
        })
    })

    describe('事件管理', () => {
        it('应该能够获取事件监听器数量', () => {
            const handler1 = vi.fn()
            const handler2 = vi.fn()

            eventBus.on('test-event', handler1)
            eventBus.on('test-event', handler2)

            expect(eventBus.listenerCount('test-event')).toBe(2)
        })

        it('应该能够获取所有事件名称', () => {
            eventBus.on('event1', vi.fn())
            eventBus.on('event2', vi.fn())

            const eventNames = eventBus.eventNames()
            expect(eventNames).toContain('event1')
            expect(eventNames).toContain('event2')
        })

        it('应该能够清除所有事件监听器', () => {
            eventBus.on('event1', vi.fn())
            eventBus.on('event2', vi.fn())

            eventBus.removeAllListeners()

            expect(eventBus.eventNames()).toHaveLength(0)
        })
    })

    describe('错误处理', () => {
        it('应该能够处理监听器中的错误', () => {
            const errorHandler = vi.fn()
            const normalHandler = vi.fn()

            eventBus.on('error', errorHandler)
            eventBus.on('test-event', () => {
                throw new Error('Test error')
            })
            eventBus.on('test-event', normalHandler)

            eventBus.emit('test-event', 'data')

            expect(errorHandler).toHaveBeenCalled()
            expect(normalHandler).toHaveBeenCalledWith('data')
        })

        it('应该在没有监听器时正常工作', () => {
            expect(() => {
                eventBus.emit('nonexistent-event', 'data')
            }).not.toThrow()
        })
    })

    describe('性能测试', () => {
        it('应该能够处理大量事件', () => {
            const handler = vi.fn()
            eventBus.on('test-event', handler)

            const eventCount = 1000
            for (let i = 0; i < eventCount; i++) {
                eventBus.emit('test-event', i)
            }

            expect(handler).toHaveBeenCalledTimes(eventCount)
        })

        it('应该能够处理大量监听器', () => {
            const handlers = Array.from({ length: 100 }, () => vi.fn())

            handlers.forEach(handler => {
                eventBus.on('test-event', handler)
            })

            eventBus.emit('test-event', 'data')

            handlers.forEach(handler => {
                expect(handler).toHaveBeenCalledWith('data')
            })
        })
    })
})