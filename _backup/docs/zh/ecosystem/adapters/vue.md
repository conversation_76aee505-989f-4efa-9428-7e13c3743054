# Vue 适配器

Micro-Core Vue 适配器提供了与 Vue 应用的深度集成，支持 Vue 2.7+、Vue 3.x 版本，包含完整的 Composition API 和 Options API 支持。

## 📋 目录

- [适配器概述](#适配器概述)
- [安装配置](#安装配置)
- [基础用法](#基础用法)
- [Composition API](#composition-api)
- [Options API](#options-api)
- [状态管理](#状态管理)
- [路由集成](#路由集成)
- [最佳实践](#最佳实践)

## 适配器概述

### 核心特性

```typescript
// Vue 适配器特性
const vueAdapterFeatures = {
  // Vue 版本支持
  versions: [
    'Vue 2.7+',      // Composition API 支持
    'Vue 3.x'        // 完整的 Vue 3 特性
  ],
  
  // 集成功能
  integration: [
    'Composition API',
    'Options API',
    'Vuex/Pinia',
    'Vue Router',
    'Teleport',
    'Suspense'
  ],
  
  // 微前端特性
  microFrontend: [
    '应用生命周期',
    '状态共享',
    '事件通信',
    '路由集成',
    '组件隔离'
  ]
}
```

### 架构设计

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Vue 适配器架构                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Vue 应用层                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 组件树       │  │ Composition │  │ Options API         │ │ │
│  │  │             │  │ API         │  │                     │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    适配器层                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 生命周期适配 │  │ 状态适配     │  │ 事件适配             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Micro-Core 核心                          │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 应用管理     │  │ 通信系统     │  │ 路由系统             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 安装配置

### 安装适配器

```bash
# 安装 Vue 适配器
npm install @micro-core/adapter-vue

# 安装 Vue 依赖 (如果还没有)
npm install vue

# Vue 2 项目
npm install vue@2.7

# Vue 3 项目  
npm install vue@3

# TypeScript 支持
npm install @types/vue
```

### 基础配置

```typescript
// main.ts - 主应用配置
import { createApp } from 'vue'
import { MicroCore } from '@micro-core/core'
import { VueAdapter } from '@micro-core/adapter-vue'
import App from './App.vue'

// 创建 Vue 适配器
const vueAdapter = new VueAdapter({
  // Vue 版本配置
  version: '3',
  
  // 渲染配置
  render: {
    mode: 'createApp', // 'createApp' | 'new Vue'
    mountStrategy: 'replace' // 'replace' | 'append'
  },
  
  // 错误处理配置
  errorHandler: {
    enabled: true,
    handler: (error, instance, info) => {
      console.error('Vue Error:', error, info)
    }
  },
  
  // 生命周期配置
  lifecycle: {
    enableCompositionAPI: true,
    autoCleanup: true
  }
})

// 创建 Micro-Core 实例
const microCore = new MicroCore({
  apps: [
    {
      name: 'vue-app',
      entry: 'http://localhost:3001',
      activeWhen: '/vue-app',
      adapter: vueAdapter
    }
  ]
})

// 启动微前端
microCore.start()

// 创建主应用
const app = createApp(App)
app.mount('#app')
```

## 基础用法

### Vue 3 微应用

```typescript
// vue-micro-app/main.ts
import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import App from './App.vue'
import routes from './routes'

let app: any = null
let router: any = null
let pinia: any = null

// 微应用生命周期
export async function bootstrap() {
  console.log('Vue 微应用启动中...')
}

export async function mount(props: any) {
  console.log('Vue 微应用挂载中...', props)
  
  // 创建路由
  router = createRouter({
    history: createWebHistory(props.basePath || '/vue-app'),
    routes
  })
  
  // 创建状态管理
  pinia = createPinia()
  
  // 创建应用实例
  app = createApp(App)
  app.use(router)
  app.use(pinia)
  
  // 注入微前端属性
  app.provide('microProps', props)
  app.provide('microCore', props.microCore)
  
  // 挂载应用
  const container = props.container || '#vue-app'
  app.mount(container)
}

export async function unmount() {
  console.log('Vue 微应用卸载中...')
  
  if (app) {
    app.unmount()
    app = null
  }
  
  router = null
  pinia = null
}

// 独立运行模式
if (!window.__MICRO_CORE__) {
  mount({
    container: '#app'
  })
}
```

### Vue 2 微应用

```typescript
// vue2-micro-app/main.ts
import Vue from 'vue'
import VueRouter from 'vue-router'
import Vuex from 'vuex'
import App from './App.vue'
import routes from './routes'
import store from './store'

Vue.use(VueRouter)
Vue.use(Vuex)

let instance: any = null

// 微应用生命周期
export async function bootstrap() {
  console.log('Vue 2 微应用启动中...')
}

export async function mount(props: any) {
  console.log('Vue 2 微应用挂载中...', props)
  
  // 创建路由
  const router = new VueRouter({
    mode: 'history',
    base: props.basePath || '/vue2-app',
    routes
  })
  
  // 创建 Vue 实例
  instance = new Vue({
    router,
    store,
    render: h => h(App),
    data() {
      return {
        microProps: props,
        microCore: props.microCore
      }
    }
  })
  
  // 挂载应用
  const container = props.container || '#vue2-app'
  instance.$mount(container)
}

export async function unmount() {
  console.log('Vue 2 微应用卸载中...')
  
  if (instance) {
    instance.$destroy()
    instance.$el.innerHTML = ''
    instance = null
  }
}

// 独立运行模式
if (!window.__MICRO_CORE__) {
  mount({
    container: '#app'
  })
}
```

## Composition API

### 微前端 Hooks

```typescript
// composables/useMicroCore.ts
import { inject, ref, onMounted, onUnmounted } from 'vue'

export function useMicroCore() {
  const microCore = inject('microCore')
  const microProps = inject('microProps')
  
  return {
    microCore,
    microProps
  }
}

// 事件通信 Hook
export function useMicroEvents() {
  const { microCore } = useMicroCore()
  const eventBus = microCore?.eventBus
  
  const emit = (event: string, data?: any) => {
    eventBus?.emit(event, data)
  }
  
  const on = (event: string, handler: Function) => {
    eventBus?.on(event, handler)
    
    onUnmounted(() => {
      eventBus?.off(event, handler)
    })
  }
  
  return {
    emit,
    on
  }
}

// 全局状态 Hook
export function useMicroState() {
  const { microCore } = useMicroCore()
  const globalState = microCore?.globalState
  
  const getState = (key: string) => {
    return globalState?.get(key)
  }
  
  const setState = (key: string, value: any) => {
    globalState?.set(key, value)
  }
  
  const watchState = (key: string, callback: Function) => {
    const unwatch = globalState?.watch(key, callback)
    
    onUnmounted(() => {
      unwatch?.()
    })
    
    return unwatch
  }
  
  return {
    getState,
    setState,
    watchState
  }
}
```

### 组件中使用

```vue
<!-- components/MicroComponent.vue -->
<template>
  <div class="micro-component">
    <h2>Vue 微前端组件</h2>
    <p>当前用户: {{ user?.name }}</p>
    <p>消息数量: {{ messageCount }}</p>
    
    <button @click="sendMessage">发送消息</button>
    <button @click="updateUser">更新用户</button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMicroEvents, useMicroState } from '@/composables/useMicroCore'

// 使用微前端 Hooks
const { emit, on } = useMicroEvents()
const { getState, setState, watchState } = useMicroState()

// 响应式状态
const messageCount = ref(0)
const user = computed(() => getState('currentUser'))

// 发送消息
const sendMessage = () => {
  emit('message:send', {
    from: 'vue-app',
    content: 'Hello from Vue!',
    timestamp: Date.now()
  })
  messageCount.value++
}

// 更新用户信息
const updateUser = () => {
  setState('currentUser', {
    name: 'Vue User',
    role: 'admin',
    lastActive: Date.now()
  })
}

// 监听事件
onMounted(() => {
  // 监听消息事件
  on('message:received', (data: any) => {
    console.log('收到消息:', data)
    messageCount.value++
  })
  
  // 监听用户状态变化
  watchState('currentUser', (newUser: any, oldUser: any) => {
    console.log('用户状态变化:', { newUser, oldUser })
  })
})
</script>
```

## Options API

### 混入 (Mixin) 方式

```typescript
// mixins/microCoreMixin.ts
export const microCoreMixin = {
  data() {
    return {
      microCore: null,
      microProps: null
    }
  },
  
  created() {
    this.microCore = this.$microCore
    this.microProps = this.$microProps
  },
  
  methods: {
    // 发送事件
    $emit(event: string, data?: any) {
      this.microCore?.eventBus?.emit(event, data)
    },
    
    // 监听事件
    $on(event: string, handler: Function) {
      this.microCore?.eventBus?.on(event, handler)
    },
    
    // 取消监听
    $off(event: string, handler: Function) {
      this.microCore?.eventBus?.off(event, handler)
    },
    
    // 获取全局状态
    $getState(key: string) {
      return this.microCore?.globalState?.get(key)
    },
    
    // 设置全局状态
    $setState(key: string, value: any) {
      this.microCore?.globalState?.set(key, value)
    }
  },
  
  beforeDestroy() {
    // 清理事件监听
    if (this._microEventHandlers) {
      this._microEventHandlers.forEach(({ event, handler }) => {
        this.$off(event, handler)
      })
    }
  }
}
```

### 组件中使用

```vue
<!-- components/OptionsComponent.vue -->
<template>
  <div class="options-component">
    <h2>Options API 组件</h2>
    <p>当前主题: {{ theme }}</p>
    <p>在线用户: {{ onlineUsers.length }}</p>
    
    <button @click="changeTheme">切换主题</button>
    <button @click="notifyUsers">通知用户</button>
  </div>
</template>

<script>
import { microCoreMixin } from '@/mixins/microCoreMixin'

export default {
  name: 'OptionsComponent',
  mixins: [microCoreMixin],
  
  data() {
    return {
      theme: 'light',
      onlineUsers: []
    }
  },
  
  created() {
    // 获取初始状态
    this.theme = this.$getState('theme') || 'light'
    this.onlineUsers = this.$getState('onlineUsers') || []
    
    // 监听状态变化
    this.$on('theme:changed', this.handleThemeChange)
    this.$on('user:online', this.handleUserOnline)
    this.$on('user:offline', this.handleUserOffline)
  },
  
  methods: {
    // 切换主题
    changeTheme() {
      const newTheme = this.theme === 'light' ? 'dark' : 'light'
      this.theme = newTheme
      this.$setState('theme', newTheme)
      this.$emit('theme:changed', newTheme)
    },
    
    // 通知用户
    notifyUsers() {
      this.$emit('notification:send', {
        type: 'info',
        message: '来自 Vue 应用的通知',
        timestamp: Date.now()
      })
    },
    
    // 处理主题变化
    handleThemeChange(newTheme) {
      this.theme = newTheme
      document.body.className = `theme-${newTheme}`
    },
    
    // 处理用户上线
    handleUserOnline(user) {
      if (!this.onlineUsers.find(u => u.id === user.id)) {
        this.onlineUsers.push(user)
      }
    },
    
    // 处理用户下线
    handleUserOffline(userId) {
      this.onlineUsers = this.onlineUsers.filter(u => u.id !== userId)
    }
  }
}
</script>
```

## 状态管理

### Pinia 集成 (Vue 3)

```typescript
// stores/microStore.ts
import { defineStore } from 'pinia'
import { useMicroCore } from '@/composables/useMicroCore'

export const useMicroStore = defineStore('micro', () => {
  const { microCore } = useMicroCore()
  const globalState = microCore?.globalState
  
  // 本地状态
  const localData = ref({})
  
  // 同步全局状态
  const syncGlobalState = (key: string, value: any) => {
    globalState?.set(key, value)
  }
  
  // 获取全局状态
  const getGlobalState = (key: string) => {
    return globalState?.get(key)
  }
  
  // 监听全局状态变化
  const watchGlobalState = (key: string, callback: Function) => {
    return globalState?.watch(key, callback)
  }
  
  return {
    localData,
    syncGlobalState,
    getGlobalState,
    watchGlobalState
  }
})
```

### Vuex 集成 (Vue 2)

```typescript
// store/modules/micro.ts
const microModule = {
  namespaced: true,
  
  state: {
    globalData: {},
    localData: {}
  },
  
  mutations: {
    SET_GLOBAL_DATA(state, { key, value }) {
      state.globalData = {
        ...state.globalData,
        [key]: value
      }
    },
    
    SET_LOCAL_DATA(state, { key, value }) {
      state.localData = {
        ...state.localData,
        [key]: value
      }
    }
  },
  
  actions: {
    // 同步全局状态
    syncGlobalState({ commit }, { key, value }) {
      // 更新本地状态
      commit('SET_GLOBAL_DATA', { key, value })
      
      // 同步到微前端全局状态
      if (window.__MICRO_CORE__) {
        window.__MICRO_CORE__.globalState.set(key, value)
      }
    },
    
    // 监听全局状态变化
    watchGlobalState({ commit }, { key, callback }) {
      if (window.__MICRO_CORE__) {
        return window.__MICRO_CORE__.globalState.watch(key, (newValue) => {
          commit('SET_GLOBAL_DATA', { key, value: newValue })
          callback?.(newValue)
        })
      }
    }
  },
  
  getters: {
    getGlobalData: (state) => (key) => {
      return state.globalData[key]
    },
    
    getLocalData: (state) => (key) => {
      return state.localData[key]
    }
  }
}

export default microModule
```

## 路由集成

### Vue Router 配置

```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import { useMicroCore } from '@/composables/useMicroCore'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue')
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/About.vue')
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const { microCore } = useMicroCore()
  
  // 通知路由变化
  microCore?.eventBus?.emit('route:change', {
    from: from.fullPath,
    to: to.fullPath,
    app: 'vue-app'
  })
  
  next()
})

export default router
```

### 路由同步

```typescript
// utils/routeSync.ts
import { useMicroCore } from '@/composables/useMicroCore'
import router from '@/router'

export function setupRouteSync() {
  const { microCore } = useMicroCore()
  
  // 监听主应用路由变化
  microCore?.eventBus?.on('route:navigate', (data) => {
    if (data.app !== 'vue-app' && data.path.startsWith('/vue-app')) {
      const localPath = data.path.replace('/vue-app', '') || '/'
      router.push(localPath)
    }
  })
  
  // 监听其他应用路由变化
  microCore?.eventBus?.on('route:sync', (data) => {
    console.log('路由同步:', data)
  })
}
```

## 最佳实践

### 1. 组件设计原则

```vue
<!-- 推荐的组件设计 -->
<template>
  <div class="micro-component" :class="themeClass">
    <!-- 使用作用域样式 -->
    <div class="content">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useMicroState } from '@/composables/useMicroCore'

// 响应全局主题
const { getState } = useMicroState()
const theme = computed(() => getState('theme') || 'light')
const themeClass = computed(() => `theme-${theme.value}`)
</script>

<style scoped>
.micro-component {
  /* 使用 CSS 变量支持主题切换 */
  background-color: var(--bg-color);
  color: var(--text-color);
}

.theme-light {
  --bg-color: #ffffff;
  --text-color: #333333;
}

.theme-dark {
  --bg-color: #1a1a1a;
  --text-color: #ffffff;
}
</style>
```

### 2. 性能优化

```typescript
// 懒加载组件
const LazyComponent = defineAsyncComponent({
  loader: () => import('./HeavyComponent.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

// 缓存计算属性
const expensiveValue = computed(() => {
  return heavyCalculation(props.data)
})

// 防抖处理
import { debounce } from 'lodash-es'

const debouncedSearch = debounce((query: string) => {
  performSearch(query)
}, 300)
```

### 3. 错误处理

```typescript
// 全局错误处理
app.config.errorHandler = (error, instance, info) => {
  console.error('Vue Error:', error)
  console.error('Component:', instance)
  console.error('Info:', info)
  
  // 上报错误到微前端系统
  microCore?.eventBus?.emit('error:report', {
    error: error.message,
    stack: error.stack,
    component: instance?.$options.name,
    info,
    app: 'vue-app'
  })
}

// 组件级错误处理
export default {
  errorCaptured(error, instance, info) {
    console.error('Component Error:', error)
    return false // 阻止错误继续传播
  }
}
```

### 4. 内存管理

```typescript
// 组件卸载时清理资源
onUnmounted(() => {
  // 清理定时器
  if (timer) {
    clearInterval(timer)
  }
  
  // 清理事件监听
  eventHandlers.forEach(({ event, handler }) => {
    microCore?.eventBus?.off(event, handler)
  })
  
  // 清理观察者
  watchers.forEach(unwatch => unwatch())
})
```

### 5. 开发调试

```typescript
// 开发环境调试工具
if (process.env.NODE_ENV === 'development') {
  // 暴露调试接口
  window.__VUE_APP_DEBUG__ = {
    microCore,
    router,
    store: pinia,
    app: getCurrentInstance()
  }
  
  // 性能监控
  app.config.performance = true
}
```

## 总结

Vue 适配器为 Vue 应用提供了完整的微前端集成方案：

- ✅ **完整支持** - Vue 2.7+ 和 Vue 3.x 全版本支持
- ✅ **API 兼容** - Composition API 和 Options API 双重支持
- ✅ **状态管理** - Pinia/Vuex 深度集成
- ✅ **路由同步** - Vue Router 无缝集成
- ✅ **性能优化** - 懒加载、缓存、防抖等优化策略
- ✅ **开发体验** - 完整的 TypeScript 支持和调试工具

通过 Vue 适配器，您可以轻松将现有的 Vue 应用集成到微前端架构中，享受微前端带来的技术栈独立、团队协作和渐进式升级等优势。