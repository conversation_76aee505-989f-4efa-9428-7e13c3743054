/**
 * 共享工具包 - 配置管理工具
 *
 * @description 提供配置管理、合并、验证等功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * 配置管理器接口
 */
export interface IConfigManager<T = Record<string, any>> {
    get<K extends keyof T>(key: K): T[K];
    set<K extends keyof T>(key: K, value: T[K]): void;
    merge(config: Partial<T>): void;
    reset(): void;
    validate?(): boolean;
}
/**
 * 通用配置管理器
 */
export declare class ConfigManager<T extends Record<string, any> = Record<string, any>> implements IConfigManager<T> {
    private config;
    private defaultConfig;
    constructor(defaultConfig: T);
    /**
     * 获取配置值
     */
    get<K extends keyof T>(key: K): T[K];
    /**
     * 设置配置值
     */
    set<K extends keyof T>(key: K, value: T[K]): void;
    /**
     * 合并配置
     */
    merge(config: Partial<T>): void;
    /**
     * 重置配置
     */
    reset(): void;
    /**
     * 获取所有配置
     */
    getAll(): T;
    /**
     * 验证配置
     */
    validate(): boolean;
    /**
     * 克隆配置管理器
     */
    clone(): ConfigManager<T>;
}
/**
 * 深度合并配置对象
 */
export declare function mergeConfig<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T;
/**
 * 验证配置对象
 */
export declare function validateConfig<T>(config: T, schema: ConfigSchema<T>): ConfigValidationResult;
/**
 * 配置规则接口
 */
export interface ConfigRule {
    required?: boolean;
    type?: 'string' | 'number' | 'boolean' | 'object' | 'function';
    validator?: (value: any) => boolean;
}
/**
 * 配置模式类型
 */
export type ConfigSchema<T> = {
    [K in keyof T]?: ConfigRule;
};
/**
 * 配置验证结果
 */
export interface ConfigValidationResult {
    valid: boolean;
    errors: string[];
}
/**
 * 创建配置管理器
 */
export declare function createConfigManager<T extends Record<string, any>>(defaultConfig: T): ConfigManager<T>;
/**
 * 配置工具函数集合
 */
export declare const configUtils: {
    merge: typeof mergeConfig;
    validate: typeof validateConfig;
    create: typeof createConfigManager;
};
//# sourceMappingURL=config-utils.d.ts.map