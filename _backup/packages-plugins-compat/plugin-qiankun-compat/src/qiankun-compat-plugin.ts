import type { MicroCoreKernel, Plugin } from '@micro-core/core';
import { logger } from '@micro-core/core';
import { QiankunAdapter, type QiankunAppConfig, type QiankunGlobalState } from './qiankun-adapter';

/**
 * qiankun 兼容插件配置
 */
export interface QiankunCompatPluginConfig {
    /** 是否自动暴露全局 API */
    exposeGlobalAPI?: boolean;
    /** 全局 API 命名空间 */
    globalNamespace?: string;
    /** 是否启用调试模式 */
    debug?: boolean;
}

/**
 * qiankun 兼容插件
 * 提供与 qiankun 完全兼容的 API 接口
 */
export class QiankunCompatPlugin implements Plugin {
    name = 'qiankun-compat';
    version = '0.1.0';
    private config: QiankunCompatPluginConfig;
    private adapter?: QiankunAdapter;

    constructor(config: QiankunCompatPluginConfig = {}) {
        this.config = {
            exposeGlobalAPI: true,
            globalNamespace: 'qiankun',
            debug: false,
            ...config
        };
    }

    /**
     * 安装插件
     */
    install(kernel: MicroCoreKernel): void {
        // 创建适配器
        this.adapter = new QiankunAdapter(kernel);

        // 扩展内核
        this.extendKernel(kernel);

        // 暴露全局 API
        if (this.config.exposeGlobalAPI) {
            this.exposeGlobalAPI();
        }

        logger.info('qiankun 兼容插件安装完成');
    }

    /**
     * 卸载插件
     */
    uninstall(kernel: MicroCoreKernel): void {
        // 清理全局 API
        if (this.config.exposeGlobalAPI) {
            this.cleanupGlobalAPI();
        }

        // 移除内核扩展
        this.removeKernelExtensions(kernel);

        this.adapter = undefined;

        logger.info('qiankun 兼容插件卸载完成');
    }

    /**
     * 扩展内核
     */
    private extendKernel(kernel: MicroCoreKernel): void {
        if (!this.adapter) return;

        // 添加 qiankun 兼容方法到内核
        (kernel as any).qiankun = {
            registerMicroApps: (apps: QiankunAppConfig[]) => {
                this.adapter?.registerMicroApps(apps);
            },
            registerMicroApp: (app: QiankunAppConfig) => {
                this.adapter?.registerMicroApp(app);
            },
            start: (options?: any) => {
                return this.adapter?.start(options);
            },
            loadMicroApp: (app: QiankunAppConfig, configuration?: any) => {
                return this.adapter?.loadMicroApp(app, configuration);
            },
            initGlobalState: (state?: Record<string, any>) => {
                return this.adapter?.initGlobalState(state);
            },
            getAppStatus: (name: string) => {
                return this.adapter?.getAppStatus(name);
            },
            getApps: () => {
                return this.adapter?.getApps();
            }
        };
    }

    /**
     * 移除内核扩展
     */
    private removeKernelExtensions(kernel: MicroCoreKernel): void {
        delete (kernel as any).qiankun;
    }

    /**
     * 暴露全局 API
     */
    private exposeGlobalAPI(): void {
        if (!this.adapter) return;

        const globalAPI = {
            registerMicroApps: (apps: QiankunAppConfig[]) => {
                this.adapter?.registerMicroApps(apps);
            },
            start: (options?: any) => {
                return this.adapter?.start(options);
            },
            loadMicroApp: (app: QiankunAppConfig, configuration?: any) => {
                return this.adapter?.loadMicroApp(app, configuration);
            },
            initGlobalState: (state?: Record<string, any>) => {
                return this.adapter?.initGlobalState(state);
            },
            getAppStatus: (name: string) => {
                return this.adapter?.getAppStatus(name);
            },
            getApps: () => {
                return this.adapter?.getApps();
            }
        };

        // 暴露到全局
        if (this.config.globalNamespace) {
            (window as any)[this.config.globalNamespace] = globalAPI;
        }

        // 兼容原始 qiankun API
        (window as any).registerMicroApps = globalAPI.registerMicroApps;
        (window as any).start = globalAPI.start;
        (window as any).loadMicroApp = globalAPI.loadMicroApp;
        (window as any).initGlobalState = globalAPI.initGlobalState;

        logger.debug('qiankun 全局 API 已暴露');
    }

    /**
     * 清理全局 API
     */
    private cleanupGlobalAPI(): void {
        if (this.config.globalNamespace) {
            delete (window as any)[this.config.globalNamespace];
        }

        delete (window as any).registerMicroApps;
        delete (window as any).start;
        delete (window as any).loadMicroApp;
        delete (window as any).initGlobalState;

        logger.debug('qiankun 全局 API 已清理');
    }
}

/**
 * 创建 qiankun 兼容插件实例
 */
export function createQiankunCompatPlugin(config?: QiankunCompatPluginConfig): QiankunCompatPlugin {
    return new QiankunCompatPlugin(config);
}

/**
 * 默认 qiankun 兼容插件实例
 */
export const qiankunCompatPlugin = new QiankunCompatPlugin();

// 导出 qiankun 兼容的类型和接口
export type { QiankunAppConfig, QiankunGlobalState };
