/**
 * @fileoverview 微前端核心初始化配置
 * <AUTHOR> <<EMAIL>>
 */

import { MicroCoreKernel } from '@micro-core/core';
import type { SidecarConfig } from '@micro-core/sidecar';
import { createSidecar } from '@micro-core/sidecar';

// 全局微前端实例
let microCoreKernel: MicroCoreKernel | null = null;
let sidecar: any = null;

/**
 * 微前端配置
 */
const MICRO_CORE_CONFIG = {
    // 核心配置
    core: {
        debug: import.meta.env.DEV,
        timeout: 30000,
        retryCount: 3,
        errorHandler: (error: Error) => {
            console.error('[MicroCore] 错误:', error);
            if (window.__MICRO_CORE_PLAYGROUND__?.showError) {
                window.__MICRO_CORE_PLAYGROUND__.showError(
                    `微前端核心错误: ${error.message}`
                );
            }
        }
    },

    // 边车配置
    sidecar: {
        autoDiscovery: {
            enabled: true,
            scanInterval: 5000,
            patterns: [
                'http://localhost:3001',  // React 应用
                'http://localhost:3002',  // Vue 应用
                'http://localhost:3003',  // Angular 应用
                'http://localhost:3004',  // HTML 应用
                'http://127.0.0.1:*',
                'http://0.0.0.0:*'
            ],
            exclude: [
                'http://localhost:3000'   // 排除主应用
            ]
        },

        frameworkDetection: {
            enabled: true,
            timeout: 10000,
            fallback: 'vanilla'
        },

        routing: {
            mode: 'history' as const,
            base: '/',
            fallback: '/404'
        },

        sandbox: {
            enabled: true,
            type: 'proxy' as const,
            isolation: true
        },

        prefetch: {
            enabled: true,
            strategy: 'idle' as const,
            delay: 2000
        },

        development: {
            enabled: import.meta.env.DEV,
            hotReload: true,
            devtools: true,
            logging: true
        },

        plugins: [
            '@micro-core/plugin-router',
            '@micro-core/plugin-communication',
            '@micro-core/plugin-sandbox-proxy'
        ],

        global: {
            timeout: 30000,
            retryCount: 3,
            errorHandler: (error: Error) => {
                console.error('[Sidecar] 错误:', error);
                if (window.__MICRO_CORE_PLAYGROUND__?.showError) {
                    window.__MICRO_CORE_PLAYGROUND__.showError(
                        `边车模式错误: ${error.message}`
                    );
                }
            }
        }
    } as SidecarConfig
};

/**
 * 预定义的微前端应用配置
 */
const PREDEFINED_APPS = [
    {
        name: 'react-app',
        displayName: 'React 应用',
        entry: 'http://localhost:3001',
        container: '#react-app-container',
        activeWhen: '/react-app',
        framework: 'react',
        props: {
            title: 'React 微前端应用',
            theme: 'light'
        }
    },
    {
        name: 'vue-app',
        displayName: 'Vue 应用',
        entry: 'http://localhost:3002',
        container: '#vue-app-container',
        activeWhen: '/vue-app',
        framework: 'vue',
        props: {
            title: 'Vue 微前端应用',
            theme: 'light'
        }
    },
    {
        name: 'angular-app',
        displayName: 'Angular 应用',
        entry: 'http://localhost:3003',
        container: '#angular-app-container',
        activeWhen: '/angular-app',
        framework: 'angular',
        props: {
            title: 'Angular 微前端应用',
            theme: 'light'
        }
    },
    {
        name: 'html-app',
        displayName: 'HTML 应用',
        entry: 'http://localhost:3004',
        container: '#html-app-container',
        activeWhen: '/html-app',
        framework: 'vanilla',
        props: {
            title: 'HTML 微前端应用',
            theme: 'light'
        }
    }
];

/**
 * 初始化微前端核心
 */
export async function initializeMicroCore(): Promise<void> {
    try {
        console.log('🔧 开始初始化微前端核心...');

        // 1. 创建内核实例
        microCoreKernel = new MicroCoreKernel();
        console.log('✅ 微前端内核创建完成');

        // 2. 创建边车实例
        sidecar = createSidecar(MICRO_CORE_CONFIG.sidecar);
        console.log('✅ 边车实例创建完成');

        // 3. 初始化边车
        await sidecar.initialize();
        console.log('✅ 边车初始化完成');

        // 4. 注册预定义应用
        await registerPredefinedApps();
        console.log('✅ 预定义应用注册完成');

        // 5. 设置事件监听
        setupEventListeners();
        console.log('✅ 事件监听设置完成');

        // 6. 暴露全局实例
        exposeGlobalInstances();
        console.log('✅ 全局实例暴露完成');

        console.log('🎉 微前端核心初始化完成！');

    } catch (error) {
        console.error('❌ 微前端核心初始化失败:', error);
        throw error;
    }
}

/**
 * 注册预定义应用
 */
async function registerPredefinedApps(): Promise<void> {
    if (!sidecar) {
        throw new Error('边车实例未初始化');
    }

    for (const appConfig of PREDEFINED_APPS) {
        try {
            await sidecar.registerApp(appConfig);
            console.log(`✅ 应用注册成功: ${appConfig.displayName}`);
        } catch (error) {
            console.warn(`⚠️ 应用注册失败: ${appConfig.displayName}`, error);
            // 不抛出错误，继续注册其他应用
        }
    }
}

/**
 * 设置事件监听
 */
function setupEventListeners(): void {
    if (!sidecar) return;

    const kernel = sidecar.getKernel();

    // 应用生命周期事件
    kernel.on?.('app:mounted', (event: any) => {
        console.log('📱 应用已挂载:', event.data?.name);
        // 可以在这里添加应用挂载后的逻辑
    });

    kernel.on?.('app:unmounted', (event: any) => {
        console.log('📱 应用已卸载:', event.data?.name);
        // 可以在这里添加应用卸载后的逻辑
    });

    kernel.on?.('app:error', (event: any) => {
        console.error('❌ 应用错误:', event.data?.error);
        if (window.__MICRO_CORE_PLAYGROUND__?.showError) {
            window.__MICRO_CORE_PLAYGROUND__.showError(
                `应用运行错误: ${event.data?.error?.message || '未知错误'}`
            );
        }
    });

    // 边车事件
    kernel.on?.('sidecar:initialized', (event: any) => {
        console.log('🚗 边车已初始化:', event.data?.config);
    });

    kernel.on?.('sidecar:config-updated', (event: any) => {
        console.log('🔧 边车配置已更新:', event.data?.config);
    });

    // 路由事件
    kernel.on?.('router:navigate', (event: any) => {
        console.log('🧭 路由导航:', event.data?.path);
    });

    // 通信事件
    kernel.on?.('communication:message', (event: any) => {
        console.log('💬 应用间通信:', event.data?.message);
    });
}

/**
 * 暴露全局实例
 */
function exposeGlobalInstances(): void {
    if (typeof window !== 'undefined') {
        // 暴露给开发者调试使用
        (window as any).__MICRO_CORE__ = {
            kernel: microCoreKernel,
            sidecar: sidecar,
            config: MICRO_CORE_CONFIG,
            apps: PREDEFINED_APPS,

            // 工具方法
            getApps: () => sidecar?.getApps() || [],
            getAppStatus: (name: string) => sidecar?.getAppStatus(name),
            startApp: (name: string) => sidecar?.startApp(name),
            stopApp: (name: string) => sidecar?.stopApp(name),
            registerApp: (config: any) => sidecar?.registerApp(config),

            // 调试方法
            debug: {
                enableLogging: () => {
                    console.log('🔍 启用调试日志');
                    // 启用详细日志
                },
                disableLogging: () => {
                    console.log('🔇 禁用调试日志');
                    // 禁用详细日志
                },
                getStats: () => {
                    return {
                        kernel: microCoreKernel,
                        sidecar: sidecar?.getManager?.()?.getStats?.(),
                        apps: sidecar?.getApps?.()?.map((app: any) => ({
                            name: app.name,
                            status: app.status,
                            framework: app.framework
                        }))
                    };
                }
            }
        };

        console.log('🌍 全局实例已暴露到 window.__MICRO_CORE__');
        console.log('💡 在控制台中输入 window.__MICRO_CORE__ 查看可用的调试方法');
    }
}

/**
 * 获取微前端内核实例
 */
export function getMicroCoreKernel(): MicroCoreKernel | null {
    return microCoreKernel;
}

/**
 * 获取边车实例
 */
export function getSidecar(): any {
    return sidecar;
}

/**
 * 获取配置
 */
export function getConfig() {
    return MICRO_CORE_CONFIG;
}

/**
 * 获取预定义应用
 */
export function getPredefinedApps() {
    return PREDEFINED_APPS;
}

/**
 * 销毁微前端核心
 */
export async function destroyMicroCore(): Promise<void> {
    try {
        console.log('🧹 开始销毁微前端核心...');

        if (sidecar) {
            await sidecar.destroy();
            sidecar = null;
            console.log('✅ 边车实例已销毁');
        }

        if (microCoreKernel) {
            await microCoreKernel.destroy?.();
            microCoreKernel = null;
            console.log('✅ 微前端内核已销毁');
        }

        // 清理全局实例
        if (typeof window !== 'undefined') {
            delete (window as any).__MICRO_CORE__;
        }

        console.log('🎉 微前端核心销毁完成！');

    } catch (error) {
        console.error('❌ 微前端核心销毁失败:', error);
        throw error;
    }
}

// 页面卸载时自动清理
if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
        destroyMicroCore().catch(console.error);
    });
}

// 默认导出
export default {
    initializeMicroCore,
    getMicroCoreKernel,
    getSidecar,
    getConfig,
    getPredefinedApps,
    destroyMicroCore
};
