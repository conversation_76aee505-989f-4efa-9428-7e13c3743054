import solid from 'vite-plugin-solid'
import { defineConfig } from 'vitest/config'

export default defineConfig({
    plugins: [solid()],

    test: {
        globals: true,
        environment: 'jsdom',
        setupFiles: ['./src/test/setup.ts'],
        coverage: {
            provider: 'v8',
            reporter: ['text', 'json', 'html', 'lcov'],
            exclude: [
                'node_modules/',
                'dist/',
                'coverage/',
                '**/*.d.ts',
                '**/*.config.*',
                '**/test/**',
                '**/*.test.*',
                '**/*.spec.*'
            ],
            thresholds: {
                global: {
                    branches: 100,
                    functions: 100,
                    lines: 100,
                    statements: 100
                }
            }
        },
        testTimeout: 10000,
        hookTimeout: 10000
    },

    resolve: {
        alias: {
            '@': '/src'
        }
    }
})