/**
 * @fileoverview 应用注册器测试
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { AppRegistry } from '../src/runtime/app-registry';
import type { AppInfo } from '../src/types';

describe('AppRegistry', () => {
    let registry: AppRegistry;
    let mockAppInfo: AppInfo;

    beforeEach(() => {
        registry = new AppRegistry();
        mockAppInfo = {
            name: 'test-app',
            entry: 'http://localhost:3001',
            container: '#test-container',
            activeRule: '/test-app',
            props: {
                title: '测试应用'
            }
        };
    });

    describe('应用注册', () => {
        it('应该成功注册应用', () => {
            registry.register(mockAppInfo);
            expect(registry.has('test-app')).toBe(true);
            expect(registry.get('test-app')).toEqual(mockAppInfo);
        });

        it('应该拒绝重复注册', () => {
            registry.register(mockAppInfo);
            expect(() => registry.register(mockAppInfo)).toThrow('应用 test-app 已存在');
        });

        it('应该验证应用名称', () => {
            const invalidApp = { ...mockAppInfo, name: '' };
            expect(() => registry.register(invalidApp)).toThrow('应用名称不能为空');
        });

        it('应该验证入口地址', () => {
            const invalidApp = { ...mockAppInfo, entry: '' };
            expect(() => registry.register(invalidApp)).toThrow('应用入口不能为空');
        });

        it('应该验证容器选择器', () => {
            const invalidApp = { ...mockAppInfo, container: '' };
            expect(() => registry.register(invalidApp)).toThrow('应用容器不能为空');
        });

        it('应该验证激活规则', () => {
            const invalidApp = { ...mockAppInfo, activeRule: '' };
            expect(() => registry.register(invalidApp)).toThrow('应用激活规则不能为空');
        });
    });

    describe('应用查询', () => {
        beforeEach(() => {
            registry.register(mockAppInfo);
        });

        it('应该正确查询应用是否存在', () => {
            expect(registry.has('test-app')).toBe(true);
            expect(registry.has('non-existent')).toBe(false);
        });

        it('应该正确获取应用信息', () => {
            const app = registry.get('test-app');
            expect(app).toEqual(mockAppInfo);
        });

        it('应该在应用不存在时返回undefined', () => {
            const app = registry.get('non-existent');
            expect(app).toBeUndefined();
        });

        it('应该正确获取所有应用', () => {
            const secondApp = {
                ...mockAppInfo,
                name: 'second-app',
                activeRule: '/second-app'
            };
            registry.register(secondApp);

            const apps = registry.getAll();
            expect(apps).toHaveLength(2);
            expect(apps.map(app => app.name)).toContain('test-app');
            expect(apps.map(app => app.name)).toContain('second-app');
        });
    });

    describe('应用卸载', () => {
        beforeEach(() => {
            registry.register(mockAppInfo);
        });

        it('应该成功卸载应用', () => {
            expect(registry.has('test-app')).toBe(true);
            registry.unregister('test-app');
            expect(registry.has('test-app')).toBe(false);
        });

        it('应该在卸载不存在的应用时抛出错误', () => {
            expect(() => registry.unregister('non-existent')).toThrow('应用 non-existent 不存在');
        });
    });

    describe('应用匹配', () => {
        beforeEach(() => {
            registry.register(mockAppInfo);
            registry.register({
                ...mockAppInfo,
                name: 'wildcard-app',
                activeRule: '/wildcard/*'
            });
            registry.register({
                ...mockAppInfo,
                name: 'exact-app',
                activeRule: '/exact'
            });
        });

        it('应该正确匹配精确路径', () => {
            const matches = registry.getMatchingApps('/test-app');
            expect(matches).toHaveLength(1);
            expect(matches[0].name).toBe('test-app');
        });

        it('应该正确匹配通配符路径', () => {
            const matches = registry.getMatchingApps('/wildcard/sub-path');
            expect(matches).toHaveLength(1);
            expect(matches[0].name).toBe('wildcard-app');
        });

        it('应该在没有匹配时返回空数组', () => {
            const matches = registry.getMatchingApps('/no-match');
            expect(matches).toHaveLength(0);
        });

        it('应该支持多个应用匹配同一路径', () => {
            registry.register({
                ...mockAppInfo,
                name: 'another-test-app',
                activeRule: '/test-app'
            });

            const matches = registry.getMatchingApps('/test-app');
            expect(matches).toHaveLength(2);
        });
    });

    describe('应用状态管理', () => {
        beforeEach(() => {
            registry.register(mockAppInfo);
        });

        it('应该正确设置和获取应用状态', () => {
            registry.setAppStatus('test-app', 'mounted');
            expect(registry.getAppStatus('test-app')).toBe('mounted');
        });

        it('应该在应用不存在时抛出错误', () => {
            expect(() => registry.setAppStatus('non-existent', 'mounted')).toThrow('应用 non-existent 不存在');
        });

        it('应该返回默认状态', () => {
            expect(registry.getAppStatus('test-app')).toBe('idle');
        });
    });

    describe('应用属性更新', () => {
        beforeEach(() => {
            registry.register(mockAppInfo);
        });

        it('应该成功更新应用属性', () => {
            const newProps = { title: '更新的标题', theme: 'dark' };
            registry.updateAppProps('test-app', newProps);

            const app = registry.get('test-app');
            expect(app?.props).toEqual({
                title: '更新的标题',
                theme: 'dark'
            });
        });

        it('应该合并现有属性', () => {
            registry.updateAppProps('test-app', { theme: 'dark' });

            const app = registry.get('test-app');
            expect(app?.props).toEqual({
                title: '测试应用',
                theme: 'dark'
            });
        });

        it('应该在应用不存在时抛出错误', () => {
            expect(() => registry.updateAppProps('non-existent', {})).toThrow('应用 non-existent 不存在');
        });
    });

    describe('清理操作', () => {
        beforeEach(() => {
            registry.register(mockAppInfo);
            registry.register({
                ...mockAppInfo,
                name: 'second-app',
                activeRule: '/second-app'
            });
        });

        it('应该清理所有应用', () => {
            expect(registry.getAll()).toHaveLength(2);
            registry.clear();
            expect(registry.getAll()).toHaveLength(0);
        });
    });

    describe('事件通知', () => {
        it('应该在注册应用时触发事件', () => {
            const mockCallback = vi.fn();
            registry.on('app:registered', mockCallback);

            registry.register(mockAppInfo);
            expect(mockCallback).toHaveBeenCalledWith(mockAppInfo);
        });

        it('应该在卸载应用时触发事件', () => {
            const mockCallback = vi.fn();
            registry.register(mockAppInfo);
            registry.on('app:unregistered', mockCallback);

            registry.unregister('test-app');
            expect(mockCallback).toHaveBeenCalledWith('test-app');
        });

        it('应该在状态变化时触发事件', () => {
            const mockCallback = vi.fn();
            registry.register(mockAppInfo);
            registry.on('app:status-changed', mockCallback);

            registry.setAppStatus('test-app', 'mounted');
            expect(mockCallback).toHaveBeenCalledWith('test-app', 'mounted', 'idle');
        });
    });
});