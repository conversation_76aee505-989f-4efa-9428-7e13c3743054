/**
 * @fileoverview HTML 微前端子应用入口 - 标准生命周期实现
 * <AUTHOR> <<EMAIL>>
 */

import { errorBoundary } from './utils/error-boundary'
import { performanceMonitor, ResourcePreloader } from './utils/performance'


// 微前端应用属性接口
interface MicroAppProps {
    container?: HTMLElement | string
    basename?: string
    theme?: 'light' | 'dark'
    user?: any
    [key: string]: any
}

// HTML 子应用主逻辑类
class HtmlMicroApp {
    private clickCount = 0
    private timeInterval: number | null = null
    private eventListeners: Array<{ element: any, event: string, handler: any }> = []
    private props: MicroAppProps = {}

    constructor(props: MicroAppProps = {}) {
        this.props = props
        this.init()
    }

    private init(): void {
        console.log('HTML 子应用初始化', this.props)
        this.bindEvents()
        this.startTimeUpdate()
        this.applyTheme()
    }

    private bindEvents(): void {
        const demoButton = document.getElementById('demo-button')
        if (demoButton) {
            const clickHandler = () => this.handleButtonClick()
            demoButton.addEventListener('click', clickHandler)
            this.eventListeners.push({ element: demoButton, event: 'click', handler: clickHandler })
        }
    }

    private handleButtonClick(): void {
        this.clickCount++
        const countElement = document.getElementById('count')
        if (countElement) {
            countElement.textContent = this.clickCount.toString()
        }

        // 显示点击效果
        const button = document.getElementById('demo-button')
        if (button) {
            button.style.transform = 'scale(0.95)'
            setTimeout(() => {
                button.style.transform = 'scale(1)'
            }, 100)
        }

        console.log('HTML 子应用按钮点击，当前点击次数:', this.clickCount)

        // 发送消息到主应用
        this.sendToMain('button-clicked', { count: this.clickCount })
    }

    private startTimeUpdate(): void {
        this.updateTime()
        this.timeInterval = window.setInterval(() => this.updateTime(), 1000)
    }

    private updateTime(): void {
        const timeElement = document.getElementById('current-time')
        if (timeElement) {
            timeElement.textContent = new Date().toLocaleString('zh-CN')
        }
    }

    private applyTheme(): void {
        if (this.props.theme) {
            document.documentElement.setAttribute('data-theme', this.props.theme)
        }
    }

    private sendToMain(type: string, data: any): void {
        window.postMessage({
            type: 'MICRO_APP_MESSAGE',
            source: 'sub-app-html',
            target: 'main',
            data: { type, data, timestamp: Date.now() }
        }, '*')
    }

    public updateProps(newProps: MicroAppProps): void {
        this.props = { ...this.props, ...newProps }
        this.applyTheme()
    }

    public destroy(): void {
        // 清理定时器
        if (this.timeInterval) {
            clearInterval(this.timeInterval)
            this.timeInterval = null
        }

        // 清理事件监听器
        this.eventListeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler)
        })
        this.eventListeners = []

        console.log('HTML 子应用销毁')
    }
}

// 应用状态管理
let htmlAppInstance: HtmlMicroApp | null = null
let isBootstrapped = false
let isMounted = false
let eventListeners: Array<{ element: any, event: string, handler: any }> = []

/**
 * 微前端应用启动函数
 * 在应用首次加载时调用，用于初始化应用资源
 */
export async function bootstrap(props: MicroAppProps = {}): Promise<void> {
    try {
        performanceMonitor.startTiming('bootstrap')
        console.log('🚀 HTML 子应用开始启动', props)

        // 防止重复启动
        if (isBootstrapped) {
            console.warn('⚠️ HTML 子应用已经启动，跳过重复启动')
            return
        }

        // 初始化应用资源
        await initializeResources(props)

        // 设置全局错误处理
        setupErrorHandling()

        // 标记为已启动
        isBootstrapped = true

        const duration = performanceMonitor.endTiming('bootstrap')
        console.log(`✅ HTML 子应用启动完成 (${duration.toFixed(2)}ms)`)
    } catch (error) {
        console.error('❌ HTML 子应用启动失败:', error)
        errorBoundary.captureError(error as Error, 'bootstrap')
        throw error
    }
}

/**
 * 微前端应用挂载函数
 * 将应用挂载到指定容器
 */
export async function mount(props: MicroAppProps = {}): Promise<void> {
    try {
        performanceMonitor.startTiming('mount')
        console.log('🔄 HTML 子应用开始挂载', props)

        // 确保应用已启动
        if (!isBootstrapped) {
            await bootstrap(props)
        }

        // 防止重复挂载
        if (isMounted) {
            console.warn('⚠️ HTML 子应用已经挂载，先卸载再重新挂载')
            await unmount(props)
        }

        // 获取容器元素
        const container = getContainer(props.container)
        if (!container) {
            throw new Error('找不到挂载容器')
        }

        // 创建应用内容
        await createAppContent(container, props)

        // 初始化应用实例
        htmlAppInstance = new HtmlMicroApp(props)

        // 标记为已挂载
        isMounted = true

        // 触发挂载完成事件
        dispatchLifecycleEvent('mounted', props)

        const duration = performanceMonitor.endTiming('mount')
        performanceMonitor.measureMemoryUsage()
        console.log(`✅ HTML 子应用挂载完成 (${duration.toFixed(2)}ms)`)
    } catch (error) {
        console.error('❌ HTML 子应用挂载失败:', error)
        errorBoundary.captureError(error as Error, 'mount')
        throw error
    }
}

/**
 * 微前端应用卸载函数
 * 清理应用资源和DOM
 */
export async function unmount(props: MicroAppProps = {}): Promise<void> {
    try {
        console.log('🔄 HTML 子应用开始卸载', props)

        if (!isMounted) {
            console.warn('⚠️ HTML 子应用未挂载，跳过卸载')
            return
        }

        // 销毁应用实例
        if (htmlAppInstance) {
            htmlAppInstance.destroy()
            htmlAppInstance = null
        }

        // 清理 DOM
        const container = getContainer(props.container)
        if (container) {
            container.innerHTML = ''
        }

        // 清理事件监听器
        cleanupEventListeners()

        // 标记为未挂载
        isMounted = false

        // 触发卸载完成事件
        dispatchLifecycleEvent('unmounted', props)

        console.log('✅ HTML 子应用卸载完成')
    } catch (error) {
        console.error('❌ HTML 子应用卸载失败:', error)
        throw error
    }
}

/**
 * 微前端应用更新函数
 * 更新应用属性
 */
export async function update(props: MicroAppProps = {}): Promise<void> {
    try {
        performanceMonitor.startTiming('update')
        console.log('🔄 HTML 子应用开始更新', props)

        if (!isMounted) {
            console.warn('⚠️ HTML 子应用未挂载，无法更新')
            return
        }

        // 更新应用实例属性
        if (htmlAppInstance) {
            htmlAppInstance.updateProps(props)
        }

        const duration = performanceMonitor.endTiming('update')
        console.log(`✅ HTML 子应用更新完成 (${duration.toFixed(2)}ms)`)
    } catch (error) {
        console.error('❌ HTML 子应用更新失败:', error)
        errorBoundary.captureError(error as Error, 'update')
        throw error
    }
}

/**
 * 获取挂载容器
 */
function getContainer(container?: HTMLElement | string): HTMLElement | null {
    if (!container) {
        return document.getElementById('html-app') || document.getElementById('app')
    }

    if (typeof container === 'string') {
        return document.querySelector(container)
    }

    return container
}

/**
 * 创建应用内容
 */
async function createAppContent(container: HTMLElement, props: MicroAppProps): Promise<void> {
    // HTML 特定优化：使用 DocumentFragment 进行批量 DOM 操作
    const fragment = document.createDocumentFragment()
    const appDiv = document.createElement('div')
    appDiv.className = 'html-app'

    // 使用模板字符串创建内容，但通过 DOM API 优化性能
    appDiv.innerHTML = `
        <header class="app-header">
            <h1>HTML 微前端子应用</h1>
            <p>这是一个基于原生 HTML/JavaScript/CSS 的微前端子应用示例</p>
        </header>
        
        <main class="app-main">
            <section class="feature-section">
                <h2>功能特性</h2>
                <ul>
                    <li>✅ 原生 HTML/JavaScript/CSS 实现</li>
                    <li>✅ 无框架依赖，轻量级</li>
                    <li>✅ 完整的微前端生命周期管理</li>
                    <li>✅ 支持独立运行和微前端模式</li>
                    <li>✅ 错误边界和优雅降级</li>
                    <li>✅ DOM 操作优化和性能监控</li>
                </ul>
            </section>
            
            <section class="demo-section">
                <h2>演示功能</h2>
                <div class="demo-content">
                    <p>当前时间: <span id="current-time"></span></p>
                    <button id="demo-button" class="demo-button">
                        点击测试 HTML 功能
                    </button>
                    <div id="click-counter" class="counter">
                        点击次数: <span id="count">0</span>
                    </div>
                </div>
            </section>
        </main>
        
        <footer class="app-footer">
            <p>Powered by Micro-Core 微前端框架</p>
        </footer>
    `

    fragment.appendChild(appDiv)

    // 批量插入 DOM，减少重排重绘
    container.appendChild(fragment)

    // 加载样式
    await loadStyles()

    // HTML 特定优化：预加载可能需要的资源
    await preloadResources()
}

/**
 * 预加载资源 - HTML 特定优化
 */
async function preloadResources(): Promise<void> {
    // 预加载可能需要的图片资源
    const imagesToPreload = [
        // 可以在这里添加需要预加载的图片
    ]

    // 预加载字体
    const fontsToPreload = [
        // 可以在这里添加需要预加载的字体
    ]

    try {
        await Promise.all([
            ...imagesToPreload.map(src => ResourcePreloader.preloadImage(src)),
            ...fontsToPreload.map(href => ResourcePreloader.preloadStyle(href))
        ])
    } catch (error) {
        console.warn('资源预加载失败:', error)
    }
}

/**
 * 加载样式
 */
async function loadStyles(): Promise<void> {
    const style = document.createElement('style')
    style.textContent = `
        .html-app {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }

        .app-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .app-header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .app-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1em;
        }

        .feature-section,
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .feature-section h2,
        .demo-section h2 {
            color: #007bff;
            margin-top: 0;
            font-size: 1.5em;
        }

        .feature-section ul {
            list-style: none;
            padding: 0;
        }

        .feature-section li {
            padding: 8px 0;
            font-size: 1.1em;
        }

        .demo-content {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .demo-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            align-self: flex-start;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
        }

        .counter {
            padding: 10px 15px;
            background: #e3f2fd;
            border-radius: 6px;
            font-size: 1.1em;
            align-self: flex-start;
        }

        .app-footer {
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #eee;
            margin-top: 30px;
        }

        [data-theme="dark"] .html-app {
            color: #e0e0e0;
            background: #1a1a1a;
        }

        [data-theme="dark"] .feature-section,
        [data-theme="dark"] .demo-section {
            background: #2d2d2d;
            color: #e0e0e0;
        }
    `
    document.head.appendChild(style)
}

/**
 * 初始化应用资源
 */
async function initializeResources(props: MicroAppProps): Promise<void> {
    // 初始化主题
    if (props.theme) {
        document.documentElement.setAttribute('data-theme', props.theme)
    }

    // 初始化用户上下文
    if (props.user) {
        // 设置用户相关的全局状态
    }
}

/**
 * 设置全局错误处理
 */
function setupErrorHandling(): void {
    const unhandledRejectionHandler = (event: PromiseRejectionEvent) => {
        console.error('HTML 子应用未捕获的 Promise 拒绝:', event.reason)
    }

    const globalErrorHandler = (event: ErrorEvent) => {
        console.error('HTML 子应用全局错误:', event.error)
    }

    window.addEventListener('unhandledrejection', unhandledRejectionHandler)
    window.addEventListener('error', globalErrorHandler)

    // 记录事件监听器以便清理
    eventListeners.push(
        { element: window, event: 'unhandledrejection', handler: unhandledRejectionHandler },
        { element: window, event: 'error', handler: globalErrorHandler }
    )
}

/**
 * 清理事件监听器
 */
function cleanupEventListeners(): void {
    eventListeners.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler)
    })
    eventListeners = []
}

/**
 * 触发生命周期事件
 */
function dispatchLifecycleEvent(type: string, props: MicroAppProps): void {
    const event = new CustomEvent(`html-app:${type}`, {
        detail: { props, timestamp: Date.now() }
    })
    window.dispatchEvent(event)
}

/**
 * 获取应用状态
 */
export function getAppStatus() {
    return {
        name: 'sub-app-html',
        isBootstrapped,
        isMounted,
        framework: 'HTML',
        version: '1.0.0'
    }
}

// 独立运行模式
if (!(window as any).__POWERED_BY_MICRO_CORE__) {
    console.log('🏃 HTML 应用以独立模式运行')

    // 等待 DOM 加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            mount({
                container: document.body,
                basename: '/',
                theme: 'light'
            }).catch(error => {
                console.error('HTML 应用独立模式启动失败:', error)
                errorBoundary.captureError(error, 'standalone-mode')
            })
        })
    } else {
        mount({
            container: document.body,
            basename: '/',
            theme: 'light'
        }).catch(error => {
            console.error('HTML 应用独立模式启动失败:', error)
            errorBoundary.captureError(error, 'standalone-mode')
        })
    }
}

// 暴露给微前端框架的接口
declare global {
    interface Window {
        __HTML_MICRO_APP__?: {
            bootstrap: typeof bootstrap
            mount: typeof mount
            unmount: typeof unmount
            update: typeof update
            getAppStatus: typeof getAppStatus
        }
    }
}

if (typeof window !== 'undefined') {
    (window as any).__HTML_MICRO_APP__ = {
        bootstrap,
        mount,
        unmount,
        update,
        getAppStatus
    }
}

// 导出生命周期函数供微前端框架使用
export default {
    bootstrap,
    mount,
    unmount,
    update,
    getAppStatus
}