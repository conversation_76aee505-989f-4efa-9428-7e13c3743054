# 插件系统

Micro-Core 采用 100% 插件化架构，所有功能都通过插件实现。本指南将帮助您理解插件系统的工作原理以及如何开发自定义插件。

## 插件架构概述

### 核心概念

- **插件接口**：所有插件必须实现统一的插件接口
- **生命周期钩子**：插件可以监听和响应系统生命周期事件
- **依赖注入**：插件可以声明依赖关系，系统自动解析
- **热插拔**：支持运行时动态加载和卸载插件

### 插件类型

```typescript
// 核心插件类型
export enum PluginType {
  CORE = 'core',           // 核心功能插件
  SANDBOX = 'sandbox',     // 沙箱插件
  ROUTER = 'router',       // 路由插件
  COMMUNICATION = 'communication', // 通信插件
  ADAPTER = 'adapter',     // 适配器插件
  MIDDLEWARE = 'middleware' // 中间件插件
}
```

## 插件开发

### 基础插件结构

```typescript
import { Plugin, PluginContext } from '@micro-core/core'

export class MyPlugin implements Plugin {
  name = 'my-plugin'
  version = '1.0.0'
  type = PluginType.CORE
  
  // 插件依赖
  dependencies = ['@micro-core/plugin-router']
  
  // 初始化
  async initialize(context: PluginContext) {
    // 插件初始化逻辑
  }
  
  // 启动
  async start(context: PluginContext) {
    // 插件启动逻辑
  }
  
  // 停止
  async stop(context: PluginContext) {
    // 插件清理逻辑
  }
}
```

### 生命周期钩子

```typescript
export class MyPlugin implements Plugin {
  // 应用加载前
  async beforeAppLoad(app: MicroApp) {
    console.log(`应用 ${app.name} 即将加载`)
  }
  
  // 应用加载后
  async afterAppLoad(app: MicroApp) {
    console.log(`应用 ${app.name} 加载完成`)
  }
  
  // 应用卸载前
  async beforeAppUnload(app: MicroApp) {
    console.log(`应用 ${app.name} 即将卸载`)
  }
  
  // 应用卸载后
  async afterAppUnload(app: MicroApp) {
    console.log(`应用 ${app.name} 卸载完成`)
  }
}
```

## 插件配置

### 插件注册

```typescript
import { MicroCore } from '@micro-core/core'
import { MyPlugin } from './my-plugin'

const microCore = new MicroCore({
  plugins: [
    new MyPlugin({
      // 插件配置选项
      option1: 'value1',
      option2: 'value2'
    })
  ]
})
```

### 动态加载插件

```typescript
// 运行时加载插件
await microCore.pluginManager.loadPlugin('my-plugin', {
  url: '/plugins/my-plugin.js',
  config: {
    option1: 'value1'
  }
})

// 卸载插件
await microCore.pluginManager.unloadPlugin('my-plugin')
```

## 内置插件

### 认证插件

```typescript
import { AuthPlugin } from '@micro-core/plugin-auth'

const authPlugin = new AuthPlugin({
  providers: ['oauth', 'jwt'],
  tokenStorage: 'localStorage'
})
```

### 路由插件

```typescript
import { RouterPlugin } from '@micro-core/plugin-router'

const routerPlugin = new RouterPlugin({
  mode: 'history',
  base: '/micro-app/',
  routes: [
    { path: '/app1/*', app: 'app1' },
    { path: '/app2/*', app: 'app2' }
  ]
})
```

### 沙箱插件

```typescript
import { ProxySandboxPlugin } from '@micro-core/plugin-proxy-sandbox'

const sandboxPlugin = new ProxySandboxPlugin({
  isolation: {
    css: true,
    js: true,
    dom: true
  }
})
```

## 最佳实践

### 1. 插件设计原则

- **单一职责**：每个插件只负责一个特定功能
- **松耦合**：插件之间通过事件和接口通信
- **可配置**：提供丰富的配置选项
- **可测试**：编写完整的单元测试

### 2. 错误处理

```typescript
export class MyPlugin implements Plugin {
  async start(context: PluginContext) {
    try {
      // 插件逻辑
    } catch (error) {
      context.logger.error('插件启动失败', error)
      throw new PluginError('MY_PLUGIN_START_FAILED', error)
    }
  }
}
```

### 3. 性能优化

```typescript
export class MyPlugin implements Plugin {
  private cache = new Map()
  
  async processData(data: any) {
    // 使用缓存提升性能
    const cacheKey = this.getCacheKey(data)
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }
    
    const result = await this.heavyComputation(data)
    this.cache.set(cacheKey, result)
    return result
  }
}
```

## 调试和测试

### 插件调试

```typescript
// 开启插件调试模式
const microCore = new MicroCore({
  debug: {
    plugins: true,
    level: 'verbose'
  }
})
```

### 插件测试

```typescript
import { describe, it, expect } from 'vitest'
import { createPluginTestContext } from '@micro-core/test-utils'
import { MyPlugin } from './my-plugin'

describe('MyPlugin', () => {
  it('should initialize correctly', async () => {
    const plugin = new MyPlugin()
    const context = createPluginTestContext()
    
    await plugin.initialize(context)
    
    expect(plugin.isInitialized).toBe(true)
  })
})
```

## 故障排除

### 常见问题

1. **插件加载失败**
   - 检查插件依赖是否正确安装
   - 验证插件接口实现是否完整

2. **插件冲突**
   - 检查插件依赖关系
   - 使用插件管理器的依赖解析功能

3. **性能问题**
   - 使用性能监控工具分析插件性能
   - 优化插件初始化和执行逻辑

## 参考资料

- [插件 API 参考](/api/plugin-api)
- [内置插件文档](/ecosystem/plugins)
- [插件开发示例](/examples/plugin-development)
