/**
 * @fileoverview 路由插件
 * @description 提供微前端应用的路由管理功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import { type PluginConfig } from '@micro-core/shared';
import { BasePlugin } from './base-plugin';
interface MicroCoreKernel {
    registerAPI(name: string, api: any): void;
    getEventBus(): any;
}
interface NavigationOptions {
    replace?: boolean;
    params?: Record<string, any>;
    state?: any;
    cache?: boolean;
    fromBrowser?: boolean;
}
interface RouteConfig {
    path: string;
    component?: any;
    meta?: Record<string, any>;
    children?: RouteConfig[];
}
interface RouteGuard {
    name?: string;
    canNavigate(from: string, to: string, options: NavigationOptions): Promise<boolean | {
        allowed: boolean;
        reason?: string;
    }> | boolean | {
        allowed: boolean;
        reason?: string;
    };
}
/**
 * 路由插件配置接口
 */
interface RouterPluginConfig extends PluginConfig {
    /** 路由模式 */
    mode?: 'hash' | 'history';
    /** 基础路径 */
    base?: string;
    /** 默认路由 */
    defaultRoute?: string;
    /** 是否启用路由守卫 */
    enableGuards?: boolean;
    /** 路由切换动画 */
    transition?: {
        enter?: string;
        leave?: string;
        duration?: number;
    };
    /** 是否启用路由缓存 */
    enableCache?: boolean;
    /** 缓存大小限制 */
    cacheSize?: number;
}
/**
 * 路由记录接口
 */
interface RouteRecord {
    /** 路由ID */
    id: string;
    /** 路由路径 */
    path: string;
    /** 应用名称 */
    appName: string;
    /** 路由配置 */
    config: RouteConfig;
    /** 创建时间 */
    createdAt: number;
    /** 最后访问时间 */
    lastAccessTime?: number;
    /** 访问次数 */
    accessCount: number;
}
/**
 * 导航历史记录
 */
interface NavigationHistory {
    /** 历史记录ID */
    id: string;
    /** 来源路径 */
    from: string;
    /** 目标路径 */
    to: string;
    /** 导航时间 */
    timestamp: number;
    /** 导航参数 */
    params?: Record<string, any>;
    /** 导航状态 */
    state?: any;
}
/**
 * 路由插件类
 * 提供微前端应用的路由管理、导航控制和路由守卫功能
 */
export declare class RouterPlugin extends BasePlugin {
    /** 内核实例 */
    protected kernel?: MicroCoreKernel;
    /** 路由插件配置 */
    private routerConfig;
    /** 路由注册表 */
    private routes;
    /** 路径到路由的映射 */
    private pathRouteMap;
    /** 当前激活的路由 */
    private currentRoute;
    /** 路由守卫列表 */
    private guards;
    /** 导航历史记录 */
    private history;
    /** 路由缓存 */
    private routeCache;
    /** 是否正在导航 */
    private isNavigating;
    /** 导航队列 */
    private navigationQueue;
    constructor(config?: RouterPluginConfig);
    /**
     * 执行安装逻辑
     */
    protected doInstall(kernel: MicroCoreKernel): Promise<void>;
    /**
     * 执行卸载逻辑
     */
    protected doUninstall(): Promise<void>;
    /**
     * 注册路由
     */
    registerRoute(appName: string, config: RouteConfig): Promise<string>;
    /**
     * 取消注册路由
     */
    unregisterRoute(routeId: string): Promise<void>;
    /**
     * 导航到指定路径
     */
    navigate(path: string, options?: NavigationOptions): Promise<void>;
    /**
     * 执行导航
     */
    private doNavigate;
    /**
     * 替换当前路由
     */
    replace(path: string, options?: NavigationOptions): Promise<void>;
    /**
     * 后退
     */
    back(): Promise<void>;
    /**
     * 前进
     */
    forward(): Promise<void>;
    /**
     * 获取当前路由
     */
    getCurrentRoute(): RouteRecord | null;
    /**
     * 获取当前路径
     */
    getCurrentPath(): string;
    /**
     * 获取所有路由
     */
    getRoutes(): RouteRecord[];
    /**
     * 获取导航历史
     */
    getHistory(): NavigationHistory[];
    /**
     * 添加路由守卫
     */
    addGuard(guard: RouteGuard): void;
    /**
     * 移除路由守卫
     */
    removeGuard(guard: RouteGuard | string): void;
    /**
     * 清理路由缓存
     */
    clearCache(path?: string): void;
    /**
     * 获取缓存大小
     */
    getCacheSize(): number;
    /**
     * 设置路由监听
     */
    private setupRouteListener;
    /**
     * 清理路由监听
     */
    private cleanupRouteListener;
    /**
     * 处理路由变化
     */
    private handleRouteChange;
    /**
     * 初始化路由
     */
    private initializeRouter;
    /**
     * 执行路由守卫
     */
    private executeGuards;
    /**
     * 更新浏览器历史
     */
    private updateBrowserHistory;
    /**
     * 添加导航历史
     */
    private addNavigationHistory;
    /**
     * 缓存路由数据
     */
    private cacheRoute;
    /**
     * 获取插件统计信息
     */
    getStats(): Record<string, any>;
}
export {};
//# sourceMappingURL=router-plugin.d.ts.map