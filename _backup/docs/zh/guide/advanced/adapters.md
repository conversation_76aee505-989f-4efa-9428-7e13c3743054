# 多框架适配

Micro-Core 提供了完善的多框架适配能力，支持 React、Vue、Angular、Svelte 等主流前端框架，让不同技术栈的应用能够无缝集成在同一个微前端系统中。

## 适配器架构

### 设计原理

适配器模式是 Micro-Core 多框架支持的核心，通过标准化的适配器接口，将不同框架的特性抽象为统一的生命周期和 API。

```typescript
// 适配器基类
export abstract class BaseAdapter {
  abstract name: string
  abstract version: string
  
  // 应用挂载
  abstract mount(element: HTMLElement, props?: any): Promise<void>
  
  // 应用卸载
  abstract unmount(element: HTMLElement): Promise<void>
  
  // 应用更新
  abstract update?(props: any): Promise<void>
  
  // 获取应用实例
  abstract getInstance?(): any
}
```

### 适配器类型

```typescript
export enum AdapterType {
  REACT = 'react',
  VUE = 'vue',
  ANGULAR = 'angular',
  SVELTE = 'svelte',
  SOLID = 'solid',
  VANILLA = 'vanilla'
}
```

## React 适配器

### 基础使用

```typescript
import { ReactAdapter } from '@micro-core/adapter-react'
import { createRoot } from 'react-dom/client'

const reactAdapter = new ReactAdapter({
  // React 18 支持
  createRoot: true,
  
  // 严格模式
  strictMode: true,
  
  // 错误边界
  errorBoundary: true
})

// 注册 React 应用
microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001',
  container: '#react-container',
  adapter: reactAdapter,
  activeWhen: '/react-app'
})
```

### React 应用配置

```typescript
// React 微应用入口文件
import React from 'react'
import { createRoot } from 'react-dom/client'
import App from './App'

// 微前端生命周期
export async function mount(element: HTMLElement, props: any) {
  const root = createRoot(element)
  root.render(
    <React.StrictMode>
      <App {...props} />
    </React.StrictMode>
  )
  
  // 保存 root 实例用于卸载
  ;(element as any).__react_root = root
}

export async function unmount(element: HTMLElement) {
  const root = (element as any).__react_root
  if (root) {
    root.unmount()
    delete (element as any).__react_root
  }
}

export async function update(props: any) {
  // 处理属性更新
  console.log('Props updated:', props)
}
```

### React Hooks 集成

```typescript
import { useMicroCore } from '@micro-core/adapter-react'

function MyComponent() {
  const microCore = useMicroCore()
  
  // 使用全局状态
  const [globalState, setGlobalState] = microCore.useGlobalState('user')
  
  // 应用间通信
  const sendMessage = microCore.useEventBus()
  
  // 路由导航
  const navigate = microCore.useRouter()
  
  return (
    <div>
      <h1>React 微应用</h1>
      <button onClick={() => sendMessage('hello', { from: 'react-app' })}>
        发送消息
      </button>
    </div>
  )
}
```

## Vue 适配器

### Vue 3 适配

```typescript
import { VueAdapter } from '@micro-core/adapter-vue'

const vueAdapter = new VueAdapter({
  // Vue 版本
  version: 3,
  
  // 全局配置
  globalProperties: {
    $microCore: microCore
  },
  
  // 插件
  plugins: [router, store]
})

// 注册 Vue 应用
microCore.registerApp({
  name: 'vue-app',
  entry: 'http://localhost:3002',
  container: '#vue-container',
  adapter: vueAdapter,
  activeWhen: '/vue-app'
})
```

### Vue 应用配置

```typescript
// Vue 微应用入口文件
import { createApp } from 'vue'
import App from './App.vue'

let app: any = null

export async function mount(element: HTMLElement, props: any) {
  app = createApp(App)
  
  // 注入属性
  app.provide('microCoreProps', props)
  
  // 挂载应用
  app.mount(element)
}

export async function unmount(element: HTMLElement) {
  if (app) {
    app.unmount()
    app = null
  }
}

export async function update(props: any) {
  if (app) {
    // 更新注入的属性
    app.provide('microCoreProps', props)
  }
}
```

### Vue Composition API 集成

```vue
<template>
  <div>
    <h1>Vue 微应用</h1>
    <button @click="sendMessage">发送消息</button>
    <p>全局状态: {{ globalState }}</p>
  </div>
</template>

<script setup>
import { useMicroCore } from '@micro-core/adapter-vue'

const microCore = useMicroCore()

// 使用全局状态
const globalState = microCore.useGlobalState('user')

// 应用间通信
const sendMessage = () => {
  microCore.eventBus.emit('hello', { from: 'vue-app' })
}

// 路由导航
const navigate = microCore.useRouter()
</script>
```

### Vue 2 兼容

```typescript
import { VueAdapter } from '@micro-core/adapter-vue'

const vue2Adapter = new VueAdapter({
  version: 2,
  Vue: require('vue'), // Vue 2 构造函数
})

// Vue 2 微应用
import Vue from 'vue'
import App from './App.vue'

let instance: Vue | null = null

export async function mount(element: HTMLElement, props: any) {
  instance = new Vue({
    render: h => h(App, { props })
  }).$mount(element)
}

export async function unmount(element: HTMLElement) {
  if (instance) {
    instance.$destroy()
    instance = null
  }
}
```

## Angular 适配器

### 基础配置

```typescript
import { AngularAdapter } from '@micro-core/adapter-angular'

const angularAdapter = new AngularAdapter({
  // Zone.js 配置
  zoneConfig: {
    enableLongStackTrace: false
  },
  
  // 模块配置
  moduleConfig: {
    providers: [
      { provide: 'MICRO_CORE', useValue: microCore }
    ]
  }
})

// 注册 Angular 应用
microCore.registerApp({
  name: 'angular-app',
  entry: 'http://localhost:3003',
  container: '#angular-container',
  adapter: angularAdapter,
  activeWhen: '/angular-app'
})
```

### Angular 应用配置

```typescript
// main.ts
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic'
import { AppModule } from './app/app.module'
import { NgModuleRef } from '@angular/core'

let platformRef: any = null
let moduleRef: NgModuleRef<AppModule> | null = null

export async function mount(element: HTMLElement, props: any) {
  // 设置挂载元素
  const appElement = document.createElement('app-root')
  element.appendChild(appElement)
  
  // 启动 Angular 应用
  platformRef = platformBrowserDynamic([
    { provide: 'MICRO_CORE_PROPS', useValue: props }
  ])
  
  moduleRef = await platformRef.bootstrapModule(AppModule)
}

export async function unmount(element: HTMLElement) {
  if (moduleRef) {
    moduleRef.destroy()
    moduleRef = null
  }
  
  if (platformRef) {
    platformRef.destroy()
    platformRef = null
  }
  
  // 清理 DOM
  element.innerHTML = ''
}
```

### Angular 服务集成

```typescript
// micro-core.service.ts
import { Injectable, Inject } from '@angular/core'

@Injectable({
  providedIn: 'root'
})
export class MicroCoreService {
  constructor(@Inject('MICRO_CORE') private microCore: any) {}
  
  // 全局状态管理
  getGlobalState(key: string) {
    return this.microCore.globalState.get(key)
  }
  
  setGlobalState(key: string, value: any) {
    this.microCore.globalState.set(key, value)
  }
  
  // 应用间通信
  sendMessage(event: string, data: any) {
    this.microCore.eventBus.emit(event, data)
  }
  
  onMessage(event: string, callback: Function) {
    this.microCore.eventBus.on(event, callback)
  }
  
  // 路由导航
  navigate(path: string) {
    this.microCore.router.push(path)
  }
}
```

## Svelte 适配器

### 基础使用

```typescript
import { SvelteAdapter } from '@micro-core/adapter-svelte'

const svelteAdapter = new SvelteAdapter({
  // 编译选项
  compilerOptions: {
    dev: process.env.NODE_ENV === 'development'
  }
})

// 注册 Svelte 应用
microCore.registerApp({
  name: 'svelte-app',
  entry: 'http://localhost:3004',
  container: '#svelte-container',
  adapter: svelteAdapter,
  activeWhen: '/svelte-app'
})
```

### Svelte 应用配置

```typescript
// main.ts
import App from './App.svelte'

let app: any = null

export async function mount(element: HTMLElement, props: any) {
  app = new App({
    target: element,
    props: {
      ...props,
      microCore: window.__MICRO_CORE__
    }
  })
}

export async function unmount(element: HTMLElement) {
  if (app) {
    app.$destroy()
    app = null
  }
}

export async function update(props: any) {
  if (app) {
    app.$set(props)
  }
}
```

## 自定义适配器

### 创建自定义适配器

```typescript
import { BaseAdapter } from '@micro-core/core'

export class CustomFrameworkAdapter extends BaseAdapter {
  name = 'custom-framework'
  version = '1.0.0'
  
  private instance: any = null
  
  constructor(private options: CustomAdapterOptions) {
    super()
  }
  
  async mount(element: HTMLElement, props?: any): Promise<void> {
    try {
      // 创建框架实例
      this.instance = new CustomFramework({
        container: element,
        props,
        ...this.options
      })
      
      // 启动应用
      await this.instance.start()
      
      console.log(`${this.name} mounted successfully`)
    } catch (error) {
      console.error(`${this.name} mount failed:`, error)
      throw error
    }
  }
  
  async unmount(element: HTMLElement): Promise<void> {
    try {
      if (this.instance) {
        await this.instance.destroy()
        this.instance = null
      }
      
      // 清理 DOM
      element.innerHTML = ''
      
      console.log(`${this.name} unmounted successfully`)
    } catch (error) {
      console.error(`${this.name} unmount failed:`, error)
      throw error
    }
  }
  
  async update(props: any): Promise<void> {
    if (this.instance && this.instance.updateProps) {
      await this.instance.updateProps(props)
    }
  }
  
  getInstance() {
    return this.instance
  }
}
```

### 适配器配置接口

```typescript
export interface CustomAdapterOptions {
  // 基础配置
  debug?: boolean
  
  // 生命周期钩子
  hooks?: {
    beforeMount?: (element: HTMLElement, props: any) => void
    afterMount?: (element: HTMLElement, instance: any) => void
    beforeUnmount?: (element: HTMLElement, instance: any) => void
    afterUnmount?: (element: HTMLElement) => void
  }
  
  // 错误处理
  errorHandler?: (error: Error, phase: 'mount' | 'unmount' | 'update') => void
  
  // 自定义配置
  customConfig?: Record<string, any>
}
```

## 适配器管理

### 注册适配器

```typescript
import { AdapterRegistry } from '@micro-core/core'

// 注册全局适配器
AdapterRegistry.register('react', ReactAdapter)
AdapterRegistry.register('vue', VueAdapter)
AdapterRegistry.register('angular', AngularAdapter)

// 使用注册的适配器
microCore.registerApp({
  name: 'my-app',
  entry: 'http://localhost:3001',
  adapter: 'react', // 使用字符串引用
  activeWhen: '/my-app'
})
```

### 适配器检测

```typescript
// 自动检测适配器
const autoAdapter = AdapterRegistry.detect(appEntry)

// 手动指定适配器
const manualAdapter = AdapterRegistry.create('vue', vueOptions)
```

## 最佳实践

### 适配器选择指南

1. **React 应用**：使用 ReactAdapter，支持 React 16.8+ 和 React 18
2. **Vue 应用**：使用 VueAdapter，同时支持 Vue 2 和 Vue 3
3. **Angular 应用**：使用 AngularAdapter，支持 Angular 12+
4. **多框架混合**：为每个应用选择对应的适配器

### 性能优化

```typescript
// 懒加载适配器
const lazyAdapter = () => import('@micro-core/adapter-react')
  .then(module => new module.ReactAdapter(options))

// 适配器缓存
const adapterCache = new Map()

function getAdapter(type: string, options: any) {
  const cacheKey = `${type}-${JSON.stringify(options)}`
  
  if (!adapterCache.has(cacheKey)) {
    const adapter = AdapterRegistry.create(type, options)
    adapterCache.set(cacheKey, adapter)
  }
  
  return adapterCache.get(cacheKey)
}
```

### 错误处理

```typescript
// 适配器错误处理
const errorHandlingAdapter = new ReactAdapter({
  errorHandler: (error, phase) => {
    console.error(`Adapter error in ${phase}:`, error)
    
    // 发送错误报告
    errorReporter.report(error, { phase, adapter: 'react' })
    
    // 降级处理
    if (phase === 'mount') {
      // 显示错误页面
      showErrorPage(error)
    }
  }
})
```

通过多框架适配器，Micro-Core 实现了真正的技术栈无关性，让不同框架的应用能够和谐共存，为大型项目的技术选型提供了最大的灵活性。