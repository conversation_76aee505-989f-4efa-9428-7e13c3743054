/**
 * 共享工具包 - 插件相关工具函数
 *
 * @description 提供插件系统相关的通用工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import type { MicroCoreKernel, Plugin, PluginConfig, PluginType } from '../types';
/**
 * 插件元数据接口
 */
export interface PluginMetadata {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 插件描述 */
    description?: string;
    /** 插件作者 */
    author?: string;
    /** 插件类型 */
    type: PluginType;
    /** 插件依赖 */
    dependencies?: string[];
    /** 插件标签 */
    tags?: string[];
    /** 插件主页 */
    homepage?: string;
    /** 插件仓库 */
    repository?: string;
    /** 插件许可证 */
    license?: string;
}
/**
 * 插件状态枚举
 */
export declare enum PluginStatus {
    /** 未安装 */
    NOT_INSTALLED = "not_installed",
    /** 已安装 */
    INSTALLED = "installed",
    /** 已启用 */
    ENABLED = "enabled",
    /** 已禁用 */
    DISABLED = "disabled",
    /** 安装中 */
    INSTALLING = "installing",
    /** 卸载中 */
    UNINSTALLING = "uninstalling",
    /** 错误状态 */
    ERROR = "error"
}
/**
 * 插件统计信息接口
 */
export interface PluginStats {
    /** 插件名称 */
    name: string;
    /** 插件状态 */
    status: PluginStatus;
    /** 安装时间 */
    installedAt?: number;
    /** 启用时间 */
    enabledAt?: number;
    /** 使用次数 */
    usageCount: number;
    /** 错误次数 */
    errorCount: number;
    /** 最后错误 */
    lastError?: any;
    /** 性能指标 */
    performance?: {
        installTime: number;
        averageExecutionTime: number;
        totalExecutionTime: number;
    };
}
/**
 * 插件依赖解析结果接口
 */
export interface DependencyResolution {
    /** 是否满足所有依赖 */
    satisfied: boolean;
    /** 缺失的依赖 */
    missing: string[];
    /** 版本冲突的依赖 */
    conflicts: Array<{
        name: string;
        required: string;
        actual: string;
    }>;
    /** 依赖图 */
    graph: Record<string, string[]>;
}
/**
 * 验证插件配置
 * @param config 插件配置
 * @throws {Error} 配置无效时抛出错误
 */
export declare function validatePluginConfig(config: PluginConfig): void;
/**
 * 标准化插件配置
 * @param config 原始插件配置
 * @returns 标准化后的插件配置
 */
export declare function normalizePluginConfig(config: Partial<PluginConfig>): PluginConfig;
/**
 * 解析插件依赖
 * @param plugins 插件列表
 * @returns 依赖解析结果
 */
export declare function resolvePluginDependencies(plugins: Plugin[]): DependencyResolution;
/**
 * 检查插件版本兼容性
 * @param requiredVersion 要求的版本
 * @param actualVersion 实际版本
 * @returns 是否兼容
 */
export declare function isPluginVersionCompatible(requiredVersion: string, actualVersion: string): boolean;
/**
 * 获取插件加载顺序
 * @param plugins 插件列表
 * @returns 按依赖顺序排序的插件列表
 */
export declare function getPluginLoadOrder(plugins: Plugin[]): Plugin[];
/**
 * 创建插件实例
 * @param metadata 插件元数据
 * @param installFn 安装函数
 * @param uninstallFn 卸载函数
 * @returns 插件实例
 */
export declare function createPluginInstance(metadata: PluginMetadata, installFn: (kernel: MicroCoreKernel) => Promise<void>, uninstallFn?: () => Promise<void>): Plugin;
/**
 * 获取插件状态描述
 * @param status 插件状态
 * @returns 状态描述
 */
export declare function getPluginStatusDescription(status: PluginStatus): string;
/**
 * 检查插件是否为核心插件
 * @param plugin 插件实例
 * @returns 是否为核心插件
 */
export declare function isCorePlugin(plugin: Plugin): boolean;
/**
 * 按类型分组插件
 * @param plugins 插件列表
 * @returns 按类型分组的插件
 */
export declare function groupPluginsByType(plugins: Plugin[]): Record<PluginType, Plugin[]>;
/**
 * 搜索插件
 * @param plugins 插件列表
 * @param query 搜索查询
 * @returns 匹配的插件列表
 */
export declare function searchPlugins(plugins: Plugin[], query: string): Plugin[];
/**
 * 获取插件性能报告
 * @param plugins 插件列表
 * @returns 性能报告
 */
export declare function getPluginPerformanceReport(plugins: Plugin[]): {
    totalPlugins: number;
    enabledPlugins: number;
    averageInstallTime: number;
    totalErrors: number;
    performanceByType: Record<PluginType, {
        count: number;
        averageInstallTime: number;
        errorRate: number;
    }>;
};
//# sourceMappingURL=plugin-utils.d.ts.map