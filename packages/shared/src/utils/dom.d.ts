/**
 * @fileoverview DOM操作工具函数
 * @description 提供各种DOM操作和查询功能
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 查询DOM元素
 */
export declare function querySelector(selector: string, context?: Document | Element): Element | null;
/**
 * 查询所有匹配的DOM元素
 */
export declare function querySelectorAll(selector: string, context?: Document | Element): NodeListOf<Element> | [];
/**
 * 创建DOM元素
 */
export declare function createElement(tagName: string, attributes?: Record<string, string>, textContent?: string): HTMLElement | null;
/**
 * 添加CSS类
 */
export declare function addClass(element: Element, className: string): void;
/**
 * 移除CSS类
 */
export declare function removeClass(element: Element, className: string): void;
/**
 * 切换CSS类
 */
export declare function toggleClass(element: Element, className: string): boolean;
/**
 * 检查是否包含CSS类
 */
export declare function hasClass(element: Element, className: string): boolean;
/**
 * 设置元素样式
 */
export declare function setStyle(element: HTMLElement, styles: Record<string, string>): void;
/**
 * 获取元素样式
 */
export declare function getStyle(element: Element, property: string): string;
/**
 * 添加事件监听器
 */
export declare function addEventListener(element: Element | Window | Document, event: string, handler: EventListener, options?: boolean | AddEventListenerOptions): void;
/**
 * 移除事件监听器
 */
export declare function removeEventListener(element: Element | Window | Document, event: string, handler: EventListener, options?: boolean | EventListenerOptions): void;
/**
 * 获取元素位置信息
 */
export declare function getBoundingRect(element: Element): DOMRect | null;
/**
 * 检查元素是否在视口中
 */
export declare function isInViewport(element: Element): boolean;
/**
 * 滚动到元素
 */
export declare function scrollToElement(element: Element, options?: ScrollIntoViewOptions): void;
//# sourceMappingURL=dom.d.ts.map