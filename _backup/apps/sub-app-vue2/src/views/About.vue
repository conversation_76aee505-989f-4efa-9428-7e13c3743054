<!--
  @fileoverview Vue 2 关于页面组件
  <AUTHOR> <<EMAIL>>
-->

<template>
  <div class="vue2-about">
    <h1>关于 Vue 2 子应用</h1>

    <div v-if="info" class="info-grid">
      <div class="info-card">
        <h3>基本信息</h3>
        <ul>
          <li><strong>应用名称:</strong> {{ info.name }}</li>
          <li><strong>Vue 版本:</strong> {{ info.version }}</li>
          <li><strong>框架:</strong> {{ info.framework }}</li>
          <li><strong>构建工具:</strong> {{ info.buildTool }}</li>
        </ul>
      </div>

      <div class="info-card">
        <h3>技术特性</h3>
        <ul>
          <li v-for="(feature, index) in info.features" :key="index">
            ✅ {{ feature }}
          </li>
        </ul>
      </div>
    </div>

    <div class="architecture-section">
      <h2>微前端架构</h2>
      <p>
        这个 Vue 2 子应用是 Micro-Core 微前端架构的一部分，
        展示了如何将 Vue 2 应用集成到微前端系统中。
      </p>

      <div class="architecture-features">
        <div class="arch-card">
          <h4>🔄 生命周期管理</h4>
          <p>支持 bootstrap、mount、unmount 生命周期</p>
        </div>
        <div class="arch-card">
          <h4>🛡️ 沙箱隔离</h4>
          <p>JavaScript 和 CSS 完全隔离</p>
        </div>
        <div class="arch-card">
          <h4>📡 应用通信</h4>
          <p>支持应用间消息传递和状态共享</p>
        </div>
        <div class="arch-card">
          <h4>🚀 独立部署</h4>
          <p>可以独立开发、测试和部署</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'About',
  data() {
    return {
      info: null
    }
  },
  mounted() {
    this.info = {
      name: 'Vue 2 子应用',
      version: '2.7.0',
      framework: 'Vue 2',
      buildTool: 'Vite',
      features: [
        'TypeScript 支持',
        'Hot Module Replacement',
        'ES6+ 语法',
        'Scoped CSS',
        'Vue Router',
        'Options API'
      ]
    }
  }
}
</script>

<style scoped>
.vue2-about {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.vue2-about h1 {
  color: #4fc08d;
  text-align: center;
  margin-bottom: 30px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.info-card {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
}

.info-card h3 {
  color: #4fc08d;
  margin-bottom: 15px;
}

.info-card ul {
  list-style: none;
  padding: 0;
}

.info-card li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.info-card li:last-child {
  border-bottom: none;
}

.architecture-section {
  margin-top: 40px;
}

.architecture-section h2 {
  color: #4fc08d;
  margin-bottom: 15px;
}

.architecture-section p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 30px;
}

.architecture-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.arch-card {
  padding: 20px;
  border: 1px solid #4fc08d;
  border-radius: 8px;
  background: #f0f9f5;
}

.arch-card h4 {
  color: #4fc08d;
  margin-bottom: 10px;
}

.arch-card p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}
</style>