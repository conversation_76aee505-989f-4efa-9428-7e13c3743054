# wujie 配置迁移指南

本指南详细说明如何将 wujie 的配置迁移到 Micro-Core。

## 📋 目录

- [配置结构对比](#配置结构对比)
- [基础配置迁移](#基础配置迁移)
- [沙箱配置迁移](#沙箱配置迁移)
- [路由配置迁移](#路由配置迁移)
- [生命周期配置迁移](#生命周期配置迁移)
- [插件配置迁移](#插件配置迁移)
- [完整配置示例](#完整配置示例)
- [迁移工具](#迁移工具)

## 配置结构对比

### wujie 配置结构

```javascript
// wujie 配置
import { setupApp, preloadApp, startApp } from 'wujie'

// 预加载配置
preloadApp({
  name: 'vue3',
  url: 'http://localhost:3001',
  exec: true,
  fetch: customFetch,
  plugins: [
    {
      htmlLoader: (code) => code,
      jsLoader: (code) => code,
      cssLoader: (code) => code
    }
  ]
})

// 启动配置
startApp({
  name: 'vue3',
  url: 'http://localhost:3001',
  el: '#container',
  sync: true,
  alive: true,
  fetch: customFetch,
  props: {
    data: { id: 1 }
  },
  beforeLoad: () => console.log('before load'),
  beforeMount: () => console.log('before mount'),
  afterMount: () => console.log('after mount'),
  beforeUnmount: () => console.log('before unmount'),
  afterUnmount: () => console.log('after unmount'),
  activated: () => console.log('activated'),
  deactivated: () => console.log('deactivated')
})
```

### Micro-Core 配置结构

```typescript
// Micro-Core 配置
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  // 应用配置
  apps: [
    {
      name: 'vue3',
      entry: 'http://localhost:3001',
      container: '#container',
      activeWhen: '/vue3',
      
      // 沙箱配置
      sandbox: {
        type: 'iframe', // 对应 wujie 的沙箱模式
        css: true,
        js: true
      },
      
      // 生命周期
      lifecycle: {
        beforeLoad: () => console.log('before load'),
        beforeMount: () => console.log('before mount'),
        afterMount: () => console.log('after mount'),
        beforeUnmount: () => console.log('before unmount'),
        afterUnmount: () => console.log('after unmount'),
        activated: () => console.log('activated'),
        deactivated: () => console.log('deactivated')
      },
      
      // 预加载
      preload: true,
      
      // 保活模式
      keepAlive: true,
      
      // 自定义属性
      props: {
        data: { id: 1 }
      }
    }
  ],
  
  // 全局配置
  fetch: customFetch,
  plugins: [
    // 资源加载插件
    {
      name: 'resource-loader',
      htmlLoader: (code) => code,
      jsLoader: (code) => code,
      cssLoader: (code) => code
    }
  ]
})
```

## 基础配置迁移

### 应用注册迁移

```javascript
// wujie 应用注册
import { setupApp } from 'wujie'

setupApp({
  name: 'vue3-app',
  url: 'http://localhost:3001',
  exec: true,
  alive: true,
  sync: true,
  prefix: {
    'http://localhost:3001': 'http://localhost:8080/proxy'
  },
  replace: (code) => code.replace('old', 'new')
})
```

```typescript
// Micro-Core 应用注册
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  apps: [
    {
      name: 'vue3-app',
      entry: 'http://localhost:3001',
      activeWhen: '/vue3-app',
      
      // 执行模式
      exec: true,
      
      // 保活模式
      keepAlive: true,
      
      // 同步模式
      sync: true,
      
      // 代理配置
      proxy: {
        'http://localhost:3001': 'http://localhost:8080/proxy'
      },
      
      // 代码替换
      transform: {
        html: (code) => code.replace('old', 'new'),
        js: (code) => code,
        css: (code) => code
      }
    }
  ]
})
```

### 容器配置迁移

```javascript
// wujie 容器配置
startApp({
  name: 'vue3',
  url: 'http://localhost:3001',
  el: '#container',
  width: '100%',
  height: '100vh',
  degradeAttrs: ['width', 'height']
})
```

```typescript
// Micro-Core 容器配置
const microCore = new MicroCore({
  apps: [
    {
      name: 'vue3',
      entry: 'http://localhost:3001',
      container: '#container',
      
      // 容器样式
      containerStyle: {
        width: '100%',
        height: '100vh'
      },
      
      // 降级属性
      degradeAttrs: ['width', 'height']
    }
  ]
})
```

## 沙箱配置迁移

### wujie 沙箱模式

```javascript
// wujie 沙箱配置
startApp({
  name: 'vue3',
  url: 'http://localhost:3001',
  el: '#container',
  
  // iframe 沙箱（默认）
  degrade: false,
  
  // 降级模式（类似 proxy 沙箱）
  degrade: true,
  
  // 样式隔离
  shadowRoot: true,
  
  // 插件配置
  plugins: [
    {
      cssExcludes: ['http://localhost:3001/exclude.css'],
      jsExcludes: ['http://localhost:3001/exclude.js']
    }
  ]
})
```

### Micro-Core 沙箱迁移

```typescript
// Micro-Core 沙箱配置
const microCore = new MicroCore({
  apps: [
    {
      name: 'vue3',
      entry: 'http://localhost:3001',
      container: '#container',
      
      // 沙箱类型选择
      sandbox: {
        // iframe 沙箱（对应 wujie 默认模式）
        type: 'iframe',
        
        // 或 proxy 沙箱（对应 wujie 降级模式）
        // type: 'proxy',
        
        // 或 WebComponent 沙箱（对应 wujie shadowRoot）
        // type: 'webcomponent',
        
        // 样式隔离
        css: true,
        
        // JS 隔离
        js: true,
        
        // 资源排除
        excludes: {
          css: ['http://localhost:3001/exclude.css'],
          js: ['http://localhost:3001/exclude.js']
        }
      }
    }
  ]
})
```

### 沙箱类型映射

```typescript
// wujie 到 Micro-Core 沙箱映射
const sandboxMapping = {
  // wujie iframe 模式 -> Micro-Core iframe 沙箱
  wujie_iframe: {
    type: 'iframe',
    css: true,
    js: true
  },
  
  // wujie 降级模式 -> Micro-Core proxy 沙箱
  wujie_degrade: {
    type: 'proxy',
    css: true,
    js: true
  },
  
  // wujie shadowRoot -> Micro-Core webcomponent 沙箱
  wujie_shadow: {
    type: 'webcomponent',
    css: true,
    js: false
  }
}

// 自动选择沙箱类型
function migrateSandbox(wujieConfig) {
  if (wujieConfig.degrade) {
    return { type: 'proxy', css: true, js: true }
  } else if (wujieConfig.shadowRoot) {
    return { type: 'webcomponent', css: true, js: false }
  } else {
    return { type: 'iframe', css: true, js: true }
  }
}
```

## 路由配置迁移

### wujie 路由同步

```javascript
// wujie 路由同步
startApp({
  name: 'vue3',
  url: 'http://localhost:3001',
  el: '#container',
  sync: true, // 开启路由同步
  
  // 路由前缀
  prefix: {
    'http://localhost:3001': '/vue3'
  }
})

// 手动路由跳转
import { bus } from 'wujie'
bus.$emit('vue3-router-change', '/new-path')
```

### Micro-Core 路由迁移

```typescript
// Micro-Core 路由配置
const microCore = new MicroCore({
  // 路由配置
  router: {
    mode: 'history',
    base: '/',
    
    // 路由同步
    sync: true,
    
    // 路由守卫
    beforeEach: (to, from, next) => {
      console.log('路由跳转:', to.path)
      next()
    }
  },
  
  apps: [
    {
      name: 'vue3',
      entry: 'http://localhost:3001',
      
      // 激活条件
      activeWhen: '/vue3',
      
      // 路由前缀
      routePrefix: '/vue3',
      
      // 路由同步
      sync: true
    }
  ]
})

// 路由跳转
microCore.router.push('/vue3/new-path')

// 或使用事件总线
microCore.eventBus.emit('router:push', '/vue3/new-path')
```

## 生命周期配置迁移

### wujie 生命周期

```javascript
// wujie 生命周期钩子
startApp({
  name: 'vue3',
  url: 'http://localhost:3001',
  el: '#container',
  
  // 生命周期钩子
  beforeLoad: (appWindow) => {
    console.log('应用加载前', appWindow)
  },
  
  beforeMount: (appWindow) => {
    console.log('应用挂载前', appWindow)
  },
  
  afterMount: (appWindow) => {
    console.log('应用挂载后', appWindow)
  },
  
  beforeUnmount: (appWindow) => {
    console.log('应用卸载前', appWindow)
  },
  
  afterUnmount: (appWindow) => {
    console.log('应用卸载后', appWindow)
  },
  
  activated: (appWindow) => {
    console.log('应用激活', appWindow)
  },
  
  deactivated: (appWindow) => {
    console.log('应用失活', appWindow)
  }
})
```

### Micro-Core 生命周期迁移

```typescript
// Micro-Core 生命周期配置
const microCore = new MicroCore({
  apps: [
    {
      name: 'vue3',
      entry: 'http://localhost:3001',
      container: '#container',
      
      // 生命周期钩子
      lifecycle: {
        beforeLoad: (app) => {
          console.log('应用加载前', app)
        },
        
        beforeMount: (app) => {
          console.log('应用挂载前', app)
        },
        
        afterMount: (app) => {
          console.log('应用挂载后', app)
        },
        
        beforeUnmount: (app) => {
          console.log('应用卸载前', app)
        },
        
        afterUnmount: (app) => {
          console.log('应用卸载后', app)
        },
        
        activated: (app) => {
          console.log('应用激活', app)
        },
        
        deactivated: (app) => {
          console.log('应用失活', app)
        }
      }
    }
  ]
})
```

### 生命周期增强功能

```typescript
// Micro-Core 提供更丰富的生命周期
const microCore = new MicroCore({
  apps: [
    {
      name: 'vue3',
      entry: 'http://localhost:3001',
      lifecycle: {
        // 资源加载阶段
        beforeLoad: async (app) => {
          // 预处理逻辑
          await setupPrerequisites()
        },
        
        afterLoad: (app) => {
          // 资源加载完成
          console.log('资源加载完成')
        },
        
        // 挂载阶段
        beforeMount: (app) => {
          // 挂载前准备
          prepareMount(app)
        },
        
        afterMount: (app) => {
          // 挂载完成后
          trackAppMount(app.name)
        },
        
        // 更新阶段
        beforeUpdate: (app) => {
          console.log('应用更新前')
        },
        
        afterUpdate: (app) => {
          console.log('应用更新后')
        },
        
        // 错误处理
        onError: (error, app) => {
          console.error('应用错误:', error)
          reportError(error, app.name)
        }
      }
    }
  ]
})
```

## 插件配置迁移

### wujie 插件系统

```javascript
// wujie 插件配置
startApp({
  name: 'vue3',
  url: 'http://localhost:3001',
  el: '#container',
  
  plugins: [
    {
      // HTML 处理器
      htmlLoader: (code, url) => {
        return code.replace(/<title>.*<\/title>/, '<title>New Title</title>')
      },
      
      // JS 处理器
      jsLoader: (code, url) => {
        return `
          console.log('JS loaded from: ${url}');
          ${code}
        `
      },
      
      // CSS 处理器
      cssLoader: (code, url) => {
        return code.replace(/color:\s*red/g, 'color: blue')
      },
      
      // 资源排除
      cssExcludes: ['http://localhost:3001/exclude.css'],
      jsExcludes: ['http://localhost:3001/exclude.js']
    }
  ]
})
```

### Micro-Core 插件迁移

```typescript
// Micro-Core 插件配置
import { MicroCore, Plugin } from '@micro-core/core'

// 创建资源处理插件
const resourcePlugin: Plugin = {
  name: 'resource-processor',
  
  // HTML 处理
  processHtml: (code, url) => {
    return code.replace(/<title>.*<\/title>/, '<title>New Title</title>')
  },
  
  // JS 处理
  processJs: (code, url) => {
    return `
      console.log('JS loaded from: ${url}');
      ${code}
    `
  },
  
  // CSS 处理
  processCss: (code, url) => {
    return code.replace(/color:\s*red/g, 'color: blue')
  },
  
  // 资源过滤
  shouldLoadResource: (url, type) => {
    const excludes = {
      css: ['http://localhost:3001/exclude.css'],
      js: ['http://localhost:3001/exclude.js']
    }
    return !excludes[type]?.includes(url)
  }
}

const microCore = new MicroCore({
  plugins: [resourcePlugin],
  
  apps: [
    {
      name: 'vue3',
      entry: 'http://localhost:3001',
      container: '#container'
    }
  ]
})
```

### 高级插件功能

```typescript
// Micro-Core 高级插件
const advancedPlugin: Plugin = {
  name: 'advanced-processor',
  
  // 插件初始化
  install: (microCore) => {
    console.log('插件安装完成')
  },
  
  // 应用加载前
  beforeAppLoad: (app) => {
    console.log(`准备加载应用: ${app.name}`)
  },
  
  // 资源拦截
  interceptFetch: async (url, options) => {
    // 自定义 fetch 逻辑
    if (url.includes('/api/')) {
      return customApiFetch(url, options)
    }
    return fetch(url, options)
  },
  
  // 错误处理
  onError: (error, context) => {
    console.error('插件错误:', error)
    // 错误上报
    reportPluginError(error, context)
  }
}
```

## 完整配置示例

### wujie 完整配置

```javascript
// wujie 完整配置示例
import { setupApp, preloadApp, startApp, bus } from 'wujie'

// 1. 预加载应用
preloadApp({
  name: 'vue3-app',
  url: 'http://localhost:3001',
  exec: true
})

// 2. 设置应用
setupApp({
  name: 'vue3-app',
  url: 'http://localhost:3001',
  exec: true,
  alive: true,
  fetch: (url, options) => {
    return fetch(url, {
      ...options,
      headers: {
        ...options?.headers,
        'Authorization': 'Bearer token'
      }
    })
  },
  plugins: [
    {
      htmlLoader: (code) => code,
      jsLoader: (code) => code,
      cssLoader: (code) => code,
      cssExcludes: ['http://localhost:3001/exclude.css']
    }
  ]
})

// 3. 启动应用
startApp({
  name: 'vue3-app',
  url: 'http://localhost:3001',
  el: '#app-container',
  sync: true,
  alive: true,
  props: {
    userInfo: { id: 1, name: 'admin' },
    onMessage: (msg) => console.log(msg)
  },
  beforeLoad: () => console.log('before load'),
  afterMount: () => console.log('after mount'),
  beforeUnmount: () => console.log('before unmount')
})

// 4. 事件通信
bus.$on('vue3-app-message', (data) => {
  console.log('接收消息:', data)
})

bus.$emit('main-app-message', { type: 'greeting' })
```

### Micro-Core 迁移后配置

```typescript
// Micro-Core 完整配置示例
import { MicroCore, GlobalState, EventBus } from '@micro-core/core'

// 1. 创建全局状态
const globalState = new GlobalState({
  userInfo: { id: 1, name: 'admin' },
  theme: 'light'
})

// 2. 创建事件总线
const eventBus = new EventBus()

// 3. 创建自定义 fetch
const customFetch = (url: string, options?: RequestInit) => {
  return fetch(url, {
    ...options,
    headers: {
      ...options?.headers,
      'Authorization': 'Bearer token'
    }
  })
}

// 4. 创建插件
const resourcePlugin = {
  name: 'resource-processor',
  processHtml: (code: string) => code,
  processJs: (code: string) => code,
  processCss: (code: string) => code,
  shouldLoadResource: (url: string, type: string) => {
    return !url.includes('exclude.css')
  }
}

// 5. 创建微前端实例
const microCore = new MicroCore({
  // 全局配置
  globalState,
  eventBus,
  fetch: customFetch,
  plugins: [resourcePlugin],
  
  // 应用配置
  apps: [
    {
      name: 'vue3-app',
      entry: 'http://localhost:3001',
      container: '#app-container',
      activeWhen: '/vue3-app',
      
      // 预加载
      preload: true,
      
      // 保活模式
      keepAlive: true,
      
      // 路由同步
      sync: true,
      
      // 沙箱配置
      sandbox: {
        type: 'iframe',
        css: true,
        js: true
      },
      
      // 生命周期
      lifecycle: {
        beforeLoad: () => console.log('before load'),
        afterMount: () => console.log('after mount'),
        beforeUnmount: () => console.log('before unmount')
      },
      
      // 自定义属性
      props: {
        userInfo: () => globalState.getState().userInfo,
        onMessage: (msg: string) => console.log(msg)
      }
    }
  ]
})

// 6. 启动应用
microCore.start()

// 7. 事件通信
eventBus.on('vue3-app-message', (data) => {
  console.log('接收消息:', data)
})

eventBus.emit('main-app-message', { type: 'greeting' })
```

## 迁移工具

### 自动迁移脚本

```typescript
// wujie 到 Micro-Core 配置转换工具
interface WujieConfig {
  name: string
  url: string
  el: string
  sync?: boolean
  alive?: boolean
  degrade?: boolean
  shadowRoot?: boolean
  props?: any
  plugins?: any[]
  [key: string]: any
}

interface MicroCoreAppConfig {
  name: string
  entry: string
  container: string
  activeWhen: string
  sync?: boolean
  keepAlive?: boolean
  sandbox?: {
    type: 'iframe' | 'proxy' | 'webcomponent'
    css: boolean
    js: boolean
  }
  props?: any
  lifecycle?: any
}

function migrateWujieConfig(wujieConfig: WujieConfig): MicroCoreAppConfig {
  const microCoreConfig: MicroCoreAppConfig = {
    name: wujieConfig.name,
    entry: wujieConfig.url,
    container: wujieConfig.el,
    activeWhen: `/${wujieConfig.name}`,
    sync: wujieConfig.sync,
    keepAlive: wujieConfig.alive,
    props: wujieConfig.props
  }
  
  // 沙箱配置迁移
  if (wujieConfig.degrade) {
    microCoreConfig.sandbox = { type: 'proxy', css: true, js: true }
  } else if (wujieConfig.shadowRoot) {
    microCoreConfig.sandbox = { type: 'webcomponent', css: true, js: false }
  } else {
    microCoreConfig.sandbox = { type: 'iframe', css: true, js: true }
  }
  
  // 生命周期迁移
  const lifecycleKeys = [
    'beforeLoad', 'beforeMount', 'afterMount',
    'beforeUnmount', 'afterUnmount', 'activated', 'deactivated'
  ]
  
  const lifecycle: any = {}
  lifecycleKeys.forEach(key => {
    if (wujieConfig[key]) {
      lifecycle[key] = wujieConfig[key]
    }
  })
  
  if (Object.keys(lifecycle).length > 0) {
    microCoreConfig.lifecycle = lifecycle
  }
  
  return microCoreConfig
}

// 使用示例
const wujieConfig = {
  name: 'vue3-app',
  url: 'http://localhost:3001',
  el: '#container',
  sync: true,
  alive: true,
  degrade: false,
  props: { data: { id: 1 } },
  beforeMount: () => console.log('before mount')
}

const microCoreConfig = migrateWujieConfig(wujieConfig)
console.log('迁移后配置:', microCoreConfig)
```

### 配置验证工具

```typescript
// 配置验证工具
function validateMicroCoreConfig(config: any): { valid: boolean; errors: string[] } {
  const errors: string[] = []
  
  // 必需字段验证
  if (!config.apps || !Array.isArray(config.apps)) {
    errors.push('apps 配置是必需的，且必须是数组')
  }
  
  config.apps?.forEach((app: any, index: number) => {
    if (!app.name) {
      errors.push(`应用 ${index}: name 是必需的`)
    }
    
    if (!app.entry) {
      errors.push(`应用 ${index}: entry 是必需的`)
    }
    
    if (!app.container) {
      errors.push(`应用 ${index}: container 是必需的`)
    }
    
    if (!app.activeWhen) {
      errors.push(`应用 ${index}: activeWhen 是必需的`)
    }
    
    // 沙箱配置验证
    if (app.sandbox) {
      const validTypes = ['iframe', 'proxy', 'webcomponent', 'namespace', 'defineProperty', 'federation']
      if (!validTypes.includes(app.sandbox.type)) {
        errors.push(`应用 ${index}: 无效的沙箱类型 ${app.sandbox.type}`)
      }
    }
  })
  
  return {
    valid: errors.length === 0,
    errors
  }
}
```

### 迁移检查清单

```typescript
// 迁移检查清单
const migrationChecklist = {
  // 基础配置
  basic: [
    '应用名称 (name)',
    '入口地址 (entry/url)',
    '容器元素 (container/el)',
    '激活条件 (activeWhen)'
  ],
  
  // 沙箱配置
  sandbox: [
    '沙箱类型选择',
    '样式隔离配置',
    'JS 隔离配置',
    '资源排除规则'
  ],
  
  // 生命周期
  lifecycle: [
    'beforeLoad 钩子',
    'beforeMount 钩子',
    'afterMount 钩子',
    'beforeUnmount 钩子',
    'afterUnmount 钩子',
    'activated 钩子',
    'deactivated 钩子'
  ],
  
  // 高级功能
  advanced: [
    '预加载配置',
    '保活模式',
    '路由同步',
    '自定义 fetch',
    '插件配置',
    'Props 传递'
  ],
  
  // 通信机制
  communication: [
    '事件总线配置',
    '全局状态管理',
    '应用间通信'
  ]
}

// 生成迁移报告
function generateMigrationReport(wujieConfig: any, microCoreConfig: any) {
  const report = {
    summary: {
      totalApps: wujieConfig.length || 1,
      migratedApps: 0,
      warnings: [],
      errors: []
    },
    details: []
  }
  
  // 详细分析每个应用的迁移情况
  // ... 实现迁移报告逻辑
  
  return report
}
```

## 迁移最佳实践

### 1. 渐进式迁移

```typescript
// 渐进式迁移策略
const migrationStrategy = {
  // 阶段1: 基础迁移
  phase1: {
    scope: ['应用注册', '基础配置', '容器配置'],
    duration: '1-2 天',
    risk: '低'
  },
  
  // 阶段2: 沙箱迁移
  phase2: {
    scope: ['沙箱配置', '资源处理', '样式隔离'],
    duration: '2-3 天',
    risk: '中'
  },
  
  // 阶段3: 高级功能
  phase3: {
    scope: ['生命周期', '插件系统', '通信机制'],
    duration: '3-5 天',
    risk: '中'
  },
  
  // 阶段4: 优化完善
  phase4: {
    scope: ['性能优化', '错误处理', '监控告警'],
    duration: '2-3 天',
    risk: '低'
  }
}
```

### 2. 兼容性处理

```typescript
// wujie 兼容层
class WujieCompatLayer {
  private microCore: MicroCore
  
  constructor(microCore: MicroCore) {
    this.microCore = microCore
  }
  
  // 兼容 wujie startApp API
  startApp(config: WujieConfig) {
    const microCoreConfig = migrateWujieConfig(config)
    return this.microCore.loadApp(microCoreConfig.name)
  }
  
  // 兼容 wujie destroyApp API
  destroyApp(name: string) {
    return this.microCore.unloadApp(name)
  }
  
  // 兼容 wujie bus API
  get bus() {
    return {
      $on: (event: string, handler: Function) => {
        this.microCore.eventBus.on(event, handler)
      },
      $emit: (event: string, data: any) => {
        this.microCore.eventBus.emit(event, data)
      },
      $off: (event: string, handler?: Function) => {
        this.microCore.eventBus.off(event, handler)
      }
    }
  }
}

// 使用兼容层
const microCore = new MicroCore(config)
const wujieCompat = new WujieCompatLayer(microCore)

// 可以继续使用 wujie API
wujieCompat.startApp({
  name: 'vue3',
  url: 'http://localhost:3001',
  el: '#container'
})
```

### 3. 测试验证

```typescript
// 迁移测试套件
describe('wujie 迁移测试', () => {
  let microCore: MicroCore
  
  beforeEach(() => {
    microCore = new MicroCore(migratedConfig)
  })
  
  test('应用加载功能', async () => {
    await microCore.loadApp('vue3-app')
    expect(microCore.getApp('vue3-app')).toBeDefined()
  })
  
  test('沙箱隔离功能', () => {
    const app = microCore.getApp('vue3-app')
    expect(app.sandbox.type).toBe('iframe')
  })
  
  test('生命周期钩子', async () => {
    const mockFn = jest.fn()
    const config = {
      ...migratedConfig,
      apps: [{
        ...migratedConfig.apps[0],
        lifecycle: { beforeMount: mockFn }
      }]
    }
    
    const mc = new MicroCore(config)
    await mc.loadApp('vue3-app')
    
    expect(mockFn).toHaveBeenCalled()
  })
  
  test('事件通信功能', () => {
    const mockHandler = jest.fn()
    microCore.eventBus.on('test-event', mockHandler)
    microCore.eventBus.emit('test-event', { data: 'test' })
    
    expect(mockHandler).toHaveBeenCalledWith({ data: 'test' })
  })
})
```

通过以上详细的迁移指南，您可以将 wujie 配置平滑迁移到 Micro-Core，并享受更强大的功能和更好的开发体验。
