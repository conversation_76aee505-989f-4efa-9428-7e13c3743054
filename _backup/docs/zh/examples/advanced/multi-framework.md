# 多框架集成示例

本示例展示如何在同一个微前端系统中集成多个不同技术栈的微应用，实现真正的技术栈无关架构。

## 🎯 示例概述

### 架构设计

```
主应用 (Shell App)
├── React 微应用 (用户中心)
├── Vue 微应用 (商品目录)
├── Angular 微应用 (订单管理)
├── Svelte 微应用 (数据分析)
└── 原生 JS 微应用 (通知中心)
```

### 技术栈组合

| 微应用 | 框架 | 状态管理 | 路由 | 构建工具 |
|--------|------|----------|------|----------|
| Shell | React 18 | Redux Toolkit | React Router | Vite |
| 用户中心 | React 18 | Zustand | React Router | Vite |
| 商品目录 | Vue 3 | Pinia | Vue Router | Vite |
| 订单管理 | Angular 16 | NgRx | Angular Router | Angular CLI |
| 数据分析 | Svelte 4 | Svelte Store | SPA Router | Vite |
| 通知中心 | 原生 JS | LocalStorage | History API | Rollup |

## 🏗️ 项目结构

```
multi-framework-example/
├── shell-app/                    # 主应用 (React)
│   ├── src/
│   │   ├── components/
│   │   ├── store/
│   │   ├── App.tsx
│   │   └── main.tsx
│   ├── vite.config.ts
│   └── package.json
├── user-center/                  # React 微应用
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   └── main.tsx
│   └── package.json
├── product-catalog/              # Vue 微应用
│   ├── src/
│   │   ├── components/
│   │   ├── views/
│   │   └── main.ts
│   └── package.json
├── order-management/             # Angular 微应用
│   ├── src/
│   │   ├── app/
│   │   └── main.ts
│   └── package.json
├── data-analytics/               # Svelte 微应用
│   ├── src/
│   │   ├── components/
│   │   └── main.js
│   └── package.json
├── notification-center/          # 原生 JS 微应用
│   ├── src/
│   │   ├── components/
│   │   └── index.js
│   └── package.json
├── shared/                       # 共享资源
│   ├── types/
│   ├── utils/
│   └── styles/
└── docker-compose.yml            # 开发环境
```

## 🚀 主应用实现

### 1. 主应用配置

```typescript
// shell-app/src/main.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { MicroCore } from '@micro-core/core';
import { store } from './store';
import App from './App';
import './index.css';

// 初始化微前端核心
const microCore = new MicroCore({
  container: '#micro-container',
  
  // 全局配置
  globalConfig: {
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
    theme: 'light',
    locale: 'zh-CN'
  },
  
  // 共享依赖
  shared: {
    'react': React,
    'react-dom': ReactDOM,
    'react-router-dom': require('react-router-dom')
  }
});

// 注册微应用
const microApps = [
  {
    name: 'user-center',
    entry: 'http://localhost:3001',
    container: '#micro-container',
    activeRule: '/user',
    framework: 'react',
    props: {
      basename: '/user'
    }
  },
  {
    name: 'product-catalog',
    entry: 'http://localhost:3002',
    container: '#micro-container',
    activeRule: '/products',
    framework: 'vue',
    props: {
      basename: '/products'
    }
  },
  {
    name: 'order-management',
    entry: 'http://localhost:3003',
    container: '#micro-container',
    activeRule: '/orders',
    framework: 'angular',
    props: {
      basename: '/orders'
    }
  },
  {
    name: 'data-analytics',
    entry: 'http://localhost:3004',
    container: '#micro-container',
    activeRule: '/analytics',
    framework: 'svelte',
    props: {
      basename: '/analytics'
    }
  },
  {
    name: 'notification-center',
    entry: 'http://localhost:3005',
    container: '#notification-container',
    activeRule: () => true, // 始终激活
    framework: 'vanilla',
    props: {
      position: 'top-right'
    }
  }
];

// 批量注册微应用
microApps.forEach(app => {
  microCore.registerApp(app);
});

// 启动微前端系统
microCore.start();

// 渲染主应用
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Provider store={store}>
      <BrowserRouter>
        <App microCore={microCore} />
      </BrowserRouter>
    </Provider>
  </React.StrictMode>
);
```

### 2. 主应用组件

```typescript
// shell-app/src/App.tsx
import React, { useEffect, useState } from 'react';
import { Routes, Route, useLocation, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { MicroCore } from '@micro-core/core';
import Navigation from './components/Navigation';
import ErrorBoundary from './components/ErrorBoundary';
import LoadingSpinner from './components/LoadingSpinner';
import { setCurrentApp, setGlobalState } from './store/slices/appSlice';
import type { RootState } from './store';

interface AppProps {
  microCore: MicroCore;
}

const App: React.FC<AppProps> = ({ microCore }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  const { currentApp, globalState, isLoading } = useSelector(
    (state: RootState) => state.app
  );
  
  const [microApps, setMicroApps] = useState<string[]>([]);
  
  useEffect(() => {
    // 设置微前端全局状态
    microCore.setGlobalState(globalState);
    
    // 监听微应用状态变化
    microCore.on('app:mounted', (appName: string) => {
      console.log(`微应用 ${appName} 已挂载`);
      dispatch(setCurrentApp(appName));
      
      setMicroApps(prev => [...prev.filter(name => name !== appName), appName]);
    });
    
    microCore.on('app:unmounted', (appName: string) => {
      console.log(`微应用 ${appName} 已卸载`);
      
      setMicroApps(prev => prev.filter(name => name !== appName));
    });
    
    // 监听微应用间通信
    microCore.on('global:state:change', (data: any) => {
      dispatch(setGlobalState(data));
    });
    
    // 监听路由变化
    microCore.on('route:change', (data: any) => {
      if (data.external) {
        navigate(data.path);
      }
    });
    
    return () => {
      microCore.off('app:mounted');
      microCore.off('app:unmounted');
      microCore.off('global:state:change');
      microCore.off('route:change');
    };
  }, [microCore, dispatch, navigate, globalState]);
  
  // 处理导航
  const handleNavigation = (path: string) => {
    navigate(path);
    
    // 通知微应用路由变化
    microCore.emit('navigation:change', {
      path,
      timestamp: new Date().toISOString()
    });
  };
  
  // 处理全局状态更新
  const handleGlobalStateUpdate = (key: string, value: any) => {
    const newState = { ...globalState, [key]: value };
    dispatch(setGlobalState(newState));
    microCore.setGlobalState(newState);
  };
  
  return (
    <div className="app">
      {/* 导航栏 */}
      <Navigation
        currentPath={location.pathname}
        onNavigate={handleNavigation}
        microApps={microApps}
        globalState={globalState}
        onGlobalStateUpdate={handleGlobalStateUpdate}
      />
      
      {/* 加载状态 */}
      {isLoading && <LoadingSpinner />}
      
      {/* 通知中心容器 */}
      <div id="notification-container" className="notification-container" />
      
      {/* 主内容区域 */}
      <main className="main-content">
        <ErrorBoundary>
          <Routes>
            {/* 首页 */}
            <Route path="/" element={<HomePage />} />
            
            {/* 微应用路由 */}
            <Route path="/user/*" element={<MicroAppContainer appName="user-center" />} />
            <Route path="/products/*" element={<MicroAppContainer appName="product-catalog" />} />
            <Route path="/orders/*" element={<MicroAppContainer appName="order-management" />} />
            <Route path="/analytics/*" element={<MicroAppContainer appName="data-analytics" />} />
            
            {/* 404 页面 */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </ErrorBoundary>
        
        {/* 微应用容器 */}
        <div id="micro-container" className="micro-container" />
      </main>
    </div>
  );
};

// 微应用容器组件
const MicroAppContainer: React.FC<{ appName: string }> = ({ appName }) => {
  return (
    <div className={`micro-app-wrapper ${appName}`}>
      {/* 微应用将在这里渲染 */}
    </div>
  );
};

// 首页组件
const HomePage: React.FC = () => {
  return (
    <div className="home-page">
      <h1>欢迎使用多框架微前端系统</h1>
      <div className="feature-grid">
        <div className="feature-card">
          <h3>🧑‍💼 用户中心</h3>
          <p>基于 React 18 开发的用户管理系统</p>
          <a href="/user">进入 →</a>
        </div>
        <div className="feature-card">
          <h3>🛍️ 商品目录</h3>
          <p>基于 Vue 3 开发的商品展示系统</p>
          <a href="/products">进入 →</a>
        </div>
        <div className="feature-card">
          <h3>📋 订单管理</h3>
          <p>基于 Angular 16 开发的订单处理系统</p>
          <a href="/orders">进入 →</a>
        </div>
        <div className="feature-card">
          <h3>📊 数据分析</h3>
          <p>基于 Svelte 4 开发的数据可视化系统</p>
          <a href="/analytics">进入 →</a>
        </div>
      </div>
    </div>
  );
};

// 404 页面组件
const NotFoundPage: React.FC = () => {
  return (
    <div className="not-found-page">
      <h1>404 - 页面未找到</h1>
      <p>抱歉，您访问的页面不存在。</p>
      <a href="/">返回首页</a>
    </div>
  );
};

export default App;
```

## 🔄 跨框架通信

### 1. 事件总线通信

```typescript
// shared/utils/eventBus.ts
export interface EventBusMessage {
  type: string;
  payload: any;
  source: string;
  timestamp: number;
}

export class CrossFrameworkEventBus {
  private listeners = new Map<string, Function[]>();
  
  // 发送事件
  emit(type: string, payload: any, source: string = 'unknown') {
    const message: EventBusMessage = {
      type,
      payload,
      source,
      timestamp: Date.now()
    };
    
    // 本地事件分发
    const localListeners = this.listeners.get(type) || [];
    localListeners.forEach(listener => {
      try {
        listener(message);
      } catch (error) {
        console.error(`事件处理错误 [${type}]:`, error);
      }
    });
    
    // 跨框架事件分发
    this.broadcastToAllFrameworks(message);
  }
  
  // 监听事件
  on(type: string, listener: (message: EventBusMessage) => void) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, []);
    }
    
    this.listeners.get(type)!.push(listener);
    
    return () => this.off(type, listener);
  }
  
  // 取消监听
  off(type: string, listener?: Function) {
    if (!listener) {
      this.listeners.delete(type);
      return;
    }
    
    const listeners = this.listeners.get(type) || [];
    const index = listeners.indexOf(listener);
    
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }
  
  // 广播到所有框架
  private broadcastToAllFrameworks(message: EventBusMessage) {
    // 通过 CustomEvent 广播到所有框架
    const customEvent = new CustomEvent('micro-app-message', {
      detail: message
    });
    
    window.dispatchEvent(customEvent);
    
    // 通过 PostMessage 广播到 iframe 中的应用
    const iframes = document.querySelectorAll('iframe[data-micro-app]');
    iframes.forEach(iframe => {
      const iframeWindow = (iframe as HTMLIFrameElement).contentWindow;
      if (iframeWindow) {
        iframeWindow.postMessage(message, '*');
      }
    });
  }
}

// 全局事件总线实例
export const globalEventBus = new CrossFrameworkEventBus();
```

### 2. 全局状态管理

```typescript
// shared/utils/globalState.ts
export interface GlobalStateData {
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    permissions: string[];
  } | null;
  theme: 'light' | 'dark';
  locale: string;
  cart: {
    items: any[];
    total: number;
  };
  notifications: any[];
  [key: string]: any;
}

export class CrossFrameworkGlobalState {
  private state: GlobalStateData;
  private watchers = new Map<string, Function[]>();
  
  constructor(initialState: Partial<GlobalStateData> = {}) {
    this.state = {
      user: null,
      theme: 'light',
      locale: 'zh-CN',
      cart: { items: [], total: 0 },
      notifications: [],
      ...initialState
    };
  }
  
  // 获取状态
  get<K extends keyof GlobalStateData>(key: K): GlobalStateData[K] {
    return this.state[key];
  }
  
  // 设置状态
  set<K extends keyof GlobalStateData>(key: K, value: GlobalStateData[K]) {
    const oldValue = this.state[key];
    this.state[key] = value;
    
    // 通知观察者
    this.notifyWatchers(key, value, oldValue);
    
    // 广播状态变化
    this.broadcastStateChange(key, value, oldValue);
  }
  
  // 批量更新状态
  update(updates: Partial<GlobalStateData>) {
    const changes: Array<{
      key: keyof GlobalStateData;
      newValue: any;
      oldValue: any;
    }> = [];
    
    Object.entries(updates).forEach(([key, value]) => {
      const oldValue = this.state[key as keyof GlobalStateData];
      this.state[key as keyof GlobalStateData] = value;
      
      changes.push({
        key: key as keyof GlobalStateData,
        newValue: value,
        oldValue
      });
    });
    
    // 批量通知
    changes.forEach(({ key, newValue, oldValue }) => {
      this.notifyWatchers(key, newValue, oldValue);
    });
    
    // 广播批量变化
    this.broadcastBatchChanges(changes);
  }
  
  // 监听状态变化
  watch<K extends keyof GlobalStateData>(
    key: K,
    watcher: (newValue: GlobalStateData[K], oldValue: GlobalStateData[K]) => void
  ) {
    if (!this.watchers.has(key as string)) {
      this.watchers.set(key as string, []);
    }
    
    this.watchers.get(key as string)!.push(watcher);
    
    return () => this.unwatch(key, watcher);
  }
  
  // 取消监听
  unwatch<K extends keyof GlobalStateData>(
    key: K,
    watcher?: Function
  ) {
    if (!watcher) {
      this.watchers.delete(key as string);
      return;
    }
    
    const watchers = this.watchers.get(key as string) || [];
    const index = watchers.indexOf(watcher);
    
    if (index > -1) {
      watchers.splice(index, 1);
    }
  }
  
  // 获取完整状态
  getState(): GlobalStateData {
    return { ...this.state };
  }
  
  // 通知观察者
  private notifyWatchers(key: keyof GlobalStateData, newValue: any, oldValue: any) {
    const watchers = this.watchers.get(key as string) || [];
    
    watchers.forEach(watcher => {
      try {
        watcher(newValue, oldValue);
      } catch (error) {
        console.error(`状态观察者错误 [${key as string}]:`, error);
      }
    });
  }
  
  // 广播状态变化
  private broadcastStateChange(key: keyof GlobalStateData, newValue: any, oldValue: any) {
    const event = new CustomEvent('global-state-change', {
      detail: {
        key,
        newValue,
        oldValue,
        timestamp: Date.now()
      }
    });
    
    window.dispatchEvent(event);
  }
  
  // 广播批量变化
  private broadcastBatchChanges(changes: any[]) {
    const event = new CustomEvent('global-state-batch-change', {
      detail: {
        changes,
        timestamp: Date.now()
      }
    });
    
    window.dispatchEvent(event);
  }
}

// 全局状态实例
export const globalState = new CrossFrameworkGlobalState();
```

## 🔧 开发环境配置

### 1. Docker Compose 配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  shell-app:
    build:
      context: ./shell-app
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./shell-app:/app
      - /app/node_modules
    environment:
      - VITE_API_BASE_URL=http://localhost:8080/api
    depends_on:
      - api-server

  user-center:
    build:
      context: ./user-center
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3001"
    volumes:
      - ./user-center:/app
      - /app/node_modules
    environment:
      - VITE_API_BASE_URL=http://localhost:8080/api

  product-catalog:
    build:
      context: ./product-catalog
      dockerfile: Dockerfile.dev
    ports:
      - "3002:3002"
    volumes:
      - ./product-catalog:/app
      - /app/node_modules

  order-management:
    build:
      context: ./order-management
      dockerfile: Dockerfile.dev
    ports:
      - "3003:3003"
    volumes:
      - ./order-management:/app
      - /app/node_modules

  data-analytics:
    build:
      context: ./data-analytics
      dockerfile: Dockerfile.dev
    ports:
      - "3004:3004"
    volumes:
      - ./data-analytics:/app
      - /app/node_modules

  notification-center:
    build:
      context: ./notification-center
      dockerfile: Dockerfile.dev
    ports:
      - "3005:3005"
    volumes:
      - ./notification-center:/app
      - /app/node_modules

  api-server:
    image: node:18-alpine
    working_dir: /app
    command: npm run dev
    ports:
      - "8080:8080"
    volumes:
      - ./api-server:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=8080

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - shell-app
      - user-center
      - product-catalog
      - order-management
      - data-analytics
      - notification-center
```

### 2. 启动脚本

```bash
#!/bin/bash
# scripts/dev.sh

echo "🚀 启动多框架微前端开发环境..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 构建和启动所有服务
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

# 显示访问地址
echo ""
echo "🎉 开发环境启动完成！"
echo ""
echo "📱 访问地址："
echo "  主应用:     http://localhost:3000"
echo "  用户中心:   http://localhost:3001"
echo "  商品目录:   http://localhost:3002"
echo "  订单管理:   http://localhost:3003"
echo "  数据分析:   http://localhost:3004"
echo "  通知中心:   http://localhost:3005"
echo "  API 服务:   http://localhost:8080"
echo ""
echo "🔧 管理命令："
echo "  查看日志:   docker-compose logs -f [service-name]"
echo "  停止服务:   docker-compose down"
echo "  重启服务:   docker-compose restart [service-name]"
```

## 🎯 示例特性

- ✅ **多框架支持** - React、Vue、Angular、Svelte、原生 JS
- ✅ **统一状态管理** - 跨框架全局状态共享
- ✅ **事件通信** - 框架无关的事件总线
- ✅ **路由协调** - 统一的路由管理
- ✅ **样式隔离** - 各框架样式互不干扰
- ✅ **开发环境** - Docker 容器化开发
- ✅ **错误隔离** - 单个应用错误不影响整体
- ✅ **性能优化** - 懒加载和资源共享

这个多框架集成示例展示了 Micro-Core 强大的技术栈无关能力，为企业级微前端架构提供了完整的解决方案。
