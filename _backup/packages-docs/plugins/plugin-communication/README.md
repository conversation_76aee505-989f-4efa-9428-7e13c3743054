# @micro-core/plugin-communication

微前端应用间通信插件，提供事件总线、全局状态管理和消息通道等通信能力。

## 功能特性

- 🚌 **事件总线**: 基于发布-订阅模式的事件通信系统
- 🌐 **全局状态管理**: 跨应用的状态共享和同步
- 📡 **消息通道**: 支持点对点和广播消息传递
- 🔄 **实时同步**: 状态变更的实时同步机制
- 🛡️ **类型安全**: 完整的 TypeScript 类型支持
- 📊 **调试支持**: 内置调试和监控功能
- ⚡ **高性能**: 优化的事件处理和状态管理
- 🔌 **插件化**: 支持自定义通信协议扩展

## 安装

```bash
pnpm add @micro-core/plugin-communication
```

## 基础用法

### 插件注册

```typescript
import { MicroCoreKernel } from '@micro-core/core';
import { CommunicationPlugin } from '@micro-core/plugin-communication';

const kernel = new MicroCoreKernel();

// 注册通信插件
await kernel.installPlugin(new CommunicationPlugin({
  enableEventBus: true,
  enableGlobalState: true,
  enableMessageChannel: true,
  enableDebug: false,
  maxListeners: 100,
  stateStorageKey: 'micro-core-state'
}));
```

### 事件总线

```typescript
// 订阅事件
communicationPlugin.on('user:login', (user) => {
  console.log('用户登录:', user);
});

// 发布事件
communicationPlugin.emit('user:login', {
  id: '123',
  name: 'John Doe',
  email: '<EMAIL>'
});

// 一次性订阅
communicationPlugin.once('app:ready', () => {
  console.log('应用已就绪');
});

// 取消订阅
const unsubscribe = communicationPlugin.on('data:update', handleDataUpdate);
unsubscribe(); // 取消订阅
```

### 全局状态管理

```typescript
// 设置全局状态
communicationPlugin.setState('user', {
  id: '123',
  name: 'John Doe',
  isLoggedIn: true
});

// 获取全局状态
const user = communicationPlugin.getState('user');
console.log('当前用户:', user);

// 监听状态变化
communicationPlugin.onStateChange('user', (newUser, oldUser) => {
  console.log('用户状态变化:', { newUser, oldUser });
});

// 批量更新状态
communicationPlugin.batchUpdateState({
  'user.profile': { avatar: 'new-avatar.jpg' },
  'app.theme': 'dark',
  'settings.language': 'zh-CN'
});
```

### 消息通道

```typescript
// 创建消息通道
const channel = communicationPlugin.createChannel('app-communication');

// 发送消息到指定应用
channel.sendTo('app1', 'hello', { message: 'Hello from App2' });

// 广播消息到所有应用
channel.broadcast('notification', {
  type: 'info',
  message: '系统维护通知'
});

// 监听消息
channel.onMessage('hello', (data, from) => {
  console.log(`收到来自 ${from} 的消息:`, data);
});
```

## 配置选项

```typescript
interface CommunicationConfig {
  // 功能开关
  enableEventBus?: boolean;             // 启用事件总线
  enableGlobalState?: boolean;          // 启用全局状态
  enableMessageChannel?: boolean;       // 启用消息通道
  enableDebug?: boolean;                // 启用调试模式
  
  // 事件总线配置
  maxListeners?: number;                // 最大监听器数量
  enableWildcard?: boolean;             // 启用通配符事件
  
  // 状态管理配置
  stateStorageKey?: string;             // 状态存储键名
  enableStatePersistence?: boolean;     // 启用状态持久化
  stateSerializer?: StateSerializer;    // 状态序列化器
  
  // 消息通道配置
  channelTimeout?: number;              // 消息超时时间
  enableMessageQueue?: boolean;         // 启用消息队列
  maxMessageSize?: number;              // 最大消息大小
  
  // 性能配置
  enableBatching?: boolean;             // 启用批处理
  batchSize?: number;                   // 批处理大小
  batchTimeout?: number;                // 批处理超时
}
```

## API 参考

### 事件总线 API

#### `on(event: string, listener: Function): UnsubscribeFunction`
订阅事件

#### `once(event: string, listener: Function): UnsubscribeFunction`
一次性订阅事件

#### `emit(event: string, ...args: any[]): boolean`
发布事件

#### `off(event: string, listener?: Function): void`
取消订阅

#### `removeAllListeners(event?: string): void`
移除所有监听器

### 全局状态 API

#### `setState(key: string, value: any): void`
设置状态

#### `getState(key?: string): any`
获取状态

#### `removeState(key: string): void`
移除状态

#### `onStateChange(key: string, listener: StateChangeListener): UnsubscribeFunction`
监听状态变化

#### `batchUpdateState(updates: Record<string, any>): void`
批量更新状态

### 消息通道 API

#### `createChannel(name: string): MessageChannel`
创建消息通道

#### `getChannel(name: string): MessageChannel | null`
获取消息通道

#### `destroyChannel(name: string): void`
销毁消息通道

## 高级用法

### 自定义事件命名空间

```typescript
// 使用命名空间组织事件
communicationPlugin.on('user:profile:update', handleProfileUpdate);
communicationPlugin.on('user:settings:change', handleSettingsChange);
communicationPlugin.on('app:*', handleAllAppEvents); // 通配符事件

// 发布命名空间事件
communicationPlugin.emit('user:profile:update', newProfile);
```

### 状态订阅器

```typescript
// 创建状态订阅器
const userSubscriber = communicationPlugin.createStateSubscriber('user');

userSubscriber.subscribe((state) => {
  console.log('用户状态更新:', state);
});

// 深度监听嵌套状态
userSubscriber.subscribeDeep('profile.avatar', (avatar) => {
  console.log('头像更新:', avatar);
});
```

### 消息中间件

```typescript
// 添加消息中间件
channel.use((message, next) => {
  // 消息预处理
  console.log('发送消息:', message);
  
  // 添加时间戳
  message.timestamp = Date.now();
  
  // 继续处理
  next();
});

// 错误处理中间件
channel.use((message, next) => {
  try {
    next();
  } catch (error) {
    console.error('消息处理错误:', error);
  }
});
```

### 跨应用状态同步

```typescript
// 应用 A
communicationPlugin.setState('shared:config', {
  theme: 'dark',
  language: 'zh-CN'
});

// 应用 B 自动同步
communicationPlugin.onStateChange('shared:config', (config) => {
  // 应用配置更新
  updateAppConfig(config);
});
```

## 性能优化

### 1. 事件批处理

```typescript
const plugin = new CommunicationPlugin({
  enableBatching: true,
  batchSize: 10,
  batchTimeout: 16 // 16ms，约等于 60fps
});

// 批量发送事件
for (let i = 0; i < 100; i++) {
  plugin.emit('data:update', { index: i });
}
// 事件会被批处理，减少性能开销
```

### 2. 内存管理

```typescript
// 自动清理未使用的监听器
plugin.enableAutoCleanup({
  maxIdleTime: 300000, // 5分钟
  checkInterval: 60000  // 1分钟检查一次
});

// 手动清理
plugin.cleanup();
```

### 3. 状态优化

```typescript
// 使用浅比较优化状态更新
plugin.setState('list', newList, { shallow: true });

// 使用不可变数据结构
import { produce } from 'immer';

plugin.setState('user', produce(currentUser, draft => {
  draft.profile.name = 'New Name';
}));
```

## 调试和监控

### 1. 调试模式

```typescript
const plugin = new CommunicationPlugin({
  enableDebug: true
});

// 监听所有事件
plugin.onAny((event, ...args) => {
  console.log(`事件: ${event}`, args);
});

// 监听状态变化
plugin.onAnyStateChange((key, newValue, oldValue) => {
  console.log(`状态变化: ${key}`, { newValue, oldValue });
});
```

### 2. 性能监控

```typescript
// 获取性能指标
const metrics = plugin.getMetrics();
console.log('事件统计:', metrics.events);
console.log('状态统计:', metrics.state);
console.log('消息统计:', metrics.messages);

// 监听性能警告
plugin.on('performance:warning', (warning) => {
  console.warn('性能警告:', warning);
});
```

### 3. 事件追踪

```typescript
// 启用事件追踪
plugin.enableEventTracing();

// 获取事件历史
const history = plugin.getEventHistory();
console.log('事件历史:', history);

// 导出调试信息
const debugInfo = plugin.exportDebugInfo();
console.log('调试信息:', debugInfo);
```

## 类型定义

```typescript
interface EventListener<T = any> {
  (data: T): void;
}

interface StateChangeListener<T = any> {
  (newValue: T, oldValue: T): void;
}

interface MessageHandler<T = any> {
  (data: T, from: string): void;
}

interface MessageChannel {
  sendTo(target: string, type: string, data: any): void;
  broadcast(type: string, data: any): void;
  onMessage<T>(type: string, handler: MessageHandler<T>): UnsubscribeFunction;
}

type UnsubscribeFunction = () => void;
```

## 最佳实践

### 1. 事件命名规范

```typescript
// 推荐的事件命名规范
// 格式: 模块:操作:对象
plugin.emit('user:login:success', user);
plugin.emit('data:fetch:error', error);
plugin.emit('ui:modal:open', modalConfig);
```

### 2. 状态结构设计

```typescript
// 推荐的状态结构
const stateStructure = {
  user: {
    profile: { id, name, email },
    preferences: { theme, language },
    permissions: []
  },
  app: {
    config: { version, features },
    ui: { loading, error }
  },
  shared: {
    cache: {},
    temp: {}
  }
};
```

### 3. 错误处理

```typescript
// 事件错误处理
plugin.on('error', (error, event) => {
  console.error(`事件 ${event} 处理错误:`, error);
  // 发送错误报告
  errorReporter.report(error);
});

// 状态错误处理
plugin.onStateError((error, key, value) => {
  console.error(`状态 ${key} 设置错误:`, error);
  // 回滚状态
  plugin.rollbackState(key);
});
```

## 故障排除

### 常见问题

**Q: 事件监听器内存泄漏**
A: 确保在组件卸载时调用 `unsubscribe()` 函数取消订阅。

**Q: 状态更新不触发监听器**
A: 检查状态键名是否正确，确保使用了正确的监听方法。

**Q: 跨应用通信失败**
A: 检查消息通道配置，确保目标应用已正确注册。

### 调试技巧

```typescript
// 启用详细日志
plugin.setLogLevel('verbose');

// 检查监听器状态
console.log('当前监听器:', plugin.getListeners());

// 检查状态快照
console.log('状态快照:', plugin.getStateSnapshot());
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个插件。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持事件总线通信
- 支持全局状态管理
- 支持消息通道
- 支持调试和监控功能
