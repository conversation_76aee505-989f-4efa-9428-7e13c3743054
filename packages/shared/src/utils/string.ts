/**
 * @fileoverview 字符串处理工具
 * @description 提供各种字符串操作和转换功能
 * <AUTHOR> <<EMAIL>>
 */

/**
 * 首字母大写
 */
export function capitalize(str: string): string {
    if (!str) return str
    return str.charAt(0).toUpperCase() + str.slice(1)
}

/**
 * 转换为驼峰命名
 */
export function camelCase(str: string): string {
    return str.replace(/-([a-z])/g, (_, letter: string) => letter.toUpperCase())
}

/**
 * 转换为短横线命名
 */
export function kebabCase(str: string): string {
    return str.replace(/[A-Z]/g, (letter: string) => `-${letter.toLowerCase()}`)
}

/**
 * 转换为下划线命名
 */
export function snakeCase(str: string): string {
    return str.replace(/[A-Z]/g, (letter: string) => `_${letter.toLowerCase()}`)
}

/**
 * 转换为帕斯卡命名
 */
export function pascalCase(str: string): string {
    return capitalize(camelCase(str))
}

/**
 * 生成唯一ID
 */
export function generateId(prefix = '', length = 8): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = prefix ? `${prefix}-` : ''

    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
    }

    return result
}

/**
 * 格式化字节数
 */
export function formatBytes(bytes: number, decimals = 2): string {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

    const i = Math.floor(Math.log(bytes) / Math.log(k))
    const value = bytes / Math.pow(k, i)

    // 如果是整数，不显示小数点
    if (value % 1 === 0) {
        return value + ' ' + sizes[i]
    }

    return value.toFixed(dm) + ' ' + sizes[i]
}

/**
 * 截断字符串
 */
export function truncate(str: string, length: number, suffix = '...'): string {
    if (str.length <= length) return str
    return str.slice(0, length) + suffix
}

/**
 * 移除字符串两端空白
 */
export function trim(str: string): string {
    return str.trim()
}

/**
 * 移除字符串左端空白
 */
export function trimStart(str: string): string {
    return str.trimStart()
}

/**
 * 移除字符串右端空白
 */
export function trimEnd(str: string): string {
    return str.trimEnd()
}

/**
 * 填充字符串左侧
 */
export function padStart(str: string, length: number, fillString = ' '): string {
    return str.padStart(length, fillString)
}

/**
 * 填充字符串右侧
 */
export function padEnd(str: string, length: number, fillString = ' '): string {
    return str.padEnd(length, fillString)
}

/**
 * 重复字符串
 */
export function repeat(str: string, count: number): string {
    return str.repeat(count)
}

/**
 * 反转字符串
 */
export function reverse(str: string): string {
    return str.split('').reverse().join('')
}

/**
 * 检查字符串是否以指定前缀开始
 */
export function startsWith(str: string, searchString: string, position = 0): boolean {
    return str.startsWith(searchString, position)
}

/**
 * 检查字符串是否以指定后缀结束
 */
export function endsWith(str: string, searchString: string, length?: number): boolean {
    return str.endsWith(searchString, length)
}

/**
 * 检查字符串是否包含指定子串
 */
export function includes(str: string, searchString: string, position = 0): boolean {
    return str.includes(searchString, position)
}

/**
 * 替换字符串中的所有匹配项
 */
export function replaceAll(str: string, searchValue: string | RegExp, replaceValue: string): string {
    if (typeof searchValue === 'string') {
        return str.split(searchValue).join(replaceValue)
    }
    return str.replace(searchValue, replaceValue)
}

/**
 * 分割字符串为数组
 */
export function split(str: string, separator: string | RegExp, limit?: number): string[] {
    return str.split(separator, limit)
}

/**
 * 转换为小写
 */
export function toLowerCase(str: string): string {
    return str.toLowerCase()
}

/**
 * 转换为大写
 */
export function toUpperCase(str: string): string {
    return str.toUpperCase()
}

/**
 * 获取字符串长度
 */
export function length(str: string): number {
    return str.length
}

/**
 * 检查字符串是否为空
 */
export function isEmpty(str: string): boolean {
    return str.length === 0
}

/**
 * 检查字符串是否为空白
 */
export function isBlank(str: string): boolean {
    return str.trim().length === 0
}

/**
 * 转义HTML字符
 */
export function escapeHtml(str: string): string {
    const htmlEscapes: Record<string, string> = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;'
    }

    return str.replace(/[&<>"']/g, (match) => htmlEscapes[match])
}

/**
 * 反转义HTML字符
 */
export function unescapeHtml(str: string): string {
    const htmlUnescapes: Record<string, string> = {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#39;': "'"
    }

    return str.replace(/&(?:amp|lt|gt|quot|#39);/g, (match) => htmlUnescapes[match])
}

/**
 * 生成随机字符串
 */
export function randomString(length = 8, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string {
    let result = ''
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
}

/**
 * 计算字符串的哈希值
 */
export function hash(str: string): number {
    let hash = 0
    if (str.length === 0) return hash

    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i)
        hash = ((hash << 5) - hash) + char
        hash = hash & hash // 转换为32位整数
    }

    return hash
}

/**
 * 模板字符串插值
 */
export function template(str: string, data: Record<string, any>): string {
    return str.replace(/\{\{(\w+)\}\}/g, (match, key) => {
        return data[key] !== undefined ? String(data[key]) : match
    })
}