/**
 * 内核测试用例
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { EventBus } from '../src/communication/event-bus';
import { MicroCoreKernel } from '../src/runtime/kernel';
import { PluginSystem } from '../src/runtime/plugin-system';

// 模拟依赖
vi.mock('../src/communication/event-bus');
vi.mock('../src/runtime/plugin-system');

describe('MicroCoreKernel', () => {
    let kernel: MicroCoreKernel;
    let mockEventBus: EventBus;
    let mockPluginSystem: PluginSystem;

    beforeEach(() => {
        // 创建模拟对象
        mockEventBus = {
            on: vi.fn(),
            off: vi.fn(),
            emit: vi.fn(),
            once: vi.fn(),
            clear: vi.fn()
        } as unknown as EventBus;

        mockPluginSystem = {
            install: vi.fn(),
            uninstall: vi.fn(),
            has: vi.fn(),
            get: vi.fn(),
            getAll: vi.fn(),
            destroy: vi.fn()
        } as unknown as PluginSystem;

        // 重置模拟
        vi.resetAllMocks();

        // 创建内核实例
        kernel = new MicroCoreKernel({
            eventBus: mockEventBus
        });
    });

    describe('内核初始化', () => {
        it('应该正确初始化内核', () => {
            expect(kernel).toBeDefined();
            expect(kernel.getEventBus()).toBe(mockEventBus);
        });

        it('应该在没有提供依赖时创建默认实例', () => {
            const defaultKernel = new MicroCoreKernel();
            expect(defaultKernel.getEventBus()).toBeInstanceOf(EventBus);
        });
    });

    describe('应用程序注册', () => {
        it('应该能够注册应用', () => {
            const appConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app'
            };

            kernel.registerApplication(appConfig);

            const app = kernel.getApplication('test-app');
            expect(app).toBeDefined();
            expect(app?.name).toBe('test-app');
        });

        it('应该能够注销应用', () => {
            const appConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app'
            };

            kernel.registerApplication(appConfig);
            kernel.unregisterApplication('test-app');

            const app = kernel.getApplication('test-app');
            expect(app).toBeNull();
        });

        it('应该能够获取所有应用', () => {
            const appConfig1 = {
                name: 'test-app-1',
                entry: 'http://localhost:3001',
                container: '#app1'
            };

            const appConfig2 = {
                name: 'test-app-2',
                entry: 'http://localhost:3002',
                container: '#app2'
            };

            kernel.registerApplication(appConfig1);
            kernel.registerApplication(appConfig2);

            const apps = kernel.getApplications();
            expect(apps).toHaveLength(2);
            expect(apps[0].name).toBe('test-app-1');
            expect(apps[1].name).toBe('test-app-2');
        });
    });

    describe('内核生命周期', () => {
        it('应该能够启动内核', async () => {
            await kernel.start();

            expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:starting');
            expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:started');
            expect(kernel.isKernelStarted()).toBe(true);
        });

        it('应该能够停止内核', async () => {
            await kernel.start();
            await kernel.stop();

            expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:stopping');
            expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:stopped');
            expect(kernel.isKernelStarted()).toBe(false);
        });

        it('应该能够销毁内核', async () => {
            await kernel.destroy();

            expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:destroying');
            expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:destroyed');
            expect(mockEventBus.clear).toHaveBeenCalled();
            expect(kernel.isKernelDestroyed()).toBe(true);
        });
    });

    describe('插件管理', () => {
        it('应该能够安装插件', () => {
            const mockPlugin = {
                name: 'test-plugin',
                install: vi.fn()
            };
            const options = { option1: 'value1' };

            kernel.use(mockPlugin, options);

            expect(mockPlugin.install).toHaveBeenCalledWith(kernel, options);
            expect(kernel.hasPlugin('test-plugin')).toBe(true);
        });

        it('应该能够获取插件', () => {
            const mockPlugin = {
                name: 'test-plugin',
                install: vi.fn()
            };

            kernel.use(mockPlugin);

            const plugin = kernel.getPlugin('test-plugin');
            expect(plugin).toBe(mockPlugin);
        });

        it('应该能够获取所有插件名称', () => {
            const mockPlugin1 = {
                name: 'test-plugin-1',
                install: vi.fn()
            };

            const mockPlugin2 = {
                name: 'test-plugin-2',
                install: vi.fn()
            };

            kernel.use(mockPlugin1);
            kernel.use(mockPlugin2);

            const plugins = kernel.getPlugins();
            expect(plugins).toContain('test-plugin-1');
            expect(plugins).toContain('test-plugin-2');
        });
    });

    describe('应用生命周期管理', () => {
        it('应该能够加载应用', async () => {
            const appConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app'
            };

            kernel.registerApplication(appConfig);
            await kernel.loadApplication('test-app');

            expect(mockEventBus.emit).toHaveBeenCalled();
        });

        it('应该能够挂载应用', async () => {
            const appConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app'
            };

            kernel.registerApplication(appConfig);
            await kernel.mountApplication('test-app');

            expect(mockEventBus.emit).toHaveBeenCalled();
        });

        it('应该能够卸载应用', async () => {
            const appConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app'
            };

            kernel.registerApplication(appConfig);
            await kernel.mountApplication('test-app');
            await kernel.unmountApplication('test-app');

            expect(mockEventBus.emit).toHaveBeenCalled();
        });
    });
});