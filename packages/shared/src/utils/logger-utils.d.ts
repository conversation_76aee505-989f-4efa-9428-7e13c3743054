/**
 * 共享工具包 - 日志工具函数
 *
 * @description 提供统一的日志记录功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import type { LogLevel } from '../constants';
/**
 * 日志记录器类
 */
export declare class Logger {
    private level;
    private prefix;
    private enableTimestamp;
    private enableColor;
    constructor(prefix?: string, level?: LogLevel, options?: {
        enableTimestamp?: boolean;
        enableColor?: boolean;
    });
    /**
     * 设置日志级别
     */
    setLevel(level: LogLevel): void;
    /**
     * 获取当前日志级别
     */
    getLevel(): LogLevel;
    /**
     * 设置日志前缀
     */
    setPrefix(prefix: string): void;
    /**
     * 格式化日志消息
     */
    private formatMessage;
    /**
     * 获取颜色样式
     */
    private getColorStyle;
    /**
     * 错误日志
     */
    error(message: string, ...args: any[]): void;
    /**
     * 警告日志
     */
    warn(message: string, ...args: any[]): void;
    /**
     * 信息日志
     */
    info(message: string, ...args: any[]): void;
    /**
     * 调试日志
     */
    debug(message: string, ...args: any[]): void;
    /**
     * 跟踪日志
     */
    trace(message: string, ...args: any[]): void;
    /**
     * 分组开始
     */
    group(label: string): void;
    /**
     * 分组结束
     */
    groupEnd(): void;
    /**
     * 表格输出
     */
    table(data: any): void;
    /**
     * 计时开始
     */
    time(label: string): void;
    /**
     * 计时结束
     */
    timeEnd(label: string): void;
    /**
     * 性能标记
     */
    mark(name: string): void;
    /**
     * 性能测量
     */
    measure(name: string, startMark: string, endMark?: string): void;
}
/**
 * 创建日志记录器
 */
export declare function createLogger(prefix?: string, level?: LogLevel, options?: {
    enableTimestamp?: boolean;
    enableColor?: boolean;
}): Logger;
/**
 * 全局日志记录器实例
 */
export declare const globalLogger: Logger;
/**
 * 日志装饰器
 */
export declare function logMethod(level?: LogLevel): (target: any, propertyName: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
/**
 * 异步日志装饰器
 */
export declare function logAsyncMethod(level?: LogLevel): (target: any, propertyName: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
//# sourceMappingURL=logger-utils.d.ts.map