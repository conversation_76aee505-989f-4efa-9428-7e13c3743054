{"version": 3, "file": "error-handler.d.ts", "sourceRoot": "", "sources": ["error-handler.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAIH;;GAEG;AACH,oBAAY,SAAS;IACjB,cAAc,mBAAmB;IACjC,aAAa,kBAAkB;IAC/B,OAAO,YAAY;IACnB,aAAa,kBAAkB;IAC/B,QAAQ,aAAa;IACrB,WAAW,gBAAgB;IAC3B,OAAO,YAAY;CACtB;AAED;;GAEG;AACH,oBAAY,aAAa;IACrB,GAAG,QAAQ;IACX,MAAM,WAAW;IACjB,IAAI,SAAS;IACb,QAAQ,aAAa;CACxB;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IACzB,cAAc;IACd,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,cAAc;IACd,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,aAAa;IACb,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,UAAU;IACV,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,eAAe;IACf,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC9B;AAED;;GAEG;AACH,MAAM,WAAW,qBAAqB;IAClC,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,aAAa;IACb,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,YAAY,KAAK,OAAO,CAAC;IAC9D,WAAW;IACX,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,YAAY,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;IACpE,YAAY;IACZ,QAAQ,EAAE,MAAM,CAAC;CACpB;AAED;;GAEG;AACH,qBAAa,cAAe,SAAQ,KAAK;IACrC,SAAgB,IAAI,EAAE,MAAM,CAAC;IAC7B,SAAgB,IAAI,EAAE,SAAS,CAAC;IAChC,SAAgB,QAAQ,EAAE,aAAa,CAAC;IACxC,SAAgB,OAAO,EAAE,YAAY,CAAC;IACtC,SAAgB,SAAS,EAAE,MAAM,CAAC;IAClC,SAAgB,WAAW,EAAE,OAAO,CAAC;gBAGjC,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE,MAAM,EACf,IAAI,GAAE,SAA6B,EACnC,QAAQ,GAAE,aAAoC,EAC9C,OAAO,GAAE,YAAiB,EAC1B,WAAW,GAAE,OAAe;IAqBhC;;OAEG;IACH,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAc7B;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,cAAc;IAWtE;;OAEG;IACH,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,cAAc;IAWnE;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,cAAc;IAWvE;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,cAAc;IAW9E;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,cAAc;IAW7E;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,cAAc;IAWvE;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,cAAc;IAW7E;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,cAAc;IAWxE;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,cAAc;CAU9E;AAED;;GAEG;AACH,qBAAa,YAAY;IACrB,OAAO,CAAC,UAAU,CAA+B;IACjD,OAAO,CAAC,YAAY,CAAwB;IAC5C,OAAO,CAAC,cAAc,CAAO;IAC7B,OAAO,CAAC,OAAO,CAAC,CAAkC;IAClD,OAAO,CAAC,UAAU,CAAC,CAAoD;;IAOvE;;OAEG;IACH,gBAAgB,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,cAAc,KAAK,IAAI,GAAG,IAAI;IAIjE;;OAEG;IACH,mBAAmB,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,KAAK,IAAI,GAAG,IAAI;IAItF;;OAEG;IACH,gBAAgB,CAAC,QAAQ,EAAE,qBAAqB,GAAG,IAAI;IAKvD;;OAEG;IACG,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,cAAc,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC;IAmC1F;;OAEG;YACW,eAAe;IAuB7B;;OAEG;IACH,OAAO,CAAC,WAAW;IASnB;;OAEG;IACH,eAAe,IAAI,cAAc,EAAE;IAInC;;OAEG;IACH,aAAa,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAoBpC;;OAEG;IACH,YAAY,IAAI,IAAI;IAIpB;;OAEG;IACH,OAAO,CAAC,sBAAsB;IA6D9B;;OAEG;IACH,OAAO,CAAC,wBAAwB;CAyCnC;AAED;;GAEG;AACH,eAAO,MAAM,kBAAkB,cAAqB,CAAC;AAErD;;GAEG;AACH,wBAAgB,kBAAkB,IAAI,YAAY,CAEjD;AAED;;GAEG;AACH,wBAAgB,YAAY,CACxB,SAAS,GAAE,SAA6B,EACxC,QAAQ,GAAE,aAAoC,IAE7B,QAAQ,GAAG,EAAE,cAAc,MAAM,EAAE,YAAY,kBAAkB,wBAkDrF;AAED;;GAEG;AACH,wBAAgB,oBAAoB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,KAAK,CAKxF;AAED,eAAe,YAAY,CAAC"}