/* React 子应用样式 */
.react-app {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    color: #333;
    background-color: #f8f9fa;
    min-height: 100vh;
}

.react-app.dark {
    background-color: #1a1a1a;
    color: #e0e0e0;
}

/* 应用头部 */
.app-header {
    background: white;
    padding: 1rem 2rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .app-header {
    background: #2d2d2d;
    border-bottom-color: #404040;
}

.app-header h2 {
    margin: 0;
    color: #2c3e50;
}

.dark .app-header h2 {
    color: #e0e0e0;
}

.app-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.send-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.send-btn:hover {
    background: #0056b3;
}

/* 导航栏 */
.app-nav {
    background: white;
    padding: 0 2rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    gap: 0;
}

.dark .app-nav {
    background: #2d2d2d;
    border-bottom-color: #404040;
}

.nav-link {
    display: block;
    padding: 1rem 1.5rem;
    color: #666;
    text-decoration: none;
    border-bottom: 3px solid transparent;
    transition: all 0.3s;
}

.nav-link:hover,
.nav-link.active {
    color: #007bff;
    border-bottom-color: #007bff;
}

.dark .nav-link {
    color: #ccc;
}

.dark .nav-link:hover,
.dark .nav-link.active {
    color: #4dabf7;
    border-bottom-color: #4dabf7;
}

/* 消息显示 */
.message-display {
    background: #d4edda;
    color: #155724;
    padding: 1rem 2rem;
    border-left: 4px solid #28a745;
    margin: 1rem 2rem;
    border-radius: 4px;
}

.dark .message-display {
    background: #1e3a2e;
    color: #a3d9a5;
    border-left-color: #28a745;
}

/* 应用内容 */
.app-content {
    padding: 2rem;
}

/* 仪表板样式 */
.dashboard h3 {
    margin-bottom: 2rem;
    color: #2c3e50;
}

.dark .dashboard h3 {
    color: #e0e0e0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.dark .stat-card {
    background: #2d2d2d;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.stat-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 50%;
}

.dark .stat-icon {
    background: #404040;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    color: #2c3e50;
}

.dark .stat-number {
    color: #e0e0e0;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

.dark .stat-label {
    color: #ccc;
}

/* 图表样式 */
.chart-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.dark .chart-section {
    background: #2d2d2d;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.chart-section h4 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.dark .chart-section h4 {
    color: #e0e0e0;
}

.simple-chart {
    display: flex;
    align-items: end;
    gap: 4px;
    height: 200px;
    padding: 1rem 0;
}

.chart-bar {
    flex: 1;
    background: linear-gradient(to top, #007bff, #4dabf7);
    border-radius: 2px 2px 0 0;
    min-height: 10px;
    transition: all 0.3s;
    cursor: pointer;
}

.chart-bar:hover {
    opacity: 0.8;
}

/* 活动列表 */
.recent-activity {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dark .recent-activity {
    background: #2d2d2d;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.recent-activity h4 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.dark .recent-activity h4 {
    color: #e0e0e0;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.activity-item {
    display: flex;
    gap: 1rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.dark .activity-item {
    background: #404040;
}

.activity-time {
    color: #666;
    font-size: 0.85rem;
    white-space: nowrap;
}

.dark .activity-time {
    color: #ccc;
}

.activity-text {
    color: #333;
}

.dark .activity-text {
    color: #e0e0e0;
}

/* 用户管理样式 */
.user-management {
    max-width: 1200px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.page-header h3 {
    margin: 0;
    color: #2c3e50;
}

.dark .page-header h3 {
    color: #e0e0e0;
}

.filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.search-input,
.role-select {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.dark .search-input,
.dark .role-select {
    background: #404040;
    border-color: #555;
    color: #e0e0e0;
}

.search-input {
    min-width: 250px;
}

/* 表格样式 */
.user-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.dark .user-table {
    background: #2d2d2d;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.user-table table {
    width: 100%;
    border-collapse: collapse;
}

.user-table th,
.user-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.dark .user-table th,
.dark .user-table td {
    border-bottom-color: #404040;
}

.user-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.dark .user-table th {
    background: #404040;
    color: #e0e0e0;
}

.role-badge,
.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.role-admin {
    background: #ffeaa7;
    color: #d63031;
}

.role-editor {
    background: #a7f3d0;
    color: #047857;
}

.role-user {
    background: #bfdbfe;
    color: #1d4ed8;
}

.status-active {
    background: #d1fae5;
    color: #047857;
}

.status-inactive {
    background: #fee2e2;
    color: #dc2626;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* 加载状态 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.no-data {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.dark .no-data {
    color: #ccc;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .app-nav {
        padding: 0 1rem;
    }

    .app-content {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .filters {
        flex-direction: column;
    }

    .search-input {
        min-width: auto;
        width: 100%;
    }

    .user-table {
        overflow-x: auto;
    }

    .page-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
}