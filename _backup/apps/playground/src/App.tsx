/**
 * @fileoverview Playground 主应用组件
 * <AUTHOR> <<EMAIL>>
 */

import {
    ApiOutlined,
    AppstoreOutlined,
    BugOutlined,
    CloudOutlined,
    DatabaseOutlined,
    MonitorOutlined,
    RocketOutlined,
    SettingOutlined
} from '@ant-design/icons';
import { Button, Card, Col, Divider, Layout, Menu, Row, Space, Tag, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

import DebugPanel from './components/DebugPanel';
import { getPredefinedApps, getSidecar } from './core/micro-core-setup';

const { Header, Content, Sider } = Layout;
const { Title, Paragraph, Text } = Typography;

/**
 * 主应用组件
 */
const App: React.FC = () => {
    const [collapsed, setCollapsed] = useState(false);
    const [selectedKey, setSelectedKey] = useState('overview');
    const [microApps, setMicroApps] = useState<any[]>([]);
    const [sidecarStatus, setSidecarStatus] = useState<'loading' | 'ready' | 'error'>('loading');

    // 刷新数据
    const refreshData = async () => {
        try {
            const sidecar = getSidecar();
            const predefinedApps = getPredefinedApps();

            if (sidecar) {
                // 获取已注册的应用
                const apps = await sidecar.getApps?.() || predefinedApps;
                setMicroApps(apps);
                setSidecarStatus('ready');
            } else {
                // 使用预定义应用作为备选
                setMicroApps(predefinedApps);
                setSidecarStatus('ready');
            }
        } catch (error) {
            console.error('刷新应用数据失败:', error);
            setSidecarStatus('error');
        }
    };

    // 获取框架颜色
    const getFrameworkColor = (framework: string) => {
        const colors: Record<string, string> = {
            'react': 'cyan',
            'vue': 'green',
            'angular': 'red',
            'vanilla': 'orange',
            'svelte': 'purple',
            'solid': 'blue'
        };
        return colors[framework] || 'default';
    };

    // 处理应用操作
    const handleAppAction = async (action: string, appName: string) => {
        try {
            const sidecar = getSidecar();
            if (!sidecar) {
                console.warn('边车实例未初始化');
                return;
            }

            switch (action) {
                case 'start':
                    await sidecar.startApp?.(appName);
                    console.log(`应用 ${appName} 启动成功`);
                    break;
                case 'stop':
                    await sidecar.stopApp?.(appName);
                    console.log(`应用 ${appName} 停止成功`);
                    break;
                case 'debug':
                    setSelectedKey('debug');
                    console.log(`打开应用 ${appName} 的调试面板`);
                    break;
                default:
                    console.warn(`未知操作: ${action}`);
            }

            // 刷新应用状态
            await refreshData();
        } catch (error) {
            console.error(`执行应用操作失败: ${action} - ${appName}`, error);
        }
    };

    // 初始化微前端应用状态
    useEffect(() => {
        refreshData();
    }, []);

    // 菜单项配置
    const menuItems = [
        {
            key: 'overview',
            icon: <AppstoreOutlined />,
            label: '概览'
        },
        {
            key: 'apps',
            icon: <RocketOutlined />,
            label: '微前端应用'
        },
        {
            key: 'plugins',
            icon: <ApiOutlined />,
            label: '插件系统'
        },
        {
            key: 'adapters',
            icon: <DatabaseOutlined />,
            label: '适配器'
        },
        {
            key: 'builders',
            icon: <CloudOutlined />,
            label: '构建工具'
        },
        {
            key: 'sidecar',
            icon: <MonitorOutlined />,
            label: '边车模式'
        },
        {
            key: 'debug',
            icon: <BugOutlined />,
            label: '调试工具'
        },
        {
            key: 'settings',
            icon: <SettingOutlined />,
            label: '设置'
        }
    ];

    // 渲染概览页面
    const renderOverview = () => (
        <div className="fade-in">
            <Title level={2}>🎉 欢迎使用 Micro-Core Playground</Title>
            <Paragraph>
                这是一个完整的微前端架构开发调试环境，用于测试和演示 micro-core 的各种功能。
            </Paragraph>

            <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
                <Col xs={24} sm={12} lg={8}>
                    <Card
                        title="🚀 核心运行时"
                        size="small"
                        hoverable
                        className="slide-in-up"
                    >
                        <Paragraph>
                            提供微前端应用的生命周期管理、资源加载、沙箱隔离等核心功能。
                        </Paragraph>
                        <Tag color="green">已加载</Tag>
                    </Card>
                </Col>

                <Col xs={24} sm={12} lg={8}>
                    <Card
                        title="🔌 插件系统"
                        size="small"
                        hoverable
                        className="slide-in-up"
                        style={{ animationDelay: '0.1s' }}
                    >
                        <Paragraph>
                            支持路由、通信、认证、沙箱等插件，提供可扩展的架构能力。
                        </Paragraph>
                        <Tag color="blue">4 个插件</Tag>
                    </Card>
                </Col>

                <Col xs={24} sm={12} lg={8}>
                    <Card
                        title="🔄 多框架适配"
                        size="small"
                        hoverable
                        className="slide-in-up"
                        style={{ animationDelay: '0.2s' }}
                    >
                        <Paragraph>
                            支持 React、Vue、Angular、HTML 等多种前端框架的微前端应用。
                        </Paragraph>
                        <Tag color="purple">5 个适配器</Tag>
                    </Card>
                </Col>

                <Col xs={24} sm={12} lg={8}>
                    <Card
                        title="🛠️ 构建工具集成"
                        size="small"
                        hoverable
                        className="slide-in-up"
                        style={{ animationDelay: '0.3s' }}
                    >
                        <Paragraph>
                            集成 Webpack、Vite、Rollup、ESBuild 等主流构建工具。
                        </Paragraph>
                        <Tag color="orange">4 个构建器</Tag>
                    </Card>
                </Col>

                <Col xs={24} sm={12} lg={8}>
                    <Card
                        title="📦 共享资源管理"
                        size="small"
                        hoverable
                        className="slide-in-up"
                        style={{ animationDelay: '0.4s' }}
                    >
                        <Paragraph>
                            提供依赖共享、资源缓存、版本管理等共享资源管理能力。
                        </Paragraph>
                        <Tag color="cyan">已启用</Tag>
                    </Card>
                </Col>

                <Col xs={24} sm={12} lg={8}>
                    <Card
                        title="🚗 边车模式"
                        size="small"
                        hoverable
                        className="slide-in-up"
                        style={{ animationDelay: '0.5s' }}
                    >
                        <Paragraph>
                            零配置的边车代理模式，自动发现和管理微前端应用。
                        </Paragraph>
                        <Tag color={sidecarStatus === 'ready' ? 'green' : sidecarStatus === 'error' ? 'red' : 'gold'}>
                            {sidecarStatus === 'ready' ? '运行中' : sidecarStatus === 'error' ? '错误' : '加载中'}
                        </Tag>
                    </Card>
                </Col>
            </Row>

            <Divider />

            <Title level={3}>🎯 快速开始</Title>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <Card size="small">
                    <Paragraph>
                        <Text strong>1. 查看微前端应用</Text> - 点击左侧菜单的"微前端应用"查看已注册的应用列表
                    </Paragraph>
                </Card>
                <Card size="small">
                    <Paragraph>
                        <Text strong>2. 测试插件功能</Text> - 在"插件系统"中测试路由、通信、认证等插件功能
                    </Paragraph>
                </Card>
                <Card size="small">
                    <Paragraph>
                        <Text strong>3. 调试工具</Text> - 使用"调试工具"查看应用状态、性能指标和错误日志
                    </Paragraph>
                </Card>
                <Card size="small">
                    <Paragraph>
                        <Text strong>4. 控制台调试</Text> - 在浏览器控制台中输入 <Text code>window.__MICRO_CORE__</Text> 查看调试API
                    </Paragraph>
                </Card>
            </Space>
        </div>
    );

    // 渲染微前端应用页面
    const renderApps = () => (
        <div className="fade-in">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                <div>
                    <Title level={2}>🚀 微前端应用</Title>
                    <Paragraph>
                        当前已注册 {microApps.length} 个微前端应用，支持动态加载和热重载。
                    </Paragraph>
                </div>
                <Space>
                    <Button onClick={refreshData} icon={<ApiOutlined />}>
                        刷新列表
                    </Button>
                    <Button type="primary" onClick={() => setSelectedKey('debug')}>
                        打开调试面板
                    </Button>
                </Space>
            </div>

            <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
                {microApps.map((app, index) => {
                    const isRunning = Math.random() > 0.5; // 模拟运行状态
                    const loadTime = Math.floor(Math.random() * 2000) + 500; // 模拟加载时间

                    return (
                        <Col xs={24} sm={12} lg={8} key={app.name}>
                            <Card
                                title={
                                    <Space>
                                        <span>{app.displayName || app.name}</span>
                                        <Tag color={getFrameworkColor(app.framework)}>
                                            {app.framework}
                                        </Tag>
                                        <Tag color={isRunning ? 'green' : 'orange'}>
                                            {isRunning ? '运行中' : '待启动'}
                                        </Tag>
                                    </Space>
                                }
                                size="small"
                                hoverable
                                className="slide-in-up"
                                style={{ animationDelay: `${index * 0.1}s` }}
                                actions={[
                                    <Button
                                        key="start"
                                        type="link"
                                        size="small"
                                        disabled={isRunning}
                                        onClick={() => handleAppAction('start', app.name)}
                                    >
                                        {isRunning ? '已启动' : '启动'}
                                    </Button>,
                                    <Button
                                        key="stop"
                                        type="link"
                                        size="small"
                                        disabled={!isRunning}
                                        onClick={() => handleAppAction('stop', app.name)}
                                    >
                                        停止
                                    </Button>,
                                    <Button
                                        key="debug"
                                        type="link"
                                        size="small"
                                        onClick={() => handleAppAction('debug', app.name)}
                                    >
                                        调试
                                    </Button>
                                ]}
                                extra={
                                    <Space>
                                        <Text type="secondary" style={{ fontSize: '12px' }}>
                                            {loadTime}ms
                                        </Text>
                                    </Space>
                                }
                            >
                                <div style={{ marginBottom: 12 }}>
                                    <Text strong>入口地址:</Text>
                                    <br />
                                    <Text code copyable style={{ fontSize: '12px' }}>
                                        {app.entry}
                                    </Text>
                                </div>

                                <div style={{ marginBottom: 12 }}>
                                    <Text strong>激活路由:</Text>
                                    <br />
                                    <Text code style={{ fontSize: '12px' }}>
                                        {app.activeWhen}
                                    </Text>
                                </div>

                                <div style={{ marginBottom: 12 }}>
                                    <Text strong>容器选择器:</Text>
                                    <br />
                                    <Text code style={{ fontSize: '12px' }}>
                                        {app.container}
                                    </Text>
                                </div>

                                {app.props && Object.keys(app.props).length > 0 && (
                                    <div style={{ marginBottom: 12 }}>
                                        <Text strong>传递属性:</Text>
                                        <br />
                                        <Text code style={{ fontSize: '12px' }}>
                                            {JSON.stringify(app.props, null, 2)}
                                        </Text>
                                    </div>
                                )}

                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <Space size="small">
                                        <Tag color="blue" style={{ fontSize: '11px' }}>
                                            已注册
                                        </Tag>
                                        {isRunning && (
                                            <Tag color="green" style={{ fontSize: '11px' }}>
                                                健康
                                            </Tag>
                                        )}
                                    </Space>
                                    <Text type="secondary" style={{ fontSize: '11px' }}>
                                        端口: {new URL(app.entry).port}
                                    </Text>
                                </div>
                            </Card>
                        </Col>
                    );
                })}
            </Row>

            {microApps.length === 0 && (
                <Card style={{ textAlign: 'center', marginTop: 24 }}>
                    <div style={{ padding: '40px 20px' }}>
                        <RocketOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: 16 }} />
                        <Title level={4} type="secondary">暂无微前端应用</Title>
                        <Paragraph type="secondary">
                            请确保相关应用服务已启动，或者检查边车配置是否正确。
                        </Paragraph>
                        <Space>
                            <Button type="primary" onClick={refreshData}>
                                刷新应用列表
                            </Button>
                            <Button onClick={() => setSelectedKey('sidecar')}>
                                检查边车配置
                            </Button>
                        </Space>
                    </div>
                </Card>
            )}

            {/* 应用统计信息 */}
            <Card style={{ marginTop: 24 }} title="📊 应用统计">
                <Row gutter={16}>
                    <Col span={6}>
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                                {microApps.length}
                            </div>
                            <div style={{ color: '#8c8c8c' }}>总应用数</div>
                        </div>
                    </Col>
                    <Col span={6}>
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                                {microApps.filter(() => Math.random() > 0.5).length}
                            </div>
                            <div style={{ color: '#8c8c8c' }}>运行中</div>
                        </div>
                    </Col>
                    <Col span={6}>
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#faad14' }}>
                                {new Set(microApps.map(app => app.framework)).size}
                            </div>
                            <div style={{ color: '#8c8c8c' }}>框架类型</div>
                        </div>
                    </Col>
                    <Col span={6}>
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
                                {Math.floor(Math.random() * 1000) + 500}ms
                            </div>
                            <div style={{ color: '#8c8c8c' }}>平均加载时间</div>
                        </div>
                    </Col>
                </Row>
            </Card>
        </div>
    );

    // 渲染内容区域
    const renderContent = () => {
        switch (selectedKey) {
            case 'overview':
                return renderOverview();
            case 'apps':
                return renderApps();
            case 'plugins':
                return (
                    <div className="fade-in">
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                            <div>
                                <Title level={2}>🔌 插件系统</Title>
                                <Paragraph>
                                    管理和配置微前端插件，支持路由、通信、沙箱等核心功能。
                                </Paragraph>
                            </div>
                            <Space>
                                <Button type="primary">安装插件</Button>
                                <Button>刷新列表</Button>
                            </Space>
                        </div>

                        <Row gutter={[16, 16]}>
                            {[
                                {
                                    name: 'router',
                                    displayName: '路由插件',
                                    description: '提供微前端应用间的路由管理和导航功能',
                                    version: '1.0.0',
                                    status: 'active',
                                    icon: '🧭'
                                },
                                {
                                    name: 'communication',
                                    displayName: '通信插件',
                                    description: '支持微前端应用间的消息传递和状态共享',
                                    version: '1.0.0',
                                    status: 'active',
                                    icon: '💬'
                                },
                                {
                                    name: 'sandbox',
                                    displayName: '沙箱插件',
                                    description: '提供 JavaScript 和 CSS 隔离，确保应用间不互相影响',
                                    version: '1.0.0',
                                    status: 'active',
                                    icon: '🛡️'
                                },
                                {
                                    name: 'auth',
                                    displayName: '认证插件',
                                    description: '统一身份认证和权限管理',
                                    version: '0.9.0',
                                    status: 'inactive',
                                    icon: '🔐'
                                }
                            ].map((plugin, index) => (
                                <Col xs={24} sm={12} lg={8} key={plugin.name}>
                                    <Card
                                        title={
                                            <Space>
                                                <span style={{ fontSize: '20px' }}>{plugin.icon}</span>
                                                <span>{plugin.displayName}</span>
                                                <Tag color={plugin.status === 'active' ? 'green' : 'orange'}>
                                                    {plugin.status === 'active' ? '已启用' : '未启用'}
                                                </Tag>
                                            </Space>
                                        }
                                        size="small"
                                        hoverable
                                        className="slide-in-up"
                                        style={{ animationDelay: `${index * 0.1}s` }}
                                        actions={[
                                            <Button
                                                key="toggle"
                                                type="link"
                                                size="small"
                                            >
                                                {plugin.status === 'active' ? '禁用' : '启用'}
                                            </Button>,
                                            <Button key="config" type="link" size="small">
                                                配置
                                            </Button>,
                                            <Button key="docs" type="link" size="small">
                                                文档
                                            </Button>
                                        ]}
                                    >
                                        <Paragraph style={{ marginBottom: 12 }}>
                                            {plugin.description}
                                        </Paragraph>
                                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                            <Text type="secondary" style={{ fontSize: '12px' }}>
                                                版本: {plugin.version}
                                            </Text>
                                            <Tag color="blue" style={{ fontSize: '11px' }}>
                                                {plugin.name}
                                            </Tag>
                                        </div>
                                    </Card>
                                </Col>
                            ))}
                        </Row>

                        <Card style={{ marginTop: 24 }} title="📊 插件统计">
                            <Row gutter={16}>
                                <Col span={6}>
                                    <div style={{ textAlign: 'center' }}>
                                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                                            4
                                        </div>
                                        <div style={{ color: '#8c8c8c' }}>已安装</div>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div style={{ textAlign: 'center' }}>
                                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                                            3
                                        </div>
                                        <div style={{ color: '#8c8c8c' }}>已启用</div>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div style={{ textAlign: 'center' }}>
                                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#faad14' }}>
                                            1
                                        </div>
                                        <div style={{ color: '#8c8c8c' }}>待更新</div>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div style={{ textAlign: 'center' }}>
                                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
                                            12
                                        </div>
                                        <div style={{ color: '#8c8c8c' }}>可用插件</div>
                                    </div>
                                </Col>
                            </Row>
                        </Card>
                    </div>
                );
            case 'adapters':
                return (
                    <div className="fade-in">
                        <Title level={2}>🔄 适配器</Title>
                        <Paragraph>适配器管理功能开发中...</Paragraph>
                    </div>
                );
            case 'builders':
                return (
                    <div className="fade-in">
                        <Title level={2}>🛠️ 构建工具</Title>
                        <Paragraph>构建工具集成功能开发中...</Paragraph>
                    </div>
                );
            case 'sidecar':
                return (
                    <div className="fade-in">
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                            <div>
                                <Title level={2}>🚗 边车模式</Title>
                                <Paragraph>
                                    零配置的边车代理模式，自动发现和管理微前端应用。
                                </Paragraph>
                            </div>
                            <Space>
                                <Button type="primary">重启边车</Button>
                                <Button>查看日志</Button>
                            </Space>
                        </div>

                        <Row gutter={[16, 16]}>
                            <Col xs={24} lg={12}>
                                <Card title="🔍 自动发现" size="small">
                                    <div style={{ marginBottom: 16 }}>
                                        <Text strong>扫描间隔:</Text>
                                        <Text style={{ marginLeft: 8 }}>5000ms</Text>
                                    </div>
                                    <div style={{ marginBottom: 16 }}>
                                        <Text strong>扫描模式:</Text>
                                        <Tag color="green" style={{ marginLeft: 8 }}>已启用</Tag>
                                    </div>
                                    <div style={{ marginBottom: 16 }}>
                                        <Text strong>扫描范围:</Text>
                                        <div style={{ marginTop: 8 }}>
                                            {[
                                                'http://localhost:3001',
                                                'http://localhost:3002',
                                                'http://localhost:3003',
                                                'http://localhost:3004'
                                            ].map(url => (
                                                <Tag key={url} style={{ marginBottom: 4 }}>
                                                    {url}
                                                </Tag>
                                            ))}
                                        </div>
                                    </div>
                                </Card>
                            </Col>

                            <Col xs={24} lg={12}>
                                <Card title="⚙️ 配置信息" size="small">
                                    <div style={{ marginBottom: 16 }}>
                                        <Text strong>路由模式:</Text>
                                        <Text code style={{ marginLeft: 8 }}>history</Text>
                                    </div>
                                    <div style={{ marginBottom: 16 }}>
                                        <Text strong>沙箱类型:</Text>
                                        <Text code style={{ marginLeft: 8 }}>proxy</Text>
                                    </div>
                                    <div style={{ marginBottom: 16 }}>
                                        <Text strong>预取策略:</Text>
                                        <Text code style={{ marginLeft: 8 }}>idle</Text>
                                    </div>
                                    <div style={{ marginBottom: 16 }}>
                                        <Text strong>开发模式:</Text>
                                        <Tag color="blue" style={{ marginLeft: 8 }}>
                                            {import.meta.env.DEV ? '已启用' : '已禁用'}
                                        </Tag>
                                    </div>
                                </Card>
                            </Col>
                        </Row>

                        <Card style={{ marginTop: 16 }} title="📈 运行状态">
                            <Row gutter={16}>
                                <Col span={6}>
                                    <div style={{ textAlign: 'center' }}>
                                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: sidecarStatus === 'ready' ? '#52c41a' : '#faad14' }}>
                                            {sidecarStatus === 'ready' ? '正常' : '加载中'}
                                        </div>
                                        <div style={{ color: '#8c8c8c' }}>边车状态</div>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div style={{ textAlign: 'center' }}>
                                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                                            {microApps.length}
                                        </div>
                                        <div style={{ color: '#8c8c8c' }}>发现应用</div>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div style={{ textAlign: 'center' }}>
                                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
                                            {Math.floor(Math.random() * 100) + 50}ms
                                        </div>
                                        <div style={{ color: '#8c8c8c' }}>平均响应</div>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div style={{ textAlign: 'center' }}>
                                        <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                                            {Math.floor(Math.random() * 24) + 1}h
                                        </div>
                                        <div style={{ color: '#8c8c8c' }}>运行时间</div>
                                    </div>
                                </Col>
                            </Row>
                        </Card>
                    </div>
                );
            case 'debug':
                return <DebugPanel />;
            case 'settings':
                return (
                    <div className="fade-in">
                        <Title level={2}>⚙️ 设置</Title>
                        <Paragraph>设置功能开发中...</Paragraph>
                    </div>
                );
            default:
                return renderOverview();
        }
    };

    return (
        <Layout style={{ minHeight: '100vh' }}>
            <Sider
                collapsible
                collapsed={collapsed}
                onCollapse={setCollapsed}
                theme="light"
                width={250}
            >
                <div style={{
                    height: 64,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderBottom: '1px solid #f0f0f0'
                }}>
                    <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
                        {collapsed ? 'MC' : 'Micro-Core'}
                    </Title>
                </div>
                <Menu
                    mode="inline"
                    selectedKeys={[selectedKey]}
                    items={menuItems}
                    onClick={({ key }) => setSelectedKey(key)}
                    style={{ borderRight: 0 }}
                />
            </Sider>

            <Layout>
                <Header style={{
                    background: '#fff',
                    padding: '0 24px',
                    borderBottom: '1px solid #f0f0f0',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                }}>
                    <Title level={3} style={{ margin: 0 }}>
                        Micro-Core Playground
                    </Title>
                    <Space>
                        <Tag color="green">开发环境</Tag>
                        <Tag color="blue">v1.0.0</Tag>
                    </Space>
                </Header>

                <Content style={{
                    margin: '24px',
                    padding: '24px',
                    background: '#fff',
                    borderRadius: '8px',
                    minHeight: 'calc(100vh - 112px)'
                }}>
                    {renderContent()}
                </Content>
            </Layout>
        </Layout>
    );
};

export default App;