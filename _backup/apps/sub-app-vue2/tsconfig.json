{
    "compilerOptions": {
        "target": "ES2020",
        "useDefineForClassFields": true,
        "lib": [
            "ES2020",
            "DOM",
            "DOM.Iterable"
        ],
        "module": "ESNext",
        "skipLibCheck": true,
        /* Bundler mode */
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "preserve",
        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,
        "noImplicitReturns": true,
        "noImplicitOverride": true,
        "noPropertyAccessFromIndexSignature": true,
        "forceConsistentCasingInFileNames": true,
        /* Path mapping */
        "baseUrl": ".",
        "paths": {
            "@/*": [
                "src/*"
            ],
            "@micro-core/core": [
                "../../packages/core/src"
            ],
            "@micro-core/adapter-vue2": [
                "../../packages/adapters/adapter-vue2/src"
            ],
            "@micro-core/builder-vite": [
                "../../packages/builders/builder-vite/src"
            ],
            "@micro-core/shared": [
                "../../packages/shared/src"
            ]
        }
    },
    "include": [
        "src/**/*",
        "vite.config.js"
    ],
    "exclude": [
        "dist",
        "node_modules",
        "**/*.test.js",
        "**/*.spec.js"
    ]
}