# 通信 API (第二部分)

## GlobalState (续)

### API 参考 (续)

#### set(path, value)

设置状态值。

```typescript
set(path: string, value: any): void
```

**参数：**
- `path` (string): 状态路径，使用点号分隔
- `value` (any): 新的状态值

**示例：**

```typescript
// 设置顶级状态
state.set('theme', 'dark');

// 设置嵌套状态
state.set('user.name', 'Admin');
state.set('settings.notifications.push', true);

// 设置数组
state.set('notifications', [
  { id: 1, text: '新消息', read: false },
  { id: 2, text: '系统更新', read: false }
]);

// 更新数组元素
state.set('notifications.0.read', true);

// 添加数组元素
state.set('notifications.length', { id: 3, text: '新功能上线', read: false });
```

#### delete(path)

删除状态值。

```typescript
delete(path: string): void
```

**参数：**
- `path` (string): 状态路径，使用点号分隔

**示例：**

```typescript
// 删除顶级状态
state.delete('theme');

// 删除嵌套状态
state.delete('user.name');
state.delete('settings.notifications.push');

// 删除数组元素
state.delete('notifications.0');
```

#### has(path)

检查状态路径是否存在。

```typescript
has(path: string): boolean
```

**参数：**
- `path` (string): 状态路径，使用点号分隔

**返回值：** 路径是否存在

**示例：**

```typescript
// 检查路径是否存在
const hasUser = state.has('user');
console.log('是否有用户状态:', hasUser);

// 检查嵌套路径是否存在
const hasUserName = state.has('user.name');
console.log('是否有用户名:', hasUserName);

// 条件操作
if (state.has('user.id')) {
  // 用户已登录
  showUserProfile(state.get('user'));
} else {
  // 用户未登录
  showLoginForm();
}
```

#### reset(path?)

重置状态到初始值。

```typescript
reset(path?: string): void
```

**参数：**
- `path` (string): 可选，状态路径，使用点号分隔，如果不提供则重置整个状态

**示例：**

```typescript
// 重置整个状态
state.reset();

// 重置特定状态
state.reset('user');
state.reset('settings.notifications');
```

#### subscribe(path, callback)

订阅状态变化。

```typescript
subscribe(path: string, callback: (newValue: any, oldValue: any, path: string) => void): () => void
```

**参数：**
- `path` (string): 状态路径，使用点号分隔，支持通配符
- `callback` ((newValue: any, oldValue: any, path: string) => void): 回调函数，接收新值、旧值和路径

**返回值：** 取消订阅的函数

**示例：**

```typescript
// 订阅特定状态
const unsubscribe = state.subscribe('user', (newValue, oldValue) => {
  console.log('用户状态变化:', oldValue, '->', newValue);
  
  // 更新 UI
  updateUserUI(newValue);
});

// 订阅嵌套状态
state.subscribe('settings.notifications', (newValue, oldValue) => {
  console.log('通知设置变化:', oldValue, '->', newValue);
  
  // 更新通知设置
  updateNotificationSettings(newValue);
});

// 使用通配符订阅多个状态
state.subscribe('user.*', (newValue, oldValue, path) => {
  console.log(`用户属性 ${path} 变化:`, oldValue, '->', newValue);
});

// 订阅所有状态变化
state.subscribe('*', (newValue, oldValue, path) => {
  console.log(`状态 ${path} 变化:`, oldValue, '->', newValue);
});

// 取消订阅
unsubscribe();

// 在组件卸载时取消订阅
function unmountComponent() {
  unsubscribe();
}
```

#### batch(callback)

批量更新状态，只触发一次通知。

```typescript
batch(callback: () => void): void
```

**参数：**
- `callback` (() => void): 批量更新回调函数

**示例：**

```typescript
// 批量更新状态
state.batch(() => {
  state.set('user.id', 1);
  state.set('user.name', 'Admin');
  state.set('user.isLoggedIn', true);
  state.set('notifications', []);
});

// 复杂的批量更新
state.batch(() => {
  // 更新用户
  const user = fetchUserData();
  state.set('user', user);
  
  // 更新主题
  state.set('theme', user.preferences.theme);
  
  // 更新通知
  const notifications = fetchNotifications(user.id);
  state.set('notifications', notifications);
  
  // 更新设置
  state.set('settings', user.preferences.settings);
});
```

#### snapshot()

创建当前状态的快照。

```typescript
snapshot(): object
```

**返回值：** 状态快照对象

**示例：**

```typescript
// 创建状态快照
const snapshot = state.snapshot();
console.log('当前状态快照:', snapshot);

// 保存状态快照
localStorage.setItem('appState', JSON.stringify(snapshot));

// 使用快照进行比较
function compareWithSnapshot(snapshot) {
  const currentState = state.snapshot();
  const differences = findDifferences(currentState, snapshot);
  console.log('状态差异:', differences);
}
```

#### restore(snapshot)

从快照恢复状态。

```typescript
restore(snapshot: object): void
```

**参数：**
- `snapshot` (object): 状态快照对象

**示例：**

```typescript
// 从快照恢复状态
const savedState = JSON.parse(localStorage.getItem('appState'));
if (savedState) {
  state.restore(savedState);
}

// 实现撤销功能
const history = [];

// 保存当前状态
function saveState() {
  history.push(state.snapshot());
}

// 撤销到上一个状态
function undo() {
  if (history.length > 0) {
    const previousState = history.pop();
    state.restore(previousState);
  }
}
```

#### middleware(middleware)

添加中间件。

```typescript
middleware(middleware: (path: string, newValue: any, oldValue: any, next: () => void) => void): () => void
```

**参数：**
- `middleware` ((path: string, newValue: any, oldValue: any, next: () => void) => void): 中间件函数

**返回值：** 移除中间件的函数

**示例：**

```typescript
// 添加日志中间件
const removeLogger = state.middleware((path, newValue, oldValue, next) => {
  console.log(`[状态变化] ${path}:`, oldValue, '->', newValue);
  next();
});

// 添加验证中间件
state.middleware((path, newValue, oldValue, next) => {
  if (path === 'user.email') {
    // 验证邮箱格式
    if (!isValidEmail(newValue)) {
      console.error('无效的邮箱地址:', newValue);
      return; // 不调用 next()，阻止更新
    }
  }
  
  next();
});

// 添加持久化中间件
state.middleware((path, newValue, oldValue, next) => {
  next(); // 先更新状态
  
  // 然后持久化
  if (path.startsWith('settings')) {
    localStorage.setItem('settings', JSON.stringify(state.get('settings')));
  }
});

// 移除中间件
removeLogger();
```

## AppBridge

`AppBridge` 是 Micro-Core 提供的应用桥接器，用于主应用和微应用之间的通信。

### 基本用法

```typescript
import { AppBridge } from '@micro-core/core';

// 在主应用中创建桥接器
const bridge = new AppBridge({
  name: 'main-app'
});

// 注册 API
bridge.registerAPI('getData', (params) => {
  return fetchData(params);
});

// 调用微应用 API
bridge.call('micro-app', 'getUserInfo', { userId: 1 })
  .then(userInfo => {
    console.log('用户信息:', userInfo);
  });

// 在微应用中创建桥接器
const microBridge = new AppBridge({
  name: 'micro-app'
});

// 注册 API
microBridge.registerAPI('getUserInfo', (params) => {
  return fetchUserInfo(params.userId);
});

// 调用主应用 API
microBridge.call('main-app', 'getData', { query: 'example' })
  .then(data => {
    console.log('数据:', data);
  });
```

### API 参考

#### 构造函数

创建应用桥接器实例。

```typescript
constructor(options: AppBridgeOptions)
```

**参数：**
- `options` (AppBridgeOptions): 桥接器配置选项
  - `name` (string): 应用名称，用于标识当前应用
  - `type` (string): 可选，应用类型，'main' 或 'micro'，默认自动检测
  - `timeout` (number): 可选，调用超时时间，单位为毫秒，默认为 5000
  - `strict` (boolean): 可选，是否启用严格模式，默认为 false
  - `logger` (object): 可选，日志记录器

**示例：**

```typescript
// 在主应用中创建桥接器
const bridge = new AppBridge({
  name: 'main-app',
  type: 'main',
  timeout: 10000,
  strict: true,
  logger: {
    info: (...args) => console.info('[Bridge]', ...args),
    warn: (...args) => console.warn('[Bridge]', ...args),
    error: (...args) => console.error('[Bridge]', ...args)
  }
});

// 在微应用中创建桥接器
const microBridge = new AppBridge({
  name: 'micro-app',
  type: 'micro',
  timeout: 5000
});
```

#### registerAPI(name, handler)

注册 API 处理函数。

```typescript
registerAPI(name: string, handler: (params: any, context: Context) => any): void
```

**参数：**
- `name` (string): API 名称
- `handler` ((params: any, context: Context) => any): API 处理函数，接收参数和上下文，返回结果或 Promise

**示例：**

```typescript
// 注册简单 API
bridge.registerAPI('echo', (params) => {
  return params;
});

// 注册异步 API
bridge.registerAPI('getData', async (params) => {
  const data = await fetchData(params.query);
  return data;
});

// 使用上下文
bridge.registerAPI('getUserInfo', (params, context) => {
  console.log(`调用来自: ${context.caller}`);
  console.log(`调用时间: ${new Date(context.timestamp)}`);
  
  return fetchUserInfo(params.userId);
});

// 处理错误
bridge.registerAPI('processData', (params) => {
  try {
    return processData(params);
  } catch (error) {
    throw new Error(`处理数据失败: ${error.message}`);
  }
});

// 访问控制
bridge.registerAPI('adminOperation', (params, context) => {
  // 检查权限
  if (!isAdmin(context.caller)) {
    throw new Error('权限不足');
  }
  
  return performAdminOperation(params);
});
```

#### unregisterAPI(name)

注销 API 处理函数。

```typescript
unregisterAPI(name: string): boolean
```

**参数：**
- `name` (string): API 名称

**返回值：** 是否成功注销

**示例：**

```typescript
// 注销 API
const success = bridge.unregisterAPI('getData');
if (success) {
  console.log('API 已注销');
} else {
  console.warn('API 不存在');
}
```

#### call(target, name, params)

调用目标应用的 API。

```typescript
call(target: string, name: string, params?: any): Promise<any>
```

**参数：**
- `target` (string): 目标应用名称
- `name` (string): API 名称
- `params` (any): 可选，API 参数

**返回值：** 返回一个 Promise，解析为 API 调用结果

**示例：**

```typescript
// 调用简单 API
bridge.call('micro-app', 'echo', { message: 'Hello!' })
  .then(response => {
    console.log('响应:', response);
  })
  .catch(error => {
    console.error('调用失败:', error);
  });

// 调用异步 API
bridge.call('micro-app', 'getData', { query: 'example' })
  .then(data => {
    console.log('数据:', data);
  })
  .catch(error => {
    console.error('获取数据失败:', error);
  });

// 使用 async/await
async function fetchUserData(userId) {
  try {
    const userInfo = await bridge.call('micro-app', 'getUserInfo', { userId });
    console.log('用户信息:', userInfo);
    return userInfo;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    throw error;
  }
}

// 批量调用
Promise.all([
  bridge.call('micro-app', 'getData', { query: 'example' }),
  bridge.call('micro-app', 'getUserInfo', { userId: 1 }),
  bridge.call('micro-app', 'getConfig')
])
  .then(([data, userInfo, config]) => {
    console.log('所有数据:', { data, userInfo, config });
  })
  .catch(error => {
    console.error('批量调用失败:', error);
  });
```

#### broadcast(name, data)

向所有应用广播消息。

```typescript
broadcast(name: string, data?: any): void
```

**参数：**
- `name` (string): 消息名称
- `data` (any): 可选，消息数据

**示例：**

```typescript
// 广播消息
bridge.broadcast('themeChanged', { theme: 'dark' });

// 广播系统事件
bridge.broadcast('systemEvent', {
  type: 'maintenance',
  startTime: new Date(Date.now() + 3600000),
  duration: '2小时'
});

// 广播用户事件
bridge.broadcast('userLoggedIn', {
  userId: 1,
  username: 'Admin'
});
```

#### on(name, handler)

监听消息。

```typescript
on(name: string, handler: (data: any, context: Context) => void): () => void
```

**参数：**
- `name` (string): 消息名称
- `handler` ((data: any, context: Context) => void): 消息处理函数，接收消息数据和上下文

**返回值：** 取消监听的函数

**示例：**

```typescript
// 监听消息
const unsubscribe = bridge.on('themeChanged', (data) => {
  console.log('主题变更为:', data.theme);
  applyTheme(data.theme);
});

// 使用上下文
bridge.on('userLoggedIn', (data, context) => {
  console.log(`用户登录事件，来自: ${context.sender}`);
  console.log(`用户: ${data.username}`);
});

// 监听系统事件
bridge.on('systemEvent', (data) => {
  if (data.type === 'maintenance') {
    showMaintenanceNotification(data);
  }
});

// 取消监听
unsubscribe();

// 在组件卸载时取消监听
function unmountComponent() {
  unsubscribe();
}
```

#### off(name, handler)

取消监听消息。

```typescript
off(name: string, handler?: (data: any, context: Context) => void): void
```

**参数：**
- `name` (string): 消息名称
- `handler` ((data: any, context: Context) => void): 可选，消息处理函数，如果不提供则移除所有该消息的监听器

**示例：**

```typescript
// 定义消息处理函数
function handleThemeChange(data) {
  console.log('主题变更为:', data.theme);
  applyTheme(data.theme);
}

// 监听消息
bridge.on('themeChanged', handleThemeChange);

// 取消特定处理函数的监听
bridge.off('themeChanged', handleThemeChange);

// 取消所有 'themeChanged' 消息的监听
bridge.off('themeChanged');
```

#### connect(target)

连接到目标应用。

```typescript
connect(target: string): Promise<boolean>
```

**参数：**
- `target` (string): 目标应用名称

**返回值：** 返回一个 Promise，解析为连接是否成功

**示例：**

```typescript
// 连接到目标应用
bridge.connect('micro-app')
  .then(success => {
    if (success) {
      console.log('连接成功');
    } else {
      console.error('连接失败');
    }
  });

// 连接到多个应用
Promise.all([
  bridge.connect('micro-app1'),
  bridge.connect('micro-app2'),
  bridge.connect('micro-app3')
])
  .then(results => {
    const successCount = results.filter(Boolean).length;
    console.log(`成功连接 ${successCount}/${results.length} 个应用`);
  });
```

#### disconnect(target)

断开与目标应用的连接。

```typescript
disconnect(target: string): boolean
```

**参数：**
- `target` (string): 目标应用名称

**返回值：** 断开连接是否成功

**示例：**

```typescript
// 断开与目标应用的连接
const success = bridge.disconnect('micro-app');
if (success) {
  console.log('断开连接成功');
} else {
  console.error('断开连接失败');
}
```

#### isConnected(target)

检查是否已连接到目标应用。

```typescript
isConnected(target: string): boolean
```

**参数：**
- `target` (string): 目标应用名称

**返回值：** 是否已连接

**示例：**

```typescript
// 检查是否已连接
const connected = bridge.isConnected('micro-app');
console.log('是否已连接:', connected);

// 条件调用
if (bridge.isConnected('micro-app')) {
  bridge.call('micro-app', 'getData', { query: 'example' })
    .then(data => {
      console.log('数据:', data);
    });
} else {
  console.warn('未连接到微应用，尝试连接...');
  bridge.connect('micro-app')
    .then(success => {
      if (success) {
        return bridge.call('micro-app', 'getData', { query: 'example' });
      }
      throw new Error('连接失败');
    })
    .then(data => {
      console.log('数据:', data);
    })
    .catch(error => {
      console.error('操作失败:', error);
    });
}
```

#### getConnections()

获取所有已连接的应用。

```typescript
getConnections(): Array<string>
```

**返回值：** 已连接的应用名称数组

**示例：**

```typescript
// 获取所有连接
const connections = bridge.getConnections();
console.log('已连接的应用:', connections);

// 遍历连接
connections.forEach(appName => {
  console.log(`已连接到应用: ${appName}`);
});
```

## 高级功能

### 通信安全

Micro-Core 通信系统提供了多种安全机制，用于保护通信过程中的数据安全。

#### 消息签名

```typescript
// 创建带安全选项的通道
const secureChannel = new MessageChannel({
  name: 'secure-channel',
  secure: true,
  secretKey: 'your-secret-key'
});

// 发送带签名的消息
secureChannel.send('secureData', { userId: 1, token: 'abc123' });

// 验证消息签名
secureChannel.on('secureData', (data, message) => {
  if (message.verified) {
    console.log('收到已验证的安全消息:', data);
  } else {
    console.warn('收到未验证的消息，可能被篡改');
  }
});
```

#### 访问控制

```typescript
// 创建带访问控制的桥接器
const bridge = new AppBridge({
  name: 'main-app',
  accessControl: {
    // 定义 API 访问规则
    rules: {
      'getData': ['micro-app1', 'micro-app2'],
      'updateData': ['micro-app1'],
      'adminOperation': ['admin-app']
    },
    // 默认规则
    defaultRule: 'deny'
  }
});

// 注册 API
bridge.registerAPI('getData', (params) => {
  return fetchData(params);
});

// 访问控制中间件
bridge.use((context, next) => {
  // 检查权限
  if (!hasPermission(context.caller, context.api)) {
    throw new Error('权限不足');
  }
  
  return next();
});
```

#### 数据加密

```typescript
// 创建带加密的通道
const encryptedChannel = new MessageChannel({
  name: 'encrypted-channel',
  encryption: {
    enabled: true,
    algorithm: 'aes-256-gcm',
    key: 'your-encryption-key'
  }
});

// 发送加密消息
encryptedChannel.send('sensitiveData', {
  creditCard: '1234-5678-9012-3456',
  cvv: '123'
});

// 接收加密消息
encryptedChannel.on('sensitiveData', (data) => {
  console.log('收到解密后的敏感数据:', data);
});
```

### 通信中间件

Micro-Core 通信系统支持中间件，用于拦截和处理通信过程。

```typescript
// 创建通道
const channel = new MessageChannel({
  name: 'main-channel'
});

// 添加日志中间件
channel.use((message, next) => {
  console.log(`[${new Date().toISOString()}] 发送消息:`, message);
  return next();
});

// 添加错误处理中间件
channel.use((message, next) => {
  try {
    return next();
  } catch (error) {
    console.error('消息处理错误:', error);
    throw error;
  }
});

// 添加超时中间件
channel.use((message, next) => {
  const timeout = message.options?.timeout || 5000;
  
  return Promise.race([
    next(),
    new Promise((_, reject) => {
      setTimeout(() => reject(new Error('操作超时')), timeout);
    })
  ]);
});

// 添加重试中间件
channel.use((message, next) => {
  const retryCount = message.options?.retry || 0;
  const retryDelay = message.options?.retryDelay || 1000;
  
  let attempts = 0;
  
  function attempt() {
    return next().catch(error => {
      attempts++;
      if (attempts <= retryCount) {
        console.warn(`操作失败，${attempts}/${retryCount} 次重试`);
        return new Promise(resolve => {
          setTimeout(() => resolve(attempt()), retryDelay);
        });
      }
      throw error;
    });
  }
  
  return attempt();
});
```

### 通信监控

Micro-Core 通信系统提供了监控功能，用于跟踪和分析通信过程。

```typescript
// 创建带监控的通道
const channel = new MessageChannel({
  name: 'monitored-channel',
  monitoring: {
    enabled: true,
    sampleRate: 0.1, // 采样率 10%
    maxEvents: 1000
  }
});

// 获取通信统计信息
const stats = channel.getStats();
console.log('通信统计:', stats);

// 监听通信事件
channel.on('monitoring:send', (event) => {
  console.log('发送消息:', event);
});

channel.on('monitoring:receive', (event) => {
  console.log('接收消息:', event);
});

channel.on('monitoring:error', (event) => {
  console.error('通信错误:', event);
});

// 导出通信日志
const logs = channel.exportLogs();
console.log('通信日志:', logs);

// 性能分析
const performance = channel.getPerformance();
console.log('通信性能:', performance);
```

### 通信调试

Micro-Core 通信系统提供了调试功能，用于排查通信问题。

```typescript
// 启用调试模式
MessageChannel.debug = true;

// 创建带调试选项的通道
const channel = new MessageChannel({
  name: 'debug-channel',
  debug: {
    enabled: true,
    verbose: true,
    logLevel: 'debug'
  }
});

// 设置调试回调
channel.setDebugCallback((level, ...args) => {
  switch (level) {
    case 'debug':
      console.debug('[Channel Debug]', ...args);
      break;
    case 'info':
      console.info('[Channel Info]', ...args);
      break;
    case 'warn':
      console.warn('[Channel Warning]', ...args);
      break;
    case 'error':
      console.error('[Channel Error]', ...args);
      break;
  }
});

// 启用消息跟踪
channel.enableTracing();

// 获取跟踪日志
const traces = channel.getTraces();
console.log('消息跟踪:', traces);

// 禁用消息跟踪
channel.disableTracing();
```

## 与其他框架集成

### 与 qiankun 集成

```typescript
import { registerMicroApps, start } from 'qiankun';
import { AppBridge } from '@micro-core/core';

// 创建桥接器
const bridge = new AppBridge({
  name: 'main-app'
});

// 注册 API
bridge.registerAPI('getData', (params) => {
  return fetchData(params);
});

// 注册微应用
registerMicroApps([
  {
    name: 'micro-app',
    entry: '//localhost:8081',
    container: '#micro-container',
    activeRule: '/micro-app',
    props: {
      bridge // 传递桥接器给微应用
    }
  }
]);

// 启动 qiankun
start();

// 在微应用中使用桥接器
export async function bootstrap(props) {
  const { bridge } = props;
  
  // 注册 API
  bridge.registerAPI('getUserInfo', (params) => {
    return fetchUserInfo(params.userId);
  });
  
  // 调用主应用 API
  bridge.call('main-app', 'getData', { query: 'example' })
    .then(data => {
      console.log('数据:', data);
    });
}
```

### 与 wujie (无界) 集成

```typescript
import { bus, setupApp, preloadApp, startApp } from 'wujie';
import { AppBridge } from '@micro-core/core';

// 创建桥接器
const bridge = new AppBridge({
  name: 'main-app'
});

// 注册 API
bridge.registerAPI('getData', (params) => {
  return fetchData(params);
});

// 设置微应用
setupApp({
  name: 'micro-app',
  url: '//localhost:8081',
  exec: true,
  props: {
    bridge // 传递桥接器给微应用
  }
});

// 预加载微应用
preloadApp({
  name: 'micro-app'
});

// 启动微应用
startApp({
  name: 'micro-app',
  container: '#micro-container',
  url: '//localhost:8081'
});

// 在微应用中使用桥接器
window.$wujie.props.bridge.registerAPI('getUserInfo', (params) => {
  return fetchUserInfo(params.userId);
});

window.$wujie.props.bridge.call('main-app', 'getData', { query: 'example' })
  .then(data => {
    console.log('数据:', data);
  });
```

## 常见问题与解决方案

### 通信超时

**问题**：通信