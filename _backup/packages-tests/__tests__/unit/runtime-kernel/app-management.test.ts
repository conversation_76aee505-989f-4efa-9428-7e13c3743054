/**
 * @fileoverview MicroCoreKernel 应用管理测试
 * @description 测试内核的应用注册、注销、获取等管理功能
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { APP_STATUS } from '../../../src/constants';
import { MicroCoreError } from '../../../src/errors';
import { MicroCoreKernel } from '../../../src/runtime/kernel';
import type { AppConfig, AppInstance } from '../../../src/types';

// Mock 依赖模块
vi.mock('../../../src/communication/event-bus');
vi.mock('../../../src/runtime/app-registry');
vi.mock('../../../src/runtime/lifecycle-manager');
vi.mock('../../../src/utils');

describe('MicroCoreKernel 应用管理', () => {
    let kernel: MicroCoreKernel;
    let mockEventBus: any;
    let mockAppRegistry: any;
    let mockLifecycleManager: any;
    let mockLogger: any;

    beforeEach(() => {
        // 创建模拟对象
        mockEventBus = {
            emit: vi.fn(),
            on: vi.fn(),
            off: vi.fn(),
            clear: vi.fn()
        };

        mockAppRegistry = {
            register: vi.fn(),
            unregister: vi.fn(),
            get: vi.fn(),
            getAll: vi.fn(),
            getByStatus: vi.fn(),
            updateStatus: vi.fn(),
            setError: vi.fn(),
            clear: vi.fn()
        };

        mockLifecycleManager = {
            bootstrap: vi.fn(),
            mount: vi.fn(),
            unmount: vi.fn(),
            clearHooks: vi.fn()
        };

        mockLogger = {
            debug: vi.fn(),
            info: vi.fn(),
            warn: vi.fn(),
            error: vi.fn()
        };

        // Mock 构造函数
        const { EventBus } = require('../../../src/communication/event-bus');
        const { AppRegistry } = require('../../../src/runtime/app-registry');
        const { LifecycleManager } = require('../../../src/runtime/lifecycle-manager');
        const { createLogger } = require('../../../src/utils');

        EventBus.mockImplementation(() => mockEventBus);
        AppRegistry.mockImplementation(() => mockAppRegistry);
        LifecycleManager.mockImplementation(() => mockLifecycleManager);
        createLogger.mockReturnValue(mockLogger);

        kernel = new MicroCoreKernel();
    });

    afterEach(() => {
        vi.clearAllMocks();
        vi.resetModules();
    });

    describe('registerApplication', () => {
        it('应该成功注册应用', () => {
            const appConfig: AppConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app',
                activeWhen: '/test'
            };

            kernel.registerApplication(appConfig);

            expect(mockLogger.debug).toHaveBeenCalledWith('注册应用:', 'test-app');
            expect(mockAppRegistry.register).toHaveBeenCalledWith(appConfig);
        });

        it('应该处理无效的应用配置', () => {
            expect(() => {
                kernel.registerApplication(null as any);
            }).toThrow(MicroCoreError);

            expect(mockLogger.error).toHaveBeenCalledWith('应用注册失败:', expect.any(Error));
        });

        it('应该处理注册失败的情况', () => {
            const error = new Error('注册失败');
            mockAppRegistry.register.mockImplementation(() => {
                throw error;
            });

            const appConfig: AppConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app',
                activeWhen: '/test'
            };

            expect(() => {
                kernel.registerApplication(appConfig);
            }).toThrow(error);

            expect(mockLogger.error).toHaveBeenCalledWith('应用注册失败:', error);
        });

        it('应该在内核销毁后抛出错误', async () => {
            await kernel.destroy();

            const appConfig: AppConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app',
                activeWhen: '/test'
            };

            expect(() => {
                kernel.registerApplication(appConfig);
            }).toThrow(MicroCoreError);
        });

        it('应该处理复杂的应用配置', () => {
            const complexConfig: AppConfig = {
                name: 'complex-app',
                entry: 'http://localhost:3000',
                container: '#app',
                activeWhen: ['/complex', '/advanced'],
                props: { theme: 'dark' },
                sandbox: { strictStyleIsolation: true },
                loader: { timeout: 10000 }
            };

            kernel.registerApplication(complexConfig);

            expect(mockAppRegistry.register).toHaveBeenCalledWith(complexConfig);
        });
    });

    describe('unregisterApplication', () => {
        it('应该成功注销应用', () => {
            kernel.unregisterApplication('test-app');

            expect(mockLogger.debug).toHaveBeenCalledWith('注销应用:', 'test-app');
            expect(mockAppRegistry.unregister).toHaveBeenCalledWith('test-app');
        });

        it('应该处理注销失败的情况', () => {
            const error = new Error('注销失败');
            mockAppRegistry.unregister.mockImplementation(() => {
                throw error;
            });

            expect(() => {
                kernel.unregisterApplication('test-app');
            }).toThrow(error);

            expect(mockLogger.error).toHaveBeenCalledWith('应用注销失败:', error);
        });

        it('应该在内核销毁后抛出错误', async () => {
            await kernel.destroy();

            expect(() => {
                kernel.unregisterApplication('test-app');
            }).toThrow(MicroCoreError);
        });

        it('应该处理空字符串应用名', () => {
            expect(() => {
                kernel.unregisterApplication('');
            }).toThrow();
        });

        it('应该处理null应用名', () => {
            expect(() => {
                kernel.unregisterApplication(null as any);
            }).toThrow();
        });
    });

    describe('getApplication', () => {
        it('应该返回指定的应用实例', () => {
            const mockApp: any = {
                name: 'test-app',
                status: APP_STATUS.NOT_LOADED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            const result = kernel.getApplication('test-app');

            expect(result).toBe(mockApp);
            expect(mockAppRegistry.get).toHaveBeenCalledWith('test-app');
        });

        it('应该在应用不存在时返回null', () => {
            mockAppRegistry.get.mockReturnValue(null);

            const result = kernel.getApplication('non-existent-app');

            expect(result).toBeNull();
        });

        it('应该在内核销毁后抛出错误', async () => {
            await kernel.destroy();

            expect(() => {
                kernel.getApplication('test-app');
            }).toThrow(MicroCoreError);
        });

        it('应该处理空字符串应用名', () => {
            const result = kernel.getApplication('');
            expect(mockAppRegistry.get).toHaveBeenCalledWith('');
        });
    });

    describe('getApplications', () => {
        it('应该返回所有应用实例', () => {
            const mockApps: AppInstance[] = [
                {
                    name: 'app1',
                    status: APP_STATUS.NOT_LOADED,
                    config: {
                        name: 'app1',
                        entry: 'http://localhost:3001',
                        container: '#app1',
                        activeWhen: '/app1'
                    }
                },
                {
                    name: 'app2',
                    status: APP_STATUS.MOUNTED,
                    config: {
                        name: 'app2',
                        entry: 'http://localhost:3002',
                        container: '#app2',
                        activeWhen: '/app2'
                    }
                }
            ];

            mockAppRegistry.getAll.mockReturnValue(mockApps);

            const result = kernel.getApplications();

            expect(result).toBe(mockApps);
            expect(mockAppRegistry.getAll).toHaveBeenCalled();
        });

        it('应该返回空数组当没有应用时', () => {
            mockAppRegistry.getAll.mockReturnValue([]);

            const result = kernel.getApplications();

            expect(result).toEqual([]);
        });

        it('应该在内核销毁后抛出错误', async () => {
            await kernel.destroy();

            expect(() => {
                kernel.getApplications();
            }).toThrow(MicroCoreError);
        });
    });

    describe('性能测试', () => {
        it('应该高效处理大量应用注册', () => {
            const start = performance.now();

            for (let i = 0; i < 1000; i++) {
                const appConfig: any = {
                    name: `app-${i}`,
                    entry: `http://localhost:${3000 + i}`,
                    container: `#app-${i}`,
                    activeWhen: `/app-${i}`
                };
                kernel.registerApplication(appConfig);
            }

            const end = performance.now();
            expect(end - start).toBeLessThan(1000); // 应该在1秒内完成
            expect(mockAppRegistry.register).toHaveBeenCalledTimes(1000);
        });

        it('应该高效处理大量应用查询', () => {
            const start = performance.now();

            for (let i = 0; i < 1000; i++) {
                kernel.getApplication(`app-${i}`);
            }

            const end = performance.now();
            expect(end - start).toBeLessThan(500); // 应该在500ms内完成
            expect(mockAppRegistry.get).toHaveBeenCalledTimes(1000);
        });
    });

    describe('边界情况', () => {
        it('应该处理特殊字符的应用名', () => {
            const specialNames = [
                'app-with-dash',
                'app_with_underscore',
                'app.with.dot',
                'app@with@at',
                'app#with#hash'
            ];

            specialNames.forEach(name => {
                const config: AppConfig = {
                    name,
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                };

                expect(() => {
                    kernel.registerApplication(config);
                }).not.toThrow();
            });
        });

        it('应该处理长应用名', () => {
            const longName = 'a'.repeat(1000);
            const config: AppConfig = {
                name: longName,
                entry: 'http://localhost:3000',
                container: '#app',
                activeWhen: '/test'
            };

            expect(() => {
                kernel.registerApplication(config);
            }).not.toThrow();
        });

        it('应该处理复杂的activeWhen条件', () => {
            const complexActiveWhen = [
                '/path1',
                '/path2',
                (location: Location) => location.pathname.startsWith('/dynamic'),
                { path: '/object-path', exact: true }
            ];

            const config: AppConfig = {
                name: 'complex-app',
                entry: 'http://localhost:3000',
                container: '#app',
                activeWhen: complexActiveWhen as any
            };

            expect(() => {
                kernel.registerApplication(config);
            }).not.toThrow();
        });
    });
});