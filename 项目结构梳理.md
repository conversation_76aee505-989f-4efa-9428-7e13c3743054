# 微前端 Monorepo 项目架构设计

## 项目概述

基于微内核架构设计的微前端解决方案，采用 monorepo 架构，支持多层沙箱隔离、插件化扩展、多框架适配等特性。

## 核心设计原则

1. **微内核架构**：核心包 <15KB，插件化扩展
2. **多层沙箱隔离**：6种沙箱策略，JavaScript/CSS/HTML 三维隔离
3. **插件系统**：按需加载，智能预加载，支持自定义插件
4. **框架无关**：支持 React/Vue/Angular 等主流框架
5. **一行代码接入**：零配置启动，渐进式迁移
6. **高性能**：应用加载 <500ms，内存占用 <50MB

## 完整目录结构

```
micro-core/
├── packages/                           # 核心包目录
│   ├── core/                          # 核心运行时包 (<15KB)
│   │   ├── src/
│   │   │   ├── index.ts               # 主入口文件
│   │   │   ├── kernel/                # 微内核实现
│   │   │   │   ├── index.ts           # 内核主入口
│   │   │   │   ├── lifecycle.ts       # 生命周期调度器
│   │   │   │   ├── scheduler.ts       # 任务调度器
│   │   │   │   ├── registry.ts        # 应用注册表
│   │   │   │   └── loader.ts          # 资源加载器
│   │   │   ├── plugin/                # 插件管理器
│   │   │   │   ├── index.ts           # 插件管理器主入口
│   │   │   │   ├── manager.ts         # 插件管理器实现
│   │   │   │   ├── registry.ts        # 插件注册表
│   │   │   │   ├── loader.ts          # 插件加载器
│   │   │   │   └── hooks.ts           # 插件钩子系统
│   │   │   ├── application/           # 应用管理
│   │   │   │   ├── index.ts           # 应用管理主入口
│   │   │   │   ├── manager.ts         # 应用管理器
│   │   │   │   ├── state.ts           # 应用状态管理
│   │   │   │   └── router.ts          # 路由管理
│   │   │   ├── resource/              # 资源管理
│   │   │   │   ├── index.ts           # 资源管理主入口
│   │   │   │   ├── loader.ts          # 资源加载器
│   │   │   │   ├── cache.ts           # 资源缓存
│   │   │   │   └── version.ts         # 版本管理
│   │   │   └── types/                 # 核心类型定义
│   │   │       ├── index.ts           # 类型主入口
│   │   │       ├── kernel.ts          # 内核类型
│   │   │       ├── plugin.ts          # 插件类型
│   │   │       ├── application.ts     # 应用类型
│   │   │       └── resource.ts        # 资源类型
│   │   ├── __tests__/                 # 核心包测试
│   │   │   ├── kernel/
│   │   │   │   ├── lifecycle.test.ts
│   │   │   │   ├── scheduler.test.ts
│   │   │   │   ├── registry.test.ts
│   │   │   │   └── loader.test.ts
│   │   │   ├── plugin/
│   │   │   │   ├── manager.test.ts
│   │   │   │   ├── registry.test.ts
│   │   │   │   ├── loader.test.ts
│   │   │   │   └── hooks.test.ts
│   │   │   ├── application/
│   │   │   │   ├── manager.test.ts
│   │   │   │   ├── state.test.ts
│   │   │   │   └── router.test.ts
│   │   │   └── resource/
│   │   │       ├── loader.test.ts
│   │   │       ├── cache.test.ts
│   │   │       └── version.test.ts
│   │   ├── package.json               # 核心包配置
│   │   ├── tsconfig.json              # TypeScript 配置
│   │   ├── vite.config.ts             # Vite 构建配置
│   │   └── README.md                  # 核心包文档
│   │
│   ├── shared/                        # 共享基础设施包
│   │   ├── src/
│   │   │   ├── index.ts               # 共享包主入口
│   │   │   ├── utils/                 # 工具函数库 (9个核心模块)
│   │   │   │   ├── index.ts           # 工具函数主入口
│   │   │   │   ├── dom.ts             # DOM 操作工具
│   │   │   │   ├── event.ts           # 事件处理工具
│   │   │   │   ├── url.ts             # URL 处理工具
│   │   │   │   ├── storage.ts         # 存储工具
│   │   │   │   ├── async.ts           # 异步处理工具
│   │   │   │   ├── validation.ts      # 验证工具
│   │   │   │   ├── performance.ts     # 性能监控工具
│   │   │   │   ├── security.ts        # 安全工具
│   │   │   │   └── logger.ts          # 日志工具
│   │   │   ├── types/                 # 类型定义系统
│   │   │   │   ├── index.ts           # 类型主入口
│   │   │   │   ├── common.ts          # 通用类型
│   │   │   │   ├── sandbox.ts         # 沙箱类型
│   │   │   │   ├── communication.ts   # 通信类型
│   │   │   │   ├── adapter.ts         # 适配器类型
│   │   │   │   ├── plugin.ts          # 插件类型
│   │   │   │   └── error.ts           # 错误类型
│   │   │   ├── constants/             # 常量管理
│   │   │   │   ├── index.ts           # 常量主入口
│   │   │   │   ├── events.ts          # 事件常量
│   │   │   │   ├── status.ts          # 状态常量
│   │   │   │   ├── errors.ts          # 错误常量
│   │   │   │   └── config.ts          # 配置常量
│   │   │   ├── enums/                 # 枚举定义
│   │   │   │   ├── index.ts           # 枚举主入口
│   │   │   │   ├── application.ts     # 应用枚举
│   │   │   │   ├── sandbox.ts         # 沙箱枚举
│   │   │   │   ├── plugin.ts          # 插件枚举
│   │   │   │   └── framework.ts       # 框架枚举
│   │   │   ├── errors/                # 错误处理
│   │   │   │   ├── index.ts           # 错误处理主入口
│   │   │   │   ├── base.ts            # 基础错误类
│   │   │   │   ├── micro-core.ts      # MicroCoreError 标准化错误
│   │   │   │   ├── recovery.ts        # 错误恢复策略
│   │   │   │   └── monitor.ts         # 错误监控上报
│   │   │   └── components/            # 公共组件
│   │   │       ├── index.ts           # 组件主入口
│   │   │       ├── loading.ts         # 加载组件
│   │   │       ├── error-boundary.ts  # 错误边界组件
│   │   │       └── dev-tools.ts       # 开发工具组件
│   │   ├── __tests__/                 # 共享包测试
│   │   │   ├── utils/
│   │   │   │   ├── dom.test.ts
│   │   │   │   ├── event.test.ts
│   │   │   │   ├── url.test.ts
│   │   │   │   ├── storage.test.ts
│   │   │   │   ├── async.test.ts
│   │   │   │   ├── validation.test.ts
│   │   │   │   ├── performance.test.ts
│   │   │   │   ├── security.test.ts
│   │   │   │   └── logger.test.ts
│   │   │   ├── errors/
│   │   │   │   ├── base.test.ts
│   │   │   │   ├── micro-core.test.ts
│   │   │   │   ├── recovery.test.ts
│   │   │   │   └── monitor.test.ts
│   │   │   └── components/
│   │   │       ├── loading.test.ts
│   │   │       ├── error-boundary.test.ts
│   │   │       └── dev-tools.test.ts
│   │   ├── package.json               # 共享包配置
│   │   ├── tsconfig.json              # TypeScript 配置
│   │   ├── vite.config.ts             # Vite 构建配置
│   │   └── README.md                  # 共享包文档
│   │
│   ├── sandbox/                       # 沙箱系统包
│   │   ├── src/
│   │   │   ├── index.ts               # 沙箱主入口
│   │   │   ├── factory/               # 沙箱工厂
│   │   │   │   ├── index.ts           # 工厂主入口
│   │   │   │   ├── sandbox-factory.ts # 沙箱工厂实现
│   │   │   │   └── strategy-selector.ts # 策略选择器
│   │   │   ├── strategies/            # 6种沙箱策略
│   │   │   │   ├── index.ts           # 策略主入口
│   │   │   │   ├── proxy.ts           # Proxy 沙箱策略
│   │   │   │   ├── define-property.ts # DefineProperty 沙箱策略
│   │   │   │   ├── iframe.ts          # iframe 沙箱策略
│   │   │   │   ├── web-component.ts   # WebComponent 沙箱策略
│   │   │   │   ├── namespace.ts       # Namespace 沙箱策略
│   │   │   │   └── federation.ts      # Federation 沙箱策略
│   │   │   ├── manager/               # 沙箱管理器
│   │   │   │   ├── index.ts           # 管理器主入口
│   │   │   │   ├── sandbox-manager.ts # 沙箱管理器实现
│   │   │   │   ├── isolation.ts       # 隔离管理
│   │   │   │   └── performance.ts     # 性能优化
│   │   │   ├── isolation/             # 多层隔离实现
│   │   │   │   ├── index.ts           # 隔离主入口
│   │   │   │   ├── javascript.ts      # JavaScript 隔离
│   │   │   │   ├── css.ts             # CSS 隔离
│   │   │   │   └── global.ts          # 全局变量隔离
│   │   │   └── types/                 # 沙箱类型定义
│   │   │       ├── index.ts           # 类型主入口
│   │   │       ├── sandbox.ts         # 沙箱类型
│   │   │       ├── strategy.ts        # 策略类型
│   │   │       └── isolation.ts       # 隔离类型
│   │   ├── __tests__/                 # 沙箱测试
│   │   │   ├── strategies/
│   │   │   │   ├── proxy.test.ts
│   │   │   │   ├── define-property.test.ts
│   │   │   │   ├── iframe.test.ts
│   │   │   │   ├── web-component.test.ts
│   │   │   │   ├── namespace.test.ts
│   │   │   │   └── federation.test.ts
│   │   │   ├── manager/
│   │   │   │   ├── sandbox-manager.test.ts
│   │   │   │   ├── isolation.test.ts
│   │   │   │   └── performance.test.ts
│   │   │   └── isolation/
│   │   │       ├── javascript.test.ts
│   │   │       ├── css.test.ts
│   │   │       └── global.test.ts
│   │   ├── package.json               # 沙箱包配置
│   │   ├── tsconfig.json              # TypeScript 配置
│   │   ├── vite.config.ts             # Vite 构建配置
│   │   └── README.md                  # 沙箱包文档
│   │
│   ├── plugins/                       # 插件系统包
│   │   ├── src/
│   │   │   ├── index.ts               # 插件系统主入口
│   │   │   ├── base/                  # 插件基础设施
│   │   │   │   ├── index.ts           # 基础设施主入口
│   │   │   │   ├── plugin-base.ts     # 插件基类
│   │   │   │   ├── lifecycle.ts       # 插件生命周期
│   │   │   │   ├── registry.ts        # 插件注册表
│   │   │   │   └── loader.ts          # 插件加载器
│   │   │   ├── core/                  # 核心插件
│   │   │   │   ├── index.ts           # 核心插件主入口
│   │   │   │   ├── router.ts          # 路由插件
│   │   │   │   ├── communication.ts   # 通信插件
│   │   │   │   ├── auth.ts            # 权限插件
│   │   │   │   ├── dev-tools.ts       # 开发工具插件
│   │   │   │   ├── performance.ts     # 性能监控插件
│   │   │   │   └── error-handler.ts   # 错误处理插件
│   │   │   ├── extensions/            # 扩展插件
│   │   │   │   ├── index.ts           # 扩展插件主入口
│   │   │   │   ├── theme.ts           # 主题插件
│   │   │   │   ├── i18n.ts            # 国际化插件
│   │   │   │   ├── analytics.ts       # 分析插件
│   │   │   │   └── cache.ts           # 缓存插件
│   │   │   ├── market/                # 插件市场
│   │   │   │   ├── index.ts           # 插件市场主入口
│   │   │   │   ├── registry.ts        # 市场注册表
│   │   │   │   ├── installer.ts       # 插件安装器
│   │   │   │   └── validator.ts       # 插件验证器
│   │   │   └── types/                 # 插件类型定义
│   │   │       ├── index.ts           # 类型主入口
│   │   │       ├── plugin.ts          # 插件类型
│   │   │       ├── lifecycle.ts       # 生命周期类型
│   │   │       └── market.ts          # 市场类型
│   │   ├── __tests__/                 # 插件测试
│   │   │   ├── base/
│   │   │   │   ├── plugin-base.test.ts
│   │   │   │   ├── lifecycle.test.ts
│   │   │   │   ├── registry.test.ts
│   │   │   │   └── loader.test.ts
│   │   │   ├── core/
│   │   │   │   ├── router.test.ts
│   │   │   │   ├── communication.test.ts
│   │   │   │   ├── auth.test.ts
│   │   │   │   ├── dev-tools.test.ts
│   │   │   │   ├── performance.test.ts
│   │   │   │   └── error-handler.test.ts
│   │   │   ├── extensions/
│   │   │   │   ├── theme.test.ts
│   │   │   │   ├── i18n.test.ts
│   │   │   │   ├── analytics.test.ts
│   │   │   │   └── cache.test.ts
│   │   │   └── market/
│   │   │       ├── registry.test.ts
│   │   │       ├── installer.test.ts
│   │   │       └── validator.test.ts
│   │   ├── package.json               # 插件包配置
│   │   ├── tsconfig.json              # TypeScript 配置
│   │   ├── vite.config.ts             # Vite 构建配置
│   │   └── README.md                  # 插件包文档
│   │
│   ├── communication/                 # 应用间通信包
│   │   ├── src/
│   │   │   ├── index.ts               # 通信主入口
│   │   │   ├── event-bus/             # 事件总线系统
│   │   │   │   ├── index.ts           # 事件总线主入口
│   │   │   │   ├── event-bus.ts       # 事件总线实现
│   │   │   │   ├── subscriber.ts      # 订阅者管理
│   │   │   │   └── publisher.ts       # 发布者管理
│   │   │   ├── message/               # 消息通道
│   │   │   │   ├── index.ts           # 消息通道主入口
│   │   │   │   ├── channel.ts         # 消息通道实现
│   │   │   │   ├── worker.ts          # SharedWorker 通信
│   │   │   │   └── broadcast.ts       # BroadcastChannel 通信
│   │   │   ├── manager/               # 通信管理器
│   │   │   │   ├── index.ts           # 管理器主入口
│   │   │   │   ├── communication-manager.ts # 通信管理器实现
│   │   │   │   ├── state.ts           # 状态管理
│   │   │   │   └── middleware.ts      # 中间件系统
│   │   │   └── types/                 # 通信类型定义
│   │   │       ├── index.ts           # 类型主入口
│   │   │       ├── event.ts           # 事件类型
│   │   │       ├── message.ts         # 消息类型
│   │   │       └── channel.ts         # 通道类型
│   │   ├── __tests__/                 # 通信测试
│   │   │   ├── event-bus/
│   │   │   │   ├── event-bus.test.ts
│   │   │   │   ├── subscriber.test.ts
│   │   │   │   └── publisher.test.ts
│   │   │   ├── message/
│   │   │   │   ├── channel.test.ts
│   │   │   │   ├── worker.test.ts
│   │   │   │   └── broadcast.test.ts
│   │   │   └── manager/
│   │   │       ├── communication-manager.test.ts
│   │   │       ├── state.test.ts
│   │   │       └── middleware.test.ts
│   │   ├── package.json               # 通信包配置
│   │   ├── tsconfig.json              # TypeScript 配置
│   │   ├── vite.config.ts             # Vite 构建配置
│   │   └── README.md                  # 通信包文档
│   │
│   ├── adapters/                      # 框架适配器包
│   │   ├── src/
│   │   │   ├── index.ts               # 适配器主入口
│   │   │   ├── base/                  # 基础适配器抽象
│   │   │   │   ├── index.ts           # 基础适配器主入口
│   │   │   │   ├── adapter-base.ts    # 适配器基类
│   │   │   │   ├── lifecycle.ts       # 适配器生命周期
│   │   │   │   └── factory.ts         # 适配器工厂
│   │   │   ├── frameworks/            # 多框架支持
│   │   │   │   ├── index.ts           # 框架适配器主入口
│   │   │   │   ├── react.ts           # React 适配器
│   │   │   │   ├── vue2.ts            # Vue2 适配器
│   │   │   │   ├── vue3.ts            # Vue3 适配器
│   │   │   │   ├── angular.ts         # Angular 适配器
│   │   │   │   ├── svelte.ts          # Svelte 适配器
│   │   │   │   ├── solid.ts           # Solid 适配器
│   │   │   │   ├── lit.ts             # Lit 适配器
│   │   │   │   ├── html.ts            # HTML 适配器
│   │   │   │   └── vanilla.ts         # 原生 JS 适配器
│   │   │   ├── detector/              # 框架检测器
│   │   │   │   ├── index.ts           # 检测器主入口
│   │   │   │   ├── framework-detector.ts # 框架检测器实现
│   │   │   │   └── version-checker.ts # 版本兼容性检查
│   │   │   └── types/                 # 适配器类型定义
│   │   │       ├── index.ts           # 类型主入口
│   │   │       ├── adapter.ts         # 适配器类型
│   │   │       ├── framework.ts       # 框架类型
│   │   │       └── lifecycle.ts       # 生命周期类型
│   │   ├── __tests__/                 # 适配器测试
│   │   │   ├── base/
│   │   │   │   ├── adapter-base.test.ts
│   │   │   │   ├── lifecycle.test.ts
│   │   │   │   └── factory.test.ts
│   │   │   ├── frameworks/
│   │   │   │   ├── react.test.ts
│   │   │   │   ├── vue2.test.ts
│   │   │   │   ├── vue3.test.ts
│   │   │   │   ├── angular.test.ts
│   │   │   │   ├── svelte.test.ts
│   │   │   │   ├── solid.test.ts
│   │   │   │   ├── lit.test.ts
│   │   │   │   ├── html.test.ts
│   │   │   │   └── vanilla.test.ts
│   │   │   └── detector/
│   │   │       ├── framework-detector.test.ts
│   │   │       └── version-checker.test.ts
│   │   ├── package.json               # 适配器包配置
│   │   ├── tsconfig.json              # TypeScript 配置
│   │   ├── vite.config.ts             # Vite 构建配置
│   │   └── README.md                  # 适配器包文档
│   │
│   ├── builders/                      # 构建工具适配包
│   │   ├── src/
│   │   │   ├── index.ts               # 构建工具主入口
│   │   │   ├── base/                  # 构建器基础抽象
│   │   │   │   ├── index.ts           # 基础构建器主入口
│   │   │   │   ├── builder-base.ts    # 构建器基类
│   │   │   │   ├── config.ts          # 配置管理
│   │   │   │   └── plugin.ts          # 插件系统
│   │   │   ├── tools/                 # 7种构建工具支持
│   │   │   │   ├── index.ts           # 构建工具主入口
│   │   │   │   ├── webpack.ts         # Webpack 适配器
│   │   │   │   ├── rollup.ts          # Rollup 适配器
│   │   │   │   ├── parcel.ts          # Parcel 适配器
│   │   │   │   ├── vite.ts            # Vite 适配器
│   │   │   │   ├── esbuild.ts         # esbuild 适配器
│   │   │   │   ├── rspack.ts          # Rspack 适配器
│   │   │   │   └── turbopack.ts       # Turbopack 适配器
│   │   │   ├── templates/             # 配置模板
│   │   │   │   ├── index.ts           # 模板主入口
│   │   │   │   ├── webpack.config.ts  # Webpack 配置模板
│   │   │   │   ├── rollup.config.ts   # Rollup 配置模板
│   │   │   │   ├── vite.config.ts     # Vite 配置模板
│   │   │   │   └── common.ts          # 通用配置
│   │   │   └── types/                 # 构建器类型定义
│   │   │       ├── index.ts           # 类型主入口
│   │   │       ├── builder.ts         # 构建器类型
│   │   │       ├── config.ts          # 配置类型
│   │   │       └── plugin.ts          # 插件类型
│   │   ├── __tests__/                 # 构建器测试
│   │   │   ├── base/
│   │   │   │   ├── builder-base.test.ts
│   │   │   │   ├── config.test.ts
│   │   │   │   └── plugin.test.ts
│   │   │   ├── tools/
│   │   │   │   ├── webpack.test.ts
│   │   │   │   ├── rollup.test.ts
│   │   │   │   ├── parcel.test.ts
│   │   │   │   ├── vite.test.ts
│   │   │   │   ├── esbuild.test.ts
│   │   │   │   ├── rspack.test.ts
│   │   │   │   └── turbopack.test.ts
│   │   │   └── templates/
│   │   │       ├── webpack.config.test.ts
│   │   │       ├── rollup.config.test.ts
│   │   │       ├── vite.config.test.ts
│   │   │       └── common.test.ts
│   │   ├── package.json               # 构建器包配置
│   │   ├── tsconfig.json              # TypeScript 配置
│   │   ├── vite.config.ts             # Vite 构建配置
│   │   └── README.md                  # 构建器包文档
│   │
│   ├── sidecar/                       # Sidecar 模式包
│   │   ├── src/
│   │   │   ├── index.ts               # Sidecar 主入口
│   │   │   ├── core/                  # Sidecar 核心
│   │   │   │   ├── index.ts           # 核心主入口
│   │   │   │   ├── sidecar.ts         # Sidecar 实现
│   │   │   │   ├── auto-start.ts      # 自动启动
│   │   │   │   └── zero-config.ts     # 零配置启动
│   │   │   ├── discovery/             # 自动发现
│   │   │   │   ├── index.ts           # 发现主入口
│   │   │   │   ├── app-discovery.ts   # 应用发现
│   │   │   │   ├── config-discovery.ts # 配置发现
│   │   │   │   └── route-discovery.ts # 路由发现
│   │   │   ├── migration/             # 渐进式迁移
│   │   │   │   ├── index.ts           # 迁移主入口
│   │   │   │   ├── migration-helper.ts # 迁移助手
│   │   │   │   ├── compatibility.ts   # 兼容性检查
│   │   │   │   └── upgrade.ts         # 升级工具
│   │   │   └── types/                 # Sidecar 类型定义
│   │   │       ├── index.ts           # 类型主入口
│   │   │       ├── sidecar.ts         # Sidecar 类型
│   │   │       ├── discovery.ts       # 发现类型
│   │   │       └── migration.ts       # 迁移类型
│   │   ├── __tests__/                 # Sidecar 测试
│   │   │   ├── core/
│   │   │   │   ├── sidecar.test.ts
│   │   │   │   ├── auto-start.test.ts
│   │   │   │   └── zero-config.test.ts
│   │   │   ├── discovery/
│   │   │   │   ├── app-discovery.test.ts
│   │   │   │   ├── config-discovery.test.ts
│   │   │   │   └── route-discovery.test.ts
│   │   │   └── migration/
│   │   │       ├── migration-helper.test.ts
│   │   │       ├── compatibility.test.ts
│   │   │       └── upgrade.test.ts
│   │   ├── package.json               # Sidecar 包配置
│   │   ├── tsconfig.json              # TypeScript 配置
│   │   ├── vite.config.ts             # Vite 构建配置
│   │   └── README.md                  # Sidecar 包文档
│   │
│   ├── compatibility/                 # 兼容模式包
│   │   ├── src/
│   │   │   ├── index.ts               # 兼容模式主入口
│   │   │   ├── qiankun/               # qiankun 兼容
│   │   │   │   ├── index.ts           # qiankun 主入口
│   │   │   │   ├── adapter.ts         # qiankun 适配器
│   │   │   │   ├── api.ts             # API 兼容层
│   │   │   │   └── migration.ts       # 迁移工具
│   │   │   ├── wujie/                 # wujie 兼容
│   │   │   │   ├── index.ts           # wujie 主入口
│   │   │   │   ├── adapter.ts         # wujie 适配器
│   │   │   │   ├── api.ts             # API 兼容层
│   │   │   │   └── migration.ts       # 迁移工具
│   │   │   ├── micro-app/             # micro-app 兼容
│   │   │   │   ├── index.ts           # micro-app 主入口
│   │   │   │   ├── adapter.ts         # micro-app 适配器
│   │   │   │   ├── api.ts             # API 兼容层
│   │   │   │   └── migration.ts       # 迁移工具
│   │   │   ├── bridge/                # 兼容桥接
│   │   │   │   ├── index.ts           # 桥接主入口
│   │   │   │   ├── api-bridge.ts      # API 桥接
│   │   │   │   ├── lifecycle-bridge.ts # 生命周期桥接
│   │   │   │   └── event-bridge.ts    # 事件桥接
│   │   │   └── types/                 # 兼容类型定义
│   │   │       ├── index.ts           # 类型主入口
│   │   │       ├── qiankun.ts         # qiankun 类型
│   │   │       ├── wujie.ts           # wujie 类型
│   │   │       └── micro-app.ts       # micro-app 类型
│   │   ├── __tests__/                 # 兼容测试
│   │   │   ├── qiankun/
│   │   │   │   ├── adapter.test.ts
│   │   │   │   ├── api.test.ts
│   │   │   │   └── migration.test.ts
│   │   │   ├── wujie/
│   │   │   │   ├── adapter.test.ts
│   │   │   │   ├── api.test.ts
│   │   │   │   └── migration.test.ts
│   │   │   ├── micro-app/
│   │   │   │   ├── adapter.test.ts
│   │   │   │   ├── api.test.ts
│   │   │   │   └── migration.test.ts
│   │   │   └── bridge/
│   │   │       ├── api-bridge.test.ts
│   │   │       ├── lifecycle-bridge.test.ts
│   │   │       └── event-bridge.test.ts
│   │   ├── package.json               # 兼容包配置
│   │   ├── tsconfig.json              # TypeScript 配置
│   │   ├── vite.config.ts             # Vite 构建配置
│   │   └── README.md                  # 兼容包文档
│   │
│   ├── performance/                   # 性能优化包
│   │   ├── src/
│   │   │   ├── index.ts               # 性能优化主入口
│   │   │   ├── monitor/               # 性能监控
│   │   │   │   ├── index.ts           # 监控主入口
│   │   │   │   ├── performance-monitor.ts # 性能监控器
│   │   │   │   ├── metrics.ts         # 指标收集
│   │   │   │   └── reporter.ts        # 数据上报
│   │   │   ├── memory/                # 内存管理
│   │   │   │   ├── index.ts           # 内存管理主入口
│   │   │   │   ├── memory-manager.ts  # 内存管理器
│   │   │   │   ├── leak-detector.ts   # 内存泄漏检测
│   │   │   │   └── gc-optimizer.ts    # 垃圾回收优化
│   │   │   ├── loader/                # 智能加载
│   │   │   │   ├── index.ts           # 加载器主入口
│   │   │   │   ├── preloader.ts       # 智能预加载
│   │   │   │   ├── lazy-loader.ts     # 懒加载
│   │   │   │   ├── worker-loader.ts   # Worker 加载器
│   │   │   │   └── wasm-loader.ts     # WebAssembly 加载器
│   │   │   ├── cache/                 # 缓存策略
│   │   │   │   ├── index.ts           # 缓存主入口
│   │   │   │   ├── cache-manager.ts   # 缓存管理器
│   │   │   │   ├── strategies.ts      # 缓存策略
│   │   │   │   └── storage.ts         # 存储适配
│   │   │   └── types/                 # 性能类型定义
│   │   │       ├── index.ts           # 类型主入口
│   │   │       ├── monitor.ts         # 监控类型
│   │   │       ├── memory.ts          # 内存类型
│   │   │       ├── loader.ts          # 加载器类型
│   │   │       └── cache.ts           # 缓存类型
│   │   ├── __tests__/                 # 性能测试
│   │   │   ├── monitor/
│   │   │   │   ├── performance-monitor.test.ts
│   │   │   │   ├── metrics.test.ts
│   │   │   │   └── reporter.test.ts
│   │   │   ├── memory/
│   │   │   │   ├── memory-manager.test.ts
│   │   │   │   ├── leak-detector.test.ts
│   │   │   │   └── gc-optimizer.test.ts
│   │   │   ├── loader/
│   │   │   │   ├── preloader.test.ts
│   │   │   │   ├── lazy-loader.test.ts
│   │   │   │   ├── worker-loader.test.ts
│   │   │   │   └── wasm-loader.test.ts
│   │   │   └── cache/
│   │   │       ├── cache-manager.test.ts
│   │   │       ├── strategies.test.ts
│   │   │       └── storage.test.ts
│   │   ├── package.json               # 性能包配置
│   │   ├── tsconfig.json              # TypeScript 配置
│   │   ├── vite.config.ts             # Vite 构建配置
│   │   └── README.md                  # 性能包文档
│   │
│   ├── auth/                          # 权限管理包
│   │   ├── src/
│   │   │   ├── index.ts               # 权限管理主入口
│   │   │   ├── core/                  # 权限核心
│   │   │   │   ├── index.ts           # 权限核心主入口
│   │   │   │   ├── auth-manager.ts    # 权限管理器
│   │   │   │   ├── permission.ts      # 权限控制
│   │   │   │   └── role.ts            # 角色管理
│   │   │   ├── validator/             # 分层权限校验
│   │   │   │   ├── index.ts           # 校验器主入口
│   │   │   │   ├── permission-validator.ts # 权限校验器
│   │   │   │   ├── route-guard.ts     # 路由守卫
│   │   │   │   └── resource-guard.ts  # 资源守卫
│   │   │   ├── providers/             # 权限提供者
│   │   │   │   ├── index.ts           # 提供者主入口
│   │   │   │   ├── jwt-provider.ts    # JWT 提供者
│   │   │   │   ├── oauth-provider.ts  # OAuth 提供者
│   │   │   │   └── custom-provider.ts # 自定义提供者
│   │   │   └── types/                 # 权限类型定义
│   │   │       ├── index.ts           # 类型主入口
│   │   │       ├── auth.ts            # 权限类型
│   │   │       ├── permission.ts      # 权限类型
│   │   │       └── provider.ts        # 提供者类型
│   │   ├── __tests__/                 # 权限测试
│   │   │   ├── core/
│   │   │   │   ├── auth-manager.test.ts
│   │   │   │   ├── permission.test.ts
│   │   │   │   └── role.test.ts
│   │   │   ├── validator/
│   │   │   │   ├── permission-validator.test.ts
│   │   │   │   ├── route-guard.test.ts
│   │   │   │   └── resource-guard.test.ts
│   │   │   └── providers/
│   │   │       ├── jwt-provider.test.ts
│   │   │       ├── oauth-provider.test.ts
│   │   │       └── custom-provider.test.ts
│   │   ├── package.json               # 权限包配置
│   │   ├── tsconfig.json              # TypeScript 配置
│   │   ├── vite.config.ts             # Vite 构建配置
│   │   └── README.md                  # 权限包文档
│   │
│   ├── dev-tools/                     # 开发工具包
│   │   ├── src/
│   │   │   ├── index.ts               # 开发工具主入口
│   │   │   ├── debugger/              # 调试器
│   │   │   │   ├── index.ts           # 调试器主入口
│   │   │   │   ├── micro-debugger.ts  # 微前端调试器
│   │   │   │   ├── inspector.ts       # 应用检查器
│   │   │   │   └── console.ts         # 调试控制台
│   │   │   ├── devserver/             # 开发服务器
│   │   │   │   ├── index.ts           # 开发服务器主入口
│   │   │   │   ├── dev-server.ts      # 开发服务器实现
│   │   │   │   ├── hot-reload.ts      # 热更新
│   │   │   │   └── proxy.ts           # 代理配置
│   │   │   ├── panel/                 # 调试面板
│   │   │   │   ├── index.ts           # 面板主入口
│   │   │   │   ├── debug-panel.ts     # 调试面板实现
│   │   │   │   ├── app-tree.ts        # 应用树视图
│   │   │   │   └── performance-panel.ts # 性能面板
│   │   │   └── types/                 # 开发工具类型定义
│   │   │       ├── index.ts           # 类型主入口
│   │   │       ├── debugger.ts        # 调试器类型
│   │   │       ├── devserver.ts       # 开发服务器类型
│   │   │       └── panel.ts           # 面板类型
│   │   ├── __tests__/                 # 开发工具测试
│   │   │   ├── debugger/
│   │   │   │   ├── micro-debugger.test.ts
│   │   │   │   ├── inspector.test.ts
│   │   │   │   └── console.test.ts
│   │   │   ├── devserver/
│   │   │   │   ├── dev-server.test.ts
│   │   │   │   ├── hot-reload.test.ts
│   │   │   │   └── proxy.test.ts
│   │   │   └── panel/
│   │   │       ├── debug-panel.test.ts
│   │   │       ├── app-tree.test.ts
│   │   │       └── performance-panel.test.ts
│   │   ├── package.json               # 开发工具包配置
│   │   ├── tsconfig.json              # TypeScript 配置
│   │   ├── vite.config.ts             # Vite 构建配置
│   │   └── README.md                  # 开发工具包文档
│   │
│   └── resource/                      # 资源管理包
│       ├── src/
│       │   ├── index.ts               # 资源管理主入口
│       │   ├── manager/               # 资源管理器
│       │   │   ├── index.ts           # 管理器主入口
│       │   │   ├── resource-manager.ts # 资源管理器实现
│       │   │   ├── dynamic-import.ts  # 动态导入
│       │   │   └── version-control.ts # 版本控制
│       │   ├── loader/                # 资源加载器
│       │   │   ├── index.ts           # 加载器主入口
│       │   │   ├── es-loader.ts       # ES 模块加载器
│       │   │   ├── umd-loader.ts      # UMD 格式加载器
│       │   │   └── css-loader.ts      # CSS 加载器
│       │   ├── cache/                 # 资源缓存
│       │   │   ├── index.ts           # 缓存主入口
│       │   │   ├── resource-cache.ts  # 资源缓存实现
│       │   │   ├── strategy.ts        # 缓存策略
│       │   │   └── storage.ts         # 存储适配
│       │   └── types/                 # 资源类型定义
│       │       ├── index.ts           # 类型主入口
│       │       ├── resource.ts        # 资源类型
│       │       ├── loader.ts          # 加载器类型
│       │       └── cache.ts           # 缓存类型
│       ├── __tests__/                 # 资源测试
│       │   ├── manager/
│       │   │   ├── resource-manager.test.ts
│       │   │   ├── dynamic-import.test.ts
│       │   │   └── version-control.test.ts
│       │   ├── loader/
│       │   │   ├── es-loader.test.ts
│       │   │   ├── umd-loader.test.ts
│       │   │   └── css-loader.test.ts
│       │   └── cache/
│       │       ├── resource-cache.test.ts
│       │       ├── strategy.test.ts
│       │       └── storage.test.ts
│       ├── package.json               # 资源包配置
│       ├── tsconfig.json              # TypeScript 配置
│       ├── vite.config.ts             # Vite 构建配置
│       └── README.md                  # 资源包文档
│
│   └── cli/                           # CLI 命令行工具包 (基于现有 Vite 项目集成)
│       ├── src/
│       │   ├── index.ts               # CLI 主入口
│       │   ├── commands/              # 命令实现
│       │   │   ├── index.ts           # 命令入口
│       │   │   ├── init.ts            # 初始化微前端命令
│       │   │   ├── add-main.ts        # 添加基座应用配置命令
│       │   │   ├── add-sub.ts         # 添加子应用配置命令
│       │   │   ├── dev.ts             # 开发命令
│       │   │   ├── build.ts           # 构建命令
│       │   │   └── upgrade.ts         # 升级命令
│       │   ├── generators/            # 代码生成器
│       │   │   ├── index.ts           # 生成器入口
│       │   │   ├── main-app.ts        # 基座应用生成器
│       │   │   ├── sub-app.ts         # 子应用生成器
│       │   │   ├── vite-config.ts     # Vite 配置生成器
│       │   │   └── micro-config.ts    # 微前端配置生成器
│       │   ├── detectors/             # 项目检测器
│       │   │   ├── index.ts           # 检测器入口
│       │   │   ├── framework.ts       # 框架检测器
│       │   │   ├── vite.ts            # Vite 项目检测器
│       │   │   └── structure.ts       # 项目结构检测器
│       │   ├── transformers/          # 代码转换器
│       │   │   ├── index.ts           # 转换器入口
│       │   │   ├── package-json.ts    # package.json 转换器
│       │   │   ├── vite-config.ts     # vite.config 转换器
│       │   │   ├── entry-file.ts      # 入口文件转换器
│       │   │   └── router.ts          # 路由配置转换器
│       │   ├── templates/             # 代码模板
│       │   │   ├── index.ts           # 模板入口
│       │   │   ├── main-app/          # 基座应用模板
│       │   │   │   ├── vue3.ts        # Vue3 基座模板
│       │   │   │   ├── react.ts       # React 基座模板
│       │   │   │   └── html.ts        # HTML 基座模板
│       │   │   ├── sub-app/           # 子应用模板
│       │   │   │   ├── vue3.ts        # Vue3 子应用模板
│       │   │   │   ├── vue2.ts        # Vue2 子应用模板
│       │   │   │   ├── react.ts       # React 子应用模板
│       │   │   │   └── html.ts        # HTML 子应用模板
│       │   │   ├── vite-configs/      # Vite 配置模板
│       │   │   │   ├── main-app.ts    # 基座应用 Vite 配置
│       │   │   │   └── sub-app.ts     # 子应用 Vite 配置
│       │   │   └── micro-configs/     # 微前端配置模板
│       │   │       ├── main-app.ts    # 基座应用微前端配置
│       │   │       └── sub-app.ts     # 子应用微前端配置
│       │   ├── utils/                 # CLI 工具函数
│       │   │   ├── index.ts           # 工具函数入口
│       │   │   ├── file.ts            # 文件操作工具
│       │   │   ├── ast.ts             # AST 操作工具
│       │   │   ├── npm.ts             # NPM 操作工具
│       │   │   ├── prompt.ts          # 交互式提示工具
│       │   │   ├── logger.ts          # 日志工具
│       │   │   └── validator.ts       # 验证工具
│       │   ├── config/                # CLI 配置
│       │   │   ├── index.ts           # 配置入口
│       │   │   ├── dependencies.ts    # 依赖配置
│       │   │   ├── versions.ts        # 版本配置
│       │   │   └── frameworks.ts      # 框架配置
│       │   └── types/                 # CLI 类型定义
│       │       ├── index.ts           # 类型入口
│       │       ├── command.ts         # 命令类型
│       │       ├── project.ts         # 项目类型
│       │       └── config.ts          # 配置类型
│       ├── bin/                       # 可执行文件
│       │   └── micro-core.js          # CLI 可执行文件
│       ├── __tests__/                 # CLI 测试
│       │   ├── commands/
│       │   │   ├── init.test.ts
│       │   │   ├── add-main.test.ts
│       │   │   ├── add-sub.test.ts
│       │   │   ├── dev.test.ts
│       │   │   ├── build.test.ts
│       │   │   └── upgrade.test.ts
│       │   ├── generators/
│       │   │   ├── main-app.test.ts
│       │   │   ├── sub-app.test.ts
│       │   │   ├── vite-config.test.ts
│       │   │   └── micro-config.test.ts
│       │   ├── detectors/
│       │   │   ├── framework.test.ts
│       │   │   ├── vite.test.ts
│       │   │   └── structure.test.ts
│       │   ├── transformers/
│       │   │   ├── package-json.test.ts
│       │   │   ├── vite-config.test.ts
│       │   │   ├── entry-file.test.ts
│       │   │   └── router.test.ts
│       │   └── utils/
│       │       ├── file.test.ts
│       │       ├── ast.test.ts
│       │       ├── npm.test.ts
│       │       ├── prompt.test.ts
│       │       ├── logger.test.ts
│       │       └── validator.test.ts
│       ├── package.json               # CLI 包配置
│       ├── tsconfig.json              # TypeScript 配置
│       ├── vite.config.ts             # Vite 构建配置
│       └── README.md                  # CLI 包文档
│
├── examples/                          # 演示项目目录 (Vue3 基座 + 四种子应用)
│   ├── main-app/                      # Vue3 基座应用
│   │   ├── src/
│   │   │   ├── main.ts                # Vue3 基座入口
│   │   │   ├── App.vue                # Vue3 基座组件
│   │   │   ├── router/                # 路由配置
│   │   │   │   ├── index.ts           # 路由主配置
│   │   │   │   └── micro-routes.ts    # 微前端路由配置
│   │   │   ├── components/            # 基座组件
│   │   │   │   ├── Layout.vue         # 布局组件
│   │   │   │   ├── Navigation.vue     # 导航组件
│   │   │   │   └── MicroContainer.vue # 微应用容器组件
│   │   │   ├── stores/                # 状态管理
│   │   │   │   ├── index.ts           # Store 入口
│   │   │   │   └── micro.ts           # 微前端状态管理
│   │   │   └── micro-config.ts        # 微前端基座配置
│   │   ├── public/
│   │   │   └── index.html             # HTML 模板
│   │   ├── package.json               # Vue3 基座配置
│   │   ├── vite.config.ts             # Vite 7.0.6 配置
│   │   ├── vitest.config.ts           # Vitest 3.2.4 配置
│   │   └── tsconfig.json              # TypeScript 配置
│   │
│   ├── sub-react/                     # React 子应用
│   │   ├── src/
│   │   │   ├── index.tsx              # React 子应用入口
│   │   │   ├── App.tsx                # React 主组件
│   │   │   ├── components/            # React 组件
│   │   │   │   ├── Home.tsx           # 首页组件
│   │   │   │   ├── About.tsx          # 关于页组件
│   │   │   │   └── Counter.tsx        # 计数器组件
│   │   │   ├── router/                # React 路由
│   │   │   │   └── index.tsx          # 路由配置
│   │   │   ├── hooks/                 # React Hooks
│   │   │   │   └── useMicroApp.ts     # 微前端 Hook
│   │   │   └── micro.ts               # 微前端生命周期
│   │   ├── public/
│   │   │   └── index.html             # HTML 模板
│   │   ├── package.json               # React 子应用配置
│   │   ├── vite.config.ts             # Vite 7.0.6 配置
│   │   ├── vitest.config.ts           # Vitest 3.2.4 配置
│   │   └── tsconfig.json              # TypeScript 配置
│   │
│   ├── sub-vue2/                      # Vue2 子应用
│   │   ├── src/
│   │   │   ├── main.js                # Vue2 子应用入口
│   │   │   ├── App.vue                # Vue2 主组件
│   │   │   ├── components/            # Vue2 组件
│   │   │   │   ├── Home.vue           # 首页组件
│   │   │   │   ├── About.vue          # 关于页组件
│   │   │   │   └── Counter.vue        # 计数器组件
│   │   │   ├── router/                # Vue2 路由
│   │   │   │   └── index.js           # 路由配置
│   │   │   ├── store/                 # Vuex 状态管理
│   │   │   │   └── index.js           # Store 配置
│   │   │   └── micro.js               # 微前端生命周期
│   │   ├── public/
│   │   │   └── index.html             # HTML 模板
│   │   ├── package.json               # Vue2 子应用配置
│   │   ├── vite.config.js             # Vite 7.0.6 配置
│   │   ├── vitest.config.js           # Vitest 3.2.4 配置
│   │   └── jsconfig.json              # JavaScript 配置
│   │
│   ├── sub-vue3/                      # Vue3 子应用
│   │   ├── src/
│   │   │   ├── main.ts                # Vue3 子应用入口
│   │   │   ├── App.vue                # Vue3 主组件
│   │   │   ├── components/            # Vue3 组件
│   │   │   │   ├── Home.vue           # 首页组件
│   │   │   │   ├── About.vue          # 关于页组件
│   │   │   │   └── Counter.vue        # 计数器组件
│   │   │   ├── router/                # Vue3 路由
│   │   │   │   └── index.ts           # 路由配置
│   │   │   ├── stores/                # Pinia 状态管理
│   │   │   │   └── index.ts           # Store 配置
│   │   │   ├── composables/           # 组合式函数
│   │   │   │   └── useMicroApp.ts     # 微前端组合式函数
│   │   │   └── micro.ts               # 微前端生命周期
│   │   ├── public/
│   │   │   └── index.html             # HTML 模板
│   │   ├── package.json               # Vue3 子应用配置
│   │   ├── vite.config.ts             # Vite 7.0.6 配置
│   │   ├── vitest.config.ts           # Vitest 3.2.4 配置
│   │   └── tsconfig.json              # TypeScript 配置
│   │
│   ├── sub-html/                      # HTML 子应用
│   │   ├── src/
│   │   │   ├── index.js               # HTML 子应用入口
│   │   │   ├── app.js                 # HTML 主逻辑
│   │   │   ├── components/            # HTML 组件
│   │   │   │   ├── home.js            # 首页组件
│   │   │   │   ├── about.js           # 关于页组件
│   │   │   │   └── counter.js         # 计数器组件
│   │   │   ├── router/                # HTML 路由
│   │   │   │   └── index.js           # 路由配置
│   │   │   ├── utils/                 # 工具函数
│   │   │   │   └── micro-helper.js    # 微前端辅助函数
│   │   │   └── micro.js               # 微前端生命周期
│   │   ├── public/
│   │   │   ├── index.html             # HTML 模板
│   │   │   └── styles.css             # 样式文件
│   │   ├── package.json               # HTML 子应用配置
│   │   ├── vite.config.js             # Vite 7.0.6 配置
│   │   └── vitest.config.js           # Vitest 3.2.4 配置
│   │
│   ├── advanced/                      # 高级示例
│   │   ├── plugin-system/             # 插件系统示例
│   │   │   ├── main-app/              # 基座应用（带自定义插件）
│   │   │   ├── custom-plugin/         # 自定义插件开发示例
│   │   │   ├── package.json           # 插件示例配置
│   │   │   └── README.md              # 插件系统示例文档
│   │   ├── sidecar-mode/              # Sidecar 模式示例
│   │   │   ├── legacy-app/            # 遗留应用改造示例
│   │   │   ├── micro-config.ts        # 微前端配置
│   │   │   ├── package.json           # Sidecar 示例配置
│   │   │   └── README.md              # Sidecar 示例文档
│   │   ├── compatibility/             # 兼容模式示例
│   │   │   ├── qiankun-migration/     # qiankun 迁移示例
│   │   │   ├── wujie-migration/       # wujie 迁移示例
│   │   │   ├── micro-app-migration/   # micro-app 迁移示例
│   │   │   ├── package.json           # 兼容示例配置
│   │   │   └── README.md              # 兼容示例文档
│   │   └── performance/               # 性能优化示例
│   │       ├── lazy-loading/          # 懒加载示例
│   │       ├── preloading/            # 预加载示例
│   │       ├── caching/               # 缓存策略示例
│   │       ├── package.json           # 性能示例配置
│   │       └── README.md              # 性能优化示例文档
│   │
│   ├── scripts/                       # 示例项目脚本
│   │   ├── dev.js                     # 开发环境启动脚本
│   │   ├── build.js                   # 构建脚本
│   │   ├── test.js                    # 测试脚本
│   │   └── start-all.js               # 一键启动所有应用脚本
│   │
│   ├── package.json                   # 演示项目根配置
│   └── README.md                      # 演示项目总文档
│
├── docs/                              # VitePress 2.0.0-alpha.8 文档系统
│   ├── .vitepress/                    # VitePress 配置
│   │   ├── config/                    # 配置文件
│   │   │   ├── index.ts               # 主配置文件
│   │   │   ├── zh.ts                  # 中文配置
│   │   │   ├── en.ts                  # 英文配置
│   │   │   ├── shared.ts              # 共享配置
│   │   │   └── theme.ts               # 主题配置
│   │   ├── theme/                     # 自定义主题
│   │   │   ├── index.ts               # 主题入口
│   │   │   ├── components/            # 主题组件
│   │   │   │   ├── Demo.vue           # 演示组件
│   │   │   │   ├── ApiTable.vue       # API 表格组件
│   │   │   │   ├── CodeGroup.vue      # 代码组组件
│   │   │   │   ├── ThemeToggle.vue    # 主题切换组件
│   │   │   │   └── LangToggle.vue     # 语言切换组件
│   │   │   ├── styles/                # 主题样式
│   │   │   │   ├── vars.css           # CSS 变量
│   │   │   │   ├── custom.css         # 自定义样式
│   │   │   │   ├── dark.css           # 深色主题
│   │   │   │   └── light.css          # 浅色主题
│   │   │   └── composables/           # 组合式函数
│   │   │       ├── useTheme.ts        # 主题切换逻辑
│   │   │       └── useLang.ts         # 语言切换逻辑
│   │   └── dist/                      # 构建输出
│   ├── public/                        # 静态资源
│   │   ├── logo.svg                   # Logo 图标
│   │   ├── favicon.ico                # 网站图标
│   │   ├── images/                    # 图片资源
│   │   │   ├── zh/                    # 中文图片
│   │   │   └── en/                    # 英文图片
│   │   └── assets/                    # 其他资源
│   ├── zh/                            # 中文文档
│   │   ├── index.md                   # 中文首页
│   │   ├── guide/                     # 使用指南
│   │   │   ├── index.md               # 指南首页
│   │   │   ├── getting-started.md     # 快速开始
│   │   │   ├── core-concepts.md       # 核心概念
│   │   │   ├── sandbox.md             # 沙箱系统
│   │   │   ├── plugins.md             # 插件系统
│   │   │   ├── communication.md       # 应用间通信
│   │   │   ├── performance.md         # 性能优化
│   │   │   └── best-practices.md      # 最佳实践
│   │   ├── api/                       # API 文档
│   │   │   ├── index.md               # API 首页
│   │   │   ├── core.md                # 核心 API
│   │   │   ├── sandbox.md             # 沙箱 API
│   │   │   ├── plugins.md             # 插件 API
│   │   │   ├── communication.md       # 通信 API
│   │   │   ├── adapters.md            # 适配器 API
│   │   │   └── compatibility.md       # 兼容 API
│   │   ├── examples/                  # 示例文档
│   │   │   ├── index.md               # 示例首页
│   │   │   ├── react.md               # React 示例
│   │   │   ├── vue2.md                # Vue2 示例
│   │   │   ├── vue3.md                # Vue3 示例
│   │   │   ├── html.md                # HTML 示例
│   │   │   ├── advanced.md            # 高级示例
│   │   │   └── migration.md           # 迁移示例
│   │   ├── migration/                 # 迁移指南
│   │   │   ├── index.md               # 迁移首页
│   │   │   ├── from-qiankun.md        # 从 qiankun 迁移
│   │   │   ├── from-wujie.md          # 从 wujie 迁移
│   │   │   └── from-micro-app.md      # 从 micro-app 迁移
│   │   └── cli/                       # CLI 文档
│   │       ├── index.md               # CLI 首页
│   │       ├── create.md              # 创建项目
│   │       ├── commands.md            # 命令参考
│   │       └── config.md              # 配置参考
│   ├── en/                            # 英文文档
│   │   ├── index.md                   # English Homepage
│   │   ├── guide/                     # User Guide
│   │   │   ├── index.md               # Guide Homepage
│   │   │   ├── getting-started.md     # Getting Started
│   │   │   ├── core-concepts.md       # Core Concepts
│   │   │   ├── sandbox.md             # Sandbox System
│   │   │   ├── plugins.md             # Plugin System
│   │   │   ├── communication.md       # Inter-App Communication
│   │   │   ├── performance.md         # Performance Optimization
│   │   │   └── best-practices.md      # Best Practices
│   │   ├── api/                       # API Documentation
│   │   │   ├── index.md               # API Homepage
│   │   │   ├── core.md                # Core API
│   │   │   ├── sandbox.md             # Sandbox API
│   │   │   ├── plugins.md             # Plugin API
│   │   │   ├── communication.md       # Communication API
│   │   │   ├── adapters.md            # Adapter API
│   │   │   └── compatibility.md       # Compatibility API
│   │   ├── examples/                  # Examples Documentation
│   │   │   ├── index.md               # Examples Homepage
│   │   │   ├── react.md               # React Examples
│   │   │   ├── vue2.md                # Vue2 Examples
│   │   │   ├── vue3.md                # Vue3 Examples
│   │   │   ├── html.md                # HTML Examples
│   │   │   ├── advanced.md            # Advanced Examples
│   │   │   └── migration.md           # Migration Examples
│   │   ├── migration/                 # Migration Guide
│   │   │   ├── index.md               # Migration Homepage
│   │   │   ├── from-qiankun.md        # Migrate from qiankun
│   │   │   ├── from-wujie.md          # Migrate from wujie
│   │   │   └── from-micro-app.md      # Migrate from micro-app
│   │   └── cli/                       # CLI Documentation
│   │       ├── index.md               # CLI Homepage
│   │       ├── create.md              # Create Project
│   │       ├── commands.md            # Command Reference
│   │       └── config.md              # Configuration Reference
│   ├── package.json                   # 文档包配置
│   └── README.md                      # 文档说明
│
├── tools/                             # 工具链配置 (仅 Vite + Vitest)
│   ├── vite/                          # Vite 7.0.6 构建工具配置
│   │   ├── config/                    # Vite 配置文件
│   │   │   ├── base.ts                # Vite 基础配置
│   │   │   ├── lib.ts                 # 库构建配置
│   │   │   ├── app.ts                 # 应用构建配置
│   │   │   ├── dev.ts                 # 开发环境配置
│   │   │   └── prod.ts                # 生产环境配置
│   │   ├── plugins/                   # Vite 插件
│   │   │   ├── index.ts               # 插件入口
│   │   │   ├── micro-core.ts          # 微前端插件
│   │   │   ├── typescript.ts          # TypeScript 插件配置
│   │   │   ├── vue.ts                 # Vue 插件配置
│   │   │   └── react.ts               # React 插件配置
│   │   └── utils/                     # Vite 工具函数
│   │       ├── index.ts               # 工具函数入口
│   │       ├── env.ts                 # 环境变量处理
│   │       ├── paths.ts               # 路径处理
│   │       └── externals.ts           # 外部依赖处理
│   └── vitest/                        # Vitest 3.2.4 测试配置
│       ├── config/                    # Vitest 配置文件
│       │   ├── base.ts                # 基础测试配置
│       │   ├── unit.ts                # 单元测试配置
│       │   ├── integration.ts         # 集成测试配置
│       │   ├── e2e.ts                 # E2E 测试配置
│       │   └── coverage.ts            # 覆盖率配置
│       ├── setup/                     # 测试设置
│       │   ├── index.ts               # 测试设置入口
│       │   ├── dom.ts                 # DOM 环境设置
│       │   ├── mocks.ts               # Mock 设置
│       │   └── globals.ts             # 全局设置
│       └── utils/                     # 测试工具函数
│           ├── index.ts               # 工具函数入口
│           ├── helpers.ts             # 测试辅助函数
│           ├── fixtures.ts            # 测试数据
│           └── matchers.ts            # 自定义匹配器
│
├── scripts/                           # 项目脚本
│   ├── bootstrap.ts                   # 项目初始化脚本
│   ├── build.ts                       # 构建脚本
│   ├── dev.ts                         # 开发脚本
│   ├── test.ts                        # 测试脚本
│   ├── lint.ts                        # 代码检查脚本
│   ├── release.ts                     # 发布脚本
│   └── utils/                         # 脚本工具
│       ├── logger.ts                  # 日志工具
│       ├── file.ts                    # 文件工具
│       └── process.ts                 # 进程工具
│
├── .github/                           # GitHub 配置
│   ├── workflows/                     # GitHub Actions
│   │   ├── ci.yml                     # 持续集成
│   │   ├── release.yml                # 发布流程
│   │   └── docs.yml                   # 文档部署
│   ├── ISSUE_TEMPLATE/                # Issue 模板
│   │   ├── bug_report.md              # Bug 报告模板
│   │   ├── feature_request.md         # 功能请求模板
│   │   └── question.md                # 问题模板
│   └── PULL_REQUEST_TEMPLATE.md       # PR 模板
│
├── package.json                       # 根包配置
├── pnpm-workspace.yaml                # pnpm 工作空间配置
├── tsconfig.json                      # 根 TypeScript 配置
├── tsconfig.build.json                # 构建 TypeScript 配置
├── vite.config.ts                     # 根 Vite 配置
├── vitest.config.ts                   # Vitest 配置
├── eslint.config.js                   # ESLint 配置
├── prettier.config.js                 # Prettier 配置
├── .gitignore                         # Git 忽略文件
├── .npmrc                             # npm 配置
├── LICENSE                            # 开源协议
├── README.md                          # 项目说明
├── CHANGELOG.md                       # 更新日志
└── 项目结构梳理.md                     # 本文档
```

## 包依赖关系图

```
                    ┌─────────────────┐
                    │   @micro-core   │
                    │      /core      │ <15KB
                    │  (微内核运行时)   │
                    └─────────┬───────┘
                              │
                    ┌─────────▼───────┐
                    │   @micro-core   │
                    │     /shared     │
                    │   (共享基础设施)  │
                    └─────────┬───────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼───────┐    ┌───────▼───────┐    ┌───────▼───────┐
│ @micro-core   │    │ @micro-core   │    │ @micro-core   │
│   /sandbox    │    │   /plugins    │    │/communication │
│  (沙箱系统)    │    │  (插件系统)    │    │  (应用间通信)  │
└───────────────┘    └───────────────┘    └───────────────┘
        │                     │                     │
        └─────────────────────┼─────────────────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼───────┐    ┌───────▼───────┐    ┌───────▼───────┐
│ @micro-core   │    │ @micro-core   │    │ @micro-core   │
│  /adapters    │    │  /builders    │    │ /performance  │
│ (框架适配器)    │    │ (构建工具适配) │    │  (性能优化)    │
└───────────────┘    └───────────────┘    └───────────────┘
        │                     │                     │
        └─────────────────────┼─────────────────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼───────┐    ┌───────▼───────┐    ┌───────▼───────┐
│ @micro-core   │    │ @micro-core   │    │ @micro-core   │
│   /sidecar    │    │/compatibility │    │    /auth      │
│ (Sidecar模式)  │    │  (兼容模式)    │    │  (权限管理)    │
└───────────────┘    └───────────────┘    └───────────────┘
        │                     │                     │
        └─────────────────────┼─────────────────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼───────┐    ┌───────▼───────┐    ┌───────▼───────┐
│ @micro-core   │    │ @micro-core   │    │ @micro-core   │
│  /dev-tools   │    │   /resource   │    │     /cli      │
│  (开发工具)    │    │  (资源管理)    │    │  (命令行工具)  │
└───────────────┘    └───────────────┘    └───────────────┘
                              │                     │
                              └─────────────────────┘
                                        │
                              ┌─────────▼───────┐
                              │   create-micro  │
                              │      -core      │
                              │  (项目脚手架)    │
                              └─────────────────┘
```

## 架构设计说明

### 1. 微内核架构设计

**核心包 (@micro-core/core)**
- 体积严格控制在 15KB 以内
- 仅包含最小运行时功能：生命周期调度器、插件管理器、应用注册表、资源加载器
- 采用 Tree-shaking 友好的设计，支持按需导入
- 所有扩展功能通过插件系统实现

**共享基础设施 (@micro-core/shared)**
- 9个核心工具模块：DOM操作、事件处理、URL处理、存储、异步处理、验证、性能监控、安全、日志
- 完整的 TypeScript 类型定义系统
- 统一的错误处理机制（MicroCoreError）
- 公共组件和常量管理

### 2. 多层沙箱隔离系统

**6种沙箱策略**
- **Proxy 沙箱**：基于 Proxy 代理的沙箱，性能最优
- **DefineProperty 沙箱**：基于 Object.defineProperty 的沙箱，兼容性好
- **iframe 沙箱**：基于 iframe 的沙箱，隔离性最强
- **WebComponent 沙箱**：基于 Web Components 的沙箱
- **Namespace 沙箱**：基于命名空间的沙箱
- **Federation 沙箱**：基于模块联邦的沙箱

**三维隔离**
- **JavaScript 隔离**：全局变量、函数、对象隔离
- **CSS 隔离**：样式隔离，防止样式污染
- **HTML 隔离**：DOM 隔离，防止 DOM 污染

### 3. 插件系统架构

**插件基础设施**
- 插件基类提供统一的生命周期管理
- 插件注册表管理插件的注册和发现
- 插件加载器支持动态加载和卸载
- 插件钩子系统提供扩展点

**核心插件**
- Router 插件：路由管理
- Communication 插件：应用间通信
- Auth 插件：权限管理
- DevTools 插件：开发工具
- Performance 插件：性能监控
- ErrorHandler 插件：错误处理

**插件市场**
- 插件注册表管理第三方插件
- 插件安装器支持插件的安装和卸载
- 插件验证器确保插件的安全性

### 4. 框架适配器系统

**多框架支持**
- React、Vue2、Vue3、Angular、Svelte、Solid、Lit、HTML、原生 JS
- 统一的适配器接口和生命周期
- 自动框架检测和版本兼容性验证
- 适配器工厂自动创建适配器实例

### 5. 应用间通信系统

**事件总线系统**
- 高性能的发布订阅模式
- 支持事件的订阅、发布、取消订阅
- 事件中间件系统支持事件拦截和转换

**消息通道**
- SharedWorker 通信：跨标签页通信
- BroadcastChannel 通信：同源通信
- PostMessage 通信：跨域通信

### 6. 构建工具适配

**7种构建工具支持**
- Webpack、Rollup、Parcel、Vite、esbuild、Rspack、Turbopack
- 统一的构建器接口和配置管理
- 构建配置模板和插件系统
- 支持自定义构建流程

### 7. Sidecar 模式

**一行代码接入**
```typescript
import { init } from '@micro-core/sidecar';
await init({ autoStart: true });
```

**零配置启动**
- 自动发现应用、配置、路由
- 智能配置生成
- 渐进式迁移支持

### 8. 兼容模式

**完全 API 兼容**
- qiankun 兼容：API 完全兼容，一键迁移
- wujie 兼容：API 完全兼容，一键迁移
- micro-app 兼容：API 完全兼容，一键迁移

**兼容桥接**
- API 桥接：统一不同框架的 API
- 生命周期桥接：统一生命周期管理
- 事件桥接：统一事件系统

### 9. 性能优化

**智能加载**
- 智能预加载：根据用户行为预加载资源
- 懒加载：按需加载应用和资源
- Worker 加载器：后台加载资源
- WebAssembly 加载器：高性能计算

**内存管理**
- 内存监控：实时监控内存使用
- 内存泄漏检测：自动检测内存泄漏
- 垃圾回收优化：优化垃圾回收策略

**缓存策略**
- 多层缓存：内存缓存、本地存储缓存、HTTP 缓存
- 缓存策略：LRU、LFU、TTL 等策略
- 缓存预热：预加载热点资源

### 10. 权限管理

**分层权限校验**
- 权限管理器：统一权限管理
- 权限校验器：多层权限校验
- 路由守卫：路由级权限控制
- 资源守卫：资源级权限控制

**权限提供者**
- JWT 提供者：基于 JWT 的权限验证
- OAuth 提供者：基于 OAuth 的权限验证
- 自定义提供者：支持自定义权限验证

### 11. 开发工具

**调试器**
- 微前端调试器：专门的微前端调试工具
- 应用检查器：应用状态检查
- 调试控制台：集成调试控制台

**开发服务器**
- 集成开发环境：统一的开发环境
- 热更新：实时代码更新
- 代理配置：API 代理和跨域处理

**调试面板**
- 应用树视图：可视化应用结构
- 性能面板：性能监控面板
- 通信面板：应用间通信监控

### 12. 测试支持

**测试框架**
- Vitest 3.2.4：现代化测试框架
- 单元测试：包级别的单元测试
- 集成测试：跨包的集成测试
- E2E 测试：端到端测试

**测试覆盖率**
- 目标覆盖率：90% 以上
- 覆盖率报告：详细的覆盖率报告
- 覆盖率门禁：CI/CD 集成

### 13. CLI 命令行工具

**一键集成工具**
- 基于现有 Vite 项目的微前端集成工具
- 自动检测项目框架类型（React、Vue2、Vue3、HTML）
- 智能转换现有项目为微前端架构
- 支持基座应用和子应用的一键配置

**命令支持**
```bash
# 在现有 Vite 项目中初始化微前端
micro-core init

# 将当前项目配置为基座应用
micro-core add-main

# 将当前项目配置为子应用
micro-core add-sub

# 开发命令
micro-core dev          # 启动开发服务器
micro-core build        # 构建项目
micro-core upgrade      # 升级微前端版本
```

**智能集成特性**
- 自动检测 Vite 项目结构和框架类型
- 智能修改 package.json 添加微前端依赖
- 自动生成或修改 vite.config 配置
- 智能注入微前端生命周期钩子
- 自动配置路由和通信机制
- 保持原有项目结构不变

### 14. 文档系统

**多语言支持**
- 中文文档：`/zh/` 目录
- 英文文档：`/en/` 目录
- 文档内容同步更新
- 语言切换组件

**主题系统**
- 深色主题：`dark.css`
- 浅色主题：`light.css`
- 主题切换组件
- 用户偏好记忆

**文档特性**
- VitePress 2.0.0-alpha.8 构建
- 交互式代码演示
- API 自动生成
- 搜索功能集成

## 技术规范

### 版本信息
- **统一版本号**：0.1.0
- **发布配置**：npm @micro-core 组织
- **作者信息**：Echo <<EMAIL>>
- **仓库地址**：https://github.com/echo008/micro-core.git

### 技术栈
- **构建工具**：统一使用 Vite 7.0.6 + TypeScript 5.7.x
- **测试框架**：统一使用 Vitest 3.2.4
- **文档系统**：VitePress 2.0.0-alpha.8
- **包管理器**：pnpm + monorepo 架构
- **CLI 工具**：基于 `pnpm create vite@latest` 扩展

### 命名规范
- **包名**：@micro-core/[package-name]
- **文件名**：kebab-case（短横线命名）
- **函数名**：camelCase（驼峰命名）
- **变量名**：camelCase（驼峰命名）
- **常量名**：UPPER_SNAKE_CASE（大写下划线）
- **类型名**：PascalCase（帕斯卡命名）

### 代码质量
- **单一职责原则**：每个模块职责单一
- **文件大小限制**：单个文件不超过 500 行
- **函数复杂度**：单个函数不超过 20 行
- **测试覆盖率**：90% 以上测试覆盖率
- **类型安全**：100% TypeScript 类型覆盖

### 性能指标
- **核心包体积**：<15KB
- **应用加载时间**：<500ms
- **内存占用**：<50MB
- **首屏渲染时间**：<1s

## 总结

本架构设计严格基于 `架构特性.md` 文档要求，并根据优化需求完善，实现了：

### 核心特性
1. **微内核架构**：核心包 <15KB，插件化扩展
2. **多层沙箱隔离**：6种沙箱策略，三维隔离
3. **插件系统**：完整的插件生态和扩展机制
4. **框架无关**：支持主流前端框架
5. **一行代码接入**：零配置启动，渐进式迁移
6. **完全兼容**：qiankun、wujie、micro-app 兼容
7. **高性能**：智能加载、内存管理、缓存优化

### 优化特性
8. **统一工具链**：
   - 构建工具：统一使用 Vite 7.0.6
   - 测试框架：统一使用 Vitest 3.2.4
   - 简化配置，提升开发体验

9. **完整示例系统**：
   - Vue3 基座应用：统一的 Vue3 基座，展示微前端容器能力
   - React 子应用：完整的 React 子应用示例
   - Vue2 子应用：完整的 Vue2 子应用示例
   - Vue3 子应用：完整的 Vue3 子应用示例
   - HTML 子应用：完整的原生 HTML 子应用示例

10. **国际化文档系统**：
    - 中英文双语支持：`/zh/` 和 `/en/` 目录
    - 深浅主题切换：支持用户偏好设置
    - 文档内容同步：确保中英文文档一致性

11. **CLI 命令行工具**：
    - 基于现有 Vite 项目的一键集成工具
    - 智能检测项目框架和结构
    - 自动转换现有项目为微前端架构
    - 支持基座应用和子应用的智能配置

### 技术亮点
- **14个核心包**：功能完整，职责清晰
- **Vue3 基座 + 4种子应用**：统一基座，多框架子应用
- **双语文档**：国际化支持，深浅主题切换
- **统一工具链**：仅使用 Vite 7.0.6 + Vitest 3.2.4
- **智能集成 CLI**：基于现有 Vite 项目的一键微前端集成

架构设计确保了系统的可扩展性、可维护性和高性能，为微前端应用提供了完整的解决方案。通过统一的 Vue3 基座 + 多框架子应用的示例架构、简化的 Vite + Vitest 工具链，以及智能的 CLI 集成工具，大幅提升了开发者体验和项目可用性。
