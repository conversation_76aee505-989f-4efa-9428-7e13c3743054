/**
 * 通信系统工厂函数
 *
 * @description 提供便捷的通信组件创建方法
 * <AUTHOR> <<EMAIL>>
 */
import { CommunicationManager } from './communication-manager';
import { EventBus } from './event-bus';
import { MessageChannel } from './message-channel';
import { Protocol } from './protocol';
import { Serializer } from './serializer';
import { StateSync } from './state-sync';
import type { CommunicationConfig, EventBusConfig, MessageChannelConfig, ProtocolConfig, SerializerConfig, StateSyncConfig } from './types';
/**
 * 创建事件总线实例
 */
export declare function createEventBus(config?: EventBusConfig): EventBus;
/**
 * 创建消息通道实例
 */
export declare function createMessageChannel(config?: MessageChannelConfig): MessageChannel;
/**
 * 创建通信管理器实例
 */
export declare function createCommunicationManager(config?: CommunicationConfig): CommunicationManager;
/**
 * 创建状态同步器实例
 */
export declare function createStateSync(config?: StateSyncConfig): StateSync;
/**
 * 创建协议处理器实例
 */
export declare function createProtocol(config?: ProtocolConfig): Protocol;
/**
 * 创建序列化器实例
 */
export declare function createSerializer(config?: SerializerConfig): Serializer;
/**
 * 创建完整的通信系统
 */
export declare function createCommunicationSystem(config?: CommunicationConfig): {
    manager: CommunicationManager;
    eventBus: EventBus;
    messageChannel: MessageChannel;
    stateSync: StateSync;
    protocol: Protocol;
    serializer: Serializer;
};
/**
 * 创建默认通信系统
 * 使用推荐的默认配置
 */
export declare function createDefaultCommunicationSystem(): {
    manager: CommunicationManager;
    eventBus: EventBus;
    messageChannel: MessageChannel;
    stateSync: StateSync;
    protocol: Protocol;
    serializer: Serializer;
};
//# sourceMappingURL=factories.d.ts.map