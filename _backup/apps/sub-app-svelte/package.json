{"name": "sub-app-svelte", "version": "0.1.0", "description": "Micro-Core Svelte 子应用示例", "type": "module", "scripts": {"dev": "vite --port 3006", "build": "tsc && vite build", "preview": "vite preview", "serve": "vite preview --port 3006", "type-check": "tsc --noEmit", "check": "svelte-check --tsconfig ./tsconfig.json", "lint": "eslint src --ext .ts,.svelte", "lint:fix": "eslint src --ext .ts,.svelte --fix", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/adapter-svelte": "workspace:*", "@micro-core/builder-vite": "workspace:*"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^3.0.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-plugin-svelte": "^2.35.0", "svelte": "^4.0.0", "svelte-check": "^3.6.0", "typescript": "^5.3.0", "vite": "^5.4.0", "vitest": "^3.2.4", "@vitest/coverage-v8": "^3.2.4"}, "keywords": ["micro-frontend", "sub-app", "svelte", "vite", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}