/**
 * @fileoverview Parcel Builder Unit Tests
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import type { BaseBuilderConfig, DevServerConfig } from '@micro-core/shared/types';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { ParcelBuilder } from '../../src/parcel-builder';
import type { ParcelBuilderConfig, ParcelBuilderOptions } from '../../src/types';

// Mock Parcel (since it may not be available)
vi.mock('@parcel/core', () => ({
  Parcel: vi.fn()
}), { virtual: true });

// Mock shared utilities
vi.mock('../../../shared', () => ({
  Logger: {
    info: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn()
  },
  PerformanceMonitor: {
    startTimer: vi.fn().mockReturnValue('timer-id'),
    endTimer: vi.fn().mockReturnValue(1200)
  },
  ConfigMerger: {
    deepMerge: vi.fn().mockImplementation((base, override) => ({ ...base, ...override }))
  }
}));

describe('ParcelBuilder', () => {
  let builder: ParcelBuilder;
  let mockConfig: BaseBuilderConfig;

  beforeEach(() => {
    builder = new ParcelBuilder({
      mode: 'development',
      entry: './src/index.html',
      outDir: './dist'
    });

    mockConfig = {
      entry: './src/index.html',
      outDir: './dist',
      mode: 'development'
    };

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Constructor', () => {
    it('should initialize with default options', () => {
      const defaultBuilder = new ParcelBuilder();
      expect(defaultBuilder.name).toBe('parcel');
      expect(defaultBuilder.version).toBe('1.0.0');
    });

    it('should initialize with custom options', () => {
      const options: ParcelBuilderOptions = {
        mode: 'production',
        entry: './custom/index.html',
        outDir: './custom/dist'
      };
      const customBuilder = new ParcelBuilder(options);
      expect(customBuilder.name).toBe('parcel');
      expect(customBuilder.version).toBe('1.0.0');
    });
  });

  describe('createBuilderConfig', () => {
    it('should create basic Parcel configuration', () => {
      const parcelConfig = (builder as any).createBuilderConfig(mockConfig);

      expect(parcelConfig).toMatchObject({
        entries: ['./src/index.html'],
        defaultConfig: expect.any(Object),
        targets: {
          main: {
            distDir: './dist'
          }
        }
      });
    });

    it('should handle production mode configuration', () => {
      const prodConfig = { ...mockConfig, mode: 'production' as const };
      const parcelConfig = (builder as any).createBuilderConfig(prodConfig);

      expect(parcelConfig.mode).toBe('production');
      expect(parcelConfig.defaultConfig.minify).toBe(true);
    });

    it('should handle custom Parcel configuration', () => {
      const configWithCustom: ParcelBuilderConfig = {
        ...mockConfig,
        parcelConfig: {
          targets: {
            modern: {
              distDir: './dist/modern',
              engines: {
                browsers: ['Chrome >= 80']
              }
            }
          }
        }
      };

      const parcelConfig = (builder as any).createBuilderConfig(configWithCustom);
      expect(parcelConfig.targets.modern).toBeDefined();
      expect(parcelConfig.targets.modern.engines.browsers).toEqual(['Chrome >= 80']);
    });

    it('should handle transformers configuration', () => {
      const configWithTransformers: ParcelBuilderConfig = {
        ...mockConfig,
        transformers: {
          '*.vue': ['@parcel/transformer-vue'],
          '*.scss': ['@parcel/transformer-sass']
        }
      };

      const parcelConfig = (builder as any).createBuilderConfig(configWithTransformers);
      expect(parcelConfig.defaultConfig.transformers).toEqual({
        '*.vue': ['@parcel/transformer-vue'],
        '*.scss': ['@parcel/transformer-sass']
      });
    });
  });

  describe('executeBuild', () => {
    it('should execute successful build', async () => {
      const mockBundleGraph = {
        getBundles: () => [
          {
            filePath: '/dist/index.html',
            stats: { size: 1500 },
            type: 'html'
          },
          {
            filePath: '/dist/index.js',
            stats: { size: 2500 },
            type: 'js'
          }
        ]
      };

      const mockParcel = {
        run: vi.fn().mockResolvedValue({
          bundleGraph: mockBundleGraph
        })
      };

      (builder as any).mockParcelInstance = vi.fn().mockReturnValue(mockParcel);

      const parcelConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(parcelConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(2);
      expect(result.stats.duration).toBe(1200);
      expect(result.stats.totalSize).toBe(4000);
    });

    it('should handle build errors', async () => {
      const buildError = new Error('Parcel build failed');
      const mockParcel = {
        run: vi.fn().mockRejectedValue(buildError)
      };

      (builder as any).mockParcelInstance = vi.fn().mockReturnValue(mockParcel);

      const parcelConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(parcelConfig);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].message).toBe('Parcel build failed');
    });

    it('should handle build with warnings', async () => {
      const mockBundleGraph = {
        getBundles: () => [
          {
            filePath: '/dist/index.js',
            stats: { size: 1000 },
            type: 'js'
          }
        ]
      };

      const mockParcel = {
        run: vi.fn().mockResolvedValue({
          bundleGraph: mockBundleGraph,
          warnings: [
            { message: 'Unused import detected' },
            { message: 'Large bundle size' }
          ]
        })
      };

      (builder as any).mockParcelInstance = vi.fn().mockReturnValue(mockParcel);

      const parcelConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(parcelConfig);

      expect(result.success).toBe(true);
      expect(result.warnings).toHaveLength(2);
      expect(result.stats.warnings).toBe(2);
    });
  });

  describe('startDevServer', () => {
    it('should start development server successfully', async () => {
      const mockDevServer = {
        start: vi.fn().mockResolvedValue({
          port: 3000,
          host: 'localhost'
        }),
        stop: vi.fn().mockResolvedValue(undefined)
      };

      (builder as any).mockParcelDevServer = vi.fn().mockReturnValue(mockDevServer);

      const parcelConfig = (builder as any).createBuilderConfig(mockConfig);
      const devServerConfig: DevServerConfig = {
        port: 3000,
        host: 'localhost',
        hot: true
      };

      const server = await (builder as any).startDevServer(parcelConfig, devServerConfig);

      expect(server).toBe(mockDevServer);
      expect(mockDevServer.start).toHaveBeenCalled();
    });

    it('should handle dev server start errors', async () => {
      const serverError = new Error('Failed to start Parcel dev server');
      const mockDevServer = {
        start: vi.fn().mockRejectedValue(serverError)
      };

      (builder as any).mockParcelDevServer = vi.fn().mockReturnValue(mockDevServer);

      const parcelConfig = (builder as any).createBuilderConfig(mockConfig);

      await expect((builder as any).startDevServer(parcelConfig)).rejects.toThrow('Failed to start Parcel dev server');
    });

    it('should use default dev server configuration', async () => {
      const mockDevServer = {
        start: vi.fn().mockResolvedValue({
          port: 1234,
          host: 'localhost'
        })
      };

      (builder as any).mockParcelDevServer = vi.fn().mockReturnValue(mockDevServer);

      const parcelConfig = (builder as any).createBuilderConfig(mockConfig);
      await (builder as any).startDevServer(parcelConfig);

      expect((builder as any).mockParcelDevServer).toHaveBeenCalledWith(
        expect.objectContaining({
          port: 1234,
          host: 'localhost'
        })
      );
    });
  });

  describe('stopDevServer', () => {
    it('should stop development server', async () => {
      const mockServer = {
        stop: vi.fn().mockResolvedValue(undefined)
      };

      await (builder as any).stopDevServer(mockServer);
      expect(mockServer.stop).toHaveBeenCalled();
    });

    it('should handle server stop errors', async () => {
      const mockServer = {
        stop: vi.fn().mockRejectedValue(new Error('Stop failed'))
      };

      await expect((builder as any).stopDevServer(mockServer)).rejects.toThrow('Stop failed');
    });
  });

  describe('Build Integration', () => {
    it('should perform complete build lifecycle', async () => {
      const mockBundleGraph = {
        getBundles: () => [
          {
            filePath: '/dist/index.html',
            stats: { size: 800 },
            type: 'html'
          },
          {
            filePath: '/dist/main.js',
            stats: { size: 3200 },
            type: 'js'
          }
        ]
      };

      const mockParcel = {
        run: vi.fn().mockResolvedValue({
          bundleGraph: mockBundleGraph
        })
      };

      (builder as any).mockParcelInstance = vi.fn().mockReturnValue(mockParcel);

      const result = await builder.build(mockConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(2);
      expect(result.outputs[0].fileName).toBe('index.html');
      expect(result.outputs[1].fileName).toBe('main.js');
      expect(result.stats.totalSize).toBe(4000);
    });

    it('should handle multiple entry points', async () => {
      const multiEntryConfig = {
        ...mockConfig,
        entry: {
          main: './src/index.html',
          admin: './src/admin.html'
        }
      };

      const mockBundleGraph = {
        getBundles: () => [
          {
            filePath: '/dist/index.html',
            stats: { size: 1000 },
            type: 'html'
          },
          {
            filePath: '/dist/admin.html',
            stats: { size: 800 },
            type: 'html'
          },
          {
            filePath: '/dist/main.js',
            stats: { size: 2000 },
            type: 'js'
          }
        ]
      };

      const mockParcel = {
        run: vi.fn().mockResolvedValue({
          bundleGraph: mockBundleGraph
        })
      };

      (builder as any).mockParcelInstance = vi.fn().mockReturnValue(mockParcel);

      const result = await builder.build(multiEntryConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(3);
      expect(result.stats.totalSize).toBe(3800);
    });
  });

  describe('Performance Tests', () => {
    it('should handle large number of assets efficiently', async () => {
      const largeBundles = Array.from({ length: 200 }, (_, i) => ({
        filePath: `/dist/chunk-${i}.js`,
        stats: { size: 1000 },
        type: 'js'
      }));

      const mockBundleGraph = {
        getBundles: () => largeBundles
      };

      const mockParcel = {
        run: vi.fn().mockResolvedValue({
          bundleGraph: mockBundleGraph
        })
      };

      (builder as any).mockParcelInstance = vi.fn().mockReturnValue(mockParcel);

      const startTime = Date.now();
      const parcelConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(parcelConfig);
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(200);
      expect(endTime - startTime).toBeLessThan(2000); // Should complete within 2 seconds
    });

    it('should handle rapid successive builds', async () => {
      const mockBundleGraph = {
        getBundles: () => [
          {
            filePath: '/dist/index.js',
            stats: { size: 1000 },
            type: 'js'
          }
        ]
      };

      const mockParcel = {
        run: vi.fn().mockResolvedValue({
          bundleGraph: mockBundleGraph
        })
      };

      (builder as any).mockParcelInstance = vi.fn().mockReturnValue(mockParcel);

      const promises = Array.from({ length: 3 }, () => builder.build(mockConfig));
      const results = await Promise.all(promises);

      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid configuration gracefully', async () => {
      const invalidConfig = {
        ...mockConfig,
        entry: '', // Invalid empty entry
        outDir: null // Invalid output
      };

      // Should not throw during config creation
      expect(() => (builder as any).createBuilderConfig(invalidConfig)).not.toThrow();
    });

    it('should handle build process interruption', async () => {
      const mockParcel = {
        run: vi.fn().mockImplementation(() => {
          return new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Build interrupted')), 100);
          });
        })
      };

      (builder as any).mockParcelInstance = vi.fn().mockReturnValue(mockParcel);

      const result = await builder.build(mockConfig);
      expect(result.success).toBe(false);
      expect(result.errors[0].message).toBe('Build interrupted');
    });
  });

  describe('Configuration Validation', () => {
    it('should validate entry point configuration', () => {
      const configWithStringEntry = { ...mockConfig, entry: './src/main.html' };
      const parcelConfig = (builder as any).createBuilderConfig(configWithStringEntry);
      expect(parcelConfig.entries).toEqual(['./src/main.html']);
    });

    it('should validate output configuration', () => {
      const configWithCustomOut = { ...mockConfig, outDir: './custom-dist' };
      const parcelConfig = (builder as any).createBuilderConfig(configWithCustomOut);
      expect(parcelConfig.targets.main.distDir).toBe('./custom-dist');
    });

    it('should handle plugins configuration', () => {
      const configWithPlugins: ParcelBuilderConfig = {
        ...mockConfig,
        plugins: ['@parcel/plugin-reporter-bundle-analyzer']
      };

      const parcelConfig = (builder as any).createBuilderConfig(configWithPlugins);
      expect(parcelConfig.defaultConfig.plugins).toEqual(['@parcel/plugin-reporter-bundle-analyzer']);
    });

    it('should handle optimizers configuration', () => {
      const configWithOptimizers: ParcelBuilderConfig = {
        ...mockConfig,
        optimizers: {
          '*.js': ['@parcel/optimizer-terser'],
          '*.css': ['@parcel/optimizer-cssnano']
        }
      };

      const parcelConfig = (builder as any).createBuilderConfig(configWithOptimizers);
      expect(parcelConfig.defaultConfig.optimizers).toEqual({
        '*.js': ['@parcel/optimizer-terser'],
        '*.css': ['@parcel/optimizer-cssnano']
      });
    });
  });

  describe('Asset Processing', () => {
    it('should handle different asset types', async () => {
      const mockBundleGraph = {
        getBundles: () => [
          {
            filePath: '/dist/index.html',
            stats: { size: 500 },
            type: 'html'
          },
          {
            filePath: '/dist/main.js',
            stats: { size: 2000 },
            type: 'js'
          },
          {
            filePath: '/dist/styles.css',
            stats: { size: 800 },
            type: 'css'
          },
          {
            filePath: '/dist/image.png',
            stats: { size: 1200 },
            type: 'png'
          }
        ]
      };

      const mockParcel = {
        run: vi.fn().mockResolvedValue({
          bundleGraph: mockBundleGraph
        })
      };

      (builder as any).mockParcelInstance = vi.fn().mockReturnValue(mockParcel);

      const result = await builder.build(mockConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(4);

      const assetTypes = result.outputs.map(output => {
        if (output.fileName.endsWith('.html')) return 'html';
        if (output.fileName.endsWith('.js')) return 'chunk';
        return 'asset';
      });

      expect(assetTypes).toContain('html');
      expect(assetTypes).toContain('chunk');
      expect(assetTypes).toContain('asset');
    });
  });

  describe('getBuildStatus', () => {
    it('should return current build status', () => {
      const status = builder.getBuildStatus();
      expect(status).toHaveProperty('name', 'parcel');
      expect(status).toHaveProperty('version', '1.0.0');
      expect(status).toHaveProperty('hasActiveParcel', false);
      expect(status).toHaveProperty('isDevServerRunning', false);
    });
  });

  describe('cleanup', () => {
    it('should cleanup resources', async () => {
      await builder.cleanup();
      // Should complete without errors
      expect(true).toBe(true);
    });
  });
});
