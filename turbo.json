{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"outputs": []}, "lint:fix": {"outputs": []}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "test:coverage": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "test:watch": {"cache": false, "persistent": true}, "clean": {"cache": false}}}