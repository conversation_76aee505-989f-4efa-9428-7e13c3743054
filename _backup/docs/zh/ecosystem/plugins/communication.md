# 通信插件

Micro-Core 通信插件提供了强大的应用间通信能力，支持事件总线、状态共享、直接通信等多种通信方式。

## 📋 目录

- [插件概述](#插件概述)
- [安装配置](#安装配置)
- [事件总线](#事件总线)
- [状态管理](#状态管理)
- [直接通信](#直接通信)
- [通信中间件](#通信中间件)
- [调试工具](#调试工具)
- [API 参考](#api-参考)

## 插件概述

### 核心特性

```typescript
// 通信插件特性
const communicationFeatures = {
  // 通信方式
  methods: [
    'EventBus',      // 事件总线
    'GlobalState',   // 全局状态
    'DirectMessage', // 直接消息
    'SharedMemory'   // 共享内存
  ],
  
  // 高级功能
  advanced: [
    '消息中间件',
    '通信加密',
    '消息持久化',
    '通信监控',
    '错误重试',
    '消息队列'
  ],
  
  // 框架集成
  integrations: [
    'React Context',
    'Vue Provide/Inject',
    'Angular Services',
    'RxJS Observable'
  ]
}
```

### 架构设计

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    通信插件架构                                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    通信协调器                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 消息路由器   │  │ 状态管理器   │  │ 中间件管理器         │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    通信层                                   │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 事件总线     │  │ 全局状态     │  │ 直接通信             │ │ │
│  │  │ EventBus    │  │ GlobalState │  │ DirectMessage       │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    应用适配层                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ App A       │  │ App B       │  │ App C               │ │ │
│  │  │ React       │  │ Vue         │  │ Angular             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 安装配置

### 安装插件

```bash
# 安装通信插件
npm install @micro-core/plugin-communication

# 或使用 yarn
yarn add @micro-core/plugin-communication

# 或使用 pnpm
pnpm add @micro-core/plugin-communication
```

### 基础配置

```typescript
import { MicroCore } from '@micro-core/core'
import { CommunicationPlugin } from '@micro-core/plugin-communication'

// 创建通信插件实例
const communicationPlugin = new CommunicationPlugin({
  // 事件总线配置
  eventBus: {
    maxListeners: 100,
    enableWildcard: true,
    namespace: true
  },
  
  // 全局状态配置
  globalState: {
    persistent: true,
    storageKey: 'micro-core-state',
    enableHistory: true
  },
  
  // 直接通信配置
  directMessage: {
    timeout: 5000,
    retry: 3,
    enableEncryption: false
  },
  
  // 中间件配置
  middleware: {
    logger: true,
    validator: true,
    transformer: true
  }
})

// 注册插件
const microCore = new MicroCore()
microCore.use(communicationPlugin)
```

### 高级配置

```typescript
// 高级通信配置
const advancedConfig = {
  // 消息队列配置
  messageQueue: {
    enabled: true,
    maxSize: 1000,
    persistence: 'localStorage',
    flushInterval: 1000
  },
  
  // 通信加密
  encryption: {
    enabled: true,
    algorithm: 'AES-256-GCM',
    keyRotation: true,
    keyRotationInterval: 3600000 // 1小时
  },
  
  // 性能监控
  monitoring: {
    enabled: true,
    metricsInterval: 5000,
    maxMetricsHistory: 100
  },
  
  // 错误处理
  errorHandling: {
    retryAttempts: 3,
    retryDelay: 1000,
    exponentialBackoff: true,
    deadLetterQueue: true
  },
  
  // 调试模式
  debug: {
    enabled: process.env.NODE_ENV === 'development',
    logLevel: 'info',
    enableDevTools: true
  }
}
```

## 事件总线

### 基础事件通信

```typescript
// 事件总线使用示例
import { useEventBus } from '@micro-core/plugin-communication'

// 发送事件
export class UserService {
  private eventBus = useEventBus()
  
  async updateUser(userId: string, userData: any) {
    try {
      const updatedUser = await this.apiClient.updateUser(userId, userData)
      
      // 发送用户更新事件
      this.eventBus.emit('user:updated', {
        userId,
        userData: updatedUser,
        timestamp: Date.now()
      })
      
      return updatedUser
    } catch (error) {
      // 发送错误事件
      this.eventBus.emit('user:update:error', {
        userId,
        error: error.message,
        timestamp: Date.now()
      })
      
      throw error
    }
  }
}

// 监听事件
export class NotificationService {
  private eventBus = useEventBus()
  
  constructor() {
    this.setupEventListeners()
  }
  
  private setupEventListeners() {
    // 监听用户更新事件
    this.eventBus.on('user:updated', this.handleUserUpdated.bind(this))
    
    // 监听错误事件
    this.eventBus.on('user:update:error', this.handleUserUpdateError.bind(this))
    
    // 使用通配符监听所有用户相关事件
    this.eventBus.on('user:*', this.handleUserEvent.bind(this))
  }
  
  private handleUserUpdated(data: any) {
    this.showNotification({
      type: 'success',
      message: `用户 ${data.userId} 信息已更新`,
      duration: 3000
    })
  }
  
  private handleUserUpdateError(data: any) {
    this.showNotification({
      type: 'error',
      message: `用户更新失败: ${data.error}`,
      duration: 5000
    })
  }
  
  private handleUserEvent(eventName: string, data: any) {
    console.log(`用户事件: ${eventName}`, data)
  }
}
```

### 命名空间事件

```typescript
// 命名空间事件管理
class NamespacedEventBus {
  private eventBus = useEventBus()
  private namespace: string
  
  constructor(namespace: string) {
    this.namespace = namespace
  }
  
  // 发送命名空间事件
  emit(event: string, data: any): void {
    const namespacedEvent = `${this.namespace}:${event}`
    this.eventBus.emit(namespacedEvent, {
      namespace: this.namespace,
      event,
      data,
      timestamp: Date.now()
    })
  }
  
  // 监听命名空间事件
  on(event: string, handler: EventHandler): void {
    const namespacedEvent = `${this.namespace}:${event}`
    this.eventBus.on(namespacedEvent, handler)
  }
  
  // 监听所有命名空间事件
  onAll(handler: EventHandler): void {
    this.eventBus.on(`${this.namespace}:*`, handler)
  }
  
  // 移除事件监听
  off(event: string, handler?: EventHandler): void {
    const namespacedEvent = `${this.namespace}:${event}`
    this.eventBus.off(namespacedEvent, handler)
  }
}

// 使用示例
const userEventBus = new NamespacedEventBus('user')
const orderEventBus = new NamespacedEventBus('order')

// 用户模块发送事件
userEventBus.emit('login', { userId: '123', timestamp: Date.now() })

// 订单模块监听用户事件
userEventBus.on('login', (data) => {
  console.log('用户登录:', data)
  // 加载用户相关的订单数据
})
```

### 事件中间件

```typescript
// 事件中间件系统
class EventMiddleware {
  private middlewares: MiddlewareFunction[] = []
  
  // 添加中间件
  use(middleware: MiddlewareFunction): void {
    this.middlewares.push(middleware)
  }
  
  // 执行中间件链
  async execute(context: EventContext, next: NextFunction): Promise<void> {
    let index = 0
    
    const dispatch = async (i: number): Promise<void> => {
      if (i <= index) {
        throw new Error('next() called multiple times')
      }
      
      index = i
      
      const middleware = this.middlewares[i]
      
      if (!middleware) {
        return next()
      }
      
      return middleware(context, () => dispatch(i + 1))
    }
    
    return dispatch(0)
  }
}

// 中间件示例
const loggerMiddleware: MiddlewareFunction = async (context, next) => {
  const start = Date.now()
  
  console.log(`[EventBus] ${context.event} - 开始处理`)
  
  await next()
  
  const duration = Date.now() - start
  console.log(`[EventBus] ${context.event} - 处理完成 (${duration}ms)`)
}

const validatorMiddleware: MiddlewareFunction = async (context, next) => {
  // 验证事件数据
  if (!context.data || typeof context.data !== 'object') {
    throw new Error('Invalid event data')
  }
  
  await next()
}

const transformerMiddleware: MiddlewareFunction = async (context, next) => {
  // 转换事件数据
  if (context.data.timestamp) {
    context.data.formattedTime = new Date(context.data.timestamp).toISOString()
  }
  
  await next()
}

// 注册中间件
const eventMiddleware = new EventMiddleware()
eventMiddleware.use(loggerMiddleware)
eventMiddleware.use(validatorMiddleware)
eventMiddleware.use(transformerMiddleware)
```

## 状态管理

### 全局状态

```typescript
// 全局状态管理
import { useGlobalState } from '@micro-core/plugin-communication'

// 状态定义
interface AppGlobalState {
  user: {
    id: string
    name: string
    email: string
    role: string
  } | null
  theme: 'light' | 'dark'
  language: string
  notifications: Notification[]
  settings: Record<string, any>
}

// 状态管理服务
export class GlobalStateService {
  private globalState = useGlobalState<AppGlobalState>()
  
  constructor() {
    this.initializeState()
    this.setupStateListeners()
  }
  
  // 初始化状态
  private initializeState() {
    this.globalState.setState({
      user: null,
      theme: 'light',
      language: 'zh-CN',
      notifications: [],
      settings: {}
    })
  }
  
  // 设置状态监听
  private setupStateListeners() {
    // 监听用户状态变化
    this.globalState.subscribe('user', (newUser, oldUser) => {
      console.log('用户状态变化:', { newUser, oldUser })
      
      if (newUser && !oldUser) {
        this.onUserLogin(newUser)
      } else if (!newUser && oldUser) {
        this.onUserLogout(oldUser)
      }
    })
    
    // 监听主题变化
    this.globalState.subscribe('theme', (newTheme) => {
      document.documentElement.setAttribute('data-theme', newTheme)
    })
  }
  
  // 用户登录处理
  private onUserLogin(user: any) {
    // 加载用户设置
    this.loadUserSettings(user.id)
    
    // 发送登录事件
    const eventBus = useEventBus()
    eventBus.emit('user:login', { user, timestamp: Date.now() })
  }
  
  // 用户登出处理
  private onUserLogout(user: any) {
    // 清理用户数据
    this.clearUserData()
    
    // 发送登出事件
    const eventBus = useEventBus()
    eventBus.emit('user:logout', { user, timestamp: Date.now() })
  }
  
  // 设置用户信息
  setUser(user: any) {
    this.globalState.setState({ user })
  }
  
  // 获取用户信息
  getUser() {
    return this.globalState.getState().user
  }
  
  // 设置主题
  setTheme(theme: 'light' | 'dark') {
    this.globalState.setState({ theme })
  }
  
  // 添加通知
  addNotification(notification: Notification) {
    const notifications = this.globalState.getState().notifications
    this.globalState.setState({
      notifications: [...notifications, notification]
    })
  }
  
  // 移除通知
  removeNotification(id: string) {
    const notifications = this.globalState.getState().notifications
    this.globalState.setState({
      notifications: notifications.filter(n => n.id !== id)
    })
  }
  
  private loadUserSettings(userId: string) {
    // 加载用户设置逻辑
  }
  
  private clearUserData() {
    // 清理用户数据逻辑
  }
}
```

## API 参考

### 核心 API

```typescript
// 通信插件主要 API
interface CommunicationPlugin {
  // 事件总线
  eventBus: {
    emit(event: string, data: any): void
    on(event: string, handler: EventHandler): void
    off(event: string, handler?: EventHandler): void
    once(event: string, handler: EventHandler): void
  }
  
  // 全局状态
  globalState: {
    setState(state: Partial<any>): void
    getState(): any
    subscribe(key: string, handler: StateChangeHandler): void
    unsubscribe(key: string, handler: StateChangeHandler): void
  }
  
  // 直接通信
  directMessage: {
    send(from: string, to: string, message: any): Promise<any>
    onMessage(appName: string, handler: MessageHandler): void
    offMessage(appName: string, handler: MessageHandler): void
  }
  
  // 中间件
  middleware: {
    use(middleware: CommunicationMiddleware): void
    remove(middleware: CommunicationMiddleware): void
  }
  
  // 监控
  monitor: {
    getEvents(filter?: EventFilter): CommunicationEvent[]
    getStatistics(): CommunicationStatistics
    addListener(listener: MonitorListener): void
    removeListener(listener: MonitorListener): void
  }
}

// 事件处理器类型
type EventHandler = (data: any) => void
type StateChangeHandler = (newValue: any, oldValue: any) => void
type MessageHandler = (message: DirectMessage) => Promise<any> | any

// 通信事件接口
interface CommunicationEvent {
  id: string
  type: string
  from: string
  to: string
  data: any
  timestamp: number
  responseTime?: number
  error?: string
}
```

### 工具函数

```typescript
// 通信工具函数
export const CommunicationUtils = {
  // 创建命名空间事件总线
  createNamespacedEventBus(namespace: string): NamespacedEventBus {
    return new NamespacedEventBus(namespace)
  },
  
  // 创建状态管理器
  createStateManager<T>(initialState: T): StateManager<T> {
    return new StateManager(initialState)
  },
  
  // 创建消息队列
  createMessageQueue(name: string, options?: QueueOptions): MessageQueue {
    return new MessageQueue(name, options)
  },
  
  // 验证消息格式
  validateMessage(message: any): boolean {
    return message && 
           typeof message === 'object' &&
           typeof message.type === 'string' &&
           message.data !== undefined
  },
  
  // 序列化消息
  serializeMessage(message: any): string {
    try {
      return JSON.stringify(message)
    } catch (error) {
      throw new Error('消息序列化失败')
    }
  },
  
  // 反序列化消息
  deserializeMessage(serialized: string): any {
    try {
      return JSON.parse(serialized)
    } catch (error) {
      throw new Error('消息反序列化失败')
    }
  }
}
```

## 最佳实践

### 通信设计原则

```typescript
// 通信设计最佳实践
const communicationBestPractices = {
  // 1. 事件命名规范
  eventNaming: {
    good: 'user:profile:updated',
    bad: 'updateUserProfile'
  },
  
  // 2. 状态结构设计
  stateStructure: {
    good: {
      user: { id: '123', name: 'John' },
      ui: { theme: 'dark', language: 'zh-CN' }
    },
    bad: {
      userId: '123',
      userName: 'John',
      theme: 'dark',
      language: 'zh-CN'
    }
  },
  
  // 3. 错误处理
  errorHandling: {
    good: 'try-catch + 错误事件',
    bad: '忽略错误'
  }
}
```

### 性能优化

```typescript
// 通信性能优化策略
class CommunicationPerformanceOptimizer {
  // 1. 事件防抖
  static debounceEvent(eventBus: EventBus, event: string, delay: number = 300) {
    let timeoutId: NodeJS.Timeout
    
    return (data: any) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        eventBus.emit(event, data)
      }, delay)
    }
  }
  
  // 2. 状态批量更新
  static batchStateUpdates(globalState: GlobalState) {
    const updates: any[] = []
    let updateScheduled = false
    
    return (update: any) => {
      updates.push(update)
      
      if (!updateScheduled) {
        updateScheduled = true
        Promise.resolve().then(() => {
          const mergedUpdate = Object.assign({}, ...updates)
          globalState.setState(mergedUpdate)
          updates.length = 0
          updateScheduled = false
        })
      }
    }
  }
  
  // 3. 消息压缩
  static compressMessage(message: any): string {
    // 实现消息压缩逻辑
    const json = JSON.stringify(message)
    return btoa(json) // 简单示例，实际可使用更好的压缩算法
  }
}
```

## 总结

通信插件提供了完整的应用间通信解决方案：

1. **多种通信方式** - 事件总线、全局状态、直接通信
2. **强大的中间件系统** - 支持日志、验证、加密等中间件
3. **完善的调试工具** - 实时监控和开发者工具
4. **高性能设计** - 支持消息队列、批量更新等优化
5. **框架无关** - 支持 React、Vue、Angular 等主流框架

通过合理使用通信插件，可以构建高效、可维护的微前端应用通信架构。

## 相关链接

- [路由插件](/ecosystem/plugins/router) - 统一路由管理
- [认证插件](/ecosystem/plugins/auth) - 统一认证
- [核心 API](/api/core) - 核心功能 API
- [通信系统 API](/api/communication) - 通信系统详细 API
    })
  }
  
  // 用户登录处理
  private onUserLogin(user: any) {
    // 加载用户设置
    this.loadUserSettings(user.id)
    
    // 发送登录事件
    this.eventBus.emit('user:login', { user, timestamp: Date.now() })
  }
  
  // 用户登出处理
  private onUserLogout(user: any) {
    // 清理用户数据
    this.clearUserData()
    
    // 发送登出事件
    this.eventBus.emit('user:logout', { user, timestamp: Date.now() })
  }
  
  // 设置用户信息
  setUser(user: any) {
    this.globalState.setState({ user })
  }
  
  // 获取用户信息
  getUser() {
    return this.globalState.getState().user
  }
  
  // 设置主题
  setTheme(theme: 'light' | 'dark') {
    this.globalState.setState({ theme })
  }
  
  // 添加通知
  addNotification(notification: Notification) {
    const notifications = this.globalState.getState().notifications
    this.globalState.setState({
      notifications: [...notifications, notification]
    })
  }
  
  // 移除通知
  removeNotification(id: string) {
    const notifications = this.globalState.getState().notifications
    this.globalState.setState({
      notifications: notifications.filter(n => n.id !== id)
    })
  }
}
```

### 状态持久化

```typescript
// 状态持久化管理
class StatePersistence {
  private storage: Storage
  private storageKey: string
  
  constructor(storageKey: string = 'micro-core-state', useSessionStorage = false) {
    this.storageKey = storageKey
    this.storage = useSessionStorage ? sessionStorage : localStorage
  }
  
  // 保存状态
  saveState(state: any): void {
    try {
      const serializedState = JSON.stringify(state)
      this.storage.setItem(this.storageKey, serializedState)
    } catch (error) {
      console.warn('状态保存失败:', error)
    }
  }
  
  // 加载状态
  loadState(): any | null {
    try {
      const serializedState = this.storage.getItem(this.storageKey)
      if (serializedState === null) {
        return null
      }
      return JSON.parse(serializedState)
    } catch (error) {
      console.warn('状态加载失败:', error)
      return null
    }
  }
  
  // 清除状态
  clearState(): void {
    this.storage.removeItem(this.storageKey)
  }
  
  // 状态迁移
  migrateState(currentVersion: number, migrations: StateMigration[]): any {
    const state = this.loadState()
    if (!state || !state.version) {
      return null
    }
    
    let migratedState = state
    
    for (const migration of migrations) {
      if (migration.version > state.version && migration.version <= currentVersion) {
        migratedState = migration.migrate(migratedState)
      }
    }
    
    migratedState.version = currentVersion
    this.saveState(migratedState)
    
    return migratedState
  }
}

interface StateMigration {
  version: number
  migrate: (state: any) => any
}
```

## 直接通信

### 点对点通信

```typescript
// 直接通信管理器
class DirectCommunicationManager {
  private channels: Map<string, MessageChannel> = new Map()
  private messageHandlers: Map<string, MessageHandler[]> = new Map()
  
  // 创建通信通道
  createChannel(appA: string, appB: string): string {
    const channelId = `${appA}-${appB}`
    
    if (!this.channels.has(channelId)) {
      const channel = new MessageChannel()
      this.channels.set(channelId, channel)
      
      // 设置消息处理
      this.setupChannelHandlers(channelId, channel)
    }
    
    return channelId
  }
  
  // 发送直接消息
  sendDirectMessage(
    fromApp: string, 
    toApp: string, 
    message: DirectMessage
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const channelId = this.createChannel(fromApp, toApp)
      const channel = this.channels.get(channelId)!
      
      const messageId = this.generateMessageId()
      const fullMessage: FullDirectMessage = {
        ...message,
        id: messageId,
        from: fromApp,
        to: toApp,
        timestamp: Date.now()
      }
      
      // 设置响应处理
      const timeout = setTimeout(() => {
        reject(new Error('Message timeout'))
      }, 5000)
      
      const responseHandler = (event: MessageEvent) => {
        if (event.data.responseId === messageId) {
          clearTimeout(timeout)
          channel.port1.removeEventListener('message', responseHandler)
          
          if (event.data.error) {
            reject(new Error(event.data.error))
          } else {
            resolve(event.data.result)
          }
        }
      }
      
      channel.port1.addEventListener('message', responseHandler)
      channel.port2.postMessage(fullMessage)
    })
  }
  
  // 监听直接消息
  onDirectMessage(appName: string, handler: DirectMessageHandler): void {
    if (!this.messageHandlers.has(appName)) {
      this.messageHandlers.set(appName, [])
    }
    
    this.messageHandlers.get(appName)!.push(handler)
  }
  
  // 设置通道处理器
  private setupChannelHandlers(channelId: string, channel: MessageChannel): void {
    channel.port2.onmessage = async (event) => {
      const message: FullDirectMessage = event.data
      const handlers = this.messageHandlers.get(message.to) || []
      
      for (const handler of handlers) {
        try {
          const result = await handler(message)
          
          // 发送响应
          channel.port1.postMessage({
            responseId: message.id,
            result
          })
        } catch (error) {
          // 发送错误响应
          channel.port1.postMessage({
            responseId: message.id,
            error: error.message
          })
        }
      }
    }
  }
  
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

interface DirectMessage {
  type: string
  data: any
}

interface FullDirectMessage extends DirectMessage {
  id: string
  from: string
  to: string
  timestamp: number
}

type DirectMessageHandler = (message: FullDirectMessage) => Promise<any> | any
```

### 消息队列

```typescript
// 消息队列管理器
class MessageQueueManager {
  private queues: Map<string, MessageQueue> = new Map()
  private processors: Map<string, QueueProcessor> = new Map()
  
  // 创建消息队列
  createQueue(name: string, options: QueueOptions = {}): MessageQueue {
    if (this.queues.has(name)) {
      return this.queues.get(name)!
    }
    
    const queue = new MessageQueue(name, options)
    this.queues.set(name, queue)
    
    return queue
  }
  
  // 注册队列处理器
  registerProcessor(queueName: string, processor: QueueProcessor): void {
    this.processors.set(queueName, processor)
    
    const queue = this.queues.get(queueName)
    if (queue) {
      this.startProcessing(queue, processor)
    }
  }
  
  // 发送消息到队列
  async enqueue(queueName: string, message: QueueMessage): Promise<void> {
    const queue = this.queues.get(queueName)
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`)
    }
    
    await queue.enqueue(message)
  }
  
  // 开始处理队列
  private async startProcessing(queue: MessageQueue, processor: QueueProcessor): Promise<void> {
    while (true) {
      try {
        const message = await queue.dequeue()
        if (message) {
          await processor.process(message)
        } else {
          // 队列为空，等待一段时间
          await this.sleep(100)
        }
      } catch (error) {
        console.error(`队列处理错误 ${queue.name}:`, error)
        await this.sleep(1000) // 错误后等待更长时间
      }
    }
  }
  
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 消息队列实现
class MessageQueue {
  private messages: QueueMessage[] = []
  private maxSize: number
  private persistent: boolean
  
  constructor(public name: string, options: QueueOptions = {}) {
    this.maxSize = options.maxSize || 1000
    this.persistent = options.persistent || false
    
    if (this.persistent) {
      this.loadFromStorage()
    }
  }
  
  // 入队
  async enqueue(message: QueueMessage): Promise<void> {
    if (this.messages.length >= this.maxSize) {
      throw new Error('Queue is full')
    }
    
    const queueMessage: QueueMessage = {
      ...message,
      id: this.generateId(),
      timestamp: Date.now(),
      attempts: 0
    }
    
    this.messages.push(queueMessage)
    
    if (this.persistent) {
      this.saveToStorage()
    }
  }
  
  // 出队
  async dequeue(): Promise<QueueMessage | null> {
    const message = this.messages.shift()
    
    if (message && this.persistent) {
      this.saveToStorage()
    }
    
    return message || null
  }
  
  // 获取队列大小
  size(): number {
    return this.messages.length
  }
  
  // 清空队列
  clear(): void {
    this.messages = []
    
    if (this.persistent) {
      this.saveToStorage()
    }
  }
  
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem(`queue_${this.name}`)
      if (stored) {
        this.messages = JSON.parse(stored)
      }
    } catch (error) {
      console.warn(`加载队列 ${this.name} 失败:`, error)
    }
  }
  
  private saveToStorage(): void {
    try {
      localStorage.setItem(`queue_${this.name}`, JSON.stringify(this.messages))
    } catch (error) {
      console.warn(`保存队列 ${this.name} 失败:`, error)
    }
  }
  
  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

interface QueueMessage {
  id?: string
  type: string
  data: any
  timestamp?: number
  attempts?: number
  priority?: number
}

interface QueueOptions {
  maxSize?: number
  persistent?: boolean
}

interface QueueProcessor {
  process(message: QueueMessage): Promise<void>
}
```

## 通信中间件

### 中间件系统

```typescript
// 通信中间件管理器
class CommunicationMiddlewareManager {
  private middlewares: CommunicationMiddleware[] = []
  
  // 添加中间件
  use(middleware: CommunicationMiddleware): void {
    this.middlewares.push(middleware)
  }
  
  // 执行中间件链
  async execute(context: CommunicationContext): Promise<void> {
    let index = 0
    
    const dispatch = async (i: number): Promise<void> => {
      if (i <= index) {
        throw new Error('next() called multiple times')
      }
      
      index = i
      
      const middleware = this.middlewares[i]
      
      if (!middleware) {
        return
      }
      
      return middleware.handle(context, () => dispatch(i + 1))
    }
    
    return dispatch(0)
  }
}

// 日志中间件
class LoggerMiddleware implements CommunicationMiddleware {
  async handle(context: CommunicationContext, next: NextFunction): Promise<void> {
    const start = Date.now()
    
    console.log(`[通信] ${context.type} - 开始处理`, {
      from: context.from,
      to: context.to,
      data: context.data
    })
    
    try {
      await next()
      
      const duration = Date.now() - start
      console.log(`[通信] ${context.type} - 处理完成 (${duration}ms)`)
    } catch (error) {
      const duration = Date.now() - start
      console.error(`[通信] ${context.type} - 处理失败 (${duration}ms)`, error)
      throw error
    }
  }
}

// 验证中间件
class ValidationMiddleware implements CommunicationMiddleware {
  private validators: Map<string, Validator> = new Map()
  
  registerValidator(type: string, validator: Validator): void {
    this.validators.set(type, validator)
  }
  
  async handle(context: CommunicationContext, next: NextFunction): Promise<void> {
    const validator = this.validators.get(context.type)
    
    if (validator) {
      const isValid = await validator.validate(context.data)
      
      if (!isValid) {
        throw new Error(`Invalid data for ${context.type}`)
      }
    }
    
    await next()
  }
}

// 加密中间件
class EncryptionMiddleware implements CommunicationMiddleware {
  private encryptionKey: string
  
  constructor(encryptionKey: string) {
    this.encryptionKey = encryptionKey
  }
  
  async handle(context: CommunicationContext, next: NextFunction): Promise<void> {
    // 发送时加密
    if (context.direction === 'outgoing') {
      context.data = await this.encrypt(context.data)
    }
    
    await next()
    
    // 接收时解密
    if (context.direction === 'incoming') {
      context.data = await this.decrypt(context.data)
    }
  }
  
  private async encrypt(data: any): Promise<string> {
    // 实现加密逻辑
    const jsonString = JSON.stringify(data)
    return btoa(jsonString) // 简单的 base64 编码，实际应使用真正的加密
  }
  
  private async decrypt(encryptedData: string): Promise<any> {
    // 实现解密逻辑
    const jsonString = atob(encryptedData)
    return JSON.parse(jsonString)
  }
}

interface CommunicationMiddleware {
  handle(context: CommunicationContext, next: NextFunction): Promise<void>
}

interface CommunicationContext {
  type: string
  from: string
  to: string
  data: any
  direction: 'incoming' | 'outgoing'
  timestamp: number
}

type NextFunction = () => Promise<void>

interface Validator {
  validate(data: any): Promise<boolean> | boolean
}
```

## 调试工具

### 通信监控

```typescript
// 通信监控器
class CommunicationMonitor {
  private events: CommunicationEvent[] = []
  private maxEvents: number = 1000
  private listeners: MonitorListener[] = []
  
  // 记录通信事件
  recordEvent(event: CommunicationEvent): void {
    event.id = this.generateEventId()
    event.timestamp = Date.now()
    
    this.events.unshift(event)
    
    // 限制事件数量
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(0, this.maxEvents)
    }
    
    // 通知监听器
    this.notifyListeners(event)
  }
  
  // 获取事件历史
  getEvents(filter?: EventFilter): CommunicationEvent[] {
    if (!filter) {
      return [...this.events]
    }
    
    return this.events.filter(event => {
      if (filter.type && event.type !== filter.type) {
        return false
      }
      
      if (filter.from && event.from !== filter.from) {
        return false
      }
      
      if (filter.to && event.to !== filter.to) {
        return false
      }
      
      if (filter.timeRange) {
        const eventTime = event.timestamp
        if (eventTime < filter.timeRange.start || eventTime > filter.timeRange.end) {
          return false
        }
      }
      
      return true
    })
  }
  
  // 获取统计信息
  getStatistics(): CommunicationStatistics {
    const stats: CommunicationStatistics = {
      totalEvents: this.events.length,
      eventsByType: {},
      eventsByApp: {},
      averageResponseTime: 0,
      errorRate: 0
    }
    
    let totalResponseTime = 0
    let responseTimeCount = 0
    let errorCount = 0
    
    this.events.forEach(event => {
      // 按类型统计
      stats.eventsByType[event.type] = (stats.eventsByType[event.type] || 0) + 1
      
      // 按应用统计
      stats.eventsByApp[event.from] = (stats.eventsByApp[event.from] || 0) + 1
      
      // 响应时间统计
      if (event.responseTime) {
        totalResponseTime += event.responseTime
        responseTimeCount++
      }
      
      // 错误统计
      if (event.error) {
        errorCount++
      }
    })
    
    // 计算平均响应时间
    if (responseTimeCount > 0) {
      stats.averageResponseTime = totalResponseTime / responseTimeCount
    }
    
    // 计算错误率
    if (this.events.length > 0) {
      stats.errorRate = (errorCount / this.events.length) * 100
    }
    
    return stats
  }
  
  // 添加监听器
  addListener(listener: MonitorListener): void {
    this.listeners.push(listener)
  }
  
  // 移除监听器
  removeListener(listener: MonitorListener): void {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }
  
  // 清除事件历史
  clearEvents(): void {
    this.events = []
  }
  
  private notifyListeners(event: CommunicationEvent): void {
    this.listeners.forEach(listener => {
      try {
        listener(event)
      } catch (error) {
        console.error('监听器错误:', error)
      }
    })
  }
  
  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

interface CommunicationEvent {
  id?: string
  type: string
  from: string
  to: string
  data: any
  timestamp?: number
  responseTime?: number
  error?: string
  success?: boolean
}

interface EventFilter {
  type?: string
  from?: string
  to?: string
  timeRange?: {
    start: number
    end: number
  }
}

interface CommunicationStatistics {
  totalEvents: number
  eventsByType: Record<string, number>
  eventsByApp: Record<string, number>
  averageResponseTime: number
  errorRate: number
}

type MonitorListener = (event: CommunicationEvent) => void
```

### 开发者工具

```typescript
// 通信开发者工具
class CommunicationDevTools {
  private monitor: CommunicationMonitor
  private isEnabled: boolean = false
  
  constructor(monitor: CommunicationMonitor) {
    this.monitor = monitor
    this.setupDevTools()
  }
  
  // 启用开发者工具
  enable(): void {
    this.isEnabled = true
    this.injectDevToolsPanel()
  }
  
  // 禁用开发者工具
  disable(): void {
    this.isEnabled = false
    this.removeDevToolsPanel()
  }
  
  // 注入开发者工具面板
  private injectDevToolsPanel(): void {
    if (typeof window === 'undefined') return
    
    // 创建开发者工具面板
    const panel = document.createElement('div')
    panel.id = 'micro-core-devtools'
    panel.innerHTML = this.getDevToolsHTML()
    
    // 添加样式
    const style = document.createElement('style')
    style.textContent = this.getDevToolsCSS()
    document.head.appendChild(style)
    
    // 添加到页面
    document.body.appendChild(panel)
    
    // 绑定事件
    this.bindDevToolsEvents(panel)
  }
  
  private getDevToolsHTML(): string {
    return `
      <div class="devtools-header">
        <h3>Micro-Core 通信监控</h3>
        <button id="devtools-toggle">最小化</button>
      </div>
      <div class="devtools-content">
        <div class="devtools-tabs">
          <button class="tab-button active" data-tab="events">事件</button>
          <button class="tab-button" data-tab="stats">统计</button>
          <button class="tab-button" data-tab="apps">应用</button>
        </div>
        <div class="devtools-panels">
          <div id="events-panel" class="panel active">
            <div class="panel-controls">
              <button id="clear-events">清除</button>
              <input type="text" id="filter-input" placeholder="过滤事件...">
            </div>
            <div id="events-list"></div>
          </div>
          <div id="stats-panel" class="panel">
            <div id="stats-content"></div>
          </div>
          <div id="apps-panel" class="panel">
            <div id="apps-content"></div>
          </div>
        </div>
      </div>
    `
  }
  
  private getDevToolsCSS(): string {
    return `
      #micro-core-devtools {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 400px;
        height: 300px;
        background: white;
        border: 1px solid #ccc;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        font-family: monospace;
        font-size: 12px;
        z-index: 10000;
        resize: both;
        overflow: hidden;
      }
      
      .devtools-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: #f5f5f5;
        border-bottom: 1px solid #ddd;
      }
      
      .devtools-tabs {
        display: flex;
        background: #f9f9f9;
        border-bottom: 1px solid #ddd;
      }
      
      .tab-button {
        padding: 8px 16px;
        border: none;
        background: none;
        cursor: pointer;
      }
      
      .tab-button.active {
        background: white;
        border-bottom: 2px solid #007acc;
      }
      
      .panel {
        display: none;
        height: 200px;
        overflow-y: auto;
      }
      
      .panel.active {
        display: block;
      }
      
      .panel-controls {
        padding: 8px;
        border-bottom: 1px solid #eee;
      }
      
      #events-list {
        padding: 8px;
      }
      
      .event-item {
        padding: 4px 8px;
        margin: 2px 0;
        border-left: 3px solid #007acc;
        background: #f9f9f9;
        font-size: 11px;
      }
      
      .event-item.error {
        border-left-color: #e74c3c;
        background: #fdf2f2;
      }
    `
  }
  
  private bindDevToolsEvents(panel: HTMLElement): void {
    // 切换面板
    const tabButtons = panel.querySelectorAll('.tab-button')
    tabButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const target = e.target as HTMLElement
        const tabName = target.dataset.tab
        
        // 更新标签状态
        tabButtons.forEach(btn => btn.classList.remove('active'))
        target.classList.add('active')
        
        // 更新面板显示
        const panels = panel.querySelectorAll('.panel')
        panels.forEach(p => p.classList.remove('active'))
        panel.querySelector(`#${tabName}-panel`)?.classList.add('active')
        
        // 更新面板内容
        this.updatePanelContent(tabName)
      })
    })
    
    // 清除事件
    panel.querySelector('#clear-events')?.addEventListener('click', () => {
      this.monitor.clearEvents()
      this.updateEventsPanel()
    })
    
    // 过滤事件
    panel.querySelector('#filter-input')?.addEventListener('input', (e) => {
      const target = e.target as HTMLInputElement
      this.filterEvents(target.value)
    })
    
    // 最小化/最大化
    panel.querySelector('#devtools-toggle')?.addEventListener('click', () => {
      const content = panel.querySelector('.devtools-content') as HTMLElement
      if (content.style.display === 'none') {
        content.style.display = 'block'
      } else {
        content.style.display = 'none'
      }
    })
    
    // 实时更新
    this.monitor.addListener(() => {
      this.updateEventsPanel()
    })
    
    // 初始化内容
    this.updateEventsPanel()
  }
  
  private updatePanelContent(tabName: string): void {
    switch (tabName) {
      case 'events':
        this.updateEventsPanel()
        break
      case 'stats':
        this.updateStatsPanel()
        break
      case 'apps':
        this.updateAppsPanel()
        break
    }
  }
  
  private updateEventsPanel(): void {
    const eventsList = document.querySelector('#events-list')
    if (!eventsList) return
    
    const events = this.monitor.getEvents().slice(0, 50) // 只显示最近50个事件
    
    eventsList.innerHTML = events.map(event => `
      <div class="event-item ${event.error ? 'error' : ''}">
        <div><strong>${event.type}</strong> ${event.from} → ${event.to}</div>
        <div>${new Date(event.timestamp!).toLocaleTimeString()}</div>
        ${event.error ? `<div style="color: red;">错误: ${event.error}</div>` : ''}
      </div>
    `).join('')
  }
  
  private updateStatsPanel(): void {
    const statsContent = document.querySelector('#stats-content')
    if (!statsContent) return
    
    const stats = this.monitor.getStatistics()
    
    statsContent.innerHTML = `
      <div style="padding: 16px;">
        <h4>通信统计</h4>
        <p>总事件数: ${stats.totalEvents}</p>
        <p>平均响应时间: ${stats.averageResponseTime.toFixed(2)}ms</p>
        <p>错误率: ${stats.errorRate.toFixed(2)}%</p>
        
        <h5>按类型统计</h5>
        ${Object.entries(stats.eventsByType).map(([type, count]) => 
          `<p>${type}: ${count}</p>`
        ).join('')}
        
        <h5>按应用统计</h5>
        ${Object.entries(stats.eventsByApp).map(([app, count]) => 
          `<p>${app}: ${count}</p>`
        ).join('')}
      </div>
    `
  }
  
  private updateAppsPanel(): void {
    // 实现应用面板更新逻辑
  }
  
  private filterEvents(filter: string): void {
    // 实现事件过滤逻辑
  }
  
  private removeDevToolsPanel(): void {
    const panel = document.querySelector('#micro-core-devtools')
    if (panel) {
      panel.remove()
    }
  }
}
```

## API 参考

### 核心 API

```typescript
// 通信插件主要 API
interface CommunicationPlugin {
  // 事件总线
  eventBus: {
    emit(event: string, data: any): void
    on(event: string, handler: EventHandler): void
    off(event: string, handler?: EventHandler): void
    once(event: string, handler: EventHandler): void
  }
  
  // 全局状态
  globalState: {
    setState(state: Partial<any>): void
    getState(): any
    subscribe(key: string, handler: StateChangeHandler): void
    unsubscribe(
# 通信插件

Micro-Core 通信插件提供了强大的应用间通信能力，支持事件总线、状态共享、直接通信等多种通信方式。

## 📋 目录

- [插件概述](#插件概述)
- [安装配置](#安装配置)
- [事件总线](#事件总线)
- [状态管理](#状态管理)
- [直接通信](#直接通信)
- [通信中间件](#通信中间件)
- [调试工具](#调试工具)
- [API 参考](#api-参考)

## 插件概述

### 核心特性

```typescript
// 通信插件特性
const communicationFeatures = {
  // 通信方式
  methods: [
    'EventBus',      // 事件总线
    'GlobalState',   // 全局状态
    'DirectMessage', // 直接消息
    'SharedMemory'   // 共享内存
  ],
  
  // 高级功能
  advanced: [
    '消息中间件',
    '通信加密',
    '消息持久化',
    '通信监控',
    '错误重试',
    '消息队列'
  ],
  
  // 框架集成
  integrations: [
    'React Context',
    'Vue Provide/Inject',
    'Angular Services',
    'RxJS Observable'
  ]
}
```

### 架构设计

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    通信插件架构                                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    通信协调器                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 消息路由器   │  │ 状态管理器   │  │ 中间件管理器         │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    通信层                                   │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 事件总线     │  │ 全局状态     │  │ 直接通信             │ │ │
│  │  │ EventBus    │  │ GlobalState │  │ DirectMessage       │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    应用适配层                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ App A       │  │ App B       │  │ App C               │ │ │
│  │  │ React       │  │ Vue         │  │ Angular             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 安装配置

### 安装插件

```bash
# 安装通信插件
npm install @micro-core/plugin-communication

# 或使用 yarn
yarn add @micro-core/plugin-communication

# 或使用 pnpm
pnpm add @micro-core/plugin-communication
```

### 基础配置

```typescript
import { MicroCore } from '@micro-core/core'
import { CommunicationPlugin } from '@micro-core/plugin-communication'

// 创建通信插件实例
const communicationPlugin = new CommunicationPlugin({
  // 事件总线配置
  eventBus: {
    maxListeners: 100,
    enableWildcard: true,
    namespace: true
  },
  
  // 全局状态配置
  globalState: {
    persistent: true,
    storageKey: 'micro-core-state',
    enableHistory: true
  },
  
  // 直接通信配置
  directMessage: {
    timeout: 5000,
    retry: 3,
    enableEncryption: false
  },
  
  // 中间件配置
  middleware: {
    logger: true,
    validator: true,
    transformer: true
  }
})

// 注册插件
const microCore = new MicroCore()
microCore.use(communicationPlugin)
```

### 高级配置

```typescript
// 高级通信配置
const advancedConfig = {
  // 消息队列配置
  messageQueue: {
    enabled: true,
    maxSize: 1000,
    persistence: 'localStorage',
    flushInterval: 1000
  },
  
  // 通信加密
  encryption: {
    enabled: true,
    algorithm: 'AES-256-GCM',
    keyRotation: true,
    keyRotationInterval: 3600000 // 1小时
  },
  
  // 性能监控
  monitoring: {
    enabled: true,
    metricsInterval: 5000,
    maxMetricsHistory: 100
  },
  
  // 错误处理
  errorHandling: {
    retryAttempts: 3,
    retryDelay: 1000,
    exponentialBackoff: true,
    deadLetterQueue: true
  },
  
  // 调试模式
  debug: {
    enabled: process.env.NODE_ENV === 'development',
    logLevel: 'info',
    enableDevTools: true
  }
}
```

## 事件总线

### 基础事件通信

```typescript
// 事件总线使用示例
import { useEventBus } from '@micro-core/plugin-communication'

// 发送事件
export class UserService {
  private eventBus = useEventBus()
  
  async updateUser(userId: string, userData: any) {
    try {
      const updatedUser = await this.apiClient.updateUser(userId, userData)
      
      // 发送用户更新事件
      this.eventBus.emit('user:updated', {
        userId,
        userData: updatedUser,
        timestamp: Date.now()
      })
      
      return updatedUser
    } catch (error) {
      // 发送错误事件
      this.eventBus.emit('user:update:error', {
        userId,
        error: error.message,
        timestamp: Date.now()
      })
      
      throw error
    }
  }
}

// 监听事件
export class NotificationService {
  private eventBus = useEventBus()
  
  constructor() {
    this.setupEventListeners()
  }
  
  private setupEventListeners() {
    // 监听用户更新事件
    this.eventBus.on('user:updated', this.handleUserUpdated.bind(this))
    
    // 监听错误事件
    this.eventBus.on('user:update:error', this.handleUserUpdateError.bind(this))
    
    // 使用通配符监听所有用户相关事件
    this.eventBus.on('user:*', this.handleUserEvent.bind(this))
  }
  
  private handleUserUpdated(data: any) {
    this.showNotification({
      type: 'success',
      message: `用户 ${data.userId} 信息已更新`,
      duration: 3000
    })
  }
  
  private handleUserUpdateError(data: any) {
    this.showNotification({
      type: 'error',
      message: `用户更新失败: ${data.error}`,
      duration: 5000
    })
  }
  
  private handleUserEvent(eventName: string, data: any) {
    console.log(`用户事件: ${eventName}`, data)
  }
}
```

### 命名空间事件

```typescript
// 命名空间事件管理
class NamespacedEventBus {
  private eventBus = useEventBus()
  private namespace: string
  
  constructor(namespace: string) {
    this.namespace = namespace
  }
  
  // 发送命名空间事件
  emit(event: string, data: any): void {
    const namespacedEvent = `${this.namespace}:${event}`
    this.eventBus.emit(namespacedEvent, {
      namespace: this.namespace,
      event,
      data,
      timestamp: Date.now()
    })
  }
  
  // 监听命名空间事件
  on(event: string, handler: EventHandler): void {
    const namespacedEvent = `${this.namespace}:${event}`
    this.eventBus.on(namespacedEvent, handler)
  }
  
  // 监听所有命名空间事件
  onAll(handler: EventHandler): void {
    this.eventBus.on(`${this.namespace}:*`, handler)
  }
  
  // 移除事件监听
  off(event: string, handler?: EventHandler): void {
    const namespacedEvent = `${this.namespace}:${event}`
    this.eventBus.off(namespacedEvent, handler)
  }
}

// 使用示例
const userEventBus = new NamespacedEventBus('user')
const orderEventBus = new NamespacedEventBus('order')

// 用户模块发送事件
userEventBus.emit('login', { userId: '123', timestamp: Date.now() })

// 订单模块监听用户事件
userEventBus.on('login', (data) => {
  console.log('用户登录:', data)
  // 加载用户相关的订单数据
})
```

### 事件中间件

```typescript
// 事件中间件系统
class EventMiddleware {
  private middlewares: MiddlewareFunction[] = []
  
  // 添加中间件
  use(middleware: MiddlewareFunction): void {
    this.middlewares.push(middleware)
  }
  
  // 执行中间件链
  async execute(context: EventContext, next: NextFunction): Promise<void> {
    let index = 0
    
    const dispatch = async (i: number): Promise<void> => {
      if (i <= index) {
        throw new Error('next() called multiple times')
      }
      
      index = i
      
      const middleware = this.middlewares[i]
      
      if (!middleware) {
        return next()
      }
      
      return middleware(context, () => dispatch(i + 1))
    }
    
    return dispatch(0)
  }
}

// 中间件示例
const loggerMiddleware: MiddlewareFunction = async (context, next) => {
  const start = Date.now()
  
  console.log(`[EventBus] ${context.event} - 开始处理`)
  
  await next()
  
  const duration = Date.now() - start
  console.log(`[EventBus] ${context.event} - 处理完成 (${duration}ms)`)
}

const validatorMiddleware: MiddlewareFunction = async (context, next) => {
  // 验证事件数据
  if (!context.data || typeof context.data !== 'object') {
    throw new Error('Invalid event data')
  }
  
  await next()
}

const transformerMiddleware: MiddlewareFunction = async (context, next) => {
  // 转换事件数据
  if (context.data.timestamp) {
    context.data.formattedTime = new Date(context.data.timestamp).toISOString()
  }
  
  await next()
}

// 注册中间件
const eventMiddleware = new EventMiddleware()
eventMiddleware.use(loggerMiddleware)
eventMiddleware.use(validatorMiddleware)
eventMiddleware.use(transformerMiddleware)
```

## 状态管理

### 全局状态

```typescript
// 全局状态管理
import { useGlobalState } from '@micro-core/plugin-communication'

// 状态定义
interface AppGlobalState {
  user: {
    id: string
    name: string
    email: string
    role: string
  } | null
  theme: 'light' | 'dark'
  language: string
  notifications: Notification[]
  settings: Record<string, any>
}

// 状态管理服务
export class GlobalStateService {
  private globalState = useGlobalState<AppGlobalState>()
  
  constructor() {
    this.initializeState()
    this.setupStateListeners()
  }
  
  // 初始化状态
  private initializeState() {
    this.globalState.setState({
      user: null,
      theme: 'light',
      language: 'zh-CN',
      notifications: [],
      settings: {}
    })
  }
  
  // 设置状态监听
  private setupStateListeners() {
    // 监听用户状态变化
    this.globalState.subscribe('user', (newUser, oldUser) => {
      console.log('用户状态变化:', { newUser, oldUser })
      
      if (newUser && !oldUser) {
        this.onUserLogin(newUser)
      } else if (!newUser && oldUser) {
        this.onUserLogout(oldUser)
      }
    })
    
    // 监听主题变化
    this.globalState.subscribe('theme', (newTheme) => {
      document.documentElement.setAttribute('data-theme', newTheme)