/**
 * @fileoverview 异步工具函数
 * @description 提供防抖、节流等异步处理工具
 * <AUTHOR> <<EMAIL>>
 */

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null

    return function (...args: Parameters<T>) {
        if (timeout) {
            clearTimeout(timeout)
        }
        timeout = setTimeout(() => func.apply(this, args), wait)
    }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
    func: T,
    wait: number
): (...args: Parameters<T>) => void {
    let lastTime = 0

    return function (...args: Parameters<T>) {
        const now = Date.now()
        if (now - lastTime >= wait) {
            lastTime = now
            func.apply(this, args)
        }
    }
}

/**
 * 延迟函数
 */
export function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 超时函数
 */
export function timeout<T>(promise: Promise<T>, ms: number): Promise<T> {
    return Promise.race([
        promise,
        new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error(`操作超时: ${ms}ms`)), ms)
        )
    ])
}

/**
 * 重试函数
 */
export async function retry<T>(
    fn: () => Promise<T>,
    options: { maxAttempts: number; delay?: number } = { maxAttempts: 3, delay: 1000 }
): Promise<T> {
    const { maxAttempts, delay = 1000 } = options
    let lastError: any

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await fn()
        } catch (error) {
            lastError = error
            if (attempt === maxAttempts) {
                throw error
            }
            await sleep(delay)
        }
    }

    throw lastError
}

/**
 * 并行执行函数
 */
export async function parallel<T>(
    tasks: Array<() => Promise<T>>,
    concurrency = Infinity
): Promise<T[]> {
    if (concurrency >= tasks.length) {
        return Promise.all(tasks.map(task => task()))
    }

    const results: T[] = []
    const executing: Promise<void>[] = []

    for (let i = 0; i < tasks.length; i++) {
        const task = tasks[i]
        const promise = task().then(result => {
            results[i] = result
        })

        executing.push(promise)

        if (executing.length >= concurrency) {
            await Promise.race(executing)
            const index = executing.findIndex(p => p === promise)
            if (index !== -1) {
                executing.splice(index, 1)
            }
        }
    }

    await Promise.all(executing)
    return results
}

/**
 * 顺序执行函数
 */
export async function sequential<T>(
    tasks: Array<() => Promise<T>>
): Promise<T[]> {
    const results: T[] = []

    for (const task of tasks) {
        const result = await task()
        results.push(result)
    }

    return results
}