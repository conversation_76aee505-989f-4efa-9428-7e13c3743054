<template>
  <div class="not-found">
    <div class="not-found-content">
      <div class="error-icon">🔍</div>
      <h1>404 - 页面未找到</h1>
      <p class="error-message">
        抱歉，您访问的页面不存在或已被移动。
      </p>
      
      <div class="suggestions">
        <h3>您可以尝试：</h3>
        <ul>
          <li>检查 URL 是否正确</li>
          <li>返回首页重新导航</li>
          <li>查看可用的微应用列表</li>
        </ul>
      </div>

      <div class="quick-links">
        <h3>快速链接：</h3>
        <div class="link-grid">
          <router-link to="/" class="quick-link">
            🏠 返回首页
          </router-link>
          <router-link to="/react-app" class="quick-link">
            ⚛️ React 应用
          </router-link>
          <router-link to="/vue3-app" class="quick-link">
            💚 Vue3 应用
          </router-link>
          <router-link to="/vue2-app" class="quick-link">
            🟢 Vue2 应用
          </router-link>
          <router-link to="/angular-app" class="quick-link">
            🅰️ Angular 应用
          </router-link>
          <router-link to="/svelte-app" class="quick-link">
            🔥 Svelte 应用
          </router-link>
          <router-link to="/solid-app" class="quick-link">
            💎 Solid 应用
          </router-link>
          <router-link to="/html-app" class="quick-link">
            📄 HTML 应用
          </router-link>
        </div>
      </div>

      <div class="debug-info" v-if="showDebugInfo">
        <h3>调试信息：</h3>
        <div class="debug-details">
          <div class="debug-item">
            <span class="debug-label">请求路径:</span>
            <span class="debug-value">{{ currentPath }}</span>
          </div>
          <div class="debug-item">
            <span class="debug-label">时间戳:</span>
            <span class="debug-value">{{ timestamp }}</span>
          </div>
          <div class="debug-item">
            <span class="debug-label">用户代理:</span>
            <span class="debug-value">{{ userAgent }}</span>
          </div>
        </div>
      </div>

      <div class="actions">
        <button @click="goBack" class="action-btn primary">
          ← 返回上页
        </button>
        <button @click="goHome" class="action-btn">
          🏠 回到首页
        </button>
        <button @click="toggleDebugInfo" class="action-btn debug">
          🐛 {{ showDebugInfo ? '隐藏' : '显示' }}调试信息
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const showDebugInfo = ref(false)
const timestamp = ref('')

const currentPath = computed(() => route.fullPath)
const userAgent = computed(() => navigator.userAgent)

const toggleDebugInfo = () => {
  showDebugInfo.value = !showDebugInfo.value
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

const goHome = () => {
  router.push('/')
}

onMounted(() => {
  timestamp.value = new Date().toLocaleString()
  
  // 记录 404 错误到控制台
  console.warn(`[404 Error] Page not found: ${currentPath.value}`)
  
  // 在开发环境下自动显示调试信息
  if (import.meta.env.DEV) {
    showDebugInfo.value = true
  }
})
</script>

<style scoped>
.not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 2rem;
}

.not-found-content {
  max-width: 600px;
  text-align: center;
  background: var(--container-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 3rem 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.not-found-content h1 {
  color: var(--text-color);
  margin-bottom: 1rem;
  font-size: 2rem;
}

.error-message {
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.suggestions {
  text-align: left;
  margin-bottom: 2rem;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
}

.suggestions h3 {
  color: var(--text-color);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.suggestions ul {
  color: var(--text-secondary);
  padding-left: 1.5rem;
  line-height: 1.8;
}

.suggestions li {
  margin-bottom: 0.5rem;
}

.quick-links {
  margin-bottom: 2rem;
}

.quick-links h3 {
  color: var(--text-color);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.link-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.quick-link {
  display: block;
  background: var(--button-bg);
  color: var(--button-text);
  text-decoration: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  transition: all 0.3s;
  font-size: 0.9rem;
  text-align: center;
}

.quick-link:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.debug-info {
  text-align: left;
  margin-bottom: 2rem;
  background: #1a1a1a;
  color: #e0e0e0;
  border-radius: 8px;
  padding: 1.5rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.debug-info h3 {
  color: #74c0fc;
  margin-bottom: 1rem;
  font-family: inherit;
}

.debug-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.debug-item {
  display: flex;
  gap: 1rem;
}

.debug-label {
  color: #a78bfa;
  min-width: 100px;
  font-weight: bold;
}

.debug-value {
  color: #6bcf7f;
  word-break: break-all;
  flex: 1;
}

.actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  background: var(--button-bg);
  color: var(--button-text);
  border: 1px solid var(--border-color);
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.action-btn:hover {
  background: var(--button-hover-bg);
  transform: translateY(-1px);
}

.action-btn.primary {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.action-btn.primary:hover {
  background: #369870;
  border-color: #369870;
}

.action-btn.debug {
  background: #6c757d;
  color: white;
  border-color: #6c757d;
}

.action-btn.debug:hover {
  background: #5a6268;
  border-color: #5a6268;
}

@media (max-width: 768px) {
  .not-found {
    padding: 1rem;
  }
  
  .not-found-content {
    padding: 2rem 1rem;
  }
  
  .not-found-content h1 {
    font-size: 1.5rem;
  }
  
  .link-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
  }
  
  .quick-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .actions {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 100%;
    max-width: 200px;
    justify-content: center;
  }
  
  .debug-item {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .debug-label {
    min-width: auto;
  }
}
</style>