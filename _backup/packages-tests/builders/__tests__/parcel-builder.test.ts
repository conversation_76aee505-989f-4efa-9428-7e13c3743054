/**
 * Parcel 构建器测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { ParcelBuilder } from '../builder-parcel/src';
import { BuilderOptions } from '../shared/types';
import * as testUtils from './test-utils';

// 模拟 parcel 依赖
vi.mock('@parcel/core', () => {
    return {
        default: class MockParcel {
            constructor() { }
            run() {
                return Promise.resolve({
                    bundleGraph: {},
                    buildTime: 100
                });
            }
        }
    };
});

// 模拟 @parcel/config-default
vi.mock('@parcel/config-default', () => ({}));

import Parcel from '@parcel/core';

describe('ParcelBuilder', () => {
    let builder: ParcelBuilder;
    let mockOptions: BuilderOptions;

    beforeEach(() => {
        // 重置所有模拟
        vi.resetAllMocks();

        // 创建模拟选项
        mockOptions = {
            entry: './src/index.html',
            output: {
                path: './dist'
            },
            mode: 'development'
        };

        // 创建构建器实例
        builder = new ParcelBuilder();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('构建配置', () => {
        it('应该能够创建有效的 Parcel 配置', () => {
            const config = builder.createConfig(mockOptions);
            expect(config).toBeDefined();
            expect(config.entries).toContain(mockOptions.entry);
            expect(config.defaultConfig).toBeDefined();
            expect(config.mode).toBe(mockOptions.mode);
        });

        it('应该能够处理自定义配置', () => {
            const customOptions = {
                ...mockOptions,
                customConfig: {
                    shouldDisableCache: true
                }
            };

            const config = builder.createConfig(customOptions);
            expect(config.shouldDisableCache).toBe(true);
        });

        it('应该能够处理多入口配置', () => {
            const multiEntryOptions = {
                ...mockOptions,
                entry: ['./src/index.html', './src/about.html']
            };

            const config = builder.createConfig(multiEntryOptions);
            expect(Array.isArray(config.entries)).toBe(true);
            expect(config.entries.length).toBe(2);
        });
    });

    describe('构建过程', () => {
        it('应该能够执行构建', async () => {
            const runSpy = vi.spyOn(Parcel.prototype, 'run');
            await builder.build(mockOptions);
            expect(runSpy).toHaveBeenCalled();
        });

        it('应该在构建失败时抛出错误', async () => {
            vi.spyOn(Parcel.prototype, 'run').mockRejectedValue(new Error('构建失败'));
            await expect(builder.build(mockOptions)).rejects.toThrow('构建失败');
        });

        it('应该能够处理构建警告', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });

            vi.spyOn(Parcel.prototype, 'run').mockImplementation(() => {
                console.warn('构建警告');
                return Promise.resolve({
                    bundleGraph: {},
                    buildTime: 100
                });
            });

            await builder.build(mockOptions);
            expect(consoleSpy).toHaveBeenCalled();
        });
    });

    describe('开发服务器', () => {
        it('应该能够启动开发服务器', async () => {
            const serveSpy = vi.spyOn(builder as any, 'startDevServer').mockResolvedValue(undefined);
            await builder.serve(mockOptions);
            expect(serveSpy).toHaveBeenCalled();
        });

        it('应该能够停止开发服务器', async () => {
            const stopSpy = vi.fn().mockResolvedValue(undefined);
            (builder as any).devServer = { stop: stopSpy };

            await builder.stop();
            expect(stopSpy).toHaveBeenCalled();
        });

        it('应该在开发服务器启动失败时抛出错误', async () => {
            vi.spyOn(builder as any, 'startDevServer').mockRejectedValue(new Error('服务器启动失败'));
            await expect(builder.serve(mockOptions)).rejects.toThrow('服务器启动失败');
        });
    });

    describe('构建验证', () => {
        it('应该能够验证构建产物', async () => {
            const validateSpy = vi.spyOn(testUtils, 'validateBuildOutput').mockResolvedValue(true);

            await builder.build(mockOptions);
            expect(validateSpy).toHaveBeenCalled();
        });

        it('应该能够检测构建产物完整性', async () => {
            const checkIntegritySpy = vi.spyOn(testUtils, 'checkBuildIntegrity').mockResolvedValue(true);

            await builder.build(mockOptions);
            expect(checkIntegritySpy).toHaveBeenCalled();
        });
    });

    describe('错误处理', () => {
        it('应该能够处理配置错误', () => {
            const invalidOptions = {
                ...mockOptions,
                entry: undefined
            } as any;

            expect(() => builder.createConfig(invalidOptions)).toThrow();
        });

        it('应该能够处理构建过程错误', async () => {
            vi.spyOn(Parcel.prototype, 'run').mockRejectedValue(new Error('构建过程错误'));
            await expect(builder.build(mockOptions)).rejects.toThrow('构建过程错误');
        });

        it('应该能够处理开发服务器错误', async () => {
            vi.spyOn(builder as any, 'startDevServer').mockRejectedValue(new Error('服务器错误'));
            await expect(builder.serve(mockOptions)).rejects.toThrow('服务器错误');
        });
    });

    describe('HMR 支持', () => {
        it('应该能够配置热模块替换', () => {
            const hmrOptions = {
                ...mockOptions,
                devServer: {
                    hmr: true
                }
            };

            const config = builder.createConfig(hmrOptions);
            expect(config.hmrOptions).toBeDefined();
        });
    });

    describe('缓存管理', () => {
        it('应该能够配置缓存选项', () => {
            const cacheOptions = {
                ...mockOptions,
                cache: {
                    enabled: false
                }
            };

            const config = builder.createConfig(cacheOptions);
            expect(config.shouldDisableCache).toBe(true);
        });

        it('应该能够清除缓存', async () => {
            const clearCacheSpy = vi.spyOn(builder as any, 'clearCache').mockResolvedValue(undefined);

            await builder.build({
                ...mockOptions,
                cache: {
                    clear: true
                }
            });

            expect(clearCacheSpy).toHaveBeenCalled();
        });
    });

    describe('构建优化', () => {
        it('应该能够配置构建优化选项', () => {
            const optimizeOptions = {
                ...mockOptions,
                optimize: {
                    minify: true,
                    scopeHoist: true
                }
            };

            const config = builder.createConfig(optimizeOptions);
            expect(config.minify).toBe(true);
            expect(config.scopeHoist).toBe(true);
        });

        it('应该能够配置目标环境', () => {
            const targetOptions = {
                ...mockOptions,
                targets: {
                    browsers: ['last 2 versions']
                }
            };

            const config = builder.createConfig(targetOptions);
            expect(config.targets).toBeDefined();
            expect(config.targets.browsers).toEqual(['last 2 versions']);
        });
    });
});