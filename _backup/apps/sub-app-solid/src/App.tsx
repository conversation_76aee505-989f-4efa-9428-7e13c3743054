import { createSignal, onMount } from 'solid-js';
import './App.css';

interface MicroAppProps {
    container?: HTMLElement | string
    basename?: string
    theme?: 'light' | 'dark'
    user?: any
    [key: string]: any
}

interface AppInfo {
    name: string
    framework: string
    version: string
    basename: string
    isMicroFrontend: boolean
}

interface AppProps {
    microAppProps?: MicroAppProps
    appInfo?: AppInfo
}

function App(_props: AppProps = {}) {
    const [count, setCount] = createSignal(0);
    const [currentTime, setCurrentTime] = createSignal('');

    onMount(() => {
        console.log('Solid.js 子应用组件挂载');
        updateTime();
        setInterval(updateTime, 1000);
    });

    const updateTime = () => {
        setCurrentTime(new Date().toLocaleString('zh-CN'));
    };

    const handleClick = () => {
        setCount(count() + 1);
        console.log('Solid.js 子应用按钮点击，当前计数:', count() + 1);
        alert(`Solid.js 子应用功能测试成功！点击次数: ${count() + 1}`);
    };

    return (
        <div class="solid-app">
            <header class="app-header">
                <h1>Solid.js 微前端子应用</h1>
                <p>这是一个基于 Solid.js 的微前端子应用示例</p>
            </header>

            <main class="app-main">
                <section class="feature-section">
                    <h2>功能特性</h2>
                    <ul>
                        <li>✅ 基于 Solid.js 1.8+ 版本</li>
                        <li>✅ 细粒度响应式系统</li>
                        <li>✅ 完整的微前端生命周期管理</li>
                        <li>✅ 高性能渲染和更新</li>
                        <li>✅ TypeScript 严格模式支持</li>
                        <li>✅ 错误边界和优雅降级</li>
                    </ul>
                </section>

                <section class="demo-section">
                    <h2>演示功能</h2>
                    <div class="demo-content">
                        <p>当前时间: <span class="time-display">{currentTime()}</span></p>
                        <button onClick={handleClick} class="demo-button">
                            点击测试 Solid.js 功能
                        </button>
                        <div class="counter">
                            点击次数: <span class="count-display">{count()}</span>
                        </div>
                    </div>
                </section>
            </main>

            <footer class="app-footer">
                <p>Powered by Micro-Core 微前端框架</p>
            </footer>
        </div>
    );
}

export default App;