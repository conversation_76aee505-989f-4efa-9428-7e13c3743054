import React, { useEffect, useState } from 'react';
import { Link, Route, Routes, useLocation } from 'react-router-dom';
import './App.css';

// 首页组件
const HomePage: React.FC = () => {
    const [count, setCount] = useState(0);
    const [message, setMessage] = useState('');

    useEffect(() => {
        setMessage('欢迎使用 React 微前端子应用！');
    }, []);

    return (
        <div className="react-home">
            <div className="hero-section">
                <h1>⚛️ React 微前端子应用</h1>
                <p className="subtitle">{message}</p>

                <div className="counter-section">
                    <h2>计数器演示</h2>
                    <div className="counter">
                        <button onClick={() => setCount(count - 1)}>-</button>
                        <span className="count">{count}</span>
                        <button onClick={() => setCount(count + 1)}>+</button>
                    </div>
                    <button
                        className="reset-btn"
                        onClick={() => setCount(0)}
                    >
                        重置
                    </button>
                </div>

                <div className="features-section">
                    <h2>React 特性展示</h2>
                    <div className="features-grid">
                        <div className="feature-card">
                            <h3>🎣 Hooks</h3>
                            <p>使用 useState 和 useEffect 管理状态和副作用</p>
                        </div>
                        <div className="feature-card">
                            <h3>🧭 路由</h3>
                            <p>React Router 实现单页面应用路由</p>
                        </div>
                        <div className="feature-card">
                            <h3>🎨 样式</h3>
                            <p>CSS Modules 和现代 CSS 特性</p>
                        </div>
                        <div className="feature-card">
                            <h3>⚡ 性能</h3>
                            <p>React 18 并发特性和优化</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

// 关于页面组件
const AboutPage: React.FC = () => {
    const [info, setInfo] = useState<any>(null);

    useEffect(() => {
        setInfo({
            name: 'React 子应用',
            version: '18.2.0',
            framework: 'React',
            buildTool: 'Vite',
            features: [
                'TypeScript 支持',
                'Hot Module Replacement',
                'ES6+ 语法',
                'CSS Modules',
                'React Router',
                'React Hooks'
            ]
        });
    }, []);

    return (
        <div className="react-about">
            <h1>关于 React 子应用</h1>

            {info && (
                <div className="info-grid">
                    <div className="info-card">
                        <h3>基本信息</h3>
                        <ul>
                            <li><strong>应用名称:</strong> {info.name}</li>
                            <li><strong>React 版本:</strong> {info.version}</li>
                            <li><strong>框架:</strong> {info.framework}</li>
                            <li><strong>构建工具:</strong> {info.buildTool}</li>
                        </ul>
                    </div>

                    <div className="info-card">
                        <h3>技术特性</h3>
                        <ul>
                            {info.features.map((feature: string, index: number) => (
                                <li key={index}>✅ {feature}</li>
                            ))}
                        </ul>
                    </div>
                </div>
            )}

            <div className="architecture-section">
                <h2>微前端架构</h2>
                <p>
                    这个 React 子应用是 Micro-Core 微前端架构的一部分，
                    展示了如何将 React 应用集成到微前端系统中。
                </p>

                <div className="architecture-features">
                    <div className="arch-card">
                        <h4>🔄 生命周期管理</h4>
                        <p>支持 bootstrap、mount、unmount 生命周期</p>
                    </div>
                    <div className="arch-card">
                        <h4>🛡️ 沙箱隔离</h4>
                        <p>JavaScript 和 CSS 完全隔离</p>
                    </div>
                    <div className="arch-card">
                        <h4>📡 应用通信</h4>
                        <p>支持应用间消息传递和状态共享</p>
                    </div>
                    <div className="arch-card">
                        <h4>🚀 独立部署</h4>
                        <p>可以独立开发、测试和部署</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

// 演示页面组件
const DemoPage: React.FC = () => {
    const [todos, setTodos] = useState<string[]>([]);
    const [inputValue, setInputValue] = useState('');

    const addTodo = () => {
        if (inputValue.trim()) {
            setTodos([...todos, inputValue.trim()]);
            setInputValue('');
        }
    };

    const removeTodo = (index: number) => {
        setTodos(todos.filter((_, i) => i !== index));
    };

    return (
        <div className="react-demo">
            <h1>React 功能演示</h1>

            <div className="todo-section">
                <h2>📝 待办事项</h2>
                <div className="todo-input">
                    <input
                        type="text"
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && addTodo()}
                        placeholder="输入待办事项..."
                    />
                    <button onClick={addTodo}>添加</button>
                </div>

                <ul className="todo-list">
                    {todos.map((todo, index) => (
                        <li key={index} className="todo-item">
                            <span>{todo}</span>
                            <button
                                onClick={() => removeTodo(index)}
                                className="remove-btn"
                            >
                                删除
                            </button>
                        </li>
                    ))}
                </ul>

                {todos.length === 0 && (
                    <p className="empty-message">暂无待办事项</p>
                )}
            </div>

            <div className="stats-section">
                <h2>📊 统计信息</h2>
                <div className="stats-grid">
                    <div className="stat-card">
                        <h3>{todos.length}</h3>
                        <p>总计事项</p>
                    </div>
                    <div className="stat-card">
                        <h3>{new Date().toLocaleDateString()}</h3>
                        <p>今天日期</p>
                    </div>
                    <div className="stat-card">
                        <h3>React 18</h3>
                        <p>框架版本</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

// 应用内容组件
const AppContent: React.FC = () => {
    const location = useLocation()
    const { name, basename, isMicroFrontend } = useMicroAppInfo()
    const { theme, isDark } = useTheme()

    return (
        <div className={`react-app ${isDark ? 'dark-theme' : 'light-theme'}`}>
            <header className="app-header">
                <div className="header-content">
                    <h1>⚛️ React 子应用</h1>
                    <div className="app-info">
                        <span className="app-name">{name}</span>
                        <span className="app-mode">
                            {isMicroFrontend ? '微前端模式' : '独立模式'}
                        </span>
                        <span className="app-theme">主题: {theme}</span>
                    </div>
                    <nav className="nav-menu">
                        <Link
                            to={`${basename}`}
                            className={location.pathname === basename ? 'active' : ''}
                        >
                            首页
                        </Link>
                        <Link
                            to={`${basename}/about`}
                            className={location.pathname === `${basename}/about` ? 'active' : ''}
                        >
                            关于
                        </Link>
                        <Link
                            to={`${basename}/demo`}
                            className={location.pathname === `${basename}/demo` ? 'active' : ''}
                        >
                            演示
                        </Link>
                    </nav>
                </div>
            </header>

            <main className="app-main">
                <ErrorBoundary>
                    <Routes>
                        <Route path={`${basename}`} element={<HomePage />} />
                        <Route path={`${basename}/about`} element={<AboutPage />} />
                        <Route path={`${basename}/demo`} element={<DemoPage />} />
                        <Route path="*" element={<HomePage />} />
                    </Routes>
                </ErrorBoundary>
            </main>

            <footer className="app-footer">
                <p>React 子应用 - Powered by Micro-Core</p>
                <p>应用名称: {name} | 基础路径: {basename}</p>
            </footer>
        </div>
    )
}

// 主应用组件
const App: React.FC<any> = (props) => {
    return (
        <MicroAppProvider value={props}>
            <ErrorBoundary
                onError={(error, errorInfo) => {
                    console.error('React 应用顶层错误:', error, errorInfo)
                }}
            >
                <AppContent />
            </ErrorBoundary>
        </MicroAppProvider>
    )
}

export default App;