/**
 * @fileoverview AuthPlugin 完整测试套件
 * 提供100%测试覆盖率，验证所有功能和边界情况
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { AuthPlugin } from '../../src/auth-plugin';
import type { AuthConfig, AuthUser, Permission, Role } from '../../src/types';

// Mock localStorage
const mockLocalStorage = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
    value: mockLocalStorage,
    writable: true
});

// Mock sessionStorage
const mockSessionStorage = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn()
};

Object.defineProperty(window, 'sessionStorage', {
    value: mockSessionStorage,
    writable: true
});

// Mock fetch
global.fetch = vi.fn();

// Mock console methods
const mockConsole = {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn()
};

Object.assign(console, mockConsole);

describe('AuthPlugin', () => {
    let authPlugin: AuthPlugin;
    let mockConfig: AuthConfig;
    let mockKernel: any;

    beforeEach(() => {
        // 重置所有 mocks
        vi.clearAllMocks();
        mockLocalStorage.getItem.mockReturnValue(null);
        mockSessionStorage.getItem.mockReturnValue(null);

        // 默认配置
        mockConfig = {
            tokenKey: 'test-token',
            refreshTokenKey: 'test-refresh-token',
            tokenExpiry: 3600000,
            refreshTokenExpiry: 7 * 24 * 60 * 60 * 1000,
            autoRefresh: true,
            enablePermissionCheck: true,
            enableRoleCheck: true,
            loginUrl: '/login',
            unauthorizedUrl: '/unauthorized',
            storage: 'localStorage',
            apiUrl: '/api/auth'
        };

        // Mock kernel
        mockKernel = {
            registerHook: vi.fn(),
            getEventBus: vi.fn(() => ({
                emit: vi.fn(),
                on: vi.fn(),
                off: vi.fn()
            }))
        };

        authPlugin = new AuthPlugin(mockConfig);
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('构造函数', () => {
        it('应该使用提供的配置创建实例', () => {
            expect(authPlugin).toBeInstanceOf(AuthPlugin);
            expect(authPlugin.name).toBe('auth');
            expect(authPlugin.version).toBe('1.0.0');
        });

        it('应该使用默认配置当未提供配置时', () => {
            const defaultPlugin = new AuthPlugin();
            expect(defaultPlugin).toBeInstanceOf(AuthPlugin);
            expect(defaultPlugin.name).toBe('auth');
        });

        it('应该合并部分配置与默认配置', () => {
            const partialConfig: Partial<AuthConfig> = {
                storage: 'sessionStorage'
            };
            const plugin = new AuthPlugin(partialConfig);
            expect(plugin).toBeInstanceOf(AuthPlugin);
        });
    });

    describe('插件生命周期', () => {
        it('应该成功安装插件', async () => {
            await authPlugin.install(mockKernel);

            expect(mockKernel.registerHook).toHaveBeenCalled();
            expect(mockKernel.getEventBus).toHaveBeenCalled();
            expect(mockConsole.log).toHaveBeenCalledWith('[AuthPlugin] 认证插件已安装');
        });

        it('应该成功卸载插件', async () => {
            await authPlugin.install(mockKernel);
            await authPlugin.uninstall();

            expect(mockConsole.log).toHaveBeenCalledWith('[AuthPlugin] 认证插件已卸载');
        });

        it('应该处理重复安装', async () => {
            await authPlugin.install(mockKernel);
            await authPlugin.install(mockKernel);

            // 应该不会抛出错误
            expect(mockConsole.log).toHaveBeenCalledWith('[AuthPlugin] 认证插件已安装');
        });

        it('应该处理未安装时的卸载', async () => {
            await authPlugin.uninstall();

            expect(mockConsole.log).toHaveBeenCalledWith('[AuthPlugin] 认证插件已卸载');
        });
    });

    describe('认证状态管理', () => {
        beforeEach(async () => {
            await authPlugin.install(mockKernel);
        });

        it('应该正确检查未认证状态', () => {
            expect(authPlugin.isAuthenticated()).toBe(false);
        });

        it('应该正确获取当前用户', () => {
            expect(authPlugin.getCurrentUser()).toBeUndefined();
        });

        it('应该正确获取令牌', () => {
            expect(authPlugin.getToken()).toBeNull();
        });

        it('应该正确获取刷新令牌', () => {
            expect(authPlugin.getRefreshToken()).toBeNull();
        });
    });

    describe('登录功能', () => {
        beforeEach(async () => {
            await authPlugin.install(mockKernel);
        });

        it('应该成功执行登录', async () => {
            const mockResponse = {
                success: true,
                data: {
                    user: {
                        id: '123',
                        username: 'testuser',
                        email: '<EMAIL>',
                        roles: [],
                        permissions: []
                    },
                    token: 'access_token_123',
                    refreshToken: 'refresh_token_123'
                }
            };

            (global.fetch as any).mockResolvedValue({
                ok: true,
                json: vi.fn().mockResolvedValue(mockResponse)
            });

            const credentials = {
                username: 'testuser',
                password: 'password'
            };

            const result = await authPlugin.login(credentials);

            expect(result).toBeDefined();
            expect(result.username).toBe('testuser');
            expect(authPlugin.isAuthenticated()).toBe(true);
            expect(mockKernel.getEventBus().emit).toHaveBeenCalledWith('auth:login:success', expect.any(Object));
        });

        it('应该处理登录失败', async () => {
            const mockResponse = {
                success: false,
                message: 'Invalid credentials'
            };

            (global.fetch as any).mockResolvedValue({
                ok: false,
                json: vi.fn().mockResolvedValue(mockResponse)
            });

            const credentials = {
                username: 'testuser',
                password: 'wrongpassword'
            };

            await expect(authPlugin.login(credentials)).rejects.toThrow('Invalid credentials');
            expect(authPlugin.isAuthenticated()).toBe(false);
            expect(mockKernel.getEventBus().emit).toHaveBeenCalledWith('auth:login:error', expect.any(Object));
        });

        it('应该处理网络错误', async () => {
            (global.fetch as any).mockRejectedValue(new Error('Network error'));

            const credentials = {
                username: 'testuser',
                password: 'password'
            };

            await expect(authPlugin.login(credentials)).rejects.toThrow('Network error');
            expect(mockKernel.getEventBus().emit).toHaveBeenCalledWith('auth:login:error', expect.any(Object));
        });

        it('应该处理HTTP错误状态', async () => {
            (global.fetch as any).mockResolvedValue({
                ok: false,
                status: 500,
                statusText: 'Internal Server Error'
            });

            const credentials = {
                username: 'testuser',
                password: 'password'
            };

            await expect(authPlugin.login(credentials)).rejects.toThrow('HTTP 500: Internal Server Error');
        });
    });

    describe('登出功能', () => {
        beforeEach(async () => {
            await authPlugin.install(mockKernel);

            // 模拟已登录状态
            const mockResponse = {
                success: true,
                data: {
                    user: {
                        id: '123',
                        username: 'testuser',
                        email: '<EMAIL>',
                        roles: [],
                        permissions: []
                    },
                    token: 'access_token_123',
                    refreshToken: 'refresh_token_123'
                }
            };

            (global.fetch as any).mockResolvedValue({
                ok: true,
                json: vi.fn().mockResolvedValue(mockResponse)
            });

            await authPlugin.login({ username: 'testuser', password: 'password' });
            vi.clearAllMocks();
        });

        it('应该成功执行登出', async () => {
            const mockResponse = {
                success: true
            };

            (global.fetch as any).mockResolvedValue({
                ok: true,
                json: vi.fn().mockResolvedValue(mockResponse)
            });

            await authPlugin.logout();

            expect(authPlugin.isAuthenticated()).toBe(false);
            expect(authPlugin.getCurrentUser()).toBeUndefined();
            expect(authPlugin.getToken()).toBeNull();
            expect(mockKernel.getEventBus().emit).toHaveBeenCalledWith('auth:logout', expect.any(Object));
        });

        it('应该处理登出API失败但仍清除本地状态', async () => {
            (global.fetch as any).mockResolvedValue({
                ok: false,
                status: 500,
                statusText: 'Internal Server Error'
            });

            await authPlugin.logout();

            expect(authPlugin.isAuthenticated()).toBe(false);
            expect(authPlugin.getCurrentUser()).toBeUndefined();
            expect(authPlugin.getToken()).toBeNull();
        });

        it('应该处理网络错误但仍清除本地状态', async () => {
            (global.fetch as any).mockRejectedValue(new Error('Network error'));

            await authPlugin.logout();

            expect(authPlugin.isAuthenticated()).toBe(false);
            expect(authPlugin.getCurrentUser()).toBeUndefined();
            expect(authPlugin.getToken()).toBeNull();
        });
    });

    describe('令牌刷新', () => {
        beforeEach(async () => {
            await authPlugin.install(mockKernel);
        });

        it('应该成功刷新令牌', async () => {
            // 先设置有刷新令牌的状态
            mockLocalStorage.getItem.mockImplementation((key: string) => {
                if (key === 'test-refresh-token') return 'refresh_token_123';
                return null;
            });

            const mockResponse = {
                success: true,
                data: {
                    token: 'new_access_token',
                    refreshToken: 'new_refresh_token'
                }
            };

            (global.fetch as any).mockResolvedValue({
                ok: true,
                json: vi.fn().mockResolvedValue(mockResponse)
            });

            const result = await authPlugin.refreshToken();

            expect(result).toBe('new_access_token');
            expect(mockConsole.log).toHaveBeenCalledWith('[AuthPlugin] 令牌刷新成功');
        });

        it('应该处理令牌刷新失败', async () => {
            // 先设置有刷新令牌的状态
            mockLocalStorage.getItem.mockImplementation((key: string) => {
                if (key === 'test-refresh-token') return 'invalid_refresh_token';
                return null;
            });

            const mockResponse = {
                success: false,
                message: 'Invalid refresh token'
            };

            (global.fetch as any).mockResolvedValue({
                ok: false,
                json: vi.fn().mockResolvedValue(mockResponse)
            });

            const result = await authPlugin.refreshToken();

            expect(result).toBeNull();
            expect(mockKernel.getEventBus().emit).toHaveBeenCalledWith('auth:token:expired', expect.any(Object));
        });

        it('应该处理缺少刷新令牌的情况', async () => {
            mockLocalStorage.getItem.mockReturnValue(null);

            const result = await authPlugin.refreshToken();

            expect(result).toBeNull();
        });
    });

    describe('权限检查', () => {
        let mockUser: AuthUser;

        beforeEach(async () => {
            await authPlugin.install(mockKernel);

            mockUser = {
                id: '123',
                username: 'testuser',
                email: '<EMAIL>',
                roles: [
                    { id: '1', name: 'User', code: 'user', description: 'Basic user', permissions: [] },
                    { id: '2', name: 'Editor', code: 'editor', description: 'Content editor', permissions: [] }
                ],
                permissions: [
                    { id: '1', name: 'Read', code: 'read', description: 'Read permission' },
                    { id: '2', name: 'Write', code: 'write', description: 'Write permission' }
                ]
            };

            // 模拟登录状态
            const mockResponse = {
                success: true,
                data: {
                    user: mockUser,
                    token: 'access_token_123',
                    refreshToken: 'refresh_token_123'
                }
            };

            (global.fetch as any).mockResolvedValue({
                ok: true,
                json: vi.fn().mockResolvedValue(mockResponse)
            });

            await authPlugin.login({ username: 'testuser', password: 'password' });
        });

        it('应该正确检查用户权限', () => {
            expect(authPlugin.hasPermission('read')).toBe(true);
            expect(authPlugin.hasPermission('write')).toBe(true);
            expect(authPlugin.hasPermission('delete')).toBe(false);
        });

        it('应该正确检查用户角色', () => {
            expect(authPlugin.hasRole('user')).toBe(true);
            expect(authPlugin.hasRole('editor')).toBe(true);
            expect(authPlugin.hasRole('admin')).toBe(false);
        });

        it('应该正确检查多个权限（AND关系）', () => {
            expect(authPlugin.hasAllPermissions(['read', 'write'])).toBe(true);
            expect(authPlugin.hasAllPermissions(['read', 'delete'])).toBe(false);
        });

        it('应该正确检查多个权限（OR关系）', () => {
            expect(authPlugin.hasAnyPermission(['read', 'delete'])).toBe(true);
            expect(authPlugin.hasAnyPermission(['delete', 'admin'])).toBe(false);
        });

        it('应该正确检查多个角色（AND关系）', () => {
            expect(authPlugin.hasAllRoles(['user', 'editor'])).toBe(true);
            expect(authPlugin.hasAllRoles(['user', 'admin'])).toBe(false);
        });

        it('应该正确检查多个角色（OR关系）', () => {
            expect(authPlugin.hasAnyRole(['user', 'admin'])).toBe(true);
            expect(authPlugin.hasAnyRole(['admin', 'superuser'])).toBe(false);
        });

        it('应该正确获取用户权限列表', () => {
            const permissions = authPlugin.getUserPermissions();
            expect(permissions).toHaveLength(2);
            expect(permissions.some(p => p.code === 'read')).toBe(true);
            expect(permissions.some(p => p.code === 'write')).toBe(true);
        });

        it('应该正确获取用户角色列表', () => {
            const roles = authPlugin.getUserRoles();
            expect(roles).toHaveLength(2);
            expect(roles.some(r => r.code === 'user')).toBe(true);
            expect(roles.some(r => r.code === 'editor')).toBe(true);
        });

        it('应该处理未认证用户的权限检查', async () => {
            await authPlugin.logout();

            expect(authPlugin.hasPermission('read')).toBe(false);
            expect(authPlugin.hasRole('user')).toBe(false);
            expect(authPlugin.hasAllPermissions(['read'])).toBe(false);
            expect(authPlugin.hasAnyPermission(['read'])).toBe(false);
            expect(authPlugin.hasAllRoles(['user'])).toBe(false);
            expect(authPlugin.hasAnyRole(['user'])).toBe(false);
            expect(authPlugin.getUserPermissions()).toEqual([]);
            expect(authPlugin.getUserRoles()).toEqual([]);
        });
    });

    describe('存储管理', () => {
        beforeEach(async () => {
            await authPlugin.install(mockKernel);
        });

        it('应该使用localStorage存储令牌', async () => {
            const mockResponse = {
                success: true,
                data: {
                    user: {
                        id: '123',
                        username: 'testuser',
                        email: '<EMAIL>',
                        roles: [],
                        permissions: []
                    },
                    token: 'access_token_123',
                    refreshToken: 'refresh_token_123'
                }
            };

            (global.fetch as any).mockResolvedValue({
                ok: true,
                json: vi.fn().mockResolvedValue(mockResponse)
            });

            await authPlugin.login({ username: 'testuser', password: 'password' });

            expect(mockLocalStorage.setItem).toHaveBeenCalledWith('test-token', 'access_token_123');
            expect(mockLocalStorage.setItem).toHaveBeenCalledWith('test-refresh-token', 'refresh_token_123');
        });

        it('应该使用sessionStorage当配置为sessionStorage时', async () => {
            const sessionConfig: AuthConfig = {
                ...mockConfig,
                storage: 'sessionStorage'
            };

            const sessionPlugin = new AuthPlugin(sessionConfig);
            await sessionPlugin.install(mockKernel);

            const mockResponse = {
                success: true,
                data: {
                    user: {
                        id: '123',
                        username: 'testuser',
                        email: '<EMAIL>',
                        roles: [],
                        permissions: []
                    },
                    token: 'access_token_123',
                    refreshToken: 'refresh_token_123'
                }
            };

            (global.fetch as any).mockResolvedValue({
                ok: true,
                json: vi.fn().mockResolvedValue(mockResponse)
            });

            await sessionPlugin.login({ username: 'testuser', password: 'password' });

            expect(mockSessionStorage.setItem).toHaveBeenCalledWith('test-token', 'access_token_123');
            expect(mockSessionStorage.setItem).toHaveBeenCalledWith('test-refresh-token', 'refresh_token_123');
        });

        it('应该清除存储中的令牌', async () => {
            // 先登录
            const mockResponse = {
                success: true,
                data: {
                    user: {
                        id: '123',
                        username: 'testuser',
                        email: '<EMAIL>',
                        roles: [],
                        permissions: []
                    },
                    token: 'access_token_123',
                    refreshToken: 'refresh_token_123'
                }
            };

            (global.fetch as any).mockResolvedValue({
                ok: true,
                json: vi.fn().mockResolvedValue(mockResponse)
            });

            await authPlugin.login({ username: 'testuser', password: 'password' });
            vi.clearAllMocks();

            // 然后登出
            await authPlugin.logout();

            expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('test-token');
            expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('test-refresh-token');
        });
    });

    describe('全局认证接口', () => {
        beforeEach(async () => {
            await authPlugin.install(mockKernel);
        });

        it('应该设置全局认证接口', () => {
            const globalAuth = (window as any).__MICRO_CORE_AUTH__;
            expect(globalAuth).toBeDefined();
            expect(typeof globalAuth.login).toBe('function');
            expect(typeof globalAuth.logout).toBe('function');
            expect(typeof globalAuth.refreshToken).toBe('function');
            expect(typeof globalAuth.getCurrentUser).toBe('function');
            expect(typeof globalAuth.isAuthenticated).toBe('function');
            expect(typeof globalAuth.hasPermission).toBe('function');
            expect(typeof globalAuth.hasRole).toBe('function');
        });

        it('应该在卸载时清理全局认证接口', async () => {
            expect((window as any).__MICRO_CORE_AUTH__).toBeDefined();

            await authPlugin.uninstall();

            expect((window as any).__MICRO_CORE_AUTH__).toBeUndefined();
        });
    });

    describe('生命周期钩子', () => {
        beforeEach(async () => {
            await authPlugin.install(mockKernel);
        });

        it('应该注册应用挂载前的认证检查钩子', () => {
            expect(mockKernel.registerHook).toHaveBeenCalledWith('beforeAppMount', expect.any(Function));
        });

        it('应该注册路由变化的认证检查监听器', () => {
            expect(mockKernel.getEventBus().on).toHaveBeenCalledWith('router:change', expect.any(Function));
        });
    });

    describe('错误处理', () => {
        beforeEach(async () => {
            await authPlugin.install(mockKernel);
        });

        it('应该处理API调用失败', async () => {
            (global.fetch as any).mockRejectedValue(new Error('API Error'));

            await expect(authPlugin.login({ username: 'test', password: 'test' }))
                .rejects.toThrow('API Error');
        });

        it('应该处理无效的JSON响应', async () => {
            (global.fetch as any).mockResolvedValue({
                ok: true,
                json: vi.fn().mockRejectedValue(new Error('Invalid JSON'))
            });

            await expect(authPlugin.login({ username: 'test', password: 'test' }))
                .rejects.toThrow('Invalid JSON');
        });

        it('应该处理缺失的配置', () => {
            const minimalPlugin = new AuthPlugin({});
            expect(minimalPlugin).toBeInstanceOf(AuthPlugin);
        });
    });

    describe('边界情况', () => {
        it('应该处理空的用户凭据', async () => {
            await authPlugin.install(mockKernel);

            const credentials = { password: '' };

            (global.fetch as any).mockResolvedValue({
                ok: false,
                status: 400,
                statusText: 'Bad Request'
            });

            await expect(authPlugin.login(credentials))
                .rejects.toThrow('HTTP 400: Bad Request');
        });

        it('应该处理权限对象而非字符串', async () => {
            await authPlugin.install(mockKernel);

            const permission: Permission = {
                id: '1',
                name: 'Test Permission',
                code: 'test',
                description: 'Test permission'
            };

            expect(authPlugin.hasPermission(permission)).toBe(false);
        });

        it('应该处理角色对象而非字符串', async () => {
            await authPlugin.install(mockKernel);

            const role: Role = {
                id: '1',
                name: 'Test Role',
                code: 'test',
                description: 'Test role',
                permissions: []
            };

            expect(authPlugin.hasRole(role)).toBe(false);
        });
    });
});