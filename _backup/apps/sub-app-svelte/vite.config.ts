import { svelte } from '@sveltejs/vite-plugin-svelte'
import { defineConfig } from 'vite'

export default defineConfig({
    plugins: [svelte()],

    server: {
        port: 3006,
        host: true,
        cors: true,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
    },

    build: {
        outDir: 'dist',
        sourcemap: true,
        minify: 'esbuild',
        target: 'es2020',
        lib: {
            entry: 'src/main.ts',
            name: 'SubAppSvelte',
            fileName: 'sub-app-svelte',
            formats: ['umd']
        },
        rollupOptions: {
            external: ['svelte'],
            output: {
                globals: {
                    svelte: 'Svelte'
                }
            }
        }
    },

    define: {
        __MICRO_APP_NAME__: JSON.stringify('sub-app-svelte'),
        __POWERED_BY_MICRO_CORE__: true
    },

    resolve: {
        alias: {
            '@': '/src'
        }
    },

    optimizeDeps: {
        include: ['svelte']
    }
})