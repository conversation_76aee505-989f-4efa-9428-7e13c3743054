/**
 * 微前端微内核实现
 *
 * @description 微内核架构的核心实现，提供最小运行时功能
 * <AUTHOR> <<EMAIL>>
 */
import { ApplicationConfig, ApplicationInstance, ManagerInterface, MicroCoreConfig, PluginInterface } from '@micro-core/shared';
import type { ApplicationManager, EventBus, MicroCoreStatus, PluginManager } from '@micro-core/shared';
/**
 * 微前端微内核实现
 */
export declare class MicroCore {
    private logger;
    private managers;
    private config;
    private status;
    readonly eventBus: EventBus;
    readonly pluginManager: PluginManager;
    readonly applicationManager: ApplicationManager;
    constructor(config?: MicroCoreConfig);
    /**
     * 初始化系统
     */
    initialize(): Promise<void>;
    /**
     * 启动系统
     */
    start(): Promise<void>;
    /**
     * 注册插件
     */
    registerPlugin(plugin: PluginInterface): Promise<void>;
    /**
     * 注册管理器
     */
    registerManager(name: string, manager: ManagerInterface): void;
    /**
     * 获取管理器
     */
    getManager<T extends ManagerInterface>(name: string): T | undefined;
    /**
     * 注册应用
     */
    registerApplication(config: ApplicationConfig): Promise<void>;
    /**
     * 加载应用
     */
    loadApplication(name: string): Promise<void>;
    /**
     * 挂载应用
     */
    mountApplication(name: string, container?: HTMLElement): Promise<void>;
    /**
     * 卸载应用
     */
    unmountApplication(name: string): Promise<void>;
    /**
     * 注销应用
     */
    unregisterApplication(name: string): Promise<void>;
    /**
     * 获取应用实例
     */
    getApplication(name: string): ApplicationInstance | undefined;
    /**
     * 获取所有应用
     */
    getAllApplications(): ApplicationInstance[];
    /**
     * 获取应用状态
     */
    getApplicationStatus(name: string): string | undefined;
    /**
     * 检查应用是否存在
     */
    hasApplication(name: string): boolean;
    /**
     * 获取系统状态
     */
    getStatus(): MicroCoreStatus;
    /**
     * 停止系统
     */
    stop(): Promise<void>;
    /**
     * 销毁系统
     */
    destroy(): Promise<void>;
    /**
     * 设置错误处理
     */
    private setupErrorHandling;
}
/**
 * 默认微内核实例
 */
export declare const defaultMicroCore: MicroCore;
//# sourceMappingURL=micro-core.d.ts.map