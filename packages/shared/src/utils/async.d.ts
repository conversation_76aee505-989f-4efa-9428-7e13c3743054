/**
 * @fileoverview 异步工具函数
 * @description 提供防抖、节流等异步处理工具
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 防抖函数
 */
export declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
/**
 * 节流函数
 */
export declare function throttle<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
/**
 * 延迟函数
 */
export declare function sleep(ms: number): Promise<void>;
/**
 * 超时函数
 */
export declare function timeout<T>(promise: Promise<T>, ms: number): Promise<T>;
/**
 * 重试函数
 */
export declare function retry<T>(fn: () => Promise<T>, options?: {
    maxAttempts: number;
    delay?: number;
}): Promise<T>;
/**
 * 并行执行函数
 */
export declare function parallel<T>(tasks: Array<() => Promise<T>>, concurrency?: number): Promise<T[]>;
/**
 * 顺序执行函数
 */
export declare function sequential<T>(tasks: Array<() => Promise<T>>): Promise<T[]>;
//# sourceMappingURL=async.d.ts.map