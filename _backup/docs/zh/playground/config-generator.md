# 配置生成器

Micro-Core 配置生成器是一个交互式工具，帮助您快速生成适合项目需求的微前端配置。

## 📋 目录

- [生成器概述](#生成器概述)
- [基础配置生成](#基础配置生成)
- [高级配置生成](#高级配置生成)
- [框架适配配置](#框架适配配置)
- [性能优化配置](#性能优化配置)
- [部署配置生成](#部署配置生成)
- [配置验证](#配置验证)
- [导出和使用](#导出和使用)

## 生成器概述

### 功能特性

```typescript
// 配置生成器特性
const configGeneratorFeatures = {
  // 交互式配置
  interactive: [
    '步骤式引导',
    '智能推荐',
    '实时预览',
    '配置验证'
  ],
  
  // 配置类型
  configTypes: [
    '基础配置',
    '高级配置',
    '框架适配',
    '性能优化',
    '部署配置'
  ],
  
  // 输出格式
  outputFormats: [
    'TypeScript',
    'JavaScript',
    'JSON',
    'YAML'
  ],
  
  // 集成支持
  integrations: [
    'Webpack',
    'Vite',
    'Rollup',
    'Docker',
    'CI/CD'
  ]
}
```

### 生成器界面

```vue
<!-- 配置生成器主界面 -->
<template>
  <div class="config-generator">
    <div class="generator-header">
      <h1>Micro-Core 配置生成器</h1>
      <p>快速生成适合您项目的微前端配置</p>
    </div>
    
    <div class="generator-content">
      <!-- 步骤导航 -->
      <div class="steps-nav">
        <div 
          v-for="(step, index) in steps" 
          :key="index"
          :class="['step', { active: currentStep === index, completed: index < currentStep }]"
          @click="goToStep(index)"
        >
          <span class="step-number">{{ index + 1 }}</span>
          <span class="step-title">{{ step.title }}</span>
        </div>
      </div>
      
      <!-- 配置表单 -->
      <div class="config-form">
        <component 
          :is="currentStepComponent" 
          v-model="config"
          @next="nextStep"
          @prev="prevStep"
        />
      </div>
      
      <!-- 配置预览 -->
      <div class="config-preview">
        <h3>配置预览</h3>
        <pre><code>{{ formattedConfig }}</code></pre>
      </div>
    </div>
    
    <div class="generator-actions">
      <button @click="generateConfig" :disabled="!isConfigValid">
        生成配置
      </button>
      <button @click="downloadConfig" :disabled="!generatedConfig">
        下载配置
      </button>
      <button @click="copyConfig" :disabled="!generatedConfig">
        复制配置
      </button>
    </div>
  </div>
</template>

<script>
import BasicConfigStep from './steps/BasicConfigStep.vue'
import AdvancedConfigStep from './steps/AdvancedConfigStep.vue'
import FrameworkConfigStep from './steps/FrameworkConfigStep.vue'
import PerformanceConfigStep from './steps/PerformanceConfigStep.vue'
import DeploymentConfigStep from './steps/DeploymentConfigStep.vue'

export default {
  name: 'ConfigGenerator',
  
  components: {
    BasicConfigStep,
    AdvancedConfigStep,
    FrameworkConfigStep,
    PerformanceConfigStep,
    DeploymentConfigStep
  },
  
  data() {
    return {
      currentStep: 0,
      config: {
        basic: {},
        advanced: {},
        framework: {},
        performance: {},
        deployment: {}
      },
      generatedConfig: null,
      
      steps: [
        { title: '基础配置', component: 'BasicConfigStep' },
        { title: '高级配置', component: 'AdvancedConfigStep' },
        { title: '框架适配', component: 'FrameworkConfigStep' },
        { title: '性能优化', component: 'PerformanceConfigStep' },
        { title: '部署配置', component: 'DeploymentConfigStep' }
      ]
    }
  },
  
  computed: {
    currentStepComponent() {
      return this.steps[this.currentStep].component
    },
    
    formattedConfig() {
      return JSON.stringify(this.config, null, 2)
    },
    
    isConfigValid() {
      return this.validateConfig(this.config)
    }
  },
  
  methods: {
    goToStep(index) {
      if (index >= 0 && index < this.steps.length) {
        this.currentStep = index
      }
    },
    
    nextStep() {
      if (this.currentStep < this.steps.length - 1) {
        this.currentStep++
      }
    },
    
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },
    
    generateConfig() {
      this.generatedConfig = this.buildFinalConfig(this.config)
    },
    
    downloadConfig() {
      if (this.generatedConfig) {
        this.downloadFile(
          'micro-core.config.js',
          this.generatedConfig
        )
      }
    },
    
    copyConfig() {
      if (this.generatedConfig) {
        navigator.clipboard.writeText(this.generatedConfig)
        this.$message.success('配置已复制到剪贴板')
      }
    },
    
    validateConfig(config) {
      // 配置验证逻辑
      return config.basic.name && config.basic.apps && config.basic.apps.length > 0
    },
    
    buildFinalConfig(config) {
      // 构建最终配置
      return this.generateConfigCode(config)
    }
  }
}
</script>
```

## 基础配置生成

### 基础配置步骤

```vue
<!-- BasicConfigStep.vue -->
<template>
  <div class="basic-config-step">
    <h2>基础配置</h2>
    
    <!-- 项目信息 -->
    <div class="config-section">
      <h3>项目信息</h3>
      
      <div class="form-group">
        <label>项目名称</label>
        <input 
          v-model="config.name" 
          placeholder="my-micro-frontend"
          @input="updateConfig"
        />
      </div>
      
      <div class="form-group">
        <label>项目描述</label>
        <textarea 
          v-model="config.description" 
          placeholder="我的微前端项目"
          @input="updateConfig"
        />
      </div>
      
      <div class="form-group">
        <label>项目版本</label>
        <input 
          v-model="config.version" 
          placeholder="1.0.0"
          @input="updateConfig"
        />
      </div>
    </div>
    
    <!-- 微应用配置 -->
    <div class="config-section">
      <h3>微应用配置</h3>
      
      <div class="apps-list">
        <div 
          v-for="(app, index) in config.apps" 
          :key="index"
          class="app-config"
        >
          <div class="app-header">
            <h4>应用 {{ index + 1 }}</h4>
            <button @click="removeApp(index)" class="remove-btn">删除</button>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label>应用名称</label>
              <input 
                v-model="app.name" 
                placeholder="vue-app"
                @input="updateConfig"
              />
            </div>
            
            <div class="form-group">
              <label>入口地址</label>
              <input 
                v-model="app.entry" 
                placeholder="http://localhost:3001"
                @input="updateConfig"
              />
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label>激活路由</label>
              <input 
                v-model="app.activeWhen" 
                placeholder="/vue-app"
                @input="updateConfig"
              />
            </div>
            
            <div class="form-group">
              <label>容器选择器</label>
              <input 
                v-model="app.container" 
                placeholder="#vue-container"
                @input="updateConfig"
              />
            </div>
          </div>
        </div>
      </div>
      
      <button @click="addApp" class="add-app-btn">
        + 添加微应用
      </button>
    </div>
    
    <!-- 全局配置 -->
    <div class="config-section">
      <h3>全局配置</h3>
      
      <div class="form-group">
        <label>
          <input 
            type="checkbox" 
            v-model="config.sandbox"
            @change="updateConfig"
          />
          启用沙箱隔离
        </label>
      </div>
      
      <div class="form-group">
        <label>
          <input 
            type="checkbox" 
            v-model="config.prefetch"
            @change="updateConfig"
          />
          启用预加载
        </label>
      </div>
      
      <div class="form-group">
        <label>
          <input 
            type="checkbox" 
            v-model="config.monitoring"
            @change="updateConfig"
          />
          启用性能监控
        </label>
      </div>
    </div>
    
    <div class="step-actions">
      <button @click="$emit('next')" :disabled="!isValid">
        下一步
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BasicConfigStep',
  
  props: {
    modelValue: {
      type: Object,
      default: () => ({})
    }
  },
  
  emits: ['update:modelValue', 'next'],
  
  data() {
    return {
      config: {
        name: '',
        description: '',
        version: '1.0.0',
        apps: [
          {
            name: '',
            entry: '',
            activeWhen: '',
            container: ''
          }
        ],
        sandbox: true,
        prefetch: true,
        monitoring: false
      }
    }
  },
  
  computed: {
    isValid() {
      return this.config.name && 
             this.config.apps.length > 0 && 
             this.config.apps.every(app => app.name && app.entry && app.activeWhen)
    }
  },
  
  watch: {
    modelValue: {
      handler(newVal) {
        if (newVal.basic) {
          this.config = { ...this.config, ...newVal.basic }
        }
      },
      immediate: true
    }
  },
  
  methods: {
    addApp() {
      this.config.apps.push({
        name: '',
        entry: '',
        activeWhen: '',
        container: ''
      })
      this.updateConfig()
    },
    
    removeApp(index) {
      this.config.apps.splice(index, 1)
      this.updateConfig()
    },
    
    updateConfig() {
      this.$emit('update:modelValue', {
        ...this.modelValue,
        basic: this.config
      })
    }
  }
}
</script>
```

## 高级配置生成

### 高级配置选项

```vue
<!-- AdvancedConfigStep.vue -->
<template>
  <div class="advanced-config-step">
    <h2>高级配置</h2>
    
    <!-- 沙箱配置 -->
    <div class="config-section">
      <h3>沙箱配置</h3>
      
      <div class="form-group">
        <label>沙箱类型</label>
        <select v-model="config.sandbox.type" @change="updateConfig">
          <option value="proxy">Proxy 沙箱（推荐）</option>
          <option value="iframe">Iframe 沙箱</option>
          <option value="webcomponent">WebComponent 沙箱</option>
          <option value="defineProperty">DefineProperty 沙箱</option>
          <option value="namespace">命名空间沙箱</option>
          <option value="federation">联邦组件沙箱</option>
        </select>
      </div>
      
      <div class="form-group">
        <label>
          <input 
            type="checkbox" 
            v-model="config.sandbox.css"
            @change="updateConfig"
          />
          CSS 隔离
        </label>
      </div>
      
      <div class="form-group">
        <label>
          <input 
            type="checkbox" 
            v-model="config.sandbox.js"
            @change="updateConfig"
          />
          JavaScript 隔离
        </label>
      </div>
    </div>
    
    <!-- 路由配置 -->
    <div class="config-section">
      <h3>路由配置</h3>
      
      <div class="form-group">
        <label>路由模式</label>
        <select v-model="config.router.mode" @change="updateConfig">
          <option value="history">History 模式</option>
          <option value="hash">Hash 模式</option>
          <option value="memory">Memory 模式</option>
        </select>
      </div>
      
      <div class="form-group">
        <label>基础路径</label>
        <input 
          v-model="config.router.base" 
          placeholder="/"
          @input="updateConfig"
        />
      </div>
      
      <div class="form-group">
        <label>
          <input 
            type="checkbox" 
            v-model="config.router.guards"
            @change="updateConfig"
          />
          启用路由守卫
        </label>
      </div>
    </div>
    
    <!-- 通信配置 -->
    <div class="config-section">
      <h3>通信配置</h3>
      
      <div class="form-group">
        <label>
          <input 
            type="checkbox" 
            v-model="config.communication.eventBus"
            @change="updateConfig"
          />
          事件总线
        </label>
      </div>
      
      <div class="form-group">
        <label>
          <input 
            type="checkbox" 
            v-model="config.communication.globalState"
            @change="updateConfig"
          />
          全局状态管理
        </label>
      </div>
      
      <div class="form-group">
        <label>
          <input 
            type="checkbox" 
            v-model="config.communication.directMessage"
            @change="updateConfig"
          />
          直接消息通信
        </label>
      </div>
    </div>
    
    <!-- 插件配置 -->
    <div class="config-section">
      <h3>插件配置</h3>
      
      <div class="plugins-list">
        <div class="form-group">
          <label>
            <input 
              type="checkbox" 
              v-model="config.plugins.router"
              @change="updateConfig"
            />
            路由插件
          </label>
        </div>
        
        <div class="form-group">
          <label>
            <input 
              type="checkbox" 
              v-model="config.plugins.communication"
              @change="updateConfig"
            />
            通信插件
          </label>
        </div>
        
        <div class="form-group">
          <label>
            <input 
              type="checkbox" 
              v-model="config.plugins.auth"
              @change="updateConfig"
            />
            认证插件
          </label>
        </div>
        
        <div class="form-group">
          <label>
            <input 
              type="checkbox" 
              v-model="config.plugins.monitoring"
              @change="updateConfig"
            />
            监控插件
          </label>
        </div>
      </div>
    </div>
    
    <div class="step-actions">
      <button @click="$emit('prev')">上一步</button>
      <button @click="$emit('next')">下一步</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdvancedConfigStep',
  
  props: {
    modelValue: {
      type: Object,
      default: () => ({})
    }
  },
  
  emits: ['update:modelValue', 'next', 'prev'],
  
  data() {
    return {
      config: {
        sandbox: {
          type: 'proxy',
          css: true,
          js: true
        },
        router: {
          mode: 'history',
          base: '/',
          guards: true
        },
        communication: {
          eventBus: true,
          globalState: true,
          directMessage: false
        },
        plugins: {
          router: true,
          communication: true,
          auth: false,
          monitoring: false
        }
      }
    }
  },
  
  watch: {
    modelValue: {
      handler(newVal) {
        if (newVal.advanced) {
          this.config = { ...this.config, ...newVal.advanced }
        }
      },
      immediate: true
    }
  },
  
  methods: {
    updateConfig() {
      this.$emit('update:modelValue', {
        ...this.modelValue,
        advanced: this.config
      })
    }
  }
}
</script>
```

## 框架适配配置

### 框架选择和配置

```javascript
// 框架适配配置生成器
class FrameworkConfigGenerator {
  constructor() {
    this.supportedFrameworks = [
      'react',
      'vue',
      'angular',
      'svelte',
      'solid',
      'vanilla'
    ]
    
    this.frameworkConfigs = new Map()
    this.initializeFrameworkConfigs()
  }
  
  // 初始化框架配置
  initializeFrameworkConfigs() {
    // React 配置
    this.frameworkConfigs.set('react', {
      name: 'React',
      versions: ['16.8+', '17.x', '18.x'],
      features: [
        'Hooks 支持',
        'Concurrent 特性',
        'Suspense',
        'Error Boundaries'
      ],
      dependencies: [
        'react',
        'react-dom',
        '@micro-core/adapter-react'
      ],
      config: {
        version: '18',
        render: {
          mode: 'concurrent',
          strictMode: true,
          suspense: true
        },
        errorBoundary: {
          enabled: true,
          fallback: 'ErrorFallback'
        }
      }
    })
    
    // Vue 配置
    this.frameworkConfigs.set('vue', {
      name: 'Vue',
      versions: ['2.7+', '3.x'],
      features: [
        'Composition API',
        'Options API',
        'Vuex/Pinia',
        'Vue Router'
      ],
      dependencies: [
        'vue',
        '@micro-core/adapter-vue'
      ],
      config: {
        version: '3',
        render: {
          mode: 'createApp',
          mountStrategy: 'replace'
        },
        errorHandler: {
          enabled: true
        }
      }
    })
    
    // Angular 配置
    this.frameworkConfigs.set('angular', {
      name: 'Angular',
      versions: ['12+', '13+', '14+', '15+', '16+', '17+'],
      features: [
        '依赖注入',
        'RxJS',
        'Angular Router',
        'Standalone Components'
      ],
      dependencies: [
        '@angular/core',
        '@angular/platform-browser',
        '@micro-core/adapter-angular'
      ],
      config: {
        version: '17',
        bootstrap: {
          mode: 'standalone',
          enableIvy: true
        },
        providers: []
      }
    })
  }
  
  // 生成框架配置
  generateFrameworkConfig(framework, options = {}) {
    const frameworkInfo = this.frameworkConfigs.get(framework)
    
    if (!frameworkInfo) {
      throw new Error(`Unsupported framework: ${framework}`)
    }
    
    return {
      framework,
      ...frameworkInfo,
      config: {
        ...frameworkInfo.config,
        ...options
      }
    }
  }
  
  // 生成多框架配置
  generateMultiFrameworkConfig(frameworks) {
    const configs = {}
    
    frameworks.forEach(({ framework, options }) => {
      configs[framework] = this.generateFrameworkConfig(framework, options)
    })
    
    return {
      multiFramework: true,
      frameworks: configs,
      shared: this.generateSharedConfig(frameworks)
    }
  }
  
  // 生成共享配置
  generateSharedConfig(frameworks) {
    const sharedDependencies = new Set()
    const sharedFeatures = new Set()
    
    frameworks.forEach(({ framework }) => {
      const frameworkInfo = this.frameworkConfigs.get(framework)
      
      // 收集共享依赖
      frameworkInfo.dependencies.forEach(dep => {
        if (dep.startsWith('@micro-core/')) {
          sharedDependencies.add(dep)
        }
      })
      
      // 收集共享特性
      frameworkInfo.features.forEach(feature => {
        sharedFeatures.add(feature)
      })
    })
    
    return {
      dependencies: Array.from(sharedDependencies),
      features: Array.from(sharedFeatures),
      communication: {
        eventBus: true,
        globalState: true
      }
    }
  }
}
```

## 性能优化配置

### 性能配置选项

```javascript
// 性能优化配置生成器
class PerformanceConfigGenerator {
  constructor() {
    this.optimizationStrategies = {
      preload: {
        name: '智能预加载',
        options: ['disabled', 'basic', 'intelligent', 'aggressive'],
        impact: 'high',
        description: '根据用户行为预测和预加载应用'
      },
      
      cache: {
        name: '缓存策略',
        options: ['none', 'memory', 'disk', 'hybrid'],
        impact: 'medium',
        description: '缓存应用资源和状态'
      },
      
      bundleOptimization: {
        name: '包优化',
        options: ['none', 'basic', 'advanced'],
        impact: 'high',
        description: '代码分割和树摇优化'
      },
      
      lazyLoading: {
        name: '懒加载',
        options: ['disabled', 'route-based', 'component-based', 'intelligent'],
        impact: 'medium',
        description: '按需加载组件和路由'
      }
    }
  }
  
  // 生成性能配置
  generatePerformanceConfig(requirements) {
    const config = {
      // 预加载配置
      preload: this.generatePreloadConfig(requirements.preload),
      
      // 缓存配置
      cache: this.generateCacheConfig(requirements.cache),
      
      // 包优化配置
      bundleOptimization: this.generateBundleConfig(requirements.bundleOptimization),
      
      // 懒加载配置
      lazyLoading: this.generateLazyLoadingConfig(requirements.lazyLoading),
      
      // 监控配置
      monitoring: this.generateMonitoringConfig(requirements.monitoring)
    }
    
    return config
  }
  
  // 生成预加载配置
  generatePreloadConfig(level) {
    const configs = {
      disabled: {
        enabled: false
      },
      
      basic: {
        enabled: true,
        strategy: 'route-based',
        prefetchOnIdle: true
      },
      
      intelligent: {
        enabled: true,
        strategy: 'intelligent',
        prediction: {
          userBehavior: true,
          routeAnalysis: true,
          timeBasedPrediction: true
        },
        prefetchOnIdle: true,
        prefetchOnHover: true
      },
      
      aggressive: {
        enabled: true,
        strategy: 'aggressive',
        prediction: {
          userBehavior: true,
          routeAnalysis: true,
          timeBasedPrediction: true,
          mlPrediction: true
        },
        prefetchOnIdle: true,
        prefetchOnHover: true,
        prefetchOnVisible: true
      }
    }
    
    return configs[level] || configs.basic
  }
  
  // 生成缓存配置
  generateCacheConfig(level) {
    const configs = {
      none: {
        enabled: false
      },
      
      memory: {
        enabled: true,
        type: 'memory',
        maxSize: '50MB',
        ttl: 30 * 60 * 1000 // 30分钟
      },
      
      disk: {
        enabled: true,
        type: 'disk',
        maxSize: '200MB',
        ttl: 24 * 60 * 60 * 1000 // 24小时
      },
      
      hybrid: {
        enabled: true,
        type: 'hybrid',
        memory: {
          maxSize: '50MB',
          ttl: 30 * 60 * 1000
        },
        disk: {
          maxSize: '200MB',
          ttl: 24 * 60 * 60 * 1000
        }
      }
    }
    
    return configs[level] || configs.memory
  }
  
  // 生成包优化配置
  generateBundleConfig(level) {
    const configs = {
      none: {
        enabled: false
      },
      
      basic: {
        enabled: true,
        codeSplitting: true,
        treeShaking: true,
        minification: true
      },
      
      advanced: {
        enabled: true,
        codeSplitting: {
          strategy: 'intelligent',
          chunks: 'all',
          maxSize: 244000
        },
        treeShaking: {
          enabled: true,
          sideEffects: false
        },
        minification: {
          enabled: true,
          terser: true,
          css: true
        },
        compression: {
          gzip: true,
          brotli: true
        }
      }
    }
    
    return configs[level] || configs.basic
  }
}
```

## 部署配置生成

### 部署环境配置

```javascript
// 部署配置生成器
class DeploymentConfigGenerator {
  constructor() {
    this.deploymentTargets = {
      docker: {
        name: 'Docker',
        files: ['Dockerfile', 'docker-compose.yml', '.dockerignore'],
        features: ['容器化部署', '环境隔离', '扩展性']
      },
      
      kubernetes: {
        name: 'Kubernetes',
        files: ['k8s-deployment.yaml', 'k8s-service.yaml', 'k8s-ingress.yaml'],
        features: ['容器编排', '自动扩缩容', '服务发现']
      },
      
      nginx: {
        name: 'Nginx',
        files: ['nginx.conf', 'nginx-sites.conf'],
        features: ['反向代理', '负载均衡', '静态资源服务']
      },
      
      cdn: {
        name: 'CDN',
        files: ['cdn-config.json'],
        features: ['全球加速', '缓存优化', '高可用']
      }
    }
  }
  
  // 生成 Docker 配置
  generateDockerConfig(config) {
    const dockerfile = `
# Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
`

    const dockerCompose = `
# docker-compose.yml
version: '3.8'

services:
  main-app:
    build: .
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
    depends_on:
      - vue-app
      - react-app

  vue-app:
    build: ./apps/vue-app
    ports:
      - "3001:80"
    environment:
      - NODE_ENV=production

  react-app:
    build: ./apps/react-app
    ports:
      - "3002:80"
    environment:
      - NODE_ENV=production

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - main-app
      - vue-app
      - react-app
`

    const dockerignore = `
node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.nyc_output
coverage
.nyc_output
.vscode
`

    return {
      'Dockerfile': dockerfile,
      'docker-compose.yml': dockerCompose,
      '.dockerignore':