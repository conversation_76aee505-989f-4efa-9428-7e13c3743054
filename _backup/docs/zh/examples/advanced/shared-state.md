# 共享状态示例

本示例展示如何在 Micro-Core 微前端架构中实现应用间的状态共享，包括全局状态管理、状态同步、持久化存储等功能。

## 📋 目录

- [状态架构](#状态架构)
- [全局状态设计](#全局状态设计)
- [状态同步机制](#状态同步机制)
- [状态持久化](#状态持久化)
- [状态中间件](#状态中间件)
- [实时状态同步](#实时状态同步)
- [完整示例](#完整示例)

## 状态架构

共享状态架构采用分层设计，支持多种状态管理模式和同步策略。

### 🏗️ 状态架构图

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    共享状态架构                                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐ │
│  │ 应用 A       │    │ 应用 B       │    │ 应用 C               │ │
│  │ (React)     │    │ (Vue)       │    │ (Angular)           │ │
│  │             │    │             │    │                     │ │
│  │ 本地状态     │    │ 本地状态     │    │ 本地状态             │ │
│  │ • 组件状态   │    │ • 组件状态   │    │ • 组件状态           │ │
│  │ • 页面状态   │    │ • 页面状态   │    │ • 页面状态           │ │
│  └─────────────┘    └─────────────┘    └─────────────────────┘ │
│           │                 │                     │             │
│           └─────────────────┼─────────────────────┘             │
│                             │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    共享状态层                               │ │
│  │                                                             │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 全局状态     │  │ 业务状态     │  │ 应用状态             │ │ │
│  │  │             │  │             │  │                     │ │ │
│  │  │ • 用户信息   │  │ • 购物车     │  │ • 主题配置           │ │ │
│  │  │ • 权限数据   │  │ • 订单数据   │  │ • 语言设置           │ │ │
│  │  │ • 系统配置   │  │ • 库存信息   │  │ • 界面状态           │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  │                             │                               │ │
│  │  ┌─────────────────────────────────────────────────────────┐ │ │
│  │  │                    状态同步层                           │ │ │
│  │  │  • 状态监听        • 变更通知        • 冲突解决         │ │ │
│  │  │  • 数据验证        • 权限检查        • 事务管理         │ │ │
│  │  └─────────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    持久化层                                 │ │
│  │  • LocalStorage    • SessionStorage   • IndexedDB          │ │
│  │  • 服务器同步      • 离线缓存         • 数据恢复           │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 全局状态设计

### 状态结构定义

```typescript
// shared/state/globalState.ts
import { createGlobalStore } from '@micro-core/core'

// 全局状态接口定义
interface GlobalState {
  // 用户相关状态
  user: {
    profile: UserProfile | null
    isAuthenticated: boolean
    permissions: string[]
    preferences: UserPreferences
    session: UserSession | null
  }

  // 业务数据状态
  business: {
    // 购物车状态
    shoppingCart: {
      items: CartItem[]
      total: number
      currency: string
      lastUpdated: number
    }
    
    // 当前选中的数据
    selected: {
      customer: Customer | null
      product: Product | null
      order: Order | null
    }
    
    // 通知消息
    notifications: Notification[]
    
    // 实时数据
    realtime: {
      onlineUsers: number
      systemStatus: 'normal' | 'maintenance' | 'error'
      alerts: Alert[]
    }
  }

  // 应用配置状态
  app: {
    theme: 'light' | 'dark' | 'auto'
    language: 'zh-CN' | 'en-US'
    layout: 'sidebar' | 'topbar'
    
    // 界面状态
    ui: {
      sidebarCollapsed: boolean
      loading: boolean
      errors: AppError[]
      modals: ModalState[]
    }
    
    // 路由状态
    routing: {
      currentRoute: string
      previousRoute: string
      routeParams: Record<string, any>
    }
  }

  // 缓存数据
  cache: {
    users: Record<string, User>
    products: Record<string, Product>
    orders: Record<string, Order>
    lastFetch: Record<string, number>
  }
}

// 创建全局状态存储
export const globalStore = createGlobalStore<GlobalState>({
  user: {
    profile: null,
    isAuthenticated: false,
    permissions: [],
    preferences: {
      theme: 'light',
      language: 'zh-CN',
      notifications: true
    },
    session: null
  },
  
  business: {
    shoppingCart: {
      items: [],
      total: 0,
      currency: 'CNY',
      lastUpdated: 0
    },
    selected: {
      customer: null,
      product: null,
      order: null
    },
    notifications: [],
    realtime: {
      onlineUsers: 0,
      systemStatus: 'normal',
      alerts: []
    }
  },
  
  app: {
    theme: 'light',
    language: 'zh-CN',
    layout: 'sidebar',
    ui: {
      sidebarCollapsed: false,
      loading: false,
      errors: [],
      modals: []
    },
    routing: {
      currentRoute: '/',
      previousRoute: '/',
      routeParams: {}
    }
  },
  
  cache: {
    users: {},
    products: {},
    orders: {},
    lastFetch: {}
  }
})
```

### 状态操作方法

```typescript
// shared/state/actions.ts
import { globalStore } from './globalState'

// 用户状态操作
export const userActions = {
  // 设置用户信息
  setUser(user: UserProfile) {
    globalStore.setState('user.profile', user)
    globalStore.setState('user.isAuthenticated', true)
    globalStore.setState('user.permissions', user.permissions || [])
  },

  // 更新用户偏好
  updatePreferences(preferences: Partial<UserPreferences>) {
    const current = globalStore.getState('user.preferences')
    globalStore.setState('user.preferences', { ...current, ...preferences })
  },

  // 用户登出
  logout() {
    globalStore.setState('user.profile', null)
    globalStore.setState('user.isAuthenticated', false)
    globalStore.setState('user.permissions', [])
    globalStore.setState('user.session', null)
    
    // 清理业务数据
    globalStore.setState('business.shoppingCart.items', [])
    globalStore.setState('business.selected', {
      customer: null,
      product: null,
      order: null
    })
  }
}

// 购物车状态操作
export const cartActions = {
  // 添加商品到购物车
  addItem(product: Product, quantity: number = 1) {
    const currentItems = globalStore.getState('business.shoppingCart.items')
    const existingItem = currentItems.find(item => item.productId === product.id)
    
    let updatedItems: CartItem[]
    
    if (existingItem) {
      // 更新现有商品数量
      updatedItems = currentItems.map(item =>
        item.productId === product.id
          ? { ...item, quantity: item.quantity + quantity }
          : item
      )
    } else {
      // 添加新商品
      updatedItems = [...currentItems, {
        id: `cart_${Date.now()}`,
        productId: product.id,
        name: product.name,
        price: product.price,
        quantity,
        image: product.image
      }]
    }
    
    globalStore.setState('business.shoppingCart.items', updatedItems)
    globalStore.setState('business.shoppingCart.lastUpdated', Date.now())
    
    // 重新计算总价
    this.calculateTotal()
  },

  // 移除购物车商品
  removeItem(productId: string) {
    const currentItems = globalStore.getState('business.shoppingCart.items')
    const updatedItems = currentItems.filter(item => item.productId !== productId)
    
    globalStore.setState('business.shoppingCart.items', updatedItems)
    globalStore.setState('business.shoppingCart.lastUpdated', Date.now())
    
    this.calculateTotal()
  },

  // 更新商品数量
  updateQuantity(productId: string, quantity: number) {
    if (quantity <= 0) {
      this.removeItem(productId)
      return
    }
    
    const currentItems = globalStore.getState('business.shoppingCart.items')
    const updatedItems = currentItems.map(item =>
      item.productId === productId
        ? { ...item, quantity }
        : item
    )
    
    globalStore.setState('business.shoppingCart.items', updatedItems)
    globalStore.setState('business.shoppingCart.lastUpdated', Date.now())
    
    this.calculateTotal()
  },

  // 清空购物车
  clear() {
    globalStore.setState('business.shoppingCart.items', [])
    globalStore.setState('business.shoppingCart.total', 0)
    globalStore.setState('business.shoppingCart.lastUpdated', Date.now())
  },

  // 计算总价
  calculateTotal() {
    const items = globalStore.getState('business.shoppingCart.items')
    const total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0)
    globalStore.setState('business.shoppingCart.total', total)
  }
}

// 应用状态操作
export const appActions = {
  // 设置主题
  setTheme(theme: 'light' | 'dark' | 'auto') {
    globalStore.setState('app.theme', theme)
    
    // 应用主题到DOM
    document.documentElement.setAttribute('data-theme', theme)
    
    // 保存到本地存储
    localStorage.setItem('app-theme', theme)
  },

  // 设置语言
  setLanguage(language: 'zh-CN' | 'en-US') {
    globalStore.setState('app.language', language)
    
    // 更新HTML lang属性
    document.documentElement.lang = language
    
    // 保存到本地存储
    localStorage.setItem('app-language', language)
  },

  // 切换侧边栏
  toggleSidebar() {
    const collapsed = globalStore.getState('app.ui.sidebarCollapsed')
    globalStore.setState('app.ui.sidebarCollapsed', !collapsed)
  },

  // 设置加载状态
  setLoading(loading: boolean) {
    globalStore.setState('app.ui.loading', loading)
  },

  // 添加错误
  addError(error: AppError) {
    const currentErrors = globalStore.getState('app.ui.errors')
    globalStore.setState('app.ui.errors', [...currentErrors, {
      ...error,
      id: `error_${Date.now()}`,
      timestamp: Date.now()
    }])
  },

  // 移除错误
  removeError(errorId: string) {
    const currentErrors = globalStore.getState('app.ui.errors')
    globalStore.setState('app.ui.errors', currentErrors.filter(e => e.id !== errorId))
  }
}
```

## 状态同步机制

### 状态监听和响应

```typescript
// shared/state/stateSync.ts
import { globalStore } from './globalState'
import { useMicroCore } from '@micro-core/core'

// 状态同步管理器
export class StateSyncManager {
  private microCore: any
  private syncRules: Map<string, SyncRule[]> = new Map()
  private watchers: Map<string, Function[]> = new Map()

  constructor(microCore: any) {
    this.microCore = microCore
    this.setupGlobalWatchers()
  }

  // 添加同步规则
  addSyncRule(statePath: string, rule: SyncRule) {
    if (!this.syncRules.has(statePath)) {
      this.syncRules.set(statePath, [])
    }
    this.syncRules.get(statePath)!.push(rule)
  }

  // 设置全局状态监听器
  private setupGlobalWatchers() {
    // 监听用户状态变化
    globalStore.watch('user.*', (path: string, newValue: any, oldValue: any) => {
      this.handleStateChange('user', path, newValue, oldValue)
    })

    // 监听业务状态变化
    globalStore.watch('business.*', (path: string, newValue: any, oldValue: any) => {
      this.handleStateChange('business', path, newValue, oldValue)
    })

    // 监听应用状态变化
    globalStore.watch('app.*', (path: string, newValue: any, oldValue: any) => {
      this.handleStateChange('app', path, newValue, oldValue)
    })
  }

  // 处理状态变化
  private handleStateChange(category: string, path: string, newValue: any, oldValue: any) {
    const rules = this.syncRules.get(path) || this.syncRules.get(`${category}.*`) || []
    
    rules.forEach(rule => {
      if (rule.condition && !rule.condition(newValue, oldValue)) {
        return
      }

      // 执行同步操作
      this.executeSync(rule, path, newValue, oldValue)
    })

    // 发布状态变化事件
    this.microCore.eventBus.emit('state:changed', {
      path,
      newValue,
      oldValue,
      timestamp: Date.now()
    })
  }

  // 执行同步操作
  private async executeSync(rule: SyncRule, path: string, newValue: any, oldValue: any) {
    try {
      switch (rule.type) {
        case 'broadcast':
          // 广播到所有应用
          this.microCore.eventBus.emit(rule.eventName!, {
            path,
            value: rule.transform ? rule.transform(newValue) : newValue
          })
          break

        case 'message':
          // 发送消息到特定应用
          rule.targetApps!.forEach(app => {
            this.microCore.sendMessage(app, rule.messageType!, {
              path,
              value: rule.transform ? rule.transform(newValue) : newValue
            })
          })
          break

        case 'persist':
          // 持久化到本地存储
          this.persistState(path, newValue, rule.storage!)
          break

        case 'api':
          // 同步到服务器
          await this.syncToServer(path, newValue, rule.apiEndpoint!)
          break
      }
    } catch (error) {
      console.error(`状态同步失败 [${rule.type}]:`, error)
    }
  }

  // 持久化状态
  private persistState(path: string, value: any, storage: 'localStorage' | 'sessionStorage') {
    try {
      const storageKey = `micro-core-state-${path}`
      const storageObj = storage === 'localStorage' ? localStorage : sessionStorage
      storageObj.setItem(storageKey, JSON.stringify(value))
    } catch (error) {
      console.error('状态持久化失败:', error)
    }
  }

  // 同步到服务器
  private async syncToServer(path: string, value: any, endpoint: string) {
    try {
      await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          path,
          value,
          timestamp: Date.now()
        })
      })
    } catch (error) {
      console.error('服务器同步失败:', error)
    }
  }

  // 从本地存储恢复状态
  restoreFromStorage() {
    const restoreFromStorageType = (storage: Storage, prefix: string) => {
      for (let i = 0; i < storage.length; i++) {
        const key = storage.key(i)
        if (key && key.startsWith(prefix)) {
          try {
            const path = key.replace(prefix, '')
            const value = JSON.parse(storage.getItem(key)!)
            globalStore.setState(path, value)
          } catch (error) {
            console.error(`恢复状态失败 ${key}:`, error)
          }
        }
      }
    }

    // 从 localStorage 恢复
    restoreFromStorageType(localStorage, 'micro-core-state-')
    
    // 从 sessionStorage 恢复
    restoreFromStorageType(sessionStorage, 'micro-core-state-')
  }
}

// 同步规则接口
interface SyncRule {
  type: 'broadcast' | 'message' | 'persist' | 'api'
  condition?: (newValue: any, oldValue: any) => boolean
  transform?: (value: any) => any
  
  // broadcast 类型参数
  eventName?: string
  
  // message 类型参数
  targetApps?: string[]
  messageType?: string
  
  // persist 类型参数
  storage?: 'localStorage' | 'sessionStorage'
  
  // api 类型参数
  apiEndpoint?: string
}
```

### 同步规则配置

```typescript
// shared/state/syncRules.ts
import { StateSyncManager } from './stateSync'

export const setupSyncRules = (syncManager: StateSyncManager) => {
  // 用户登录状态同步
  syncManager.addSyncRule('user.profile', {
    type: 'broadcast',
    eventName: 'user:profile:changed',
    condition: (newValue, oldValue) => newValue?.id !== oldValue?.id
  })

  syncManager.addSyncRule('user.profile', {
    type: 'persist',
    storage: 'localStorage'
  })

  // 购物车状态同步
  syncManager.addSyncRule('business.shoppingCart.items', {
    type: 'broadcast',
    eventName: 'cart:items:changed',
    transform: (items) => ({
      count: items.length,
      total: items.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0)
    })
  })

  syncManager.addSyncRule('business.shoppingCart', {
    type: 'persist',
    storage: 'localStorage'
  })

  // 主题设置同步
  syncManager.addSyncRule('app.theme', {
    type: 'broadcast',
    eventName: 'theme:changed'
  })

  syncManager.addSyncRule('app.theme', {
    type: 'persist',
    storage: 'localStorage'
  })

  // 语言设置同步
  syncManager.addSyncRule('app.language', {
    type: 'broadcast',
    eventName: 'language:changed'
  })

  syncManager.addSyncRule('app.language', {
    type: 'persist',
    storage: 'localStorage'
  })

  // 选中数据同步到相关应用
  syncManager.addSyncRule('business.selected.customer', {
    type: 'message',
    targetApps: ['order-management', 'analytics'],
    messageType: 'customer:selected',
    condition: (newValue) => newValue !== null
  })

  syncManager.addSyncRule('business.selected.order', {
    type: 'message',
    targetApps: ['inventory-management', 'analytics'],
    messageType: 'order:selected'
  })

  // 实时数据同步到服务器
  syncManager.addSyncRule('business.realtime.alerts', {
    type: 'api',
    apiEndpoint: '/api/alerts/sync',
    condition: (newValue, oldValue) => newValue.length !== oldValue.length
  })
}
```

## 状态持久化

### 持久化管理器

```typescript
// shared/state/persistence.ts
import { globalStore } from './globalState'

export class StatePersistenceManager {
  private persistenceRules: Map<string, PersistenceRule> = new Map()
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map()

  // 添加持久化规则
  addPersistenceRule(statePath: string, rule: PersistenceRule) {
    this.persistenceRules.set(statePath, rule)
    
    // 设置状态监听
    globalStore.watch(statePath, (path: string, newValue: any) => {
      this.handleStatePersistence(path, newValue, rule)
    })
  }

  // 处理状态持久化
  private handleStatePersistence(path: string, value: any, rule: PersistenceRule) {
    // 防抖处理
    if (rule.debounce) {
      const existingTimer = this.debounceTimers.get(path)
      if (existingTimer) {
        clearTimeout(existingTimer)
      }

      const timer = setTimeout(() => {
        this.persistState(path, value, rule)
        this.debounceTimers.delete(path)
      }, rule.debounce)

      this.debounceTimers.set(path, timer)
    } else {
      this.persistState(path, value, rule)
    }
  }

  // 持久化状态
  private async persistState(path: string, value: any, rule: PersistenceRule) {
    try {
      // 数据转换
      const transformedValue = rule.transform ? rule.transform(value) : value

      // 数据验证
      if (rule.validate && !rule.validate(transformedValue)) {
        console.warn(`状态验证失败: ${path}`)
        return
      }

      // 根据存储类型持久化
      switch (rule.storage) {
        case 'localStorage':
          this.saveToLocalStorage(path, transformedValue, rule)
          break
        case 'sessionStorage':
          this.saveToSessionStorage(path, transformedValue, rule)
          break
        case 'indexedDB':
          await this.saveToIndexedDB(path, transformedValue, rule)
          break
        case 'server':
          await this.saveToServer(path, transformedValue, rule)
          break
      }
    } catch (error) {
      console.error(`状态持久化失败 ${path}:`, error)
    }
  }

  // 保存到 localStorage
  private saveToLocalStorage(path: string, value: any, rule: PersistenceRule) {
    const key = rule.key || `micro-core-${path}`
    const data = {
      value,
      timestamp: Date.now(),
      version: rule.version || 1
    }
    
    localStorage.setItem(key, JSON.stringify(data))
  }

  // 保存到 sessionStorage
  private saveToSessionStorage(path: string, value: any, rule: PersistenceRule) {
    const key = rule.key || `micro-core-${path}`
    const data = {
      value,
      timestamp: Date.now(),
      version: rule.version || 1
    }
    
    sessionStorage.setItem(key, JSON.stringify(data))
  }

  // 保存到 IndexedDB
  private async saveToIndexedDB(path: string, value: any, rule: PersistenceRule) {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('MicroCoreState', 1)
      
      request.onerror = () => reject(request.error)
      
      request.onsuccess = () => {
        const db = request.result
        const transaction = db.transaction(['states'], 'readwrite')
        const store = transaction.objectStore('states')
        
        const data = {
          path,
          value,
          timestamp: Date.now(),
          version: rule.version || 1
        }
        
        const putRequest = store.put(data)
        putRequest.onsuccess = () => resolve(putRequest.result)
        putRequest.onerror = () => reject(putRequest.error)
      }
      
      request.onupgradeneeded = () => {
        const db = request.result
        if (!db.objectStoreNames.contains('states')) {
          const store = db.createObjectStore('states', { keyPath: 'path' })
          store.createIndex('timestamp', 'timestamp')
        }
      }
    })
  }

  // 保存到服务器
  private async saveToServer(path: string, value: any, rule: PersistenceRule) {
    const endpoint = rule.endpoint || '/api/state/save'
    
    await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(rule.headers || {})
      },
      body: JSON.stringify({
        path,
        value,
        timestamp: Date.now(),
        version: rule.version || 1
      })
    })
  }

  // 恢复状态
  async restoreState(path: string): Promise<any> {
    const rule = this.persistenceRules.get(path)
    if (!rule) return null

    try {
      switch (rule.storage) {
        case 'localStorage':
          return this.restoreFromLocalStorage(path, rule)
        case 'sessionStorage':
          return this.restoreFromSessionStorage(path, rule)
        case 'indexedDB':
          return await this.restoreFromIndexedDB(path, rule)
        case 'server':
          return await this.restoreFromServer(path, rule)
        default:
          return null
      }
    } catch (error) {
      console.error(`状态恢复失败 ${path}:`, error)
      return null
    }
  }

  // 从 localStorage 恢复
  private restoreFromLocalStorage(path: string, rule: PersistenceRule): any {
    const key = rule.key || `micro-core-${path}`
    const stored = localStorage.getItem(key)
    
    if (!stored) return null
    
    try {
      const data = JSON.parse(stored)
      
      // 检查版本兼容性
      if (rule.version && data.version !== rule.version) {
        console.warn(`状态版本不匹配 ${path}: ${data.version} -> ${rule.version}`)
        return null
      }
      
      // 检查过期时间
      if (rule.ttl && Date.now() - data.timestamp > rule.ttl) {
        localStorage.removeItem(key)
        return null
      }
      
      return data.value
    } catch (error) {
      console.error(`解析存储数据失败 ${path}:`, error)
      return null
    }
  }

  // 从 sessionStorage 恢复
  private restoreFromSessionStorage(path: string, rule: PersistenceRule): any {
    const key = rule.key || `micro-core-${path}`
    const stored = sessionStorage.getItem(key)
    
    if (!stored) return null
    
    try {
      const data = JSON.parse(stored)
      return data.value
    } catch (error) {
      console.error(`解析会话数据失败 ${path}:`, error)
      return null
    }
  }

  // 从 IndexedDB 恢复
  private async restoreFromIndexedDB(path: string, rule: PersistenceRule): Promise<any> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('MicroCoreState', 1)
      
      request.onerror = () => reject(request.error)
      
      request.onsuccess = () => {
        const db = request.result
        const transaction = db.transaction(['states'], 'readonly')
        const store = transaction.objectStore('states')
        
        const getRequest = store.get(path)
        getRequest.onsuccess = () => {
          const data = getRequest.result
          if (data) {
            // 检查过期时间
            if (rule.ttl && Date.now() - data.timestamp > rule.ttl) {
              resolve(null)
            } else {
              resolve(data.value)
            }
          } else {
            resolve(null)
          }
        }
        getRequest.onerror = () => reject(getRequest.error)
      }
    })
  }

  // 从服务器恢复
  private async restoreFromServer(path: string, rule: PersistenceRule): Promise<any> {
    const endpoint = rule.endpoint || '/api/state/restore'
    
    const response = await fetch(`${endpoint}?path=${encodeURIComponent(path)}`, {
      headers: rule.headers || {}
    })
    
    if (!response.ok) {
      throw new Error(`服务器响应错误: ${response.status}`)
    }
    
    const data = await response.json()
    return data.value
  }

  // 清理过期数据
  async cleanupExpiredData() {
    // 清理 localStorage
    for (let i = localStorage.length - 1; i >= 0; i--) {
      const key = localStorage.key(i)
      if (key && key.startsWith('micro-core-')) {
        try {
          const data = JSON.parse(localStorage.getItem(key)!)
          const rule = Array.from(this.persistenceRules.values())
            .find(r => (r.key || `micro-core-${r}`) === key)
          
          if (rule && rule.ttl && Date.now() - data.timestamp > rule.ttl) {
            localStorage.removeItem(key)
          }
        } catch (error) {
          // 删除无效数据
          localStorage.removeItem(key)
        }
      }
    }

    // 清理 IndexedDB
    try {
      await this.cleanupIndexedDB()
    } catch (error) {
      console.error('清理 IndexedDB 失败:', error)
    }
  }

  private async cleanupIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('MicroCoreState', 1)
      
      request.onsuccess = () => {
        const db = request.result
        const transaction = db.transaction(['states'], 'readwrite')
        const store = transaction.objectStore('states')
        const index = store.index('timestamp')
        
        const now = Date.now()
        const cursor = index.openCursor()
        
        cursor.onsuccess = () => {
          const result = cursor.result
          if (result) {
            const data = result.value
            const rule = this.persistenceRules.get(data.path)
            
            if (rule && rule.ttl && now - data.timestamp > rule.ttl) {
              result.delete()
            }
            
            result.continue()
          } else {
            resolve()
          }
        }
        
        cursor.onerror = () => reject(cursor.error)
      }
      
      request.onerror = () => reject(request.error)
    })
  }
}

// 持久化规则接口
interface PersistenceRule {
  storage: 'localStorage' | 'sessionStorage' | 'indexedDB' | 'server'
  key?: string
  version?: number
  ttl?: number // 生存时间（毫秒）
  debounce?: number // 防抖延迟（毫秒）
  transform?: (value: any) => any
  validate?: (value: any) => boolean
  endpoint?: string // 服务器端点
  headers?: Record<string, string> // 请求头
}
```

### 持久化规则配置

```typescript
// shared/state/persistenceRules.ts
import { StatePersistenceManager } from './persistence'

export const setupPersistenceRules = (persistenceManager: StatePersistenceManager) => {
  // 用户信息持久化
  persistenceManager.addPersistenceRule('user.profile', {
    storage: 'localStorage',
    key: 'user-profile',
    version: 1,
    ttl: 7 * 24 * 60 * 60 * 1000, // 7天
    validate: (value) => value && typeof value.id === 'string'
  })

  // 用户偏好设置持久化
  persistenceManager.addPersistenceRule('user.preferences', {
    storage: 'localStorage',
    key: 'user-preferences',
    version: 1,
    debounce: 1000 // 1秒防抖
  })

  // 购物车持久化
  persistenceManager.addPersistenceRule('business.shoppingCart', {
    storage: 'localStorage',
    key: 'shopping-cart',
    version: 1,
    debounce: 500,
    transform: (cart) => ({
      items: cart.items,
      total: cart.total,
      currency: cart.currency,
      lastUpdated: cart.lastUpdated
    })
  })

  // 应用主题持久化
  persistenceManager.addPersistenceRule('app.theme', {
    storage: 'localStorage',
    key: 'app-theme',
    version: 1
  })

  // 界面状态持久化（会话级别）
  persistenceManager.addPersistenceRule('app.ui.sidebarCollapsed', {
    storage: 'sessionStorage',
    key: 'sidebar-state',
    version: 1
  })

  // 缓存数据持久化到 IndexedDB
  persistenceManager.addPersistenceRule('cache', {
    storage: 'indexedDB',
    version: 1,
    ttl: 24 * 60 * 60 * 1000, // 24小时
    debounce: 2000
  })

  // 重要业务数据同步到服务器
  persistenceManager.addPersistenceRule('business.selected', {
    storage: 'server',
    endpoint: '/api/user/state',
    version: 1,
    debounce: 3000,
    headers: {
      'Authorization': 'Bearer ' + localStorage.getItem('auth-token')
    },
    validate: (value) => value && Object.keys(value).length > 0
  })
}
```

## 状态中间件

### 中间件系统

```typescript
// shared/state/middleware.ts
export class StateMiddlewareManager {
  private middlewares: StateMiddleware[] = []

  // 添加中间件
  use(middleware: StateMiddleware) {
    this.middlewares.push(middleware)
  }

  // 执行中间件链
  async execute(context: StateContext): Promise<any> {
    let index = 0

    const next = async (): Promise<any> => {
      if (index >= this.middlewares.length) {
        return context.proceed()
      }

      const middleware = this.middlewares[index++]
      return await middleware(context, next)
    }

    return await next()
  }
}

// 状态中间件接口
interface StateMiddleware {
  (context: StateContext, next: () => Promise<any>): Promise<any>
}

interface StateContext {
  action: 'get' | 'set' | 'watch' | 'unwatch'
  path: string
  value?: any
  oldValue?: any
  timestamp: number
  source: string
  proceed: () => Promise<any>
}

// 常用状态中间件
export const createLoggingMiddleware = (): StateMiddleware => {
  return async (context, next) => {
    console.log(`[State ${context.action.toUpperCase()}] ${context.path}:`, context.value)
    const start = Date.now()
    
    try {
      const result = await next()
      const duration = Date.now() - start
      console.log(`[State ${context.action.toUpperCase()}] 完成，耗时: ${duration}ms`)
      return result
    } catch (error) {
      const duration = Date.now() - start
      console.error(`[State ${context.action.toUpperCase()}] 失败，耗时: ${duration}ms:`, error)
      throw error
    }
  }
}

export const createValidationMiddleware = (): StateMiddleware => {
  return async (context, next) => {
    if (context.action === 'set') {
      // 数据类型验证
      if (context.path.includes('user.profile') && context.value) {
        if (!context.value.id || typeof context.value.id !== 'string') {
          throw new Error('用户ID必须是字符串')
        }
      }

      // 购物车验证
      if (context.path.includes('shoppingCart.items') && Array.isArray(context.value)) {
        for (const item of context.value) {
          if (!item.productId || !item.name || typeof item.price !== 'number') {
            throw new Error('购物车商品数据格式错误')
          }
        }
      }

      // 权限验证
      if (context.path.includes('user.permissions')) {
        if (!Array.isArray(context.value)) {
          throw new Error('用户权限必须是数组')
        }
      }
    }

    return await next()
  }
}

export const createPermissionMiddleware = (microCore: any): StateMiddleware => {
  return async (context, next) => {
    if (context.action === 'set') {
      // 检查敏感数据修改权限
      const sensitivePatterns = [
        'user.permissions',
        'user.profile.role',
        'app.config'
      ]

      const isSensitive = sensitivePatterns.some(pattern => 
        context.path.includes(pattern)
      )

      if (isSensitive) {
        const currentUser = microCore.globalState.get('user.profile')
        if (!currentUser || !currentUser.permissions.includes('admin')) {
          throw new Error('权限不足，无法修改敏感数据')
        }
      }
    }

    return await next()
  }
}

export const createCacheMiddleware = (): StateMiddleware => {
  const cache = new Map<string, { value: any, timestamp: number }>()
  const CACHE_TTL = 5000 // 5秒缓存

  return async (context, next) => {
    if (context.action === 'get') {
      const cached = cache.get(context.path)
      if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
        return cached.value
      }
    }

    const result = await next()

    if (context.action === 'get') {
      cache.set(context.path, {
        value: result,
        timestamp: Date.now()
      })
    } else if (context.action === 'set') {
      // 清除相关缓存
      for (const key of cache.keys()) {
        if (key.startsWith(context.path) || context.path.startsWith(key)) {
          cache.delete(key)
        }
      }
    }

    return result
  }
}
```

## 实时状态同步

### WebSocket 状态同步

```typescript
// shared/state/realtimeSync.ts
export class RealtimeStateSync {
  private microCore: any
  private ws: WebSocket | null = null
  private syncQueue: SyncOperation[] = []
  private isConnected = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5

  constructor(microCore: any, wsUrl: string) {
    this.microCore = microCore
    this.connect(wsUrl)
    this.setupStateListeners()
  }

  // 连接 WebSocket
  private connect(wsUrl: string) {
    try {
      this.ws = new WebSocket(wsUrl)
      
      this.ws.onopen = () => {
        console.log('实时状态同步连接已建立')
        this.isConnected = true
        this.reconnectAttempts = 0
        
        // 处理队列中的同步操作
        this.processQueuedOperations()
        
        // 发送认证信息
        this.authenticate()
      }

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          this.handleServerMessage(message)
        } catch (error) {
          console.error('解析服务器消息失败:', error)
        }
      }

      this.ws.onclose = () => {
        console.log('实时状态同步连接已关闭')
        this.isConnected = false
        this.attemptReconnect(wsUrl)
      }

      this.ws.onerror = (error) => {
        console.error('WebSocket 错误:', error)
      }
    } catch (error) {
      console.error('WebSocket 连接失败:', error)
      this.attemptReconnect(wsUrl)
    }
  }

  // 设置状态监听器
  private setupStateListeners() {
    // 监听需要实时同步的状态变化
    const syncPaths = [
      'business.selected.*',
      'business.shoppingCart.*',
      'business.realtime.*'
    ]

    syncPaths.forEach(path => {
      this.microCore.globalState.watch(path, (statePath: string, newValue: any, oldValue: any) => {
        this.queueSync({
          type: 'state_change',
          path: statePath,
          value: newValue,
          oldValue,
          timestamp: Date.now()
        })
      })
    })
  }

  // 队列同步操作
  private queueSync(operation: SyncOperation) {
    this.syncQueue.push(operation)
    
    if (this.isConnected) {
      this.processQueuedOperations()
    }
  }

  // 处理队列中的同步操作
  private processQueuedOperations() {
    while (this.syncQueue.length > 0 && this.isConnected) {
      const operation = this.syncQueue.shift()!
      this.sendToServer(operation)
    }
  }

  // 发送到服务器
  private sendToServer(operation: SyncOperation) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(operation))
    } else {
      // 重新加入队列
      this.syncQueue.unshift(operation)
    }
  }

  // 处理服务器消息
  private handleServerMessage(message: any) {
    switch (message.type) {
      case 'state_sync':
        this.handleStateSync(message.data)
        break
        
      case 'state_conflict':
        this.handleStateConflict(message.data)
        break
        
      case 'user_action':
        this.handleUserAction(message.data)
        break
        
      case 'system_broadcast':
        this.handleSystemBroadcast(message.data)
        break
        
      default:
        console.log('未知服务器消息类型:', message.type)
    }
  }

  // 处理状态同步
  private handleStateSync(data: any) {
    const { path, value, source, timestamp } = data
    
    // 避免循环同步
    if (source === this.getClientId()) {
      return
    }

    // 检查时间戳，避免过期数据覆盖新数据
    const currentValue = this.microCore.globalState.get(path)
    const currentTimestamp = this.microCore.globalState.getTimestamp(path)
    
    if (timestamp > currentTimestamp) {
      // 临时禁用监听器，避免触发新的同步
      this.microCore.globalState.setWithoutTrigger(path, value)
      
      // 发布同步事件
      this.microCore.eventBus.emit('state:synced', {
        path,
        value,
        source,
        timestamp
      })
    }
  }

  // 处理状态冲突
  private handleStateConflict(data: any) {
    const { path, serverValue, clientValue, serverTimestamp, clientTimestamp } = data
    
    console.warn('状态冲突:', { path, serverValue, clientValue })
    
    // 简单的冲突解决策略：时间戳较新的获胜
    if (serverTimestamp > clientTimestamp) {
      this.microCore.globalState.setWithoutTrigger(path, serverValue)
      
      // 通知用户状态已被服务器覆盖
      this.microCore.eventBus.emit('state:conflict:resolved', {
        path,
        resolvedValue: serverValue,
        reason: 'server_newer'
      })
    }
  }

  // 处理用户操作
  private handleUserAction(data: any) {
    const { action, userId, userData } = data
    
    switch (action) {
      case 'user_online':
        this.microCore.globalState.set('business.realtime.onlineUsers', userData.count)
        break
        
      case 'user_offline':
        this.microCore.globalState.set('business.realtime.onlineUsers', userData.count)
        break
        
      case 'user_activity':
        // 更新用户活动状态
        break
    }
  }

  // 处理系统广播
  private handleSystemBroadcast(data: any) {
    const { type, message, priority } = data
    
    // 添加系统通知
    const notifications = this.microCore.globalState.get('business.notifications')
    this.microCore.globalState.set('business.notifications', [
      ...notifications,
      {
        id: `system_${Date.now()}`,
        type: 'system',
        message,
        priority,
        timestamp: Date.now(),
        read: false
      }
    ])
  }

  // 认证
  private authenticate() {
    const user = this.microCore.globalState.get('user.profile')
    if (user) {
      this.sendToServer({
        type: 'authenticate',
        userId: user.id,
        token: localStorage.getItem('auth-token'),
        timestamp: Date.now()
      })
    }
  }

  // 获取客户端ID
  private getClientId(): string {
    let clientId = localStorage.getItem('client-id')
    if (!clientId) {
      clientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      localStorage.setItem('client-id', clientId)
    }
    return clientId
  }

  // 尝试重连
  private attemptReconnect(wsUrl: string) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`尝试重连实时同步 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect(wsUrl)
      }, 1000 * this.reconnectAttempts)
    } else {
      console.error('实时同步重连失败，已达到最大重试次数')
    }
  }

  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    this.isConnected = false
  }
}

interface SyncOperation {
  type: string
  path?: string
  value?: any
  oldValue?: any
  timestamp: number
  [key: string]: any
}
```

## 完整示例

### 主应用集成

```typescript
// main-app/src/App.tsx
import React, { useEffect } from 'react'
import { useMicroCore } from '@micro-core/core'
import { globalStore, userActions, cartActions, appActions } from '../shared/state'
import { StateSyncManager } from '../shared/state/stateSync'
import { StatePersistenceManager } from '../shared/state/persistence'
import { StateMiddlewareManager } from '../shared/state/middleware'
import { RealtimeStateSync } from '../shared/state/realtimeSync'
import { setupSyncRules } from '../shared/state/syncRules'
import { setupPersistenceRules } from '../shared/state/persistenceRules'

const App: React.FC = () => {
  const microCore = useMicroCore()

  useEffect(() => {
    initializeStateManagement()
  }, [])

  const initializeStateManagement = async () => {
    // 初始化状态中间件
    const middlewareManager = new StateMiddlewareManager()
    middlewareManager.use(createLoggingMiddleware())
    middlewareManager.use(createValidationMiddleware())
    middlewareManager.use(createPermissionMiddleware(microCore))
    middlewareManager.use(createCacheMiddleware())

    // 初始化状态同步管理器
    const syncManager = new StateSyncManager(microCore)
    setupSyncRules(syncManager)

    // 初始化持久化管理器
    const persistenceManager = new StatePersistenceManager()
    setupPersistenceRules(persistenceManager)

    // 恢复持久化状态
    await restorePersistedStates(persistenceManager)

    // 初始化实时同步
    const realtimeSync = new RealtimeStateSync(microCore, 'ws://localhost:8080/state')

    // 设置全局事件监听
    setupGlobalEventListeners()

    // 定期清理过期数据
    setInterval(() => {
      persistenceManager.cleanupExpiredData()
    }, 60 * 60 * 1000) // 每小时清理一次
  }

  const restorePersistedStates = async (persistenceManager: StatePersistenceManager) => {
    try {
      // 恢复用户信息
      const userProfile = await persistenceManager.restoreState('user.profile')
      if (userProfile) {
        userActions.setUser(userProfile)
      }

      // 恢复用户偏好
      const userPreferences = await persistenceManager.restoreState('user.preferences')
      if (userPreferences) {
        userActions.updatePreferences(userPreferences)
        appActions.setTheme(userPreferences.theme)
        appActions.setLanguage(userPreferences.language)
      }

      // 恢复购物车
      const shoppingCart = await persistenceManager.restoreState('business.shoppingCart')
      if (shoppingCart && shoppingCart.items.length > 0) {
        globalStore.setState('business.shoppingCart', shoppingCart)
      }

      // 恢复应用状态
      const theme = await persistenceManager.restoreState('app.theme')
      if (theme) {
        appActions.setTheme(theme)
      }

      const sidebarState = await persistenceManager.restoreState('app.ui.sidebarCollapsed')
      if (sidebarState !== null) {
        globalStore.setState('app.ui.sidebarCollapsed', sidebarState)
      }

    } catch (error) {
      console.error('恢复持久化状态失败:', error)
    }
  }

  const setupGlobalEventListeners = () => {
    // 监听状态同步事件
    microCore.eventBus.on('state:synced', (data: any) => {
      console.log('状态已同步:', data)
    })

    // 监听状态冲突解决事件
    microCore.eventBus.on('state:conflict:resolved', (data: any) => {
      console.warn('状态冲突已解决:', data)
      
      // 显示用户通知
      appActions.addError({
        message: `状态冲突已解决: ${data.path}`,
        type: 'warning'
      })
    })

    // 监听应用错误
    microCore.eventBus.on('app:error', (error: any) => {
      appActions.addError({
        message: error.message,
        type: 'error',
        source: error.source
      })
    })
  }

  return (
    <div className="app">
      <StateProvider>
        <AppContent />
      </StateProvider>
    </div>
  )
}

// 状态提供者组件
const StateProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState(globalStore.getState())

  useEffect(() => {
    // 监听全局状态变化
    const unsubscribe = globalStore.subscribe((newState) => {
      setState(newState)
    })

    return unsubscribe
  }, [])

  return (
    <StateContext.Provider value={{ state, actions: { userActions, cartActions, appActions } }}>
      {children}
    </StateContext.Provider>
  )
}

export default App
```

### 使用状态的子应用示例

```typescript
// user-app/src/components/UserProfile.tsx
import React, { useEffect, useState } from 'react'
import { useMicroCore } from '@micro-core/adapter-react'
import { useGlobalState } from '../hooks/useGlobalState'

const UserProfile: React.FC = () => {
  const microCore = useMicroCore()
  const { state, actions } = useGlobalState()
  const [editing, setEditing] = useState(false)
  const [formData, setFormData] = useState({})

  useEffect(() => {
    // 监听用户状态变化
    const unsubscribe = microCore.globalState.watch('user.profile', (newUser) => {
      if (newUser) {
        setFormData({
          name: newUser.name,
          email: newUser.email,
          phone: newUser.phone
        })
      }
    })

    return unsubscribe
  }, [])

  const handleSave = async () => {
    try {
      const updatedUser = await microCore.request('user-service', 'updateProfile', formData)
      actions.userActions.setUser(updatedUser)
      setEditing(false)
      
      // 发布用户更新事件
      microCore.eventBus.emit('user:profile:updated', updatedUser)
      
    } catch (error) {
      console.error('更新用户信息失败:', error)
      actions.appActions.addError({
        message: '更新用户信息失败',
        type: 'error'
      })
    }
  }

  const handleThemeChange = (theme: string) => {
    actions.appActions.setTheme(theme)
    actions.userActions.updatePreferences({ theme })
  }

  if (!state.user.profile) {
    return <div>请先登录</div>
  }

  return (
    <div className="user-profile">
      <h2>个人资料</h2>
      
      {editing ? (
        <form onSubmit={(e) => { e.preventDefault(); handleSave(); }}>
          <div className="form-group">
            <label>姓名</label>
            <input
              type="text"
              value={formData.name || ''}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            />
          </div>
          
          <div className="form-group">
            <label>邮箱</label>
            <input
              type="email"
              value={formData.email || ''}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            />
          </div>
          
          <div className="form-actions">
            <button type="submit">保存</button>
            <button type="button" onClick={() => setEditing(false)}>取消</button>
          </div>
        </form>
      ) : (
        <div className="profile-display">
          <p><strong>姓名:</strong> {state.user.profile.name}</p>
          <p><strong>邮箱:</strong> {state.user.profile.email}</p>
          <p><strong>角色:</strong> {state.user.profile.role}</p>
          
          <button onClick={() => setEditing(true)}>编辑</button>
        </div>
      )}
      
      <div className="preferences">
        <h3>偏好设置</h3>
        
        <div className="theme-selector">
          <label>主题:</label>
          <select
            value={state.app.theme}
            onChange={(e) => handleThemeChange(e.target.value)}
          >
            <option value="light">浅色</option>
            <option value="dark">深色</option>
            <option value="auto">自动</option>
          </select>
        </div>
        
        <div className="language-selector">
          <label>语言:</label>
          <select
            value={state.app.language}
            onChange={(e) => actions.appActions.setLanguage(e.target.value)}
          >
            <option value="zh-CN">中文</option>
            <option value="en-US">English</option>
          </select>
        </div>
      </div>
    </div>
  )
}

export default UserProfile
```

## 相关链接

- [状态管理指南](/guide/features/state-management)
- [应用间通信示例](/examples/advanced/communication)
- [多应用协作示例](/examples/advanced/multi-app)
- [性能优化指南](/guide/best-practices/performance)

---

*最后更新: 2024-07-27*
# 共享状态示例

本示例展示如何在 Micro-Core 微前端架构中实现应用间的状态共享，包括全局状态管理、状态同步、持久化存储等功能。

## 📋 目录

- [状态架构](#状态架构)
- [全局状态设计](#全局状态设计)
- [状态同步机制](#状态同步机制)
- [状态持久化](#状态持久化)
- [状态中间件](#状态中间件)
- [实时状态同步](#实时状态同步)
- [完整示例](#完整示例)

## 状态架构

共享状态架构采用分层设计，支持多种状态管理模式和同步策略。

### 🏗️ 状态架构图

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    共享状态架构                                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐ │
│  │ 应用 A       │    │ 应用 B       │    │ 应用 C               │ │
│  │ (React)     │    │ (Vue)       │    │ (Angular)           │ │
│  │             │    │             │    │                     │ │
│  │ 本地状态     │    │ 本地状态     │    │ 本地状态             │ │
│  │ • 组件状态   │    │ • 组件状态   │    │ • 组件状态           │ │
│  │ • 页面状态   │    │ • 页面状态   │    │ • 页面状态           │ │
│  └─────────────┘    └─────────────┘    └─────────────────────┘ │
│           │                 │                     │             │
│           └─────────────────┼─────────────────────┘             │
│                             │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    共享状态层                               │ │
│  │                                                             │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 全局状态     │  │ 业务状态     │  │ 应用状态             │ │ │
│  │  │             │  │             │  │                     │ │ │
│  │  │ • 用户信息   │  │ • 购物车     │  │ • 主题配置           │ │ │
│  │  │ • 权限数据   │  │ • 订单数据   │  │ • 语言设置           │ │ │
│  │  │ • 系统配置   │  │ • 库存信息   │  │ • 界面状态           │ │ │
│  │  └─────────────┘