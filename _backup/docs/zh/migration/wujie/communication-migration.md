# wujie 通信迁移指南

本指南详细说明如何将 wujie 的应用间通信机制迁移到 Micro-Core。

## 📋 目录

- [通信机制对比](#通信机制对比)
- [事件总线迁移](#事件总线迁移)
- [Props 传递迁移](#props-传递迁移)
- [PostMessage 通信迁移](#postmessage-通信迁移)
- [全局变量通信迁移](#全局变量通信迁移)
- [完整迁移示例](#完整迁移示例)
- [最佳实践](#最佳实践)

## 通信机制对比

### wujie 通信方式

```javascript
// wujie 主要通信方式
const wujieCommunication = {
  // 1. 事件总线 (bus)
  eventBus: {
    emit: 'bus.$emit(event, data)',
    on: 'bus.$on(event, handler)',
    off: 'bus.$off(event, handler)'
  },
  
  // 2. Props 传递
  props: {
    static: 'startApp({ props: { data } })',
    reactive: '通过 bus 实现响应式'
  },
  
  // 3. PostMessage
  postMessage: {
    send: 'window.parent.postMessage(data, origin)',
    receive: 'window.addEventListener("message", handler)'
  },
  
  // 4. 全局变量
  global: {
    set: 'window.parent.globalVar = value',
    get: 'window.parent.globalVar'
  }
}
```

### Micro-Core 通信方式

```typescript
// Micro-Core 统一通信系统
import { MicroCore, EventBus, GlobalState } from '@micro-core/core'

const microCoreCommunication = {
  // 1. EventBus - 高性能事件总线
  eventBus: {
    emit: 'eventBus.emit(event, data)',
    on: 'eventBus.on(event, handler)',
    off: 'eventBus.off(event, handler)',
    namespace: 'eventBus.namespace("app").emit(event, data)',
    middleware: 'eventBus.use(middleware)'
  },
  
  // 2. GlobalState - 响应式全局状态
  globalState: {
    get: 'globalState.getState()',
    set: 'globalState.setState(data)',
    subscribe: 'globalState.subscribe(handler)',
    computed: 'globalState.computed(fn)'
  },
  
  // 3. 直接通信
  direct: {
    call: 'microCore.callApp(appName, method, data)',
    broadcast: 'microCore.broadcast(event, data)',
    unicast: 'microCore.unicast(appName, event, data)'
  },
  
  // 4. Props 传递
  props: {
    static: 'props: { data }',
    reactive: 'props: { data: () => globalState.data }',
    watch: 'props.watch("data", handler)'
  }
}
```

## 事件总线迁移

### wujie 事件总线

```javascript
// wujie 事件总线使用
import { bus } from 'wujie'

// 主应用
// 监听子应用事件
bus.$on('sub-app-event', (data) => {
  console.log('接收子应用事件:', data)
})

// 发送事件给子应用
bus.$emit('main-app-event', {
  type: 'user-login',
  user: { id: 1, name: 'admin' }
})

// 移除事件监听
bus.$off('sub-app-event')

// 子应用
// 监听主应用事件
bus.$on('main-app-event', (data) => {
  console.log('接收主应用事件:', data)
  if (data.type === 'user-login') {
    updateUserInfo(data.user)
  }
})

// 发送事件给主应用
bus.$emit('sub-app-event', {
  type: 'page-change',
  path: '/dashboard'
})
```

### Micro-Core EventBus 迁移

```typescript
// Micro-Core EventBus 迁移
import { MicroCore, EventBus } from '@micro-core/core'

// 创建事件总线
const eventBus = new EventBus({
  // 命名空间支持
  namespace: true,
  
  // 异步支持
  async: true,
  
  // 中间件支持
  middleware: [
    // 日志中间件
    (event, next) => {
      console.log('事件发送:', event.type, event.data)
      next()
    },
    
    // 验证中间件
    (event, next) => {
      if (event.type.startsWith('secure-') && !event.auth) {
        throw new Error('需要认证')
      }
      next()
    }
  ]
})

const microCore = new MicroCore({
  eventBus,
  apps: [...]
})

// 主应用
// 监听子应用事件
eventBus.on('sub-app:event', (data) => {
  console.log('接收子应用事件:', data)
})

// 发送事件给子应用
eventBus.emit('main-app:event', {
  type: 'user-login',
  user: { id: 1, name: 'admin' }
})

// 移除事件监听
eventBus.off('sub-app:event')

// 子应用
import { getMicroCore } from '@micro-core/core'

export async function mount() {
  const microCore = getMicroCore()
  const eventBus = microCore.getEventBus()
  
  // 监听主应用事件
  eventBus.on('main-app:event', (data) => {
    console.log('接收主应用事件:', data)
    if (data.type === 'user-login') {
      updateUserInfo(data.user)
    }
  })
  
  // 发送事件给主应用
  eventBus.emit('sub-app:event', {
    type: 'page-change',
    path: '/dashboard'
  })
}
```

## Props 传递迁移

### wujie Props 传递

```javascript
// wujie Props 传递
import { startApp, bus } from 'wujie'

// 主应用
let userInfo = { id: 1, name: 'admin' }

startApp({
  name: 'sub-app',
  url: 'http://localhost:3001',
  el: '#container',
  
  // 静态 props
  props: {
    userInfo: userInfo,
    apiUrl: 'https://api.example.com',
    onMessage: (msg) => {
      console.log('子应用消息:', msg)
    }
  }
})

// 响应式 props 需要通过事件实现
function updateUserInfo(newUser) {
  userInfo = newUser
  bus.$emit('user-info-change', newUser)
}

// 子应用
export async function mount(props) {
  const { userInfo, apiUrl, onMessage } = props
  
  console.log('接收 props:', { userInfo, apiUrl })
  
  // 监听 props 变化
  bus.$on('user-info-change', (newUser) => {
    console.log('用户信息更新:', newUser)
    updateUI(newUser)
  })
  
  // 调用主应用方法
  onMessage('子应用已挂载')
}
```

### Micro-Core Props 迁移

```typescript
// Micro-Core Props 迁移
import { MicroCore, GlobalState } from '@micro-core/core'

// 创建全局状态
const globalState = new GlobalState({
  userInfo: { id: 1, name: 'admin' },
  apiUrl: 'https://api.example.com'
})

const microCore = new MicroCore({
  globalState,
  
  apps: [
    {
      name: 'sub-app',
      entry: 'http://localhost:3001',
      container: '#container',
      activeWhen: '/sub-app',
      
      // Props 配置
      props: {
        // 静态 props
        apiUrl: 'https://api.example.com',
        
        // 响应式 props
        userInfo: () => globalState.getState().userInfo,
        
        // 计算属性 props
        isAdmin: () => globalState.getState().userInfo.role === 'admin',
        
        // 方法 props
        onMessage: (msg: string) => {
          console.log('子应用消息:', msg)
        }
      }
    }
  ]
})

// 更新用户信息
function updateUserInfo(newUser: any) {
  globalState.setState({ userInfo: newUser })
  // props 会自动更新
}

// 子应用
import { getMicroCore } from '@micro-core/core'

export async function mount() {
  const microCore = getMicroCore()
  const props = microCore.getProps()
  
  console.log('接收 props:', props)
  
  // 监听 props 变化
  props.watch('userInfo', (newUser, oldUser) => {
    console.log('用户信息更新:', newUser, oldUser)
    updateUI(newUser)
  })
  
  // 调用主应用方法
  props.onMessage('子应用已挂载')
}
```

## PostMessage 通信迁移

### wujie PostMessage

```javascript
// wujie PostMessage 通信
// 主应用
window.addEventListener('message', (event) => {
  if (event.origin !== 'http://localhost:3001') return
  
  console.log('接收子应用消息:', event.data)
  
  if (event.data.type === 'sub-app-ready') {
    // 发送初始化数据
    const iframe = document.querySelector('#sub-app-iframe')
    iframe.contentWindow.postMessage({
      type: 'init-data',
      data: { userInfo: { id: 1, name: 'admin' } }
    }, 'http://localhost:3001')
  }
})

// 子应用
window.addEventListener('message', (event) => {
  if (event.origin !== window.location.origin) return
  
  console.log('接收主应用消息:', event.data)
  
  if (event.data.type === 'init-data') {
    initializeApp(event.data.data)
  }
})

// 通知主应用子应用已就绪
window.parent.postMessage({
  type: 'sub-app-ready'
}, '*')
```

### Micro-Core 直接通信迁移

```typescript
// Micro-Core 直接通信迁移
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  // 通信配置
  communication: {
    // 直接通信配置
    direct: {
      // 安全配置
      security: {
        allowedOrigins: ['http://localhost:3001'],
        validateMessage: true,
        encryption: false
      },
      
      // 消息队列
      queue: {
        enabled: true,
        maxSize: 100,
        timeout: 5000
      }
    }
  },
  
  apps: [
    {
      name: 'sub-app',
      entry: 'http://localhost:3001',
      container: '#container',
      activeWhen: '/sub-app'
    }
  ]
})

// 主应用
// 监听子应用消息
microCore.onMessage('sub-app', (message) => {
  console.log('接收子应用消息:', message)
  
  if (message.type === 'sub-app-ready') {
    // 发送初始化数据
    microCore.sendMessage('sub-app', {
      type: 'init-data',
      data: { userInfo: { id: 1, name: 'admin' } }
    })
  }
})

// 广播消息给所有应用
microCore.broadcast({
  type: 'global-update',
  data: { theme: 'dark' }
})

// 子应用
import { getMicroCore } from '@micro-core/core'

export async function mount() {
  const microCore = getMicroCore()
  
  // 监听主应用消息
  microCore.onMessage((message) => {
    console.log('接收主应用消息:', message)
    
    if (message.type === 'init-data') {
      initializeApp(message.data)
    }
  })
  
  // 发送消息给主应用
  microCore.sendMessage({
    type: 'sub-app-ready'
  })
}
```

## 全局变量通信迁移

### wujie 全局变量通信

```javascript
// wujie 全局变量通信
// 主应用
window.globalData = {
  userInfo: { id: 1, name: 'admin' },
  theme: 'light',
  config: { apiUrl: 'https://api.example.com' }
}

// 更新全局数据
function updateGlobalData(key, value) {
  window.globalData[key] = value
  
  // 通知子应用数据变化
  bus.$emit('global-data-change', { key, value })
}

// 子应用
export async function mount() {
  // 访问全局数据
  const globalData = window.parent.globalData
  console.log('全局数据:', globalData)
  
  // 监听全局数据变化
  bus.$on('global-data-change', ({ key, value }) => {
    console.log('全局数据变化:', key, value)
    handleGlobalDataChange(key, value)
  })
  
  // 修改全局数据
  window.parent.globalData.userInfo.status = 'online'
}
```

### Micro-Core GlobalState 迁移

```typescript
// Micro-Core GlobalState 迁移
import { MicroCore, GlobalState } from '@micro-core/core'

// 创建全局状态
const globalState = new GlobalState({
  userInfo: { id: 1, name: 'admin' },
  theme: 'light',
  config: { apiUrl: 'https://api.example.com' }
}, {
  // 持久化配置
  persist: {
    key: 'micro-core-global-state',
    storage: localStorage,
    include: ['theme', 'config']
  },
  
  // 计算属性
  computed: {
    isLoggedIn: (state) => !!state.userInfo.id,
    isDarkTheme: (state) => state.theme === 'dark'
  }
})

const microCore = new MicroCore({
  globalState,
  apps: [...]
})

// 主应用
// 更新全局状态
function updateGlobalData(key: string, value: any) {
  globalState.setState({ [key]: value })
  // 所有订阅的应用会自动收到更新
}

// 监听状态变化
globalState.subscribe((state, prevState) => {
  console.log('全局状态变化:', state, prevState)
})

// 子应用
import { getMicroCore } from '@micro-core/core'

export async function mount() {
  const microCore = getMicroCore()
  const globalState = microCore.getGlobalState()
  
  // 获取全局状态
  const state = globalState.getState()
  console.log('全局状态:', state)
  
  // 监听状态变化
  globalState.subscribe((state, prevState) => {
    console.log('状态更新:', state, prevState)
    handleStateChange(state, prevState)
  })
  
  // 监听特定属性
  globalState.subscribe('userInfo', (userInfo, prevUserInfo) => {
    console.log('用户信息变化:', userInfo, prevUserInfo)
  })
  
  // 更新状态
  globalState.setState({
    userInfo: { ...state.userInfo, status: 'online' }
  })
}
```

## 完整迁移示例

### wujie 完整通信示例

```javascript
// wujie 完整通信示例
import { startApp, bus } from 'wujie'

// 主应用
class MainApp {
  constructor() {
    this.userInfo = { id: 1, name: 'admin' }
    this.setupCommunication()
    this.startSubApp()
  }
  
  setupCommunication() {
    // 监听子应用事件
    bus.$on('sub-app:user-action', (action) => {
      this.handleUserAction(action)
    })
    
    bus.$on('sub-app:data-request', (request) => {
      this.handleDataRequest(request)
    })
  }
  
  startSubApp() {
    startApp({
      name: 'user-center',
      url: 'http://localhost:3001',
      el: '#user-center',
      props: {
        userInfo: this.userInfo,
        onUserUpdate: (user) => {
          this.userInfo = user
          this.notifyUserChange()
        }
      }
    })
  }
  
  handleUserAction(action) {
    console.log('用户操作:', action)
    if (action.type === 'logout') {
      this.logout()
    }
  }
  
  handleDataRequest(request) {
    const data = this.fetchData(request.type)
    bus.$emit('main-app:data-response', {
      requestId: request.id,
      data: data
    })
  }
  
  notifyUserChange() {
    bus.$emit('main-app:user-change', this.userInfo)
  }
}

// 子应用
export async function mount(props) {
  const { userInfo, onUserUpdate } = props
  
  setupCommunication()
  initApp(userInfo)
}

function setupCommunication() {
  bus.$on('main-app:user-change', (userInfo) => {
    updateUserInfo(userInfo)
  })
  
  bus.$on('main-app:data-response', (response) => {
    handleDataResponse(response)
  })
}
```

### Micro-Core 迁移后示例

```typescript
// Micro-Core 完整通信示例
import { MicroCore, GlobalState, EventBus } from '@micro-core/core'

// 创建全局状态
const globalState = new GlobalState({
  userInfo: { id: 1, name: 'admin' },
  theme: 'light'
})

// 创建事件总线
const eventBus = new EventBus()

// 主应用
class MainApp {
  private microCore: MicroCore
  
  constructor() {
    this.setupMicroCore()
    this.setupCommunication()
    this.startSubApps()
  }
  
  setupMicroCore() {
    this.microCore = new MicroCore({
      globalState,
      eventBus,
      
      apps: [
        {
          name: 'user-center',
          entry: 'http://localhost:3001',
          container: '#user-center',
          activeWhen: '/user-center',
          
          props: {
            // 响应式 props
            userInfo: () => globalState.getState().userInfo,
            theme: () => globalState.getState().theme,
            
            // 方法 props
            onUserUpdate: (user: any) => {
              globalState.setState({ userInfo: user })
            }
          }
        }
      ]
    })
  }
  
  setupCommunication() {
    // 监听子应用事件
    eventBus.on('user-center:user-action', (action) => {
      this.handleUserAction(action)
    })
    
    eventBus.on('user-center:data-request', (request) => {
      this.handleDataRequest(request)
    })
    
    // 监听全局状态变化
    globalState.subscribe('userInfo', (userInfo, prevUserInfo) => {
      console.log('用户信息变化:', userInfo, prevUserInfo)
    })
  }
  
  async startSubApps() {
    await this.microCore.start()
  }
  
  handleUserAction(action: any) {
    console.log('用户操作:', action)
    if (action.type === 'logout') {
      this.logout()
    }
  }
  
  async handleDataRequest(request: any) {
    try {
      const data = await this.fetchData(request.type)
      
      eventBus.emit('main-app:data-response', {
        requestId: request.id,
        data: data
      })
    } catch (error) {
      eventBus.emit('main-app:data-error', {
        requestId: request.id,
        error: error.message
      })
    }
  }
  
  logout() {
    globalState.setState({ userInfo: null })
    eventBus.emit('main-app:logout')
  }
}

// 子应用
import { getMicroCore } from '@micro-core/core'

export async function mount() {
  const microCore = getMicroCore()
  const globalState = microCore.getGlobalState()
  const eventBus = microCore.getEventBus()
  const props = microCore.getProps()
  
  setupCommunication(eventBus, globalState)
  
  const userInfo = globalState.getState().userInfo
  initApp(userInfo)
}

function setupCommunication(eventBus: any, globalState: any) {
  // 监听主应用事件
  eventBus.on('main-app:data-response', (response: any) => {
    handleDataResponse(response)
  })
  
  eventBus.on('main-app:logout', () => {
    handleLogout()
  })
  
  // 监听全局状态变化
  globalState.subscribe('userInfo', (userInfo: any) => {
    updateUserInfo(userInfo)
  })
}

function requestData(eventBus: any, type: string) {
  eventBus.emit('user-center:data-request', {
    id: Date.now(),
    type: type
  })
}
```

## 最佳实践

### 1. 通信方式选择

```typescript
// 通信方式选择指南
const communicationGuide = {
  // 全局状态 - 适用于需要持久化的共享数据
  globalState: {
    use: ['用户信息', '主题设置', '语言配置', '权限数据'],
    example: 'globalState.setState({ user: userInfo })'
  },
  
  // 事件总线 - 适用于临时通信和业务事件
  eventBus: {
    use: ['业务事件', '临时通知', '组件通信', '状态同步'],
    example: 'eventBus.emit("order:created", orderData)'
  },
  
  // 直接通信 - 适用于特定应用间通信
  direct: {
    use: ['应用间调用', '数据传递', '方法调用', '错误处理'],
    example: 'microCore.callApp("app-name", "method", data)'
  },
  
  // Props 传递 - 适用于配置和初始化数据
  props: {
    use: ['配置数据', '初始化参数', '回调函数', '静态数据'],
    example: 'props: { config: () => getConfig() }'
  }
}
```

### 2. 性能优化

```typescript
// 通信性能优化
const performanceOptimization = {
  // 事件防抖节流
  throttle: {
    'high-frequency-event': 100,
    'scroll-event': 16,
    'resize-event': 100
  },
  
  // 批量处理
  batch: {
    enabled: true,
    size: 10,
    timeout: 16
  },
  
  // 消息队列
  queue: {
    enabled: true,
    maxSize: 1000,
    priority: true
  },
  
  // 内存管理
  memory: {
    autoCleanup: true,
    maxListeners: 100,
    gcInterval: 60000
  }
}
```

### 3. 错误处理

```typescript
// 通信错误处理
const errorHandling = {
  // 全局错误处理
  globalErrorHandler: (error: Error, context: any) => {
    console.error('通信错误:', error, context)
    
    // 错误上报
    reportError(error, context)
    
    // 错误恢复
    if (error.name === 'CommunicationError') {
      recoverCommunication(context)
    }
  },
  
  // 超时处理
  timeout: {
    enabled: true,
    duration: 5000,
    retry: 3
  },
  
  // 降级策略
  fallback: {
    enabled: true,
    strategy: 'localStorage' // 'localStorage' | 'sessionStorage' | 'memory'
  }
}
```

### 4. 安全配置

```typescript
// 通信安全配置
const securityConfig = {
  // 消息验证
  validation: {
    enabled: true,
    schema: {
      'user:login': {
        type: 'object',
        properties: {
          userId: { type: 'number' },
          token: { type: 'string' }
        },
        required: ['userId', 'token']
      }
    }
  },
  
  // 权限控制
  permissions: {
    'admin:*': ['admin'],
    'user:*': ['admin', 'user'],
    'public:*': ['*']
  },
  
  // 消息加密
  encryption: {
    enabled: false,
    algorithm: 'AES-256-GCM',
    key: process.env.ENCRYPTION_KEY
  },
  
  // 来源验证
  origin: {
    whitelist: ['http://localhost:3001', 'https://app.example.com'],
    strict: true
  }
}
```

## 迁移检查清单

- [ ] **事件总线迁移**
  - [ ] 将 wujie bus 替换为 Micro-Core EventBus
  - [ ] 添加命名空间和中间件
  - [ ] 更新事件监听和发送逻辑
  - [ ] 测试事件通信功能

- [ ] **Props 传递迁移**
  - [ ] 更新 props 传递方式
  - [ ] 添加响应式 props 支持
  - [ ] 实现 props 监听功能
  - [ ] 测试 props 更新机制

- [ ] **PostMessage 迁移**
  - [ ] 将 PostMessage 替换为直接通信
  - [ ] 添加消息验证和安全配置
  - [ ] 实现消息队列和重试机制
  - [ ] 测试消息传递功能

- [ ] **全局变量迁移**
  - [ ] 将全局变量替换为 GlobalState
  - [ ] 添加状态持久化和计算属性
  - [ ] 实现状态监听和更新
  - [ ] 测试状态同步功能

- [ ] **性能优化**
  - [ ] 实现事件防抖节流
  - [ ] 添加批量处理机制
  - [ ] 配置消息队列
  - [ ] 优化内存使用

- [ ] **错误处理**
  - [ ] 添加全局错误处理
  - [ ] 实现超时和重试机制
  - [ ] 配置降级策略
  - [ ] 添加错误监控

- [ ] **安全加固**
  - [ ] 添加消息验证
  - [ ] 配置权限控制
  - [ ] 实现来源验证
  - [ ] 考虑消息加密

- [ ] **测试验证**
  - [ ] 单元测试
  - [ ] 集成测试
  - [ ] 端到端测试
  - [ ] 性能测试

通过以上详细的迁移指南，您可以将 wujie 的通信机制平滑迁移到 Micro-Core，并享受更强大的通信能力和更好的开发体验。