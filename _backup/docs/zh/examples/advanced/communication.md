# 应用间通信示例

本示例详细展示了 Micro-Core 架构中不同微前端应用之间的通信方式，包括事件总线、状态共享、直接消息传递等多种通信模式。

## 📋 目录

- [通信概述](#通信概述)
- [事件总线通信](#事件总线通信)
- [状态共享通信](#状态共享通信)
- [直接消息传递](#直接消息传递)
- [请求响应模式](#请求响应模式)
- [实时通信](#实时通信)
- [通信中间件](#通信中间件)
- [完整示例](#完整示例)

## 通信概述

在微前端架构中，应用间通信是核心功能之一。Micro-Core 提供了多种通信方式来满足不同的业务场景需求。

### 🔄 通信架构图

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    应用间通信架构                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐ │
│  │ 应用 A       │    │ 应用 B       │    │ 应用 C               │ │
│  │ (React)     │    │ (Vue)       │    │ (Angular)           │ │
│  │             │    │             │    │                     │ │
│  │ • 用户管理   │    │ • 订单处理   │    │ • 库存管理           │ │
│  │ • 权限控制   │    │ • 支付集成   │    │ • 数据分析           │ │
│  └─────────────┘    └─────────────┘    └─────────────────────┘ │
│           │                 │                     │             │
│           └─────────────────┼─────────────────────┘             │
│                             │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    通信层                                   │ │
│  │                                                             │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ EventBus    │  │ GlobalState │  │ DirectChannel       │ │ │
│  │  │             │  │             │  │                     │ │ │
│  │  │ • 事件发布   │  │ • 状态共享   │  │ • 点对点消息         │ │ │
│  │  │ • 事件订阅   │  │ • 状态监听   │  │ • 请求响应           │ │ │
│  │  │ • 事件过滤   │  │ • 状态同步   │  │ • 消息队列           │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  │                             │                               │ │
│  │  ┌─────────────────────────────────────────────────────────┐ │ │
│  │  │                    中间件层                             │ │ │
│  │  │  • 消息验证        • 权限检查        • 日志记录         │ │ │
│  │  │  • 消息转换        • 错误处理        • 性能监控         │ │ │
│  │  └─────────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 事件总线通信

事件总线是最常用的通信方式，采用发布订阅模式，支持一对多的消息传递。

### 基础事件通信

```typescript
// 应用 A - 用户管理应用 (React)
import { useMicroCore } from '@micro-core/adapter-react'

const UserManagementApp = () => {
  const microCore = useMicroCore()

  // 用户登录成功后发布事件
  const handleUserLogin = async (userData) => {
    try {
      const user = await loginUser(userData)
      
      // 发布用户登录事件
      microCore.eventBus.emit('user:login', {
        userId: user.id,
        username: user.username,
        role: user.role,
        permissions: user.permissions,
        timestamp: Date.now()
      })
      
      console.log('用户登录事件已发布')
    } catch (error) {
      // 发布登录失败事件
      microCore.eventBus.emit('user:login:failed', {
        error: error.message,
        timestamp: Date.now()
      })
    }
  }

  // 用户信息更新
  const handleUserUpdate = (userId, updates) => {
    microCore.eventBus.emit('user:updated', {
      userId,
      updates,
      timestamp: Date.now()
    })
  }

  // 用户权限变更
  const handlePermissionChange = (userId, permissions) => {
    microCore.eventBus.emit('user:permissions:changed', {
      userId,
      permissions,
      timestamp: Date.now()
    })
  }

  return (
    <div className="user-management">
      {/* 用户管理界面 */}
    </div>
  )
}
```

```typescript
// 应用 B - 订单管理应用 (Vue)
import { useMicroCore } from '@micro-core/adapter-vue'

export default {
  name: 'OrderManagementApp',
  setup() {
    const microCore = useMicroCore()
    const currentUser = ref(null)
    const userPermissions = ref([])

    // 监听用户登录事件
    microCore.eventBus.on('user:login', (userData) => {
      currentUser.value = userData
      userPermissions.value = userData.permissions
      
      console.log('订单应用收到用户登录事件:', userData)
      
      // 根据用户权限调整界面
      updateUIBasedOnPermissions(userData.permissions)
    })

    // 监听用户权限变更事件
    microCore.eventBus.on('user:permissions:changed', (data) => {
      if (currentUser.value?.userId === data.userId) {
        userPermissions.value = data.permissions
        updateUIBasedOnPermissions(data.permissions)
      }
    })

    // 订单创建成功后发布事件
    const handleOrderCreated = (orderData) => {
      microCore.eventBus.emit('order:created', {
        orderId: orderData.id,
        userId: orderData.userId,
        items: orderData.items,
        total: orderData.total,
        status: 'pending',
        timestamp: Date.now()
      })
    }

    // 订单状态变更
    const handleOrderStatusChange = (orderId, status) => {
      microCore.eventBus.emit('order:status:changed', {
        orderId,
        status,
        timestamp: Date.now()
      })
    }

    const updateUIBasedOnPermissions = (permissions) => {
      // 根据权限更新界面显示
    }

    return {
      currentUser,
      userPermissions,
      handleOrderCreated,
      handleOrderStatusChange
    }
  }
}
```

### 事件命名空间

```typescript
// 使用命名空间组织事件
const setupEventNamespaces = (microCore) => {
  // 用户相关事件命名空间
  const userEvents = microCore.eventBus.namespace('user')
  
  userEvents.on('login', handleUserLogin)
  userEvents.on('logout', handleUserLogout)
  userEvents.on('updated', handleUserUpdate)
  userEvents.on('permissions:changed', handlePermissionChange)

  // 订单相关事件命名空间
  const orderEvents = microCore.eventBus.namespace('order')
  
  orderEvents.on('created', handleOrderCreated)
  orderEvents.on('updated', handleOrderUpdated)
  orderEvents.on('cancelled', handleOrderCancelled)
  orderEvents.on('completed', handleOrderCompleted)

  // 库存相关事件命名空间
  const inventoryEvents = microCore.eventBus.namespace('inventory')
  
  inventoryEvents.on('updated', handleInventoryUpdate)
  inventoryEvents.on('low-stock', handleLowStockAlert)
  inventoryEvents.on('out-of-stock', handleOutOfStockAlert)
}
```

### 事件过滤和条件订阅

```typescript
// 高级事件订阅 - 带条件过滤
const setupAdvancedEventListeners = (microCore) => {
  // 只监听特定用户的事件
  microCore.eventBus.on('user:*', (eventName, data) => {
    if (data.userId === getCurrentUserId()) {
      handleUserEvent(eventName, data)
    }
  })

  // 只监听高价值订单事件
  microCore.eventBus.on('order:created', (orderData) => {
    if (orderData.total > 1000) {
      handleHighValueOrder(orderData)
    }
  })

  // 监听紧急库存预警
  microCore.eventBus.on('inventory:low-stock', (data) => {
    if (data.level === 'critical') {
      handleCriticalStockAlert(data)
    }
  })
}
```

## 状态共享通信

通过全局状态管理实现应用间的数据共享和同步。

### 全局状态定义

```typescript
// shared/state/globalState.ts
import { createGlobalState } from '@micro-core/core'

// 定义全局状态结构
interface GlobalState {
  // 用户状态
  user: {
    currentUser: User | null
    isAuthenticated: boolean
    permissions: string[]
    preferences: UserPreferences
  }

  // 业务状态
  business: {
    selectedCustomer: Customer | null
    currentOrder: Order | null
    shoppingCart: CartItem[]
    notifications: Notification[]
  }

  // 应用状态
  app: {
    theme: 'light' | 'dark'
    language: 'zh-CN' | 'en-US'
    loading: boolean
    errors: AppError[]
  }

  // 实时数据
  realtime: {
    onlineUsers: number
    systemStatus: 'normal' | 'maintenance' | 'error'
    inventoryAlerts: InventoryAlert[]
  }
}

// 创建全局状态
export const globalState = createGlobalState<GlobalState>({
  user: {
    currentUser: null,
    isAuthenticated: false,
    permissions: [],
    preferences: {}
  },
  business: {
    selectedCustomer: null,
    currentOrder: null,
    shoppingCart: [],
    notifications: []
  },
  app: {
    theme: 'light',
    language: 'zh-CN',
    loading: false,
    errors: []
  },
  realtime: {
    onlineUsers: 0,
    systemStatus: 'normal',
    inventoryAlerts: []
  }
})
```

### 状态操作和监听

```typescript
// 应用 A - 用户管理应用中的状态操作
const UserApp = () => {
  const microCore = useMicroCore()

  // 监听全局用户状态变化
  useEffect(() => {
    const unsubscribe = microCore.globalState.watch('user.currentUser', (newUser, oldUser) => {
      console.log('用户状态变化:', { newUser, oldUser })
      
      if (newUser) {
        // 用户登录，更新本地状态
        setLocalUser(newUser)
        loadUserSpecificData(newUser.id)
      } else {
        // 用户登出，清理本地状态
        clearLocalData()
      }
    })

    return unsubscribe
  }, [])

  // 用户登录后更新全局状态
  const handleLogin = async (credentials) => {
    try {
      const user = await authenticateUser(credentials)
      
      // 更新全局状态
      microCore.globalState.set('user.currentUser', user)
      microCore.globalState.set('user.isAuthenticated', true)
      microCore.globalState.set('user.permissions', user.permissions)
      
      // 同时发布事件
      microCore.eventBus.emit('user:login', user)
      
    } catch (error) {
      microCore.globalState.set('app.errors', [...globalState.get('app.errors'), {
        message: error.message,
        timestamp: Date.now(),
        source: 'user-app'
      }])
    }
  }

  // 更新用户偏好设置
  const updateUserPreferences = (preferences) => {
    microCore.globalState.set('user.preferences', {
      ...microCore.globalState.get('user.preferences'),
      ...preferences
    })
  }

  return <div>用户管理界面</div>
}
```

```typescript
// 应用 B - 订单管理应用中的状态监听
const OrderApp = () => {
  const microCore = useMicroCore()
  const [currentUser, setCurrentUser] = useState(null)
  const [shoppingCart, setShoppingCart] = useState([])

  useEffect(() => {
    // 监听当前用户变化
    const unsubscribeUser = microCore.globalState.watch('user.currentUser', (user) => {
      setCurrentUser(user)
      if (user) {
        loadUserOrders(user.id)
      }
    })

    // 监听购物车变化
    const unsubscribeCart = microCore.globalState.watch('business.shoppingCart', (cart) => {
      setShoppingCart(cart)
      updateCartDisplay(cart)
    })

    // 监听主题变化
    const unsubscribeTheme = microCore.globalState.watch('app.theme', (theme) => {
      document.body.className = `theme-${theme}`
    })

    return () => {
      unsubscribeUser()
      unsubscribeCart()
      unsubscribeTheme()
    }
  }, [])

  // 添加商品到购物车
  const addToCart = (product) => {
    const currentCart = microCore.globalState.get('business.shoppingCart')
    const updatedCart = [...currentCart, {
      id: product.id,
      name: product.name,
      price: product.price,
      quantity: 1,
      timestamp: Date.now()
    }]
    
    // 更新全局购物车状态
    microCore.globalState.set('business.shoppingCart', updatedCart)
    
    // 发布购物车更新事件
    microCore.eventBus.emit('cart:item:added', {
      product,
      cartTotal: updatedCart.length
    })
  }

  return <div>订单管理界面</div>
}
```

### 计算属性和派生状态

```typescript
// 创建计算属性
const setupComputedStates = (microCore) => {
  // 计算购物车总价
  microCore.globalState.computed('business.cartTotal', () => {
    const cart = microCore.globalState.get('business.shoppingCart')
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0)
  })

  // 计算用户显示名称
  microCore.globalState.computed('user.displayName', () => {
    const user = microCore.globalState.get('user.currentUser')
    return user ? `${user.name} (${user.role})` : '未登录'
  })

  // 计算未读通知数量
  microCore.globalState.computed('business.unreadNotifications', () => {
    const notifications = microCore.globalState.get('business.notifications')
    return notifications.filter(n => !n.read).length
  })
}
```

## 直接消息传递

应用间的点对点消息传递，支持请求响应模式。

### 基础消息传递

```typescript
// 应用 A 发送消息到应用 B
const UserApp = () => {
  const microCore = useMicroCore()

  // 发送用户数据到订单应用
  const sendUserDataToOrderApp = async (userData) => {
    try {
      await microCore.sendMessage('order-management', 'user:data', {
        type: 'user_selected',
        user: userData,
        timestamp: Date.now()
      })
      
      console.log('用户数据已发送到订单应用')
    } catch (error) {
      console.error('发送消息失败:', error)
    }
  }

  // 接收来自其他应用的消息
  useEffect(() => {
    const handleMessage = (message, fromApp) => {
      console.log(`收到来自 ${fromApp} 的消息:`, message)
      
      switch (message.type) {
        case 'user_info_request':
          handleUserInfoRequest(message.data, fromApp)
          break
        case 'permission_check':
          handlePermissionCheck(message.data, fromApp)
          break
        default:
          console.log('未知消息类型:', message.type)
      }
    }

    microCore.onMessage('user:*', handleMessage)
    
    return () => microCore.offMessage('user:*', handleMessage)
  }, [])

  const handleUserInfoRequest = async (requestData, fromApp) => {
    const userInfo = await getUserInfo(requestData.userId)
    
    // 回复消息
    microCore.sendMessage(fromApp, 'user:info:response', {
      requestId: requestData.requestId,
      userInfo,
      timestamp: Date.now()
    })
  }

  return <div>用户管理界面</div>
}
```

### 消息路由和转发

```typescript
// 消息路由器
class MessageRouter {
  private microCore: any
  private routes: Map<string, RouteHandler> = new Map()

  constructor(microCore: any) {
    this.microCore = microCore
    this.setupRouting()
  }

  // 添加路由规则
  addRoute(pattern: string, handler: RouteHandler) {
    this.routes.set(pattern, handler)
  }

  // 设置消息路由
  private setupRouting() {
    this.microCore.onMessage('*', (message: any, fromApp: string) => {
      this.routeMessage(message, fromApp)
    })
  }

  // 路由消息
  private async routeMessage(message: any, fromApp: string) {
    for (const [pattern, handler] of this.routes.entries()) {
      if (this.matchPattern(pattern, message.type)) {
        try {
          await handler(message, fromApp, this.microCore)
        } catch (error) {
          console.error(`路由处理失败 ${pattern}:`, error)
        }
        break
      }
    }
  }

  // 模式匹配
  private matchPattern(pattern: string, messageType: string): boolean {
    const regex = new RegExp(pattern.replace('*', '.*'))
    return regex.test(messageType)
  }
}

// 使用消息路由器
const setupMessageRouting = (microCore) => {
  const router = new MessageRouter(microCore)

  // 用户相关消息路由
  router.addRoute('user:*', async (message, fromApp, mc) => {
    console.log(`处理用户消息: ${message.type} from ${fromApp}`)
    
    switch (message.type) {
      case 'user:info:request':
        const userInfo = await getUserService().getUser(message.data.userId)
        mc.sendMessage(fromApp, 'user:info:response', {
          requestId: message.data.requestId,
          userInfo
        })
        break
        
      case 'user:permission:check':
        const hasPermission = await checkUserPermission(
          message.data.userId, 
          message.data.permission
        )
        mc.sendMessage(fromApp, 'user:permission:response', {
          requestId: message.data.requestId,
          hasPermission
        })
        break
    }
  })

  // 订单相关消息路由
  router.addRoute('order:*', async (message, fromApp, mc) => {
    console.log(`处理订单消息: ${message.type} from ${fromApp}`)
    // 处理订单相关消息
  })
}

interface RouteHandler {
  (message: any, fromApp: string, microCore: any): Promise<void>
}
```

## 请求响应模式

实现类似 HTTP 请求响应的通信模式。

### 请求响应实现

```typescript
// 请求响应服务
class RequestResponseService {
  private microCore: any
  private pendingRequests: Map<string, PendingRequest> = new Map()
  private requestHandlers: Map<string, RequestHandler> = new Map()

  constructor(microCore: any) {
    this.microCore = microCore
    this.setupResponseHandling()
  }

  // 发送请求
  async request<T>(targetApp: string, action: string, data?: any, timeout: number = 5000): Promise<T> {
    const requestId = this.generateRequestId()
    
    return new Promise((resolve, reject) => {
      // 设置超时
      const timeoutId = setTimeout(() => {
        this.pendingRequests.delete(requestId)
        reject(new Error(`请求超时: ${targetApp}.${action}`))
      }, timeout)

      // 存储待处理请求
      this.pendingRequests.set(requestId, {
        resolve,
        reject,
        timeoutId,
        timestamp: Date.now()
      })

      // 发送请求消息
      this.microCore.sendMessage(targetApp, 'request', {
        requestId,
        action,
        data,
        timestamp: Date.now()
      })
    })
  }

  // 注册请求处理器
  registerHandler(action: string, handler: RequestHandler) {
    this.requestHandlers.set(action, handler)
  }

  // 设置响应处理
  private setupResponseHandling() {
    // 处理请求消息
    this.microCore.onMessage('request', async (message: any, fromApp: string) => {
      const { requestId, action, data } = message
      
      try {
        const handler = this.requestHandlers.get(action)
        if (!handler) {
          throw new Error(`未找到处理器: ${action}`)
        }

        const result = await handler(data, fromApp)
        
        // 发送成功响应
        this.microCore.sendMessage(fromApp, 'response', {
          requestId,
          success: true,
          data: result,
          timestamp: Date.now()
        })
      } catch (error) {
        // 发送错误响应
        this.microCore.sendMessage(fromApp, 'response', {
          requestId,
          success: false,
          error: error.message,
          timestamp: Date.now()
        })
      }
    })

    // 处理响应消息
    this.microCore.onMessage('response', (message: any) => {
      const { requestId, success, data, error } = message
      const pendingRequest = this.pendingRequests.get(requestId)
      
      if (pendingRequest) {
        clearTimeout(pendingRequest.timeoutId)
        this.pendingRequests.delete(requestId)
        
        if (success) {
          pendingRequest.resolve(data)
        } else {
          pendingRequest.reject(new Error(error))
        }
      }
    })
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

interface PendingRequest {
  resolve: (value: any) => void
  reject: (error: Error) => void
  timeoutId: NodeJS.Timeout
  timestamp: number
}

interface RequestHandler {
  (data: any, fromApp: string): Promise<any>
}
```

### 使用请求响应服务

```typescript
// 在应用中使用请求响应服务
const OrderApp = () => {
  const microCore = useMicroCore()
  const requestService = new RequestResponseService(microCore)

  useEffect(() => {
    // 注册请求处理器
    requestService.registerHandler('getOrders', async (data, fromApp) => {
      console.log(`处理来自 ${fromApp} 的订单查询请求`)
      return await getOrdersByUserId(data.userId)
    })

    requestService.registerHandler('createOrder', async (data, fromApp) => {
      console.log(`处理来自 ${fromApp} 的订单创建请求`)
      const order = await createNewOrder(data)
      
      // 通知库存应用更新库存
      microCore.eventBus.emit('order:created', order)
      
      return order
    })
  }, [])

  // 请求用户信息
  const loadUserInfo = async (userId: string) => {
    try {
      const userInfo = await requestService.request('user-management', 'getUserInfo', { userId })
      console.log('获取到用户信息:', userInfo)
      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }

  // 请求库存信息
  const checkInventory = async (productIds: string[]) => {
    try {
      const inventory = await requestService.request('inventory-management', 'checkStock', { productIds })
      return inventory
    } catch (error) {
      console.error('检查库存失败:', error)
    }
  }

  return <div>订单管理界面</div>
}
```

## 实时通信

支持 WebSocket 等实时通信协议。

### WebSocket 集成

```typescript
// WebSocket 通信服务
class WebSocketCommunicationService {
  private microCore: any
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000

  constructor(microCore: any, wsUrl: string) {
    this.microCore = microCore
    this.connect(wsUrl)
  }

  // 连接 WebSocket
  private connect(wsUrl: string) {
    try {
      this.ws = new WebSocket(wsUrl)
      
      this.ws.onopen = () => {
        console.log('WebSocket 连接已建立')
        this.reconnectAttempts = 0
        this.microCore.eventBus.emit('websocket:connected')
      }

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          this.handleWebSocketMessage(message)
        } catch (error) {
          console.error('解析 WebSocket 消息失败:', error)
        }
      }

      this.ws.onclose = () => {
        console.log('WebSocket 连接已关闭')
        this.microCore.eventBus.emit('websocket:disconnected')
        this.attemptReconnect(wsUrl)
      }

      this.ws.onerror = (error) => {
        console.error('WebSocket 错误:', error)
        this.microCore.eventBus.emit('websocket:error', error)
      }
    } catch (error) {
      console.error('WebSocket 连接失败:', error)
      this.attemptReconnect(wsUrl)
    }
  }

  // 处理 WebSocket 消息
  private handleWebSocketMessage(message: any) {
    switch (message.type) {
      case 'broadcast':
        // 广播消息到所有应用
        this.microCore.eventBus.emit('realtime:broadcast', message.data)
        break
        
      case 'targeted':
        // 定向消息
        if (message.targetApp) {
          this.microCore.sendMessage(message.targetApp, 'realtime:message', message.data)
        }
        break
        
      case 'system':
        // 系统消息
        this.microCore.eventBus.emit('system:message', message.data)
        break
        
      default:
        console.log('未知 WebSocket 消息类型:', message.type)
    }
  }

  // 发送 WebSocket 消息
  send(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket 未连接，消息发送失败')
    }
  }

  // 尝试重连
  private attemptReconnect(wsUrl: string) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`尝试重连 WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect(wsUrl)
      }, this.reconnectDelay * this.reconnectAttempts)
    } else {
      console.error('WebSocket 重连失败，已达到最大重试次数')
    }
  }

  // 关闭连接
  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }
}
```

### 实时数据同步

```typescript
// 实时数据同步服务
class RealtimeDataSync {
  private microCore: any
  private wsService: WebSocketCommunicationService
  private syncRules: Map<string, SyncRule> = new Map()

  constructor(microCore: any, wsService: WebSocketCommunicationService) {
    this.microCore = microCore
    this.wsService = wsService
    this.setupRealtimeListeners()
  }

  // 添加同步规则
  addSyncRule(dataType: string, rule: SyncRule) {
    this.syncRules.set(dataType, rule)
  }

  // 设置实时监听器
  private setupRealtimeListeners() {
    // 监听实时广播消息
    this.microCore.eventBus.on('realtime:broadcast', (data: any) => {
      this.handleRealtimeData(data)
    })

    // 监听本地数据变化，同步到服务器
    this.microCore.globalState.watch('*', (path: string, newValue: any, oldValue: any) => {
      const rule = this.findSyncRule(path)
      if (rule && rule.syncToServer) {
        this.syncToServer(path, newValue, oldValue)
      }
    })
  }

  // 处理实时数据
  private handleRealtimeData(data: any) {
    const { type, payload, timestamp } = data
    
    switch (type) {
      case 'user_online':
        this.microCore.globalState.set('realtime.onlineUsers', payload.count)
        this.microCore.eventBus.emit('user:online:changed', payload)
        break
        
      case 'inventory_update':
        this.microCore.globalState.set('realtime.inventoryAlerts', payload.alerts)
        this.microCore.eventBus.emit('inventory:realtime:update', payload)
        break
        
      case 'order_status':
        this.microCore.eventBus.emit('order:realtime:status', payload)
        break
        
      case 'system_notification':
        this.addSystemNotification(payload)
        break
    }
  }

  // 同步数据到服务器
  private syncToServer(path: string, newValue: any, oldValue: any) {
    this.wsService.send({
      type: 'data_sync',
      data: {
        path,
        newValue,
        oldValue,
        timestamp: Date.now(),
        source: 'client'
      }
    })
  }

  // 查找同步规则
  private findSyncRule(path: string): SyncRule | null {
    for (const [pattern, rule] of this.syncRules.entries()) {
      if (path.startsWith(pattern)) {
        return rule
      }
    }
    return null
  }

  // 添加系统通知
  private addSystemNotification(notification: any) {
    const notifications = this.microCore.globalState.get('business.notifications')
    this.microCore.globalState.set('business.notifications', [
      ...notifications,
      {
        ...notification,
        id: Date.now().toString(),
        timestamp: Date.now(),
        read: false
      }
    ])
  }
}

interface SyncRule {
  syncToServer: boolean
  syncFromServer: boolean
  throttle?: number
  transform?: (data: any) => any
}
```

## 通信中间件

实现通信过程中的拦截、转换和增强功能。

### 中间件系统

```typescript
// 通信中间件管理器
class CommunicationMiddleware {
  private middlewares: Middleware[] = []

  // 添加中间件
  use(middleware: Middleware) {
    this.middlewares.push(middleware)
  }

  // 执行中间件链
  async execute(context: MiddlewareContext): Promise<any> {
    let index = 0

    const next = async (): Promise<any> => {
      if (index >= this.middlewares.length) {
        return context.proceed()
      }

      const middleware = this.middlewares[index++]
      return await middleware(context, next)
    }

    return await next()
  }
}

// 中间件接口
interface Middleware {
  (context: MiddlewareContext, next: () => Promise<any>): Promise<any>
}

interface MiddlewareContext {
  type: 'event' | 'message' | 'state'
  data: any
  source: string
  target?: string
  timestamp: number
  proceed: () => Promise<any>
}

// 常用中间件实现
export const createLoggingMiddleware = (): Middleware => {
  return async (context, next) => {
    console.log(`[${context.type}] ${context.source} -> ${context.target || 'broadcast'}:`, context.data)
    const start = Date.now()
    
    try {
      const result = await next()
      const duration = Date.now() - start
      console.log(`[${context.type}] 完成，耗时: ${duration}ms`)
      return result
    } catch (error) {
      const duration = Date.now() - start
      console.error(`[${context.type}] 失败，耗时: ${duration}ms:`, error)
      throw error
    }
  }
}

export const createAuthMiddleware = (microCore: any): Middleware => {
  return async (context, next) => {
    // 检查用户权限
    const user = microCore.globalState.get('user.currentUser')
    if (!user) {
      throw new Error('用户未登录')
    }

    // 检查特定权限
    if (context.type === 'message' && context.data.requiresPermission) {
      const hasPermission = user.permissions.includes(context.data.requiresPermission)
      if (!hasPermission) {
        throw new Error('权限不足')
      }
    }

    return await next()
  }
}

export const createValidationMiddleware = (): Middleware => {
  return async (context, next) => {
    // 数据验证
    if (context.data && typeof context.data === 'object') {
      // 检查必需字段
      if (context.data.requiredFields) {
        for (const field of context.data.requiredFields) {
          if (!context.data[field]) {
            throw new Error(`缺少必需字段: ${field}`)
          }
        }
      }

      // 数据类型验证
      if (context.data.schema) {
        validateSchema(context.data, context.data.schema)
      }
    }

    return await next()
  }
}

// 使用中间件
const setupCommunicationMiddleware = (microCore: any) => {
  const middleware = new CommunicationMiddleware()
  
  // 添加中间件
  middleware.use(createLoggingMiddleware())
  middleware.use(createAuthMiddleware(microCore))
  middleware.use(createValidationMiddleware())

  // 拦截事件发布
  const originalEmit = microCore.eventBus.emit
  microCore.eventBus.emit = async (event: string, data: any) => {
    await middleware.execute({
      type: 'event',
      data: { event, data },
      source: 'current-app',
      timestamp: Date.now(),
      proceed: () => originalEmit.call(microCore.eventBus, event, data)
    })
  }

  // 拦截消息发送
  const originalSendMessage = microCore.sendMessage
  microCore.sendMessage = async (target: string, type: string, data: any) => {
    await middleware.execute({
      type: 'message',
      data: { type, data },
      source: 'current-app',
      target,
      timestamp: Date.now(),
      proceed: () => originalSendMessage.call(microCore, target, type, data)
    })
  }
}
```

## 完整示例

### 综合通信示例

```typescript
// 主应用 - 通信协调器
const MainApp = () => {
  const microCore = useMicroCore()

  useEffect(() => {
    // 设置通信中间件
    setupCommunicationMiddleware(microCore)

    // 设置消息路由
    setupMessageRouting(microCore)

    // 设置实时通信
    const wsService = new WebSocketCommunicationService(microCore, 'ws://localhost:8080')
    const realtimeSync = new RealtimeDataSync(microCore, wsService)

    // 添加实时同步规则
    realtimeSync.addSyncRule('user', { syncToServer: true, syncFromServer: true })
    realtimeSync.addSyncRule('business.shoppingCart', { syncToServer: true, syncFromServer: false })

    // 设置全局事件监听
    setupGlobalEventListeners(microCore)

    return () => {
      wsService.disconnect()
    }
  }, [])

  const setupGlobalEventListeners = (mc: any) => {
    // 监听所有应用的错误事件
    mc.eventBus.on('app:error', (error: any) => {
      console.error('应用错误:', error)
      // 发送错误报告
      mc.sendMessage('monitoring-service', 'error:report', error)
    })

    // 监听性能事件
    mc.eventBus.on('app:performance', (metrics: any) => {
      console.log('性能指标:', metrics)
      // 更新性能监控
      mc.globalState.set('app.performance', metrics)
    })

    // 监听业务事件
    mc.eventBus.on('business:*', (eventName: string, data: any) => {
      console.log(`业务事件: ${eventName}`, data)
      // 记录业务日志
      mc.sendMessage('analytics-service', 'business:event', {
        event: eventName,
        data,
        timestamp: Date.now()
      })
    })
  }

  return (
    <div className="main-app">
      <h1>微前端通信示例</h1>
      <div className="app-container">
        <div id="user-app"></div>
        <div id="order-app"></div>
        <div id="inventory-app"></div>
      </div>
    </div>
  )
}

export default MainApp
```

### 运行示例

```bash
# 启动主应用
cd main-app
npm install && npm start

# 启动用户管理应用
cd user-management
npm install && npm start

# 启动订单管理应用
cd order-management
npm install && npm start

# 启动库存管理应用
cd inventory-management
npm install && npm start

# 启动 WebSocket 服务器
cd websocket-server
npm install && npm start
```

## 相关链接

- [事件总线 API](/api/communication#事件总线)
- [状态管理指南](/guide/features/state-management)
- [多应用协作示例](/examples/advanced/multi-app)
- [共享状态示例](/examples/advanced/shared-state)
- [性能优化指南](/guide/best-practices/performance)

---

*最后更新: 2024-07-27*
