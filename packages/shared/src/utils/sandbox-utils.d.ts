/**
 * 共享工具包 - 沙箱相关工具函数
 *
 * @description 提供沙箱系统相关的通用工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import type { SandboxConfig, SandboxContext, SandboxInstance, SandboxType } from '../types';
/**
 * 沙箱选项接口
 */
export interface SandboxOptions {
    /** 沙箱名称 */
    name: string;
    /** 沙箱类型 */
    type: SandboxType;
    /** 是否严格模式 */
    strict?: boolean;
    /** 全局变量白名单 */
    globalWhitelist?: string[];
    /** 全局变量黑名单 */
    globalBlacklist?: string[];
    /** 是否启用样式隔离 */
    styleIsolation?: boolean;
    /** 自定义选项 */
    customOptions?: Record<string, any>;
}
/**
 * 沙箱统计信息接口
 */
export interface SandboxStats {
    /** 沙箱名称 */
    name: string;
    /** 沙箱类型 */
    type: SandboxType;
    /** 是否激活 */
    active: boolean;
    /** 创建时间 */
    createdAt: number;
    /** 激活时间 */
    activatedAt?: number;
    /** 停用时间 */
    deactivatedAt?: number;
    /** 执行脚本次数 */
    scriptExecutions: number;
    /** 内存使用情况 */
    memoryUsage?: {
        used: number;
        total: number;
    };
    /** 性能指标 */
    performance?: {
        averageExecutionTime: number;
        totalExecutionTime: number;
    };
}
/**
 * 默认全局变量白名单
 */
export declare const DEFAULT_GLOBAL_WHITELIST: string[];
/**
 * 默认全局变量黑名单
 */
export declare const DEFAULT_GLOBAL_BLACKLIST: string[];
/**
 * 验证沙箱配置
 * @param config 沙箱配置
 * @throws {Error} 配置无效时抛出错误
 */
export declare function validateSandboxConfig(config: SandboxConfig): void;
/**
 * 标准化沙箱配置
 * @param options 沙箱选项
 * @returns 标准化后的沙箱配置
 */
export declare function normalizeSandboxConfig(options: SandboxOptions): SandboxConfig;
/**
 * 检查沙箱类型是否支持
 * @param type 沙箱类型
 * @returns 是否支持
 */
export declare function isSandboxTypeSupported(type: SandboxType): boolean;
/**
 * 获取推荐的沙箱类型
 * @returns 推荐的沙箱类型
 */
export declare function getRecommendedSandboxType(): SandboxType;
/**
 * 创建沙箱上下文
 * @param name 沙箱名称
 * @param window 窗口对象
 * @param document 文档对象
 * @returns 沙箱上下文
 */
export declare function createSandboxContext(name: string, window?: Window, document?: Document): SandboxContext;
/**
 * 检查全局变量是否被允许访问
 * @param key 变量名
 * @param whitelist 白名单
 * @param blacklist 黑名单
 * @returns 是否允许访问
 */
export declare function isGlobalVariableAllowed(key: string, whitelist?: string[], blacklist?: string[]): boolean;
/**
 * 过滤全局变量
 * @param globals 全局变量对象
 * @param whitelist 白名单
 * @param blacklist 黑名单
 * @returns 过滤后的全局变量对象
 */
export declare function filterGlobalVariables(globals: Record<string, any>, whitelist?: string[], blacklist?: string[]): Record<string, any>;
/**
 * 创建安全的执行环境
 * @param code 要执行的代码
 * @param context 执行上下文
 * @returns 包装后的代码
 */
export declare function createSafeExecutionEnvironment(code: string, context?: Record<string, any>): string;
/**
 * 检测代码中的危险操作
 * @param code 要检测的代码
 * @returns 检测结果
 */
export declare function detectDangerousOperations(code: string): {
    safe: boolean;
    issues: string[];
};
/**
 * 清理沙箱环境
 * @param context 沙箱上下文
 */
export declare function cleanupSandboxEnvironment(context: SandboxContext): void;
/**
 * 获取沙箱性能指标
 * @param sandbox 沙箱实例
 * @returns 性能指标
 */
export declare function getSandboxPerformanceMetrics(sandbox: SandboxInstance): Record<string, any>;
/**
 * 获取内存使用情况
 * @returns 内存使用情况
 */
export declare function getMemoryUsage(): {
    used: number;
    total: number;
} | null;
/**
 * 比较沙箱性能
 * @param sandbox1 沙箱1
 * @param sandbox2 沙箱2
 * @returns 比较结果
 */
export declare function compareSandboxPerformance(sandbox1: SandboxInstance, sandbox2: SandboxInstance): {
    faster: string;
    difference: number;
    metrics: {
        sandbox1: Record<string, any>;
        sandbox2: Record<string, any>;
    };
};
//# sourceMappingURL=sandbox-utils.d.ts.map