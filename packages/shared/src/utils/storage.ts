/**
 * @fileoverview 存储工具函数
 * @description 提供localStorage和sessionStorage的封装功能
 * <AUTHOR> <<EMAIL>>
 */

import { isBrowser } from './env';

/**
 * 存储类型
 */
export type StorageType = 'localStorage' | 'sessionStorage';

/**
 * 获取存储对象
 */
function getStorage(type: StorageType): Storage | null {
    if (!isBrowser()) return null;
    return type === 'localStorage' ? window.localStorage : window.sessionStorage;
}

/**
 * 设置存储项
 */
export function setItem(key: string, value: any, type: StorageType = 'localStorage'): boolean {
    try {
        const storage = getStorage(type);
        if (!storage) return false;

        const serializedValue = JSON.stringify(value);
        storage.setItem(key, serializedValue);
        return true;
    } catch (error) {
        console.warn(`存储设置失败: ${key}`, error);
        return false;
    }
}

/**
 * 获取存储项
 */
export function getItem<T = any>(key: string, defaultValue?: T, type: StorageType = 'localStorage'): T | undefined {
    try {
        const storage = getStorage(type);
        if (!storage) return defaultValue;

        const item = storage.getItem(key);
        if (item === null) return defaultValue;

        return JSON.parse(item) as T;
    } catch (error) {
        console.warn(`存储获取失败: ${key}`, error);
        return defaultValue;
    }
}

/**
 * 移除存储项
 */
export function removeItem(key: string, type: StorageType = 'localStorage'): boolean {
    try {
        const storage = getStorage(type);
        if (!storage) return false;

        storage.removeItem(key);
        return true;
    } catch (error) {
        console.warn(`存储移除失败: ${key}`, error);
        return false;
    }
}

/**
 * 清空存储
 */
export function clear(type: StorageType = 'localStorage'): boolean {
    try {
        const storage = getStorage(type);
        if (!storage) return false;

        storage.clear();
        return true;
    } catch (error) {
        console.warn(`存储清空失败`, error);
        return false;
    }
}

/**
 * 检查存储项是否存在
 */
export function hasItem(key: string, type: StorageType = 'localStorage'): boolean {
    const storage = getStorage(type);
    if (!storage) return false;

    return storage.getItem(key) !== null;
}

/**
 * 获取所有存储键
 */
export function getKeys(type: StorageType = 'localStorage'): string[] {
    const storage = getStorage(type);
    if (!storage) return [];

    const keys: string[] = [];
    for (let i = 0; i < storage.length; i++) {
        const key = storage.key(i);
        if (key) keys.push(key);
    }

    return keys;
}

/**
 * 获取存储大小（字节）
 */
export function getSize(type: StorageType = 'localStorage'): number {
    const storage = getStorage(type);
    if (!storage) return 0;

    let size = 0;
    for (let i = 0; i < storage.length; i++) {
        const key = storage.key(i);
        if (key) {
            const value = storage.getItem(key);
            if (value) {
                size += key.length + value.length;
            }
        }
    }

    return size;
}

/**
 * 带过期时间的存储
 */
export function setItemWithExpiry(
    key: string,
    value: any,
    expiryMs: number,
    type: StorageType = 'localStorage'
): boolean {
    const now = Date.now();
    const item = {
        value,
        expiry: now + expiryMs
    };

    return setItem(key, item, type);
}

/**
 * 获取带过期时间的存储项
 */
export function getItemWithExpiry<T = any>(
    key: string,
    defaultValue?: T,
    type: StorageType = 'localStorage'
): T | undefined {
    const item = getItem<{ value: T; expiry: number }>(key, null, type);

    if (!item || typeof item !== 'object' || !('expiry' in item)) {
        return defaultValue;
    }

    const now = Date.now();
    if (now > item.expiry) {
        removeItem(key, type);
        return defaultValue;
    }

    return item.value;
}
