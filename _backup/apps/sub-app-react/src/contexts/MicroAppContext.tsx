/**
 * @fileoverview 微前端应用上下文
 * <AUTHOR> <<EMAIL>>
 */

import React, { createContext, ReactNode, useContext } from 'react'

// 微前端应用属性接口
export interface MicroAppContextValue {
    name?: string
    basename?: string
    theme?: 'light' | 'dark'
    user?: any
    container?: HTMLElement | string
    [key: string]: any
}

// 创建上下文
const MicroAppContext = createContext<MicroAppContextValue>({})

// 上下文提供者属性
interface MicroAppProviderProps {
    children: ReactNode
    value: MicroAppContextValue
}

/**
 * 微前端应用上下文提供者
 */
export const MicroAppProvider: React.FC<MicroAppProviderProps> = ({ children, value }) => {
    return (
        <MicroAppContext.Provider value={value}>
            {children}
        </MicroAppContext.Provider>
    )
}

/**
 * 使用微前端应用上下文的 Hook
 */
export const useMicroApp = (): MicroAppContextValue => {
    const context = useContext(MicroAppContext)

    if (context === undefined) {
        throw new Error('useMicroApp must be used within a MicroAppProvider')
    }

    return context
}

/**
 * 获取微前端应用信息的 Hook
 */
export const useMicroAppInfo = () => {
    const context = useMicroApp()

    return {
        name: context.name || 'sub-app-react',
        basename: context.basename || '/react-app',
        theme: context.theme || 'light',
        user: context.user,
        isMicroFrontend: !!window.__POWERED_BY_MICRO_CORE__,
        isStandalone: !window.__POWERED_BY_MICRO_CORE__
    }
}

/**
 * 主题相关的 Hook
 */
export const useTheme = () => {
    const { theme } = useMicroApp()

    return {
        theme: theme || 'light',
        isDark: theme === 'dark',
        isLight: theme === 'light' || !theme
    }
}

/**
 * 用户信息相关的 Hook
 */
export const useUser = () => {
    const { user } = useMicroApp()

    return {
        user,
        isLoggedIn: !!user,
        userId: user?.id,
        userName: user?.name,
        userRole: user?.role
    }
}

export default MicroAppContext