# React 适配器

Micro-Core React 适配器提供了与 React 应用的深度集成，支持 React 16.8+、17.x 和 18.x 版本。

## 📋 目录

- [适配器概述](#适配器概述)
- [安装配置](#安装配置)
- [基础用法](#基础用法)
- [Hooks API](#hooks-api)
- [组件集成](#组件集成)
- [状态管理](#状态管理)
- [路由集成](#路由集成)
- [最佳实践](#最佳实践)

## 适配器概述

### 核心特性

```typescript
// React 适配器特性
const reactAdapterFeatures = {
  // React 版本支持
  versions: [
    'React 16.8+',   // Hooks 支持
    'React 17.x',    // 并发特性
    'React 18.x'     // 自动批处理
  ],
  
  // 集成功能
  integration: [
    'React Hooks',
    'Context API',
    'Suspense',
    'Error Boundaries',
    'Portal',
    'Concurrent Features'
  ],
  
  // 微前端特性
  microFrontend: [
    '应用生命周期',
    '状态共享',
    '事件通信',
    '路由集成',
    '组件隔离'
  ]
}
```

### 架构设计

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    React 适配器架构                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    React 应用层                             │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 组件树       │  │ Hooks       │  │ Context             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    适配器层                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 生命周期适配 │  │ 状态适配     │  │ 事件适配             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Micro-Core 核心                          │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 应用管理     │  │ 通信系统     │  │ 路由系统             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 安装配置

### 安装适配器

```bash
# 安装 React 适配器
npm install @micro-core/adapter-react

# 安装 React 依赖 (如果还没有)
npm install react react-dom

# TypeScript 支持
npm install @types/react @types/react-dom
```

### 基础配置

```typescript
// main.tsx - 主应用配置
import React from 'react'
import ReactDOM from 'react-dom/client'
import { MicroCore } from '@micro-core/core'
import { ReactAdapter } from '@micro-core/adapter-react'
import App from './App'

// 创建 React 适配器
const reactAdapter = new ReactAdapter({
  // React 版本配置
  version: '18',
  
  // 渲染配置
  render: {
    mode: 'concurrent', // 'legacy' | 'concurrent'
    strictMode: true,
    suspense: true
  },
  
  // 错误边界配置
  errorBoundary: {
    enabled: true,
    fallback: (error, errorInfo) => (
      <div>应用加载失败: {error.message}</div>
    )
  },
  
  // 生命周期配置
  lifecycle: {
    enableHooks: true,
    autoCleanup: true
  }
})

// 创建 Micro-Core 实例
const microCore = new MicroCore({
  apps: [
    {