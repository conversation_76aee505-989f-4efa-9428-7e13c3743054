/**
 * @fileoverview Core 包 errors.ts 单元测试
 * @description 测试错误处理类和错误码定义的完整性和正确性
 */

import { describe, expect, it, vi } from 'vitest';
import {
    ERROR_CODES,
    MicroCoreError,
    SandboxError,
    defaultErrorHandler
} from '../src/errors';

describe('Core Errors', () => {
    describe('ERROR_CODES', () => {
        it('应该包含所有必需的错误码', () => {
            expect(ERROR_CODES).toEqual({
                // 通用错误
                UNKNOWN_ERROR: 'UNKNOWN_ERROR',
                INVALID_ARGUMENT: 'INVALID_ARGUMENT',
                OPERATION_FAILED: 'OPERATION_FAILED',

                // 应用相关错误
                APPLICATION_ERROR: 'APPLICATION_ERROR',
                APPLICATION_NOT_FOUND: 'APPLICATION_NOT_FOUND',
                APPLICATION_ALREADY_EXISTS: 'APPLICATION_ALREADY_EXISTS',
                APPLICATION_STILL_MOUNTED: 'APPLICATION_STILL_MOUNTED',
                APPLICATION_LOAD_FAILED: 'APPLICATION_LOAD_FAILED',
                APPLICATION_MOUNT_FAILED: 'APPLICATION_MOUNT_FAILED',
                APPLICATION_UNMOUNT_FAILED: 'APPLICATION_UNMOUNT_FAILED',

                // 生命周期错误
                LIFECYCLE_ERROR: 'LIFECYCLE_ERROR',
                LIFECYCLE_BOOTSTRAP_FAILED: 'LIFECYCLE_BOOTSTRAP_FAILED',
                LIFECYCLE_MOUNT_FAILED: 'LIFECYCLE_MOUNT_FAILED',
                LIFECYCLE_UNMOUNT_FAILED: 'LIFECYCLE_UNMOUNT_FAILED',
                LIFECYCLE_UPDATE_FAILED: 'LIFECYCLE_UPDATE_FAILED',
                LIFECYCLE_TIMEOUT: 'LIFECYCLE_TIMEOUT',

                // 插件相关错误
                PLUGIN_ERROR: 'PLUGIN_ERROR',
                PLUGIN_NOT_FOUND: 'PLUGIN_NOT_FOUND',
                PLUGIN_ALREADY_EXISTS: 'PLUGIN_ALREADY_EXISTS',
                PLUGIN_INSTALL_FAILED: 'PLUGIN_INSTALL_FAILED',
                PLUGIN_UNINSTALL_FAILED: 'PLUGIN_UNINSTALL_FAILED',

                // 沙箱相关错误
                SANDBOX_ERROR: 'SANDBOX_ERROR',
                SANDBOX_CREATE_FAILED: 'SANDBOX_CREATE_FAILED',
                SANDBOX_DESTROY_FAILED: 'SANDBOX_DESTROY_FAILED',
                SANDBOX_EXECUTION_FAILED: 'SANDBOX_EXECUTION_FAILED',
                SANDBOX_SCRIPT_EXECUTION_FAILED: 'SANDBOX_SCRIPT_EXECUTION_FAILED',

                // 路由相关错误
                ROUTER_ERROR: 'ROUTER_ERROR',
                ROUTER_NAVIGATION_FAILED: 'ROUTER_NAVIGATION_FAILED',
                ROUTER_GUARD_REJECTED: 'ROUTER_GUARD_REJECTED',

                // 通信相关错误
                COMMUNICATION_ERROR: 'COMMUNICATION_ERROR',
                EVENT_HANDLER_ERROR: 'EVENT_HANDLER_ERROR',
                STATE_UPDATE_FAILED: 'STATE_UPDATE_FAILED',

                // 资源相关错误
                RESOURCE_ERROR: 'RESOURCE_ERROR',
                RESOURCE_LOAD_FAILED: 'RESOURCE_LOAD_FAILED',
                RESOURCE_PARSE_FAILED: 'RESOURCE_PARSE_FAILED',
                RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND'
            });
        });

        it('应该是只读对象', () => {
            expect(() => {
                (ERROR_CODES as any).NEW_ERROR = 'NEW_ERROR';
            }).toThrow();
        });

        it('所有错误码值应该是字符串类型', () => {
            Object.values(ERROR_CODES).forEach(code => {
                expect(typeof code).toBe('string');
            });
        });

        it('错误码值应该与键名相同', () => {
            Object.entries(ERROR_CODES).forEach(([key, value]) => {
                expect(value).toBe(key);
            });
        });
    });

    describe('MicroCoreError', () => {
        it('应该正确创建错误实例', () => {
            const error = new MicroCoreError(
                ERROR_CODES.APPLICATION_ERROR,
                'Test error message',
                { appName: 'test-app' }
            );

            expect(error).toBeInstanceOf(Error);
            expect(error).toBeInstanceOf(MicroCoreError);
            expect(error.name).toBe('MicroCoreError');
            expect(error.code).toBe(ERROR_CODES.APPLICATION_ERROR);
            expect(error.message).toBe('Test error message');
            expect(error.context).toEqual({ appName: 'test-app' });
            expect(error.timestamp).toBeTypeOf('number');
            expect(error.timestamp).toBeGreaterThan(0);
        });

        it('应该处理没有上下文的情况', () => {
            const error = new MicroCoreError(
                ERROR_CODES.UNKNOWN_ERROR,
                'Test error without context'
            );

            expect(error.context).toEqual({});
        });
    });

    describe('SandboxError', () => {
        it('应该正确创建沙箱错误实例', () => {
            const error = new SandboxError('test-sandbox', 'Sandbox execution failed');

            expect(error).toBeInstanceOf(MicroCoreError);
            expect(error).toBeInstanceOf(SandboxError);
            expect(error.name).toBe('SandboxError');
            expect(error.code).toBe(ERROR_CODES.SANDBOX_ERROR);
            expect(error.message).toBe('[test-sandbox] Sandbox execution failed');
            expect(error.context).toEqual({ sandboxName: 'test-sandbox' });
        });
    });

    describe('defaultErrorHandler', () => {
        it('应该正确处理错误', () => {
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });

            const error = new MicroCoreError(
                ERROR_CODES.APPLICATION_ERROR,
                'Test error',
                { appName: 'test-app' }
            );

            defaultErrorHandler(error, { additional: 'context' });

            expect(consoleSpy).toHaveBeenCalled();
            consoleSpy.mockRestore();
        });
    });
});