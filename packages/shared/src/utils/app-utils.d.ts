/**
 * 共享工具包 - 应用相关工具函数
 *
 * @description 提供微前端应用相关的通用工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import type { AppConfig, AppInstance, AppStatus, FrameworkType } from '../types';
/**
 * 微应用配置接口
 */
export interface MicroAppConfig extends AppConfig {
    /** 应用激活规则 */
    activeWhen?: string | string[] | ((location: Location) => boolean);
    /** 应用自定义属性 */
    customProps?: Record<string, any>;
    /** 预加载配置 */
    preload?: boolean;
    /** 样式文件 */
    styles?: string[];
}
/**
 * 应用发现配置接口
 */
export interface AppDiscoveryConfig {
    /** 扫描的根路径 */
    rootPath: string;
    /** 包含的文件模式 */
    include?: string[];
    /** 排除的文件模式 */
    exclude?: string[];
    /** 是否递归扫描 */
    recursive?: boolean;
    /** 最大扫描深度 */
    maxDepth?: number;
}
/**
 * 应用清单接口
 */
export interface AppManifest {
    /** 应用名称 */
    name: string;
    /** 应用版本 */
    version: string;
    /** 应用描述 */
    description?: string;
    /** 应用入口 */
    entry: string;
    /** 框架类型 */
    framework: FrameworkType;
    /** 依赖列表 */
    dependencies?: string[];
    /** 应用路由 */
    routes?: string[];
    /** 应用权限 */
    permissions?: string[];
    /** 应用元数据 */
    metadata?: Record<string, any>;
}
/**
 * 验证应用配置
 * @param config 应用配置
 * @throws {Error} 配置无效时抛出错误
 */
export declare function validateAppConfig(config: MicroAppConfig): void;
/**
 * 标准化应用配置
 * @param config 原始应用配置
 * @returns 标准化后的应用配置
 */
export declare function normalizeAppConfig(config: Partial<MicroAppConfig>): MicroAppConfig;
/**
 * 检查应用是否应该激活
 * @param config 应用配置
 * @param location 当前位置
 * @returns 是否应该激活
 */
export declare function shouldActivateApp(config: MicroAppConfig, location?: Location): boolean;
/**
 * 获取应用状态描述
 * @param status 应用状态
 * @returns 状态描述
 */
export declare function getAppStatusDescription(status: AppStatus): string;
/**
 * 检查应用状态是否为错误状态
 * @param status 应用状态
 * @returns 是否为错误状态
 */
export declare function isAppErrorStatus(status: AppStatus): boolean;
/**
 * 检查应用是否正在运行
 * @param status 应用状态
 * @returns 是否正在运行
 */
export declare function isAppRunning(status: AppStatus): boolean;
/**
 * 检查应用是否已挂载
 * @param status 应用状态
 * @returns 是否已挂载
 */
export declare function isAppMounted(status: AppStatus): boolean;
/**
 * 生成应用ID
 * @param name 应用名称
 * @param version 应用版本
 * @returns 应用ID
 */
export declare function generateAppId(name: string, version?: string): string;
/**
 * 解析应用ID
 * @param appId 应用ID
 * @returns 解析结果
 */
export declare function parseAppId(appId: string): {
    name: string;
    version?: string;
};
/**
 * 创建应用实例
 * @param config 应用配置
 * @returns 应用实例
 */
export declare function createAppInstance(config: MicroAppConfig): AppInstance;
/**
 * 更新应用实例状态
 * @param app 应用实例
 * @param status 新状态
 * @param error 错误信息（可选）
 */
export declare function updateAppStatus(app: AppInstance, status: AppStatus, error?: any): void;
/**
 * 获取应用运行时统计信息
 * @param app 应用实例
 * @returns 统计信息
 */
export declare function getAppStats(app: AppInstance): Record<string, any>;
/**
 * 比较应用版本
 * @param version1 版本1
 * @param version2 版本2
 * @returns 比较结果 (-1: version1 < version2, 0: 相等, 1: version1 > version2)
 */
export declare function compareAppVersions(version1: string, version2: string): number;
/**
 * 检查应用版本兼容性
 * @param requiredVersion 要求的版本
 * @param actualVersion 实际版本
 * @returns 是否兼容
 */
export declare function isAppVersionCompatible(requiredVersion: string, actualVersion: string): boolean;
//# sourceMappingURL=app-utils.d.ts.map