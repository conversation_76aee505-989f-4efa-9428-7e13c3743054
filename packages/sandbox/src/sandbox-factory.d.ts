/**
 * 沙箱工厂实现
 *
 * @description 负责创建和管理不同类型的沙箱策略
 * <AUTHOR> <<EMAIL>>
 */
import type { SandboxConfig, SandboxContext, SandboxFactoryConfig, SandboxPerformanceMetrics, SandboxType } from '@micro-core/shared';
/**
 * 沙箱工厂类
 * 负责创建和管理不同类型的沙箱策略
 */
export declare class SandboxFactory {
    private readonly config;
    private readonly logger;
    private readonly strategies;
    constructor(config?: Partial<SandboxFactoryConfig>);
    /**
     * 创建沙箱
     */
    createSandbox(config: SandboxConfig): Promise<SandboxContext>;
    /**
     * 激活沙箱
     */
    activateSandbox(context: SandboxContext): Promise<void>;
    /**
     * 停用沙箱
     */
    deactivateSandbox(context: SandboxContext): Promise<void>;
    /**
     * 销毁沙箱
     */
    destroySandbox(context: SandboxContext): Promise<void>;
    /**
     * 在沙箱中执行代码
     */
    executeInSandbox(context: SandboxContext, code: string): Promise<any>;
    /**
     * 获取沙箱性能指标
     */
    getPerformanceMetrics(context: SandboxContext): SandboxPerformanceMetrics;
    /**
     * 获取可用的沙箱类型
     */
    getAvailableTypes(): SandboxType[];
    /**
     * 检查沙箱类型是否支持
     */
    isTypeSupported(type: SandboxType): boolean;
    /**
     * 自动选择最优沙箱类型
     */
    selectOptimalType(): SandboxType;
    /**
     * 获取策略实例
     */
    private getStrategy;
    /**
     * 初始化所有策略
     */
    private initializeStrategies;
    /**
     * 注册策略
     */
    private registerStrategy;
    /**
     * 检查类型兼容性
     */
    private isTypeCompatible;
    /**
     * 销毁工厂
     */
    destroy(): Promise<void>;
}
//# sourceMappingURL=sandbox-factory.d.ts.map