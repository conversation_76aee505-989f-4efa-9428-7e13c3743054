# wujie 沙箱迁移指南

本指南详细说明如何将 wujie 的沙箱机制迁移到 Micro-Core。

## 📋 目录

- [沙箱机制对比](#沙箱机制对比)
- [iframe 沙箱迁移](#iframe-沙箱迁移)
- [降级模式迁移](#降级模式迁移)
- [样式隔离迁移](#样式隔离迁移)
- [资源处理迁移](#资源处理迁移)
- [性能优化](#性能优化)
- [调试工具](#调试工具)
- [最佳实践](#最佳实践)

## 沙箱机制对比

### wujie 沙箱架构

```javascript
// wujie 沙箱类型
const wujieSandbox = {
  // 1. iframe 沙箱（默认）
  iframe: {
    isolation: '完全隔离',
    performance: '中等',
    compatibility: '最好'
  },
  
  // 2. 降级模式（类似 Proxy）
  degrade: {
    isolation: '部分隔离',
    performance: '最好',
    compatibility: '良好'
  },
  
  // 3. shadowRoot 样式隔离
  shadowRoot: {
    isolation: '样式隔离',
    performance: '好',
    compatibility: '现代浏览器'
  }
}
```

### Micro-Core 沙箱架构

```typescript
// Micro-Core 沙箱类型
const microCoreSandbox = {
  // 1. iframe 沙箱
  iframe: {
    isolation: '完全隔离',
    performance: '中等',
    compatibility: '最好',
    features: ['JS隔离', 'CSS隔离', 'DOM隔离']
  },
  
  // 2. Proxy 沙箱
  proxy: {
    isolation: '高级隔离',
    performance: '最好',
    compatibility: '现代浏览器',
    features: ['JS隔离', '全局变量隔离', '事件隔离']
  },
  
  // 3. WebComponent 沙箱
  webcomponent: {
    isolation: '样式隔离',
    performance: '好',
    compatibility: '现代浏览器',
    features: ['CSS隔离', 'DOM隔离', 'Shadow DOM']
  },
  
  // 4. DefineProperty 沙箱
  defineProperty: {
    isolation: '基础隔离',
    performance: '好',
    compatibility: '所有浏览器',
    features: ['属性隔离', '兼容性最佳']
  },
  
  // 5. 命名空间沙箱
  namespace: {
    isolation: '轻量隔离',
    performance: '最好',
    compatibility: '所有浏览器',
    features: ['命名空间隔离', '性能最优']
  },
  
  // 6. 模块联邦沙箱
  federation: {
    isolation: '模块隔离',
    performance: '好',
    compatibility: '现代浏览器',
    features: ['模块共享', '依赖隔离']
  }
}
```

## iframe 沙箱迁移

### wujie iframe 沙箱

```javascript
// wujie iframe 沙箱配置
import { startApp } from 'wujie'

startApp({
  name: 'vue3-app',
  url: 'http://localhost:3001',
  el: '#container',
  
  // 默认使用 iframe 沙箱
  degrade: false,
  
  // iframe 相关配置
  attrs: {
    width: '100%',
    height: '100vh'
  },
  
  // 样式处理
  shadowRoot: false,
  
  // 资源处理
  plugins: [
    {
      cssExcludes: ['http://localhost:3001/exclude.css'],
      jsExcludes: ['http://localhost:3001/exclude.js']
    }
  ]
})
```

### Micro-Core iframe 沙箱迁移

```typescript
// Micro-Core iframe 沙箱配置
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  apps: [
    {
      name: 'vue3-app',
      entry: 'http://localhost:3001',
      container: '#container',
      activeWhen: '/vue3-app',
      
      // iframe 沙箱配置
      sandbox: {
        type: 'iframe',
        
        // JS 隔离
        js: true,
        
        // CSS 隔离
        css: true,
        
        // DOM 隔离
        dom: true,
        
        // iframe 属性
        attrs: {
          width: '100%',
          height: '100vh',
          frameborder: '0',
          scrolling: 'auto'
        },
        
        // 资源排除
        excludes: {
          css: ['http://localhost:3001/exclude.css'],
          js: ['http://localhost:3001/exclude.js']
        },
        
        // 安全策略
        security: {
          allowScripts: true,
          allowSameOrigin: true,
          allowForms: true,
          allowPopups: false
        }
      }
    }
  ]
})
```

### iframe 沙箱增强功能

```typescript
// Micro-Core iframe 沙箱增强配置
const microCore = new MicroCore({
  apps: [
    {
      name: 'vue3-app',
      entry: 'http://localhost:3001',
      sandbox: {
        type: 'iframe',
        
        // 高级配置
        advanced: {
          // 预加载策略
          preload: 'metadata', // 'none' | 'metadata' | 'auto'
          
          // 懒加载
          loading: 'lazy', // 'eager' | 'lazy'
          
          // 沙箱策略
          sandbox: 'allow-scripts allow-same-origin allow-forms',
          
          // CSP 策略
          csp: "default-src 'self'; script-src 'self' 'unsafe-inline'",
          
          // 引用策略
          referrerPolicy: 'strict-origin-when-cross-origin'
        },
        
        // 通信配置
        communication: {
          // 允许的消息来源
          allowedOrigins: ['http://localhost:3001'],
          
          // 消息验证
          validateMessage: (message, origin) => {
            return origin === 'http://localhost:3001'
          },
          
          // 消息转换
          transformMessage: (message) => {
            return { ...message, timestamp: Date.now() }
          }
        },
        
        // 资源拦截
        resourceInterceptor: {
          // 请求拦截
          beforeRequest: (url, options) => {
            console.log('请求拦截:', url)
            return { url, options }
          },
          
          // 响应拦截
          afterResponse: (response, url) => {
            console.log('响应拦截:', url, response.status)
            return response
          }
        }
      }
    }
  ]
})
```

## 降级模式迁移

### wujie 降级模式

```javascript
// wujie 降级模式配置
startApp({
  name: 'vue3-app',
  url: 'http://localhost:3001',
  el: '#container',
  
  // 启用降级模式
  degrade: true,
  
  // 降级模式下的配置
  plugins: [
    {
      // 全局变量处理
      windowProxy: (window) => {
        // 代理 window 对象
        return new Proxy(window, {
          get: (target, prop) => {
            console.log('访问全局变量:', prop)
            return target[prop]
          },
          set: (target, prop, value) => {
            console.log('设置全局变量:', prop, value)
            target[prop] = value
            return true
          }
        })
      }
    }
  ]
})
```

### Micro-Core Proxy 沙箱迁移

```typescript
// Micro-Core Proxy 沙箱配置
const microCore = new MicroCore({
  apps: [
    {
      name: 'vue3-app',
      entry: 'http://localhost:3001',
      container: '#container',
      activeWhen: '/vue3-app',
      
      // Proxy 沙箱配置
      sandbox: {
        type: 'proxy',
        
        // JS 隔离
        js: true,
        
        // 全局变量隔离
        globalVariables: {
          // 白名单
          whitelist: ['console', 'setTimeout', 'setInterval'],
          
          // 黑名单
          blacklist: ['eval', 'Function'],
          
          // 自定义代理
          proxy: {
            window: (target, prop, value) => {
              console.log('访问 window 属性:', prop)
              return Reflect.get(target, prop)
            },
            
            document: (target, prop, value) => {
              console.log('访问 document 属性:', prop)
              return Reflect.get(target, prop)
            }
          }
        },
        
        // 事件隔离
        events: {
          // 隔离的事件类型
          isolate: ['click', 'scroll', 'resize'],
          
          // 事件代理
          proxy: true,
          
          // 事件命名空间
          namespace: 'vue3-app'
        },
        
        // 定时器隔离
        timers: {
          isolate: true,
          cleanup: true
        }
      }
    }
  ]
})
```

### Proxy 沙箱高级功能

```typescript
// 高级 Proxy 沙箱配置
const advancedProxyConfig = {
  sandbox: {
    type: 'proxy',
    
    // 内存管理
    memory: {
      // 自动垃圾回收
      autoGC: true,
      
      // 内存限制 (MB)
      limit: 100,
      
      // 内存监控
      monitor: true,
      
      // 内存泄漏检测
      leakDetection: true
    },
    
    // 性能监控
    performance: {
      // 性能追踪
      trace: true,
      
      // 性能指标收集
      metrics: ['memory', 'cpu', 'network'],
      
      // 性能阈值
      thresholds: {
        memory: 50, // MB
        cpu: 80,    // %
        network: 1000 // ms
      }
    },
    
    // 安全策略
    security: {
      // 代码执行限制
      codeExecution: {
        allowEval: false,
        allowFunction: false,
        allowInlineScript: false
      },
      
      // 网络请求限制
      network: {
        allowedDomains: ['localhost', 'api.example.com'],
        blockedPorts: [22, 23, 25],
        timeout: 30000
      },
      
      // 存储限制
      storage: {
        localStorage: true,
        sessionStorage: true,
        indexedDB: false,
        quota: 10 // MB
      }
    }
  }
}
```

## 样式隔离迁移

### wujie 样式隔离

```javascript
// wujie 样式隔离配置
startApp({
  name: 'vue3-app',
  url: 'http://localhost:3001',
  el: '#container',
  
  // 启用 Shadow DOM
  shadowRoot: true,
  
  // 样式处理
  plugins: [
    {
      // CSS 处理器
      cssLoader: (code, url) => {
        // 添加作用域
        return code.replace(/(\.[a-zA-Z][\w-]*)/g, '.vue3-app $1')
      },
      
      // CSS 排除
      cssExcludes: [
        'http://localhost:3001/global.css'
      ]
    }
  ]
})
```

### Micro-Core 样式隔离迁移

```typescript
// Micro-Core WebComponent 沙箱配置
const microCore = new MicroCore({
  apps: [
    {
      name: 'vue3-app',
      entry: 'http://localhost:3001',
      container: '#container',
      activeWhen: '/vue3-app',
      
      // WebComponent 沙箱配置
      sandbox: {
        type: 'webcomponent',
        
        // CSS 隔离
        css: {
          // 隔离模式
          isolation: 'shadow', // 'shadow' | 'scoped' | 'namespace'
          
          // Shadow DOM 配置
          shadowDOM: {
            mode: 'closed', // 'open' | 'closed'
            delegatesFocus: true
          },
          
          // 样式作用域
          scoped: {
            prefix: 'vue3-app',
            suffix: '',
            transform: (css, selector) => {
              return css.replace(/(\.[a-zA-Z][\w-]*)/g, `${selector} $1`)
            }
          },
          
          // 全局样式处理
          global: {
            // 继承的全局样式
            inherit: ['font-family', 'font-size', 'color'],
            
            // 排除的样式文件
            excludes: ['http://localhost:3001/global.css'],
            
            // 样式重写
            rewrite: {
              'body': '.micro-app-body',
              'html': '.micro-app-html'
            }
          }
        }
      }
    }
  ]
})
```

### 多种样式隔离策略

```typescript
// 样式隔离策略配置
const styleIsolationStrategies = {
  // 1. Shadow DOM 隔离
  shadowDOM: {
    sandbox: {
      type: 'webcomponent',
      css: {
        isolation: 'shadow',
        shadowDOM: { mode: 'closed' }
      }
    }
  },
  
  // 2. CSS Scoped 隔离
  scoped: {
    sandbox: {
      type: 'proxy',
      css: {
        isolation: 'scoped',
        scoped: {
          prefix: 'app-',
          transform: (css, prefix) => {
            return css.replace(/([^{}]+){/g, `${prefix} $1 {`)
          }
        }
      }
    }
  },
  
  // 3. CSS Modules 隔离
  modules: {
    sandbox: {
      type: 'proxy',
      css: {
        isolation: 'modules',
        modules: {
          generateScopedName: '[name]__[local]___[hash:base64:5]',
          hashPrefix: 'vue3-app'
        }
      }
    }
  },
  
  // 4. 命名空间隔离
  namespace: {
    sandbox: {
      type: 'namespace',
      css: {
        isolation: 'namespace',
        namespace: {
          prefix: 'ns-vue3-app',
          separator: '-'
        }
      }
    }
  }
}
```

## 资源处理迁移

### wujie 资源处理

```javascript
// wujie 资源处理配置
startApp({
  name: 'vue3-app',
  url: 'http://localhost:3001',
  el: '#container',
  
  // 自定义 fetch
  fetch: (url, options) => {
    console.log('加载资源:', url)
    return fetch(url, {
      ...options,
      headers: {
        ...options?.headers,
        'X-Requested-With': 'wujie'
      }
    })
  },
  
  plugins: [
    {
      // HTML 处理
      htmlLoader: (code, url) => {
        return code.replace(/<title>.*<\/title>/, '<title>Vue3 App</title>')
      },
      
      // JS 处理
      jsLoader: (code, url) => {
        return `
          console.log('加载 JS:', '${url}');
          ${code}
        `
      },
      
      // CSS 处理
      cssLoader: (code, url) => {
        return code.replace(/color:\s*red/g, 'color: blue')
      },
      
      // 资源排除
      cssExcludes: ['http://localhost:3001/exclude.css'],
      jsExcludes: ['http://localhost:3001/exclude.js']
    }
  ]
})
```

### Micro-Core 资源处理迁移

```typescript
// Micro-Core 资源处理配置
import { MicroCore, ResourcePlugin } from '@micro-core/core'

// 创建资源处理插件
const resourcePlugin: ResourcePlugin = {
  name: 'resource-processor',
  
  // 资源拦截
  interceptResource: async (url, type, options) => {
    console.log('拦截资源:', url, type)
    
    // 自定义 fetch
    const response = await fetch(url, {
      ...options,
      headers: {
        ...options?.headers,
        'X-Requested-With': 'micro-core'
      }
    })
    
    return response
  },
  
  // HTML 处理
  processHtml: (code, url) => {
    return code.replace(/<title>.*<\/title>/, '<title>Vue3 App</title>')
  },
  
  // JS 处理
  processJs: (code, url) => {
    return `
      console.log('加载 JS:', '${url}');
      ${code}
    `
  },
  
  // CSS 处理
  processCss: (code, url) => {
    return code.replace(/color:\s*red/g, 'color: blue')
  },
  
  // 资源过滤
  shouldLoadResource: (url, type) => {
    const excludes = {
      css: ['http://localhost:3001/exclude.css'],
      js: ['http://localhost:3001/exclude.js']
    }
    return !excludes[type]?.includes(url)
  }
}

const microCore = new MicroCore({
  plugins: [resourcePlugin],
  
  apps: [
    {
      name: 'vue3-app',
      entry: 'http://localhost:3001',
      container: '#container',
      activeWhen: '/vue3-app',
      
      // 资源配置
      resources: {
        // 预加载资源
        preload: [
          'http://localhost:3001/vendor.js',
          'http://localhost:3001/main.css'
        ],
        
        // 延迟加载资源
        lazy: [
          'http://localhost:3001/charts.js'
        ],
        
        // 资源缓存
        cache: {
          enabled: true,
          strategy: 'cache-first', // 'cache-first' | 'network-first' | 'stale-while-revalidate'
          maxAge: 3600000 // 1 hour
        },
        
        // 资源压缩
        compression: {
          gzip: true,
          brotli: true
        }
      }
    }
  ]
})
```

### 高级资源处理

```typescript
// 高级资源处理配置
const advancedResourceConfig = {
  resources: {
    // 资源加载策略
    loading: {
      // 并发限制
      concurrency: 6,
      
      // 超时设置
      timeout: 30000,
      
      // 重试策略
      retry: {
        times: 3,
        delay: 1000,
        backoff: 2
      },
      
      // 优先级
      priority: {
        'main.js': 'high',
        'vendor.js': 'high',
        'main.css': 'medium',
        'images/*': 'low'
      }
    },
    
    // 资源优化
    optimization: {
      // 代码分割
      codeSplitting: true,
      
      // Tree Shaking
      treeShaking: true,
      
      // 压缩
      minification: {
        js: true,
        css: true,
        html: true
      },
      
      // 图片优化
      images: {
        webp: true,
        lazy: true,
        placeholder: true
      }
    },
    
    // 资源监控
    monitoring: {
      // 加载时间监控
      loadTime: true,
      
      // 资源大小监控
      size: true,
      
      // 错误监控
      errors: true,
      
      // 性能指标
      metrics: ['FCP', 'LCP', 'FID', 'CLS']
    }
  }
}
```

## 性能优化

### 沙箱性能对比

```typescript
// 沙箱性能对比
const sandboxPerformance = {
  iframe: {
    startup: '300-500ms',
    memory: '10-20MB',
    cpu: '中等',
    isolation: '完全',
    compatibility: '最好'
  },
  
  proxy: {
    startup: '50-100ms',
    memory: '2-5MB',
    cpu: '低',
    isolation: '高',
    compatibility: '现代浏览器'
  },
  
  webcomponent: {
    startup: '100-200ms',
    memory: '5-10MB',
    cpu: '中等',
    isolation: '样式',
    compatibility: '现代浏览器'
  },
  
  namespace: {
    startup: '10-50ms',
    memory: '1-2MB',
    cpu: '最低',
    isolation: '轻量',
    compatibility: '所有浏览器'
  }
}
```

### 性能优化策略

```typescript
// 性能优化配置
const performanceConfig = {
  // 沙箱选择策略
  sandboxSelection: {
    // 根据应用特性自动选择
    auto: true,
    
    // 选择规则
    rules: [
      {
        condition: (app) => app.size > 10, // MB
        sandbox: 'iframe'
      },
      {
        condition: (app) => app.legacy,
        sandbox: 'defineProperty'
      },
      {
        condition: (app) => app.performance === 'high',
        sandbox: 'proxy'
      }
    ]
  },
  
  // 预加载优化
  preload: {
    // 智能预加载
    intelligent: true,
    
    // 预加载策略
    strategy: 'idle', // 'eager' | 'idle' | 'viewport'
    
    // 预加载资源
    resources: ['critical.js', 'main.css'],
    
    // 预加载时机
    timing: {
      delay: 1000,
      maxWait: 5000
    }
  },
  
  // 内存优化
  memory: {
    // 自动清理
    autoCleanup: true,
    
    // 清理策略
    cleanup: {
      interval: 60000, // 1 minute
      threshold: 80,   // 80% memory usage
      aggressive: false
    },
    
    // 内存池
    pool: {
      enabled: true,
      size: 10,
      reuse: true
    }
  }
}
```

## 调试工具

### 沙箱调试面板

```typescript
// 沙箱调试配置
const debugConfig = {
  debug: {
    // 启用调试模式
    enabled: process.env.NODE_ENV === 'development',
    
    // 调试面板
    panel: {
      // 显示沙箱信息
      sandbox: true,
      
      // 显示性能指标
      performance: true,
      
      // 显示内存使用
      memory: true,
      
      // 显示网络请求
      network: true,
      
      // 显示错误日志
      errors: true
    },
    
    // 日志配置
    logging: {
      level: 'debug', // 'error' | 'warn' | 'info' | 'debug'
      
      // 日志分类
      categories: {
        sandbox: true,
        lifecycle: true,
        communication: true,
        performance: true
      },
      
      // 日志输出
      output: {
        console: true,
        file: false,
        remote: false
      }
    },
    
    // 性能分析
    profiling: {
      // CPU 分析
      cpu: true,
      
      // 内存分析
      memory: true,
      
      // 网络分析
      network: true,
      
      // 渲染分析
      rendering: true
    }
  }
}

// 使用调试工具
const microCore = new MicroCore({
  ...debugConfig,
  
  apps: [
    {
      name: 'vue3-app',
      entry: 'http://localhost:3001',
      sandbox: {
        type: 'proxy',
        
        // 调试钩子
        debug: {
          onSandboxCreate: (sandbox) => {
            console.log('沙箱创建:', sandbox)
          },
          
          onSandboxDestroy: (sandbox) => {
            console.log('沙箱销毁:', sandbox)
          },
          
          onResourceLoad: (url, type, time) => {
            console.log('资源加载:', url, type, time)
          },
          
          onError: (error, context) => {
            console.error('沙箱错误:', error, context)
          }
        }
      }
    }
  ]
})
```

### 调试工具 API

```typescript
// 调试工具 API
class SandboxDebugger {
  private microCore: MicroCore
  
  constructor(microCore: MicroCore) {
    this.microCore = microCore
  }
  
  // 获取沙箱信息
  getSandboxInfo(appName: string) {
    const app = this.microCore.getApp(appName)
    return {
      type: app.sandbox.type,
      status: app.status,
      memory: this.getMemoryUsage(app),
      performance: this.getPerformanceMetrics(app)
    }
  }
  
  // 获取内存使用情况
  getMemoryUsage(app: any) {
    return {
      used: performance.memory?.usedJSHeapSize || 0,
      total: performance.memory?.totalJSHeapSize || 0,
      limit: performance.memory?.jsHeapSizeLimit || 0
    }
  }
  
  // 获取性能指标
  getPerformanceMetrics(app: any) {
    return {
      loadTime: app.metrics?.loadTime || 0,
      mountTime: app.metrics?.mountTime || 0,
      renderTime: app.metrics?.renderTime || 0
    }
  }
  
  // 导出调试报告
  exportReport() {
    const report = {
      timestamp: Date.now(),
      apps: this.microCore.getApps().map(app => ({
        name: app.name,
        sandbox: this.getSandboxInfo(app.name),
        errors: app.errors || [],
        warnings: app.warnings || []
      }))
    }
    
    return JSON.stringify(report, null, 2)
  }
}

// 使用调试器
const debugger = new SandboxDebugger(microCore)
console.log(debugger.getSandboxInfo('vue3-app'))
```

## 最佳实践

### 1. 沙箱选择指南

```typescript
// 沙箱选择决策树
const sandboxSelectionGuide = {
  // 高隔离需求
  highIsolation: {
    recommended: 'iframe',
    alternatives: ['proxy'],
    scenarios: ['第三方应用', '不可信代码', '安全要求高']
  },
  
  // 高性能需求
  highPerformance: {
    recommended: 'proxy',
    alternatives: ['namespace'],
    scenarios: ['内部应用', '频繁切换', '资源受限']
  },
  
  // 样式隔离需求
  styleIsolation: {
    recommended: 'webcomponent',
    alternatives: ['iframe'],
    scenarios: ['UI 组件', '样式冲突', '设计系统']
  },
  
  // 兼容性需求
  compatibility: {
    recommended: 'defineProperty',
    alternatives: ['namespace'],
    scenarios: ['旧版浏览器', 'IE 支持', '兼容性优先']
  }
}

// 自动选择沙箱
function selectSandbox(app: any) {
  if (app.security === 'high') return 'iframe'
  if (app.performance === 'critical') return 'proxy'
  if (app.styles === 'isolated') return 'webcomponent'
  if (app.compatibility === 'legacy') return 'defineProperty'
  return 'proxy' // 默认选择
}
```

### 2. 性能优化建议

```typescript
// 性能优化最佳实践
const performanceBestPractices = {
  // 沙箱复用
  sandboxReuse: {
    enabled: true,
    strategy: 'pool',
    poolSize: 5,
    maxAge: 300000 // 5 minutes
  },
  
  // 资源共享
  resourceSharing: {
    // 共享库
    sharedLibraries: ['react', 'vue', 'lodash'],
    
    // 共享样式
    sharedStyles: ['normalize.css', 'common.css'],
    
    // 共享配置
    sharedConfig: ['env', 'constants']
  },
  
  // 懒加载
  lazyLoading: {
    // 路由懒加载
    routes: true,
    
    // 组件懒加载
    components: true,
    
    // 资源懒加载
    resources: ['images', 'fonts', 'icons']
  },
  
  // 缓存策略
  caching: {
    // 应用缓存
    apps: {
      strategy: 'lru',
      maxSize: 10,
      ttl: 3600000 // 1 hour
    },
    
    // 资源缓存
    resources: {
      strategy: 'cache-first',
      maxAge: 86400000 // 1 day
    }
  }
}
```

### 3. 安全配置建议

```typescript
// 安全配置最佳实践
const securityBestPractices = {
  // CSP 策略
  contentSecurityPolicy: {
    'default-src': ["'self'"],
    'script-src': ["'self'", "'unsafe-inline'"],
    'style-src': ["'self'", "'unsafe-inline'"],
    'img-src': ["'self'", 'data:', 'https:'],
    'connect-src': ["'self'", 'https://api.example.com'],
    'frame-src': ["'none'"],
    'object-src': ["'none'"]
  },
  
  // 沙箱权限
  sandboxPermissions: {
    'allow-scripts': true,
    'allow-same-origin': true,
    'allow-forms': true,
    'allow-popups': false,
    'allow-modals': false,
    'allow-downloads': false
  },
  
  // 资源验证
  resourceValidation: {
    // 域名白名单
    allowedDomains: ['localhost', 'cdn.example.com'],
    
    // 文件类型限制
    allowedTypes: ['js', 'css', 'html', 'json'],
    
    // 文件大小限制
    maxFileSize: 10 * 1024 * 1024, // 10MB
    
    // 完整性检查
    integrityCheck: true
  }
}
```

### 4. 错误处理策略

```typescript
// 错误处理最佳实践
const errorHandlingBestPractices = {
  // 全局错误处理
  globalErrorHandler: (error: Error, app: string) => {
    console.error(`应用 ${app} 发生错误:`, error)
    
    // 错误上报
    reportError(error, { app, timestamp: Date.now() })
    
    // 错误恢复
    if (error.name === 'SandboxError') {
      microCore.restartApp(app)
    }
  },
  
  // 沙箱错误处理
  sandboxErrorHandler: {
    // 自动重试
    autoRetry: {
      enabled: true,
      maxRetries: 3,
      delay: 1000
    },
    
    // 降级策略
    fallback: {
      enabled: true,
      strategy: 'iframe-to-proxy'
    },
    
    // 错误隔离
    isolation: {
      enabled: true,
      scope: 'app' // 'app' | 'sandbox' | 'global'
    }
  },
  
  // 监控告警
  monitoring: {
    // 错误率阈值
    errorRateThreshold: 0.05, // 5%
    
    // 告警通知
    alerts: {
      email: ['<EMAIL>'],
      webhook: 'https://hooks.example.com/alerts'
    },
    
    // 自动处理
    autoActions: {
      restart: true,
      fallback: true,
      isolate: true
    }
  }
}
```

## 迁移检查清单

- [ ] **沙箱类型选择**
  - [ ] 分析应用隔离需求
  - [ ] 评估性能要求
  - [ ] 确定兼容性需求
  - [ ] 选择合适的沙箱类型

- [ ] **配置迁移**
  - [ ] iframe 沙箱配置迁移
  - [ ] 降级模式配置迁移
  - [ ] 样式隔离配置迁移
  - [ ] 资源处理配置迁移

- [ ] **功能验证**
  - [ ] 沙箱隔离效果验证
  - [ ] 样式隔离效果验证
  - [ ] 资源加载功能验证
  - [ ] 性能指标验证

- [ ] **性能优化**
  - [ ] 沙箱启动性能优化
  - [ ] 内存使用优化
  - [ ] 资源加载优化
  - [ ] 缓存策略配置

- [ ] **安全加固**
  - [ ] CSP 策略配置
  - [ ] 沙箱权限限制
  - [ ] 资源验证配置
  - [ ] 错误处理配置

- [ ] **调试工具**
  - [ ] 调试面板配置
  - [ ] 日志系统配置
  - [ ] 性能监控配置
  - [ ] 错误追踪配置

通过以上详细的沙箱迁移指南，您可以将 wujie 的沙箱机制平滑迁移到 Micro-Core，并享受更强大的隔离能力和更好的性能表现。
