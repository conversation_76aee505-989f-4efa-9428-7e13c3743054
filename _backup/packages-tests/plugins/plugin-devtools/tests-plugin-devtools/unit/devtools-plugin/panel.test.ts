/**
 * @fileoverview DevTools 插件面板功能测试
 * @description 测试插件的面板创建、显示、隐藏等功能
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { DevToolsPlugin } from '../../../src/devtools-plugin';

// Mock DOM 环境
Object.defineProperty(window, 'PerformanceObserver', {
    writable: true,
    value: vi.fn().mockImplementation((callback) => ({
        observe: vi.fn(),
        disconnect: vi.fn(),
        callback
    }))
});

// Mock 内核
const createMockKernel = () => ({
    registerPlugin: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    getApps: vi.fn(() => []),
    getPlugins: vi.fn(() => [])
});

describe('DevToolsPlugin 面板功能', () => {
    let plugin: DevToolsPlugin;
    let mockKernel: ReturnType<typeof createMockKernel>;

    beforeEach(() => {
        // 清理 DOM
        document.body.innerHTML = '';

        // 重置环境变量
        vi.stubEnv('NODE_ENV', 'development');

        // 创建模拟内核
        mockKernel = createMockKernel();

        // 创建插件实例
        plugin = new DevToolsPlugin({
            enabled: true,
            showPanel: false
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
        vi.unstubAllEnvs();

        // 清理 DOM
        document.body.innerHTML = '';

        // 移除事件监听器
        document.removeEventListener('keydown', (plugin as any).handleKeydown);
    });

    describe('面板创建', () => {
        it('应该能够创建面板元素', async () => {
            await plugin.install(mockKernel);

            const panel = document.getElementById('micro-core-devtools');
            expect(panel).toBeTruthy();
            expect(panel?.style.display).toBe('none');
        });

        it('应该根据配置设置面板位置', async () => {
            const topPlugin = new DevToolsPlugin({
                enabled: true,
                panelPosition: 'top'
            });

            await topPlugin.install(mockKernel);

            const panel = document.getElementById('micro-core-devtools');
            expect(panel?.style.top).toBe('0px');
            expect(panel?.style.bottom).toBe('');
        });

        it('应该根据配置设置面板初始显示状态', async () => {
            const visiblePlugin = new DevToolsPlugin({
                enabled: true,
                showPanel: true
            });

            await visiblePlugin.install(mockKernel);

            const panel = document.getElementById('micro-core-devtools');
            expect(panel?.style.display).toBe('block');
        });
    });

    describe('面板显示/隐藏', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);
        });

        it('应该能够显示面板', () => {
            (plugin as any).showPanel();

            const panel = document.getElementById('micro-core-devtools');
            expect(panel?.style.display).toBe('block');
            expect((plugin as any).isVisible).toBe(true);
        });

        it('应该能够隐藏面板', () => {
            (plugin as any).showPanel();
            (plugin as any).hidePanel();

            const panel = document.getElementById('micro-core-devtools');
            expect(panel?.style.display).toBe('none');
            expect((plugin as any).isVisible).toBe(false);
        });

        it('应该能够切换面板显示状态', () => {
            // 初始状态是隐藏的
            expect((plugin as any).isVisible).toBe(false);

            (plugin as any).togglePanel();
            expect((plugin as any).isVisible).toBe(true);

            (plugin as any).togglePanel();
            expect((plugin as any).isVisible).toBe(false);
        });
    });

    describe('面板内容', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);
        });

        it('应该包含标题栏', () => {
            const titleBar = document.querySelector('.devtools-title-bar');
            expect(titleBar).toBeTruthy();
            expect(titleBar?.textContent).toContain('MicroCore DevTools');
        });

        it('应该包含关闭按钮', () => {
            const closeBtn = document.querySelector('.devtools-close-btn');
            expect(closeBtn).toBeTruthy();
        });

        it('应该包含标签页容器', () => {
            const tabsContainer = document.querySelector('.devtools-tabs');
            expect(tabsContainer).toBeTruthy();
        });

        it('应该包含内容容器', () => {
            const contentContainer = document.querySelector('.devtools-content');
            expect(contentContainer).toBeTruthy();
        });

        it('应该包含默认标签页', () => {
            const tabs = document.querySelectorAll('.devtools-tab');
            expect(tabs.length).toBeGreaterThan(0);

            // 检查是否有应用标签页
            const appsTab = Array.from(tabs).find(tab =>
                tab.textContent?.includes('应用')
            );
            expect(appsTab).toBeTruthy();

            // 检查是否有插件标签页
            const pluginsTab = Array.from(tabs).find(tab =>
                tab.textContent?.includes('插件')
            );
            expect(pluginsTab).toBeTruthy();
        });
    });

    describe('面板交互', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);
            (plugin as any).showPanel();
        });

        it('应该能够通过关闭按钮关闭面板', () => {
            const closeBtn = document.querySelector('.devtools-close-btn') as HTMLElement;
            expect(closeBtn).toBeTruthy();

            closeBtn.click();

            const panel = document.getElementById('micro-core-devtools');
            expect(panel?.style.display).toBe('none');
            expect((plugin as any).isVisible).toBe(false);
        });

        it('应该能够切换标签页', () => {
            const tabs = document.querySelectorAll('.devtools-tab') as NodeListOf<HTMLElement>;
            expect(tabs.length).toBeGreaterThan(1);

            // 点击第二个标签页
            if (tabs[1]) {
                tabs[1].click();

                // 检查标签页是否被激活
                expect(tabs[1].classList.contains('active')).toBe(true);
                expect(tabs[0].classList.contains('active')).toBe(false);
            }
        });

        it('应该能够拖拽调整面板大小', () => {
            const resizeHandle = document.querySelector('.devtools-resize-handle') as HTMLElement;
            expect(resizeHandle).toBeTruthy();

            // 模拟拖拽事件
            const mouseDownEvent = new MouseEvent('mousedown', {
                clientY: 100
            });
            resizeHandle.dispatchEvent(mouseDownEvent);

            const mouseMoveEvent = new MouseEvent('mousemove', {
                clientY: 150
            });
            document.dispatchEvent(mouseMoveEvent);

            const mouseUpEvent = new MouseEvent('mouseup');
            document.dispatchEvent(mouseUpEvent);

            // 验证面板高度是否改变
            const panel = document.getElementById('micro-core-devtools');
            expect(panel?.style.height).toBeTruthy();
        });
    });

    describe('键盘快捷键', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);
        });

        it('应该能够通过 F12 切换面板显示', () => {
            const keyEvent = new KeyboardEvent('keydown', {
                key: 'F12',
                code: 'F12'
            });

            document.dispatchEvent(keyEvent);

            expect((plugin as any).isVisible).toBe(true);

            document.dispatchEvent(keyEvent);

            expect((plugin as any).isVisible).toBe(false);
        });

        it('应该能够通过 Ctrl+Shift+I 切换面板显示', () => {
            const keyEvent = new KeyboardEvent('keydown', {
                key: 'I',
                code: 'KeyI',
                ctrlKey: true,
                shiftKey: true
            });

            document.dispatchEvent(keyEvent);

            expect((plugin as any).isVisible).toBe(true);
        });

        it('应该能够通过 Escape 关闭面板', () => {
            (plugin as any).showPanel();
            expect((plugin as any).isVisible).toBe(true);

            const keyEvent = new KeyboardEvent('keydown', {
                key: 'Escape',
                code: 'Escape'
            });

            document.dispatchEvent(keyEvent);

            expect((plugin as any).isVisible).toBe(false);
        });
    });

    describe('面板样式', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);
        });

        it('应该应用正确的 CSS 样式', () => {
            const panel = document.getElementById('micro-core-devtools');
            expect(panel).toBeTruthy();

            const computedStyle = window.getComputedStyle(panel!);
            expect(computedStyle.position).toBe('fixed');
            expect(computedStyle.zIndex).toBe('999999');
        });

        it('应该根据位置配置应用正确的样式', async () => {
            const topPlugin = new DevToolsPlugin({
                enabled: true,
                panelPosition: 'top'
            });

            await topPlugin.install(mockKernel);

            const panel = document.getElementById('micro-core-devtools');
            expect(panel?.style.top).toBe('0px');
            expect(panel?.style.bottom).toBe('');
        });

        it('应该支持自定义主题', async () => {
            const themedPlugin = new DevToolsPlugin({
                enabled: true,
                theme: 'dark'
            });

            await themedPlugin.install(mockKernel);

            const panel = document.getElementById('micro-core-devtools');
            expect(panel?.classList.contains('dark-theme')).toBe(true);
        });
    });
});