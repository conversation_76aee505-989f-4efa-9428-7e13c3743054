/**
 * @fileoverview Rspack Builder Implementation
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { BaseBuilder } from '../../shared/base-builder';
import type { BaseBuilderConfig, BaseBuilderOptions, BuildResult, DevServerConfig } from '../../shared/types';
import { <PERSON><PERSON>, ConfigMerger, PerformanceMonitor } from '../../shared/utils';

// Rspack-specific interfaces (avoiding direct dependency)
interface RspackBuilderOptions extends BaseBuilderOptions {
  rspackConfig?: any;
  alias?: Record<string, string>;
  externals?: Record<string, string>;
}

interface RspackBuilderConfig extends BaseBuilderConfig {
  rspackConfig?: any;
  plugins?: any[];
  optimization?: {
    minimize?: boolean;
    splitChunks?: any;
    runtimeChunk?: boolean | string;
  };
  resolve?: {
    extensions?: string[];
    alias?: Record<string, string>;
  };
  module?: {
    rules?: any[];
  };
  devServer?: {
    port?: number;
    host?: string;
    hot?: boolean;
    open?: boolean;
  };
}

interface RspackConfig {
  mode?: 'development' | 'production';
  entry?: string | Record<string, string>;
  output?: {
    path?: string;
    filename?: string;
    publicPath?: string;
    library?: string;
    libraryTarget?: string;
    chunkFilename?: string;
    clean?: boolean;
  };
  resolve?: {
    extensions?: string[];
    alias?: Record<string, string>;
  };
  externals?: Record<string, string>;
  module?: {
    rules?: any[];
  };
  plugins?: any[];
  optimization?: any;
  devServer?: any;
}

/**
 * Rspack Builder for Micro-Core
 * Provides Rspack build tool integration with micro-frontend capabilities
 */
export class RspackBuilder extends BaseBuilder {
  public readonly name = 'rspack';
  public readonly version = '1.0.0';

  private compiler: any = null;
  private devServer: any = null;

  constructor(options: RspackBuilderOptions = {}) {
    super(options);
    Logger.info(`Initialized ${this.name} builder v${this.version}`);
  }

  /**
   * Create Rspack-specific builder configuration
   */
  protected createBuilderConfig(config: BaseBuilderConfig): RspackConfig {
    const rspackConfig = config as RspackBuilderConfig;
    const isProd = config.mode === 'production';

    const baseConfig: RspackConfig = {
      mode: config.mode || 'development',
      entry: config.entry || './src/index.js',
      output: {
        path: config.outDir || './dist',
        filename: isProd ? '[name].[contenthash].js' : '[name].js',
        chunkFilename: isProd ? '[name].[contenthash].chunk.js' : '[name].chunk.js',
        publicPath: '/',
        clean: true,
        library: {
          name: config.name,
          type: 'umd'
        } as any
      },
      resolve: {
        extensions: ['.ts', '.tsx', '.js', '.jsx', '.json'],
        alias: {
          '@': './src',
          ...(rspackConfig.resolve?.alias || {})
        }
      },
      module: {
        rules: [
          {
            test: /\.(ts|tsx)$/,
            use: [
              {
                loader: 'builtin:swc-loader',
                options: {
                  jsc: {
                    parser: {
                      syntax: 'typescript',
                      tsx: true
                    },
                    transform: {
                      react: {
                        runtime: 'automatic'
                      }
                    }
                  }
                }
              }
            ]
          },
          {
            test: /\.(js|jsx)$/,
            use: [
              {
                loader: 'builtin:swc-loader',
                options: {
                  jsc: {
                    parser: {
                      syntax: 'ecmascript',
                      jsx: true
                    },
                    transform: {
                      react: {
                        runtime: 'automatic'
                      }
                    }
                  }
                }
              }
            ]
          },
          {
            test: /\.css$/,
            use: ['style-loader', 'css-loader']
          },
          {
            test: /\.(png|jpg|jpeg|gif|svg)$/,
            type: 'asset/resource'
          }
        ]
      },
      plugins: [
        // Add micro-frontend specific plugins here
        ...(rspackConfig.plugins || [])
      ],
      optimization: {
        minimize: isProd,
        splitChunks: isProd ? {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all'
            }
          }
        } : false,
        runtimeChunk: isProd ? 'single' : false,
        ...(rspackConfig.optimization || {})
      },
      externals: rspackConfig.externals || {}
    };

    // Merge with user-provided Rspack config
    if (rspackConfig.rspackConfig) {
      return ConfigMerger.deepMerge(baseConfig, rspackConfig.rspackConfig);
    }

    return baseConfig;
  }

  /**
   * Execute build using Rspack
   */
  protected async executeBuild(builderConfig: RspackConfig): Promise<BuildResult> {
    const timerId = `rspack-build-${Date.now()}`;
    PerformanceMonitor.startTimer(timerId);

    try {
      Logger.info('Starting Rspack build...');

      // Mock Rspack compiler (replace with actual Rspack when available)
      const compiler = await this.createCompiler(builderConfig);
      
      return new Promise((resolve) => {
        compiler.run((err: any, stats: any) => {
          const duration = PerformanceMonitor.endTimer(timerId);

          if (err) {
            Logger.error(`Rspack build failed: ${err.message}`);
            resolve({
              success: false,
              outputs: [],
              errors: [{ message: err.message }],
              stats: {
                duration,
                fileCount: 0,
                totalSize: 0,
                errors: 1,
                warnings: 0
              }
            });
            return;
          }

          const compilation = stats.compilation || {};
          const assets = compilation.assets || {};
          const errors = compilation.errors || [];
          const warnings = compilation.warnings || [];

          // Process outputs
          const outputs = Object.keys(assets).map(fileName => ({
            type: this.getAssetType(fileName) as 'chunk' | 'asset',
            fileName,
            size: assets[fileName].size?.() || 0,
            code: this.getAssetType(fileName) === 'chunk' ? 'compiled code' : undefined
          }));

          const result: BuildResult = {
            success: errors.length === 0,
            outputs,
            errors: errors.map((err: any) => ({ message: err.message || err.toString() })),
            warnings: warnings.map((warn: any) => ({ message: warn.message || warn.toString() })),
            stats: {
              duration,
              fileCount: outputs.length,
              totalSize: outputs.reduce((sum, output) => sum + output.size, 0),
              errors: errors.length,
              warnings: warnings.length
            }
          };

          Logger.info(`Rspack build completed in ${duration}ms`);
          resolve(result);
        });
      });

    } catch (error) {
      const duration = PerformanceMonitor.endTimer(timerId);
      const errorMessage = (error as Error).message;
      
      Logger.error(`Rspack build failed: ${errorMessage}`);

      return {
        success: false,
        outputs: [],
        errors: [{ message: errorMessage }],
        stats: {
          duration,
          fileCount: 0,
          totalSize: 0,
          errors: 1,
          warnings: 0
        }
      };
    }
  }

  /**
   * Start Rspack development server
   */
  protected async startDevServer(builderConfig: RspackConfig, devServerConfig?: DevServerConfig): Promise<any> {
    try {
      Logger.info('Starting Rspack development server...');

      const serverConfig = {
        port: devServerConfig?.port || 3000,
        host: devServerConfig?.host || 'localhost',
        hot: devServerConfig?.hot ?? true,
        open: devServerConfig?.open || false,
        ...builderConfig.devServer
      };

      // Mock dev server (replace with actual Rspack dev server when available)
      this.devServer = await this.createDevServer(builderConfig, serverConfig);

      return new Promise((resolve, reject) => {
        this.devServer.listen(serverConfig.port, serverConfig.host, (err: any) => {
          if (err) {
            Logger.error(`Failed to start Rspack dev server: ${err.message}`);
            reject(err);
            return;
          }

          const url = `http://${serverConfig.host}:${serverConfig.port}`;
          Logger.info(`Rspack development server running at: ${url}`);
          resolve(this.devServer);
        });
      });

    } catch (error) {
      Logger.error(`Failed to start Rspack development server: ${(error as Error).message}`);
      throw error;
    }
  }

  /**
   * Stop Rspack development server
   */
  protected async stopDevServer(server: any): Promise<void> {
    try {
      if (this.devServer) {
        return new Promise((resolve, reject) => {
          this.devServer.close((err: any) => {
            if (err) {
              Logger.error(`Failed to stop Rspack dev server: ${err.message}`);
              reject(err);
              return;
            }

            this.devServer = null;
            Logger.info('Rspack development server stopped');
            resolve();
          });
        });
      }
    } catch (error) {
      Logger.error(`Failed to stop Rspack development server: ${(error as Error).message}`);
      throw error;
    }
  }

  /**
   * Create mock Rspack compiler (replace with actual implementation)
   */
  private async createCompiler(config: RspackConfig): Promise<any> {
    // Mock implementation - replace with actual Rspack compiler
    return {
      run: (callback: (err: any, stats: any) => void) => {
        setTimeout(() => {
          callback(null, {
            compilation: {
              assets: {
                'main.js': { size: () => 1024 },
                'main.css': { size: () => 512 }
              },
              errors: [],
              warnings: []
            }
          });
        }, 100);
      }
    };
  }

  /**
   * Create mock Rspack dev server (replace with actual implementation)
   */
  private async createDevServer(config: RspackConfig, serverConfig: any): Promise<any> {
    // Mock implementation - replace with actual Rspack dev server
    return {
      listen: (port: number, host: string, callback: (err?: any) => void) => {
        setTimeout(() => callback(), 100);
      },
      close: (callback: (err?: any) => void) => {
        setTimeout(() => callback(), 100);
      }
    };
  }

  /**
   * Get asset type from filename
   */
  private getAssetType(fileName: string): 'chunk' | 'asset' {
    const ext = fileName.split('.').pop()?.toLowerCase();
    return ['js', 'mjs', 'ts'].includes(ext || '') ? 'chunk' : 'asset';
  }

  /**
   * Get current build status
   */
  public getBuildStatus() {
    return {
      ...super.getStatus(),
      hasActiveCompiler: !!this.compiler,
      hasDevServer: !!this.devServer
    };
  }
}
