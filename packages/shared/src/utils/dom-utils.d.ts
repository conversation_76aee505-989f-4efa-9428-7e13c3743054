/**
 * 共享工具包 - DOM操作工具函数
 *
 * @description 提供DOM操作相关的工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * 等待DOM就绪
 */
export declare function waitForDOMReady(): Promise<void>;
/**
 * 创建DOM元素
 */
export declare function createElement<K extends keyof HTMLElementTagNameMap>(tagName: K, attributes?: Record<string, string>, textContent?: string): HTMLElementTagNameMap[K];
/**
 * 移除DOM元素
 */
export declare function removeElement(element: HTMLElement | string): void;
/**
 * 查找元素
 */
export declare function findElement(selector: string): HTMLElement | null;
/**
 * 查找所有元素
 */
export declare function findElements(selector: string): HTMLElement[];
/**
 * 添加CSS类
 */
export declare function addClass(element: HTMLElement, className: string): void;
/**
 * 移除CSS类
 */
export declare function removeClass(element: HTMLElement, className: string): void;
/**
 * 切换CSS类
 */
export declare function toggleClass(element: HTMLElement, className: string): void;
/**
 * 检查是否包含CSS类
 */
export declare function hasClass(element: HTMLElement, className: string): boolean;
/**
 * 设置元素样式
 */
export declare function setStyle(element: HTMLElement, styles: Record<string, string>): void;
/**
 * 获取元素样式
 */
export declare function getStyle(element: HTMLElement, property: string): string;
/**
 * 设置元素属性
 */
export declare function setAttribute(element: HTMLElement, name: string, value: string): void;
/**
 * 获取元素属性
 */
export declare function getAttribute(element: HTMLElement, name: string): string | null;
/**
 * 移除元素属性
 */
export declare function removeAttribute(element: HTMLElement, name: string): void;
/**
 * 检查元素是否在视口中
 */
export declare function isInViewport(element: HTMLElement): boolean;
/**
 * 滚动到元素
 */
export declare function scrollToElement(element: HTMLElement, behavior?: ScrollBehavior): void;
/**
 * 获取元素位置
 */
export declare function getElementPosition(element: HTMLElement): {
    top: number;
    left: number;
};
/**
 * 获取元素尺寸
 */
export declare function getElementSize(element: HTMLElement): {
    width: number;
    height: number;
};
//# sourceMappingURL=dom-utils.d.ts.map