# Vite 集成

Micro-Core 提供了与 Vite 的深度集成，支持 Vite 4.x+ 版本，包含完整的开发服务器、构建优化、模块联邦等功能。

## 📋 目录

- [集成概述](#集成概述)
- [安装配置](#安装配置)
- [开发环境](#开发环境)
- [生产构建](#生产构建)
- [模块联邦](#模块联邦)
- [插件生态](#插件生态)
- [性能优化](#性能优化)
- [最佳实践](#最佳实践)

## 集成概述

### 核心特性

```typescript
// Vite 集成特性
const viteIntegrationFeatures = {
  // 开发体验
  development: [
    '极速热更新 (HMR)',
    '即时服务器启动',
    '按需编译',
    '源码映射支持'
  ],
  
  // 构建优化
  build: [
    'Rollup 构建',
    'Tree Shaking',
    'Code Splitting',
    '资源优化'
  ],
  
  // 微前端特性
  microFrontend: [
    '模块联邦',
    '动态导入',
    '共享依赖',
    '独立部署'
  ],
  
  // 插件生态
  plugins: [
    'Vue/React/Svelte 支持',
    'TypeScript 支持',
    'CSS 预处理器',
    '自定义插件'
  ]
}
```

### 架构设计

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Vite + Micro-Core 架构                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    开发环境                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ Vite Dev    │  │ HMR 服务器   │  │ 代理服务器           │ │ │
│  │  │ Server      │  │             │  │                     │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    构建环境                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ Rollup      │  │ 模块联邦     │  │ 资源优化             │ │ │
│  │  │ 构建        │  │             │  │                     │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              │                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Micro-Core 集成                          │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ 应用管理     │  │ 模块加载     │  │ 依赖共享             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 安装配置

### 安装依赖

```bash
# 安装 Vite 和相关插件
npm install vite @vitejs/plugin-vue @vitejs/plugin-react
npm install @micro-core/vite-plugin

# TypeScript 支持
npm install typescript @types/node

# 可选插件
npm install vite-plugin-federation
npm install vite-plugin-mock
npm install vite-plugin-eslint
```

### 基础配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import react from '@vitejs/plugin-react'
import { microCore } from '@micro-core/vite-plugin'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    vue(),
    react(),
    microCore({
      // 微前端配置
      name: 'main-app',
      type: 'host', // 'host' | 'remote'
      
      // 远程应用配置
      remotes: {
        'vue-app': 'http://localhost:3001/assets/remoteEntry.js',
        'react-app': 'http://localhost:3002/assets/remoteEntry.js'
      },
      
      // 共享依赖
      shared: {
        vue: {
          singleton: true,
          requiredVersion: '^3.0.0'
        },
        react: {
          singleton: true,
          requiredVersion: '^18.0.0'
        },
        'react-dom': {
          singleton: true,
          requiredVersion: '^18.0.0'
        }
      }
    })
  ],
  
  // 开发服务器配置
  server: {
    port: 3000,
    host: true,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  
  // 构建配置
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
    
    // Rollup 配置
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      },
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router'],
          utils: ['lodash', 'axios']
        }
      }
    }
  },
  
  // 路径解析
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@utils': resolve(__dirname, 'src/utils')
    }
  },
  
  // CSS 配置
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      }
    },
    modules: {
      localsConvention: 'camelCase'
    }
  }
})
```

## 开发环境

### 主应用配置

```typescript
// 主应用 vite.config.ts
import { defineConfig } from 'vite'
import { microCore } from '@micro-core/vite-plugin'

export default defineConfig({
  plugins: [
    microCore({
      name: 'host-app',
      type: 'host',
      
      // 微应用配置
      apps: [
        {
          name: 'vue-app',
          entry: 'http://localhost:3001',
          activeWhen: '/vue-app'
        },
        {
          name: 'react-app',
          entry: 'http://localhost:3002',
          activeWhen: '/react-app'
        }
      ],
      
      // 开发环境配置
      dev: {
        autoStart: true,
        openBrowser: true,
        mockRemotes: true
      }
    })
  ],
  
  server: {
    port: 3000,
    open: true,
    
    // 代理微应用
    proxy: {
      '/vue-app': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        ws: true
      },
      '/react-app': {
        target: 'http://localhost:3002',
        changeOrigin: true,
        ws: true
      }
    }
  }
})
```

### 微应用配置

```typescript
// Vue 微应用 vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { microCore } from '@micro-core/vite-plugin'

export default defineConfig({
  plugins: [
    vue(),
    microCore({
      name: 'vue-app',
      type: 'remote',
      
      // 暴露的模块
      exposes: {
        './App': './src/App.vue',
        './router': './src/router/index.ts',
        './store': './src/store/index.ts'
      },
      
      // 共享依赖
      shared: {
        vue: {
          singleton: true,
          requiredVersion: '^3.0.0'
        }
      },
      
      // 生命周期配置
      lifecycle: {
        mount: './src/main.ts',
        unmount: './src/main.ts'
      }
    })
  ],
  
  server: {
    port: 3001,
    cors: true
  },
  
  build: {
    target: 'es2015',
    lib: {
      entry: './src/main.ts',
      name: 'VueApp',
      formats: ['es']
    }
  }
})
```

### 开发脚本

```json
{
  "scripts": {
    "dev": "vite",
    "dev:host": "vite --config vite.config.host.ts",
    "dev:remote": "vite --config vite.config.remote.ts",
    "dev:all": "concurrently \"npm run dev:host\" \"npm run dev:remote\"",
    "build": "vite build",
    "build:analyze": "vite build --mode analyze",
    "preview": "vite preview",
    "type-check": "vue-tsc --noEmit"
  }
}
```

## 生产构建

### 构建优化配置

```typescript
// vite.config.prod.ts
import { defineConfig } from 'vite'
import { microCore } from '@micro-core/vite-plugin'
import { visualizer } from 'rollup-plugin-visualizer'

export default defineConfig({
  plugins: [
    microCore({
      name: 'production-app',
      type: 'host',
      
      // 生产环境优化
      optimization: {
        splitChunks: true,
        minify: true,
        treeshake: true,
        compression: 'gzip'
      },
      
      // CDN 配置
      cdn: {
        enabled: true,
        baseUrl: 'https://cdn.example.com',
        modules: ['vue', 'react', 'lodash']
      }
    }),
    
    // 构建分析
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true
    })
  ],
  
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    
    // Rollup 配置
    rollupOptions: {
      output: {
        // 手动分包
        manualChunks: {
          // 框架代码
          framework: ['vue', 'vue-router', 'pinia'],
          // 工具库
          utils: ['lodash', 'dayjs', 'axios'],
          // UI 组件库
          ui: ['element-plus', 'ant-design-vue']
        },
        
        // 文件命名
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      }
    }
  },
  
  // 环境变量
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString())
  }
})
```

### 多环境构建

```typescript
// vite.config.ts
import { defineConfig, loadEnv } from 'vite'

export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')
  
  return {
    plugins: [
      microCore({
        name: 'multi-env-app',
        
        // 根据环境配置不同的远程应用地址
        remotes: {
          'vue-app': env.VUE_APP_URL || 'http://localhost:3001',
          'react-app': env.REACT_APP_URL || 'http://localhost:3002'
        },
        
        // 环境特定配置
        ...(mode === 'development' && {
          dev: {
            autoStart: true,
            mockRemotes: true
          }
        }),
        
        ...(mode === 'production' && {
          optimization: {
            minify: true,
            treeshake: true
          }
        })
      })
    ],
    
    // 环境变量注入
    define: {
      __DEV__: mode === 'development',
      __PROD__: mode === 'production',
      __API_BASE__: JSON.stringify(env.VITE_API_BASE)
    }
  }
})
```

## 模块联邦

### 联邦配置

```typescript
// vite.config.federation.ts
import { defineConfig } from 'vite'
import federation from '@originjs/vite-plugin-federation'

export default defineConfig({
  plugins: [
    federation({
      name: 'host-app',
      
      // 远程模块
      remotes: {
        'vue-remote': 'http://localhost:3001/assets/remoteEntry.js',
        'react-remote': 'http://localhost:3002/assets/remoteEntry.js'
      },
      
      // 共享依赖
      shared: {
        vue: {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^3.0.0'
        },
        react: {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^18.0.0'
        },
        'react-dom': {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^18.0.0'
        }
      }
    })
  ],
  
  build: {
    target: 'esnext',
    minify: false,
    cssCodeSplit: false,
    
    rollupOptions: {
      external: ['vue', 'react', 'react-dom']
    }
  }
})
```

### 动态导入

```typescript
// 动态导入远程模块
const loadRemoteComponent = async (remoteName: string, moduleName: string) => {
  try {
    // @ts-ignore
    const remote = await import(/* @vite-ignore */ `${remoteName}/${moduleName}`)
    return remote.default || remote
  } catch (error) {
    console.error(`Failed to load remote module: ${remoteName}/${moduleName}`, error)
    return null
  }
}

// 使用示例
const VueComponent = await loadRemoteComponent('vue-remote', './App')
const ReactComponent = await loadRemoteComponent('react-remote', './App')
```

## 插件生态

### 自定义插件

```typescript
// plugins/micro-core-plugin.ts
import type { Plugin } from 'vite'

interface MicroCorePluginOptions {
  apps: Array<{
    name: string
    entry: string
    activeWhen: string
  }>
}

export function microCorePlugin(options: MicroCorePluginOptions): Plugin {
  return {
    name: 'micro-core-plugin',
    
    // 配置开发服务器
    configureServer(server) {
      server.middlewares.use('/micro-core', (req, res, next) => {
        if (req.url === '/apps') {
          res.setHeader('Content-Type', 'application/json')
          res.end(JSON.stringify(options.apps))
        } else {
          next()
        }
      })
    },
    
    // 生成虚拟模块
    resolveId(id) {
      if (id === 'virtual:micro-core-config') {
        return id
      }
    },
    
    load(id) {
      if (id === 'virtual:micro-core-config') {
        return `export default ${JSON.stringify(options)}`
      }
    },
    
    // 转换代码
    transform(code, id) {
      if (id.includes('main.ts')) {
        return {
          code: `
            import microCoreConfig from 'virtual:micro-core-config'
            ${code}
            
            // 注入微前端配置
            window.__MICRO_CORE_CONFIG__ = microCoreConfig
          `,
          map: null
        }
      }
    },
    
    // 生成 bundle
    generateBundle(options, bundle) {
      // 生成微前端清单文件
      this.emitFile({
        type: 'asset',
        fileName: 'micro-apps.json',
        source: JSON.stringify(options.apps, null, 2)
      })
    }
  }
}
```

### 常用插件集成

```typescript
// vite.config.plugins.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import legacy from '@vitejs/plugin-legacy'
import { resolve } from 'path'

// 插件配置
const plugins = [
  // Vue 支持
  vue({
    include: [/\.vue$/, /\.md$/]
  }),
  
  // 兼容性支持
  legacy({
    targets: ['defaults', 'not IE 11']
  }),
  
  // Mock 数据
  mock({
    mockPath: 'mock',
    localEnabled: true,
    prodEnabled: false
  }),
  
  // ESLint 检查
  eslint({
    include: ['src/**/*.ts', 'src/**/*.vue'],
    exclude: ['node_modules']
  }),
  
  // PWA 支持
  VitePWA({
    registerType: 'autoUpdate',
    workbox: {
      globPatterns: ['**/*.{js,css,html,ico,png,svg}']
    }
  })
]

export default defineConfig({
  plugins
})
```

## 性能优化

### 构建性能优化

```typescript
// vite.config.performance.ts
import { defineConfig } from 'vite'

export default defineConfig({
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'axios',
      'lodash'
    ],
    exclude: [
      '@micro-core/core'
    ]
  },
  
  build: {
    // 启用 CSS 代码分割
    cssCodeSplit: true,
    
    // 设置 chunk 大小警告限制
    chunkSizeWarningLimit: 1000,
    
    rollupOptions: {
      // 外部化依赖
      external: ['vue', 'react'],
      
      output: {
        // 优化分包策略
        manualChunks: (id) => {
          // 第三方库
          if (id.includes('node_modules')) {
            if (id.includes('vue')) return 'vue-vendor'
            if (id.includes('react')) return 'react-vendor'
            if (id.includes('lodash')) return 'utils-vendor'
            return 'vendor'
          }
          
          // 业务代码
          if (id.includes('src/components')) return 'components'
          if (id.includes('src/utils')) return 'utils'
        }
      }
    }
  },
  
  // 服务器性能优化
  server: {
    fs: {
      // 允许访问的文件范围
      allow: ['..']
    }
  }
})
```

### 运行时性能优化

```typescript
// 懒加载配置
const lazyLoadConfig = {
  // 路由懒加载
  routes: [
    {
      path: '/vue-app',
      component: () => import('./views/VueApp.vue')
    },
    {
      path: '/react-app',
      component: () => import('./views/ReactApp.vue')
    }
  ],
  
  // 组件懒加载
  components: {
    AsyncComponent: defineAsyncComponent({
      loader: () => import('./components/HeavyComponent.vue'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
      delay: 200,
      timeout: 3000
    })
  }
}

// 预加载策略
const preloadStrategy = {
  // 关键资源预加载
  critical: [
    '/assets/main.css',
    '/assets/main.js'
  ],
  
  // 路由预加载
  routes: {
    '/vue-app': () => import('./views/VueApp.vue'),
    '/react-app': () => import('./views/ReactApp.vue')
  }
}
```

## 最佳实践

### 1. 项目结构

```
project/
├── src/
│   ├── apps/              # 微应用
│   │   ├── vue-app/
│   │   └── react-app/
│   ├── shared/            # 共享资源
│   │   ├── components/
│   │   ├── utils/
│   │   └── types/
│   ├── main.ts           # 主入口
│   └── micro-core.ts     # 微前端配置
├── public/               # 静态资源
├── dist/                 # 构建输出
├── vite.config.ts        # Vite 配置
└── package.json
```

### 2. 环境配置

```bash
# .env.development
VITE_APP_TITLE=Micro-Core Dev
VITE_API_BASE=http://localhost:8080
VITE_VUE_APP_URL=http://localhost:3001
VITE_REACT_APP_URL=http://localhost:3002

# .env.production
VITE_APP_TITLE=Micro-Core
VITE_API_BASE=https://api.example.com
VITE_VUE_APP_URL=https://vue-app.example.com
VITE_REACT_APP_URL=https://react-app.example.com
```

### 3. 类型定义

```typescript
// types/micro-core.d.ts
declare module 'virtual:micro-core-config' {
  const config: {
    apps: Array<{
      name: string
      entry: string
      activeWhen: string
    }>
  }
  export default config
}

// 扩展 Window 接口
declare global {
  interface Window {
    __MICRO_CORE_CONFIG__: any
    __MICRO_CORE__: any
  }
}
```

### 4. 调试配置

```typescript
// vite.config.debug.ts
export default defineConfig({
  plugins: [
    microCore({
      debug: true,
      logLevel: 'verbose'
    })
  ],
  
  // 开发工具
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  },
  
  // 源码映射
  build: {
    sourcemap: true
  }
})
```

## 总结

Vite 集成为 Micro-Core 提供了现代化的开发和构建体验：

- ✅ **极速开发** - 毫秒级热更新，即时服务器启动
- ✅ **现代构建** - 基于 Rollup 的优化构建
- ✅ **模块联邦** - 完整的模块联邦支持
- ✅ **插件生态** - 丰富的插件生态系统
- ✅ **性能优化** - 多种性能优化策略
- ✅ **开发体验** - 完整的 TypeScript 和调试支持

通过 Vite 集成，您可以享受到现代前端工具链的所有优势，同时获得微前端架构的灵活性和可扩展性。