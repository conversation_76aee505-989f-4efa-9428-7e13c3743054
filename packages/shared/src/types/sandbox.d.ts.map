{"version": 3, "file": "sandbox.d.ts", "sourceRoot": "", "sources": ["sandbox.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAGH,MAAM,MAAM,WAAW,GACjB,OAAO,GACP,gBAAgB,GAChB,QAAQ,GACR,cAAc,GACd,WAAW,GACX,YAAY,CAAC;AAGnB,MAAM,WAAW,aAAa;IAC1B,WAAW;IACX,IAAI,EAAE,WAAW,CAAC;IAClB,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,eAAe;IACf,MAAM,EAAE,OAAO,CAAC;IAChB,WAAW;IACX,SAAS,EAAE,eAAe,CAAC;IAC3B,WAAW;IACX,WAAW,EAAE;QACT,eAAe;QACf,UAAU,EAAE,OAAO,CAAC;QACpB,eAAe;QACf,WAAW,EAAE,MAAM,CAAC;QACpB,iBAAiB;QACjB,OAAO,EAAE,MAAM,CAAC;KACnB,CAAC;IACF,WAAW;IACX,WAAW,EAAE,iBAAiB,EAAE,CAAC;IACjC,YAAY;IACZ,gBAAgB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC1C;AAGD,MAAM,WAAW,eAAe;IAC5B,oBAAoB;IACpB,UAAU,EAAE;QACR,WAAW;QACX,OAAO,EAAE,OAAO,CAAC;QACjB,cAAc;QACd,eAAe,EAAE,MAAM,EAAE,CAAC;QAC1B,cAAc;QACd,eAAe,EAAE,MAAM,EAAE,CAAC;QAC1B,mBAAmB;QACnB,cAAc,EAAE,OAAO,CAAC;KAC3B,CAAC;IACF,aAAa;IACb,GAAG,EAAE;QACD,WAAW;QACX,OAAO,EAAE,OAAO,CAAC;QACjB,WAAW;QACX,QAAQ,EAAE,QAAQ,GAAG,QAAQ,GAAG,WAAW,CAAC;QAC5C,aAAa;QACb,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,eAAe;QACf,mBAAmB,EAAE,OAAO,CAAC;KAChC,CAAC;IACF,aAAa;IACb,GAAG,EAAE;QACD,WAAW;QACX,OAAO,EAAE,OAAO,CAAC;QACjB,sBAAsB;QACtB,YAAY,EAAE,OAAO,CAAC;QACtB,YAAY;QACZ,iBAAiB,CAAC,EAAE,MAAM,CAAC;KAC9B,CAAC;CACL;AAGD,MAAM,WAAW,iBAAiB;IAC9B,WAAW;IACX,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG,SAAS,GAAG,SAAS,GAAG,QAAQ,CAAC;IACvD,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,OAAO,EAAE,OAAO,CAAC;IACjB,WAAW;IACX,KAAK,EAAE,MAAM,GAAG,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IAC9C,WAAW;IACX,WAAW,CAAC,EAAE,MAAM,CAAC;CACxB;AAGD,MAAM,WAAW,cAAc;IAC3B,YAAY;IACZ,EAAE,EAAE,MAAM,CAAC;IACX,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,IAAI,EAAE,WAAW,CAAC;IAClB,aAAa;IACb,WAAW,EAAE,GAAG,CAAC;IACjB,aAAa;IACb,aAAa,CAAC,EAAE,QAAQ,CAAC;IACzB,aAAa;IACb,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,WAAW;IACX,KAAK,EAAE,UAAU,GAAG,QAAQ,GAAG,WAAW,CAAC;IAC3C,WAAW;IACX,SAAS,EAAE,MAAM,CAAC;IAClB,aAAa;IACb,YAAY,EAAE,MAAM,CAAC;CACxB;AAGD,MAAM,WAAW,eAAe;IAC5B,WAAW;IACX,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC;IAC3B,WAAW;IACX,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IAEtB,WAAW;IACX,MAAM,CAAC,MAAM,EAAE,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;IACvD,WAAW;IACX,QAAQ,CAAC,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACjD,WAAW;IACX,UAAU,CAAC,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACnD,WAAW;IACX,OAAO,CAAC,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAChD,WAAW;IACX,OAAO,CAAC,OAAO,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;IAC7D,aAAa;IACb,UAAU,CAAC,OAAO,EAAE,cAAc,GAAG,yBAAyB,CAAC;CAClE;AAGD,MAAM,WAAW,yBAAyB;IACtC,gBAAgB;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,mBAAmB;IACnB,QAAQ,EAAE,MAAM,CAAC;IACjB,eAAe;IACf,aAAa,EAAE,MAAM,CAAC;IACtB,eAAe;IACf,YAAY,EAAE,MAAM,CAAC;IACrB,WAAW;IACX,eAAe,EAAE,MAAM,CAAC;IACxB,WAAW;IACX,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa;IACb,WAAW,EAAE,MAAM,CAAC;CACvB;AAGD,MAAM,WAAW,YAAY;IACzB,WAAW;IACX,IAAI,EAAE,SAAS,GAAG,WAAW,GAAG,aAAa,GAAG,WAAW,GAAG,OAAO,CAAC;IACtE,YAAY;IACZ,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW;IACX,IAAI,CAAC,EAAE,GAAG,CAAC;IACX,UAAU;IACV,SAAS,EAAE,MAAM,CAAC;CACrB;AAGD,MAAM,WAAW,YAAa,SAAQ,KAAK;IACvC,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,IAAI,EAAE,iBAAiB,GAAG,mBAAmB,GAAG,kBAAkB,GAAG,mBAAmB,GAAG,SAAS,CAAC;IACrG,YAAY;IACZ,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,WAAW;IACX,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CACjC;AAGD,MAAM,WAAW,QAAQ;IACrB,YAAY;IACZ,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,WAAW;IACX,OAAO,EAAE,OAAO,CAAC;IAEjB,aAAa;IACb,UAAU,CAAC,MAAM,EAAE,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACnD,WAAW;IACX,KAAK,CAAC,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9C,WAAW;IACX,MAAM,CAAC,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/C,WAAW;IACX,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;CAC5B;AAGD,MAAM,WAAW,oBAAoB;IACjC,aAAa;IACb,WAAW,EAAE,WAAW,CAAC;IACzB,YAAY;IACZ,gBAAgB,EAAE,WAAW,EAAE,CAAC;IAChC,aAAa;IACb,UAAU,EAAE,OAAO,CAAC;IACpB,WAAW;IACX,oBAAoB,EAAE;QAClB,MAAM,EAAE,MAAM,CAAC;QACf,GAAG,EAAE,MAAM,CAAC;QACZ,aAAa,EAAE,MAAM,CAAC;KACzB,CAAC;CACL;AAGD,MAAM,WAAW,oBAAoB;IACjC,aAAa;IACb,YAAY,EAAE,MAAM,CAAC;IACrB,aAAa;IACb,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;IACtC,eAAe;IACf,qBAAqB,EAAE,OAAO,CAAC;IAC/B,eAAe;IACf,eAAe,EAAE,MAAM,CAAC;IACxB,eAAe;IACf,WAAW,EAAE,OAAO,CAAC;CACxB"}