/**
 * @fileoverview 全局样式文件
 * <AUTHOR> <<EMAIL>>
 */

/* 全局样式重置 */
*,
*::before,
*::after {
    box-sizing: border-box;
}

html {
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #262626;
    background-color: #ffffff;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 链接样式 */
a {
    color: #1890ff;
    text-decoration: none;
    background-color: transparent;
    outline: none;
    cursor: pointer;
    transition: color 0.3s;
}

a:hover,
a:focus {
    color: #40a9ff;
}

a:active {
    color: #096dd9;
}

/* 按钮样式重置 */
button {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    margin: 0;
    overflow: visible;
    text-transform: none;
    -webkit-appearance: button;
}

button::-moz-focus-inner {
    border-style: none;
    padding: 0;
}

/* 输入框样式重置 */
input,
textarea,
select {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    margin: 0;
}

input:focus,
textarea:focus,
select:focus {
    outline: none;
}

/* 代码样式 */
code,
kbd,
pre,
samp {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
    font-size: 1em;
}

pre {
    margin: 0;
    overflow: auto;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 选择文本样式 */
::selection {
    background-color: #1890ff;
    color: #ffffff;
}

::-moz-selection {
    background-color: #1890ff;
    color: #ffffff;
}

/* 工具类 */
.clearfix::after {
    content: '';
    display: table;
    clear: both;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 响应式工具类 */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

@media (min-width: 576px) {
    .container {
        padding: 0 24px;
    }
}

@media (min-width: 768px) {
    .container {
        padding: 0 32px;
    }
}

@media (min-width: 992px) {
    .container {
        padding: 0 40px;
    }
}

/* 微前端应用容器样式 */
.micro-app-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.micro-app-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #8c8c8c;
}

.micro-app-error {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #ff4d4f;
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 6px;
    padding: 20px;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-in-up {
    animation: slideInUp 0.3s ease-out;
}

.slide-in-down {
    animation: slideInDown 0.3s ease-out;
}

/* 主题变量 */
:root {
    --primary-color: #1890ff;
    --primary-color-hover: #40a9ff;
    --primary-color-active: #096dd9;
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #ff4d4f;
    --info-color: #1890ff;

    --text-color: #262626;
    --text-color-secondary: #595959;
    --text-color-disabled: #bfbfbf;

    --background-color: #ffffff;
    --background-color-light: #fafafa;
    --background-color-dark: #f5f5f5;

    --border-color: #d9d9d9;
    --border-color-light: #f0f0f0;
    --border-color-dark: #bfbfbf;

    --border-radius: 6px;
    --border-radius-small: 4px;
    --border-radius-large: 8px;

    --shadow-small: 0 2px 8px rgba(0, 0, 0, 0.15);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-large: 0 8px 24px rgba(0, 0, 0, 0.15);

    --transition-duration: 0.3s;
    --transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #ffffff;
        --text-color-secondary: #a6a6a6;
        --text-color-disabled: #595959;

        --background-color: #141414;
        --background-color-light: #1f1f1f;
        --background-color-dark: #0f0f0f;

        --border-color: #434343;
        --border-color-light: #303030;
        --border-color-dark: #595959;
    }

    body {
        background-color: var(--background-color);
        color: var(--text-color);
    }
}

/* 打印样式 */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    a,
    a:visited {
        text-decoration: underline;
    }

    pre,
    blockquote {
        border: 1px solid #999;
        page-break-inside: avoid;
    }

    thead {
        display: table-header-group;
    }

    tr,
    img {
        page-break-inside: avoid;
    }

    img {
        max-width: 100% !important;
    }

    p,
    h2,
    h3 {
        orphans: 3;
        widows: 3;
    }

    h2,
    h3 {
        page-break-after: avoid;
    }
}