# 构建集成

Micro-Core 提供了与主流构建工具的深度集成，支持 Webpack、Vite、Rollup、esbuild 等构建工具，让微前端应用的构建和部署变得简单高效。

## Webpack 集成

### 基础配置

```javascript
// webpack.config.js
const { MicroCoreWebpackPlugin } = require('@micro-core/webpack-plugin')
const ModuleFederationPlugin = require('@module-federation/webpack')

module.exports = {
  mode: 'development',
  entry: './src/index.ts',
  
  plugins: [
    // Micro-Core 插件
    new MicroCoreWebpackPlugin({
      // 应用名称
      name: 'main-app',
      
      // 微应用配置
      apps: [
        {
          name: 'react-app',
          entry: 'http://localhost:3001/remoteEntry.js',
          activeWhen: '/react-app'
        }
      ],
      
      // 共享依赖
      shared: {
        react: { singleton: true },
        'react-dom': { singleton: true },
        vue: { singleton: true }
      }
    }),
    
    // 模块联邦插件
    new ModuleFederationPlugin({
      name: 'mainApp',
      remotes: {
        reactApp: 'reactApp@http://localhost:3001/remoteEntry.js',
        vueApp: 'vueApp@http://localhost:3002/remoteEntry.js'
      },
      shared: {
        react: { singleton: true },
        'react-dom': { singleton: true }
      }
    })
  ],
  
  devServer: {
    port: 3000,
    historyApiFallback: true,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  }
}
```

### 微应用 Webpack 配置

```javascript
// 微应用 webpack.config.js
const { MicroCoreWebpackPlugin } = require('@micro-core/webpack-plugin')
const ModuleFederationPlugin = require('@module-federation/webpack')

module.exports = {
  mode: 'development',
  entry: './src/index.ts',
  
  plugins: [
    new MicroCoreWebpackPlugin({
      // 微应用模式
      type: 'micro-app',
      name: 'react-app',
      
      // 导出的生命周期函数
      exposes: {
        './App': './src/App.tsx'
      },
      
      // 生命周期配置
      lifecycle: {
        mount: './src/lifecycle/mount',
        unmount: './src/lifecycle/unmount',
        update: './src/lifecycle/update'
      }
    }),
    
    new ModuleFederationPlugin({
      name: 'reactApp',
      filename: 'remoteEntry.js',
      exposes: {
        './App': './src/App'
      },
      shared: {
        react: { singleton: true },
        'react-dom': { singleton: true }
      }
    })
  ],
  
  devServer: {
    port: 3001,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  }
}
```

### 高级 Webpack 配置

```javascript
// 高级配置示例
module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      name: 'advanced-app',
      
      // 预加载配置
      prefetch: {
        enabled: true,
        strategy: 'idle', // 'idle' | 'visible' | 'manual'
        apps: ['react-app', 'vue-app']
      },
      
      // 缓存配置
      cache: {
        enabled: true,
        strategy: 'memory', // 'memory' | 'localStorage' | 'indexedDB'
        maxAge: 3600000 // 1小时
      },
      
      // 错误处理
      errorHandling: {
        fallback: './src/ErrorFallback.tsx',
        retry: {
          enabled: true,
          maxRetries: 3,
          delay: 1000
        }
      },
      
      // 性能监控
      performance: {
        enabled: true,
        reportUrl: '/api/performance',
        metrics: ['loadTime', 'renderTime', 'memoryUsage']
      }
    })
  ]
}
```

## Vite 集成

### 基础配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import { microCore } from '@micro-core/vite-plugin'
import { federation } from '@originjs/vite-plugin-federation'

export default defineConfig({
  plugins: [
    // Micro-Core 插件
    microCore({
      name: 'main-app',
      
      // 微应用配置
      apps: [
        {
          name: 'react-app',
          entry: 'http://localhost:3001/assets/remoteEntry.js',
          activeWhen: '/react-app'
        }
      ],
      
      // 开发服务器配置
      devServer: {
        cors: true,
        proxy: {
          '/api': 'http://localhost:8080'
        }
      }
    }),
    
    // 模块联邦插件
    federation({
      name: 'main-app',
      remotes: {
        reactApp: 'http://localhost:3001/assets/remoteEntry.js',
        vueApp: 'http://localhost:3002/assets/remoteEntry.js'
      },
      shared: ['vue', 'react', 'react-dom']
    })
  ],
  
  build: {
    target: 'esnext',
    minify: false,
    cssCodeSplit: false,
    rollupOptions: {
      external: ['vue', 'react', 'react-dom']
    }
  },
  
  server: {
    port: 3000,
    cors: true
  }
})
```

### 微应用 Vite 配置

```typescript
// 微应用 vite.config.ts
import { defineConfig } from 'vite'
import { microCore } from '@micro-core/vite-plugin'
import { federation } from '@originjs/vite-plugin-federation'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [
    react(),
    
    microCore({
      type: 'micro-app',
      name: 'react-app',
      
      // 生命周期函数
      lifecycle: {
        mount: './src/lifecycle.ts#mount',
        unmount: './src/lifecycle.ts#unmount',
        update: './src/lifecycle.ts#update'
      },
      
      // 热更新支持
      hmr: {
        enabled: true,
        port: 3001
      }
    }),
    
    federation({
      name: 'react-app',
      filename: 'remoteEntry.js',
      exposes: {
        './App': './src/App.tsx'
      },
      shared: ['react', 'react-dom']
    })
  ],
  
  build: {
    target: 'esnext',
    minify: false,
    cssCodeSplit: false
  },
  
  server: {
    port: 3001,
    cors: true
  }
})
```

### Vite 开发优化

```typescript
// 开发环境优化配置
export default defineConfig({
  plugins: [
    microCore({
      // 开发模式配置
      development: {
        // 热重载
        hotReload: true,
        
        // 错误覆盖
        errorOverlay: true,
        
        // 调试工具
        devtools: {
          enabled: true,
          position: 'bottom-right'
        },
        
        // 性能分析
        profiler: {
          enabled: true,
          showInConsole: true
        }
      }
    })
  ],
  
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      '@micro-core/core',
      '@micro-core/adapter-react',
      '@micro-core/adapter-vue'
    ]
  }
})
```

## Rollup 集成

### 基础配置

```javascript
// rollup.config.js
import { microCore } from '@micro-core/rollup-plugin'
import { nodeResolve } from '@rollup/plugin-node-resolve'
import { terser } from 'rollup-plugin-terser'

export default {
  input: 'src/index.ts',
  
  output: {
    dir: 'dist',
    format: 'es',
    chunkFileNames: '[name]-[hash].js'
  },
  
  plugins: [
    nodeResolve(),
    
    microCore({
      name: 'rollup-app',
      
      // 代码分割
      codeSplitting: {
        enabled: true,
        strategy: 'dynamic', // 'dynamic' | 'manual'
        chunks: {
          vendor: ['react', 'react-dom'],
          common: ['@micro-core/core']
        }
      },
      
      // 树摇优化
      treeShaking: {
        enabled: true,
        sideEffects: false
      }
    }),
    
    // 生产环境压缩
    process.env.NODE_ENV === 'production' && terser()
  ],
  
  external: ['react', 'react-dom', 'vue']
}
```

## esbuild 集成

### 基础配置

```javascript
// esbuild.config.js
const { microCore } = require('@micro-core/esbuild-plugin')

require('esbuild').build({
  entryPoints: ['src/index.ts'],
  bundle: true,
  outdir: 'dist',
  format: 'esm',
  target: 'es2020',
  
  plugins: [
    microCore({
      name: 'esbuild-app',
      
      // 快速构建配置
      fastBuild: {
        enabled: true,
        skipTypeCheck: true,
        parallelism: 4
      },
      
      // 资源处理
      assets: {
        publicPath: '/assets/',
        optimization: {
          images: true,
          fonts: true
        }
      }
    })
  ],
  
  // 外部依赖
  external: ['react', 'react-dom'],
  
  // 开发服务器
  serve: process.env.NODE_ENV === 'development' ? {
    servedir: 'dist',
    port: 3000
  } : undefined
}).catch(() => process.exit(1))
```

## 构建优化

### 代码分割策略

```javascript
// 智能代码分割
module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      optimization: {
        // 自动分割
        autoSplitting: {
          enabled: true,
          minSize: 20000,
          maxSize: 244000,
          cacheGroups: {
            // 框架代码
            framework: {
              test: /[\\/]node_modules[\\/](react|react-dom|vue|@angular)[\\/]/,
              name: 'framework',
              chunks: 'all',
              priority: 40
            },
            
            // 微前端核心
            microCore: {
              test: /[\\/]node_modules[\\/]@micro-core[\\/]/,
              name: 'micro-core',
              chunks: 'all',
              priority: 30
            },
            
            // 第三方库
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendor',
              chunks: 'all',
              priority: 20
            }
          }
        }
      }
    })
  ]
}
```

### 资源优化

```javascript
// 资源优化配置
module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      assets: {
        // 图片优化
        images: {
          enabled: true,
          formats: ['webp', 'avif'],
          quality: 80,
          progressive: true
        },
        
        // 字体优化
        fonts: {
          enabled: true,
          preload: ['woff2'],
          display: 'swap'
        },
        
        // CSS 优化
        css: {
          enabled: true,
          minify: true,
          extractComments: false,
          optimization: {
            mergeLonghand: true,
            mergeRules: true
          }
        }
      }
    })
  ]
}
```

### 缓存策略

```javascript
// 构建缓存配置
module.exports = {
  cache: {
    type: 'filesystem',
    cacheDirectory: path.resolve(__dirname, '.webpack-cache'),
    buildDependencies: {
      config: [__filename]
    }
  },
  
  plugins: [
    new MicroCoreWebpackPlugin({
      cache: {
        // 构建缓存
        build: {
          enabled: true,
          directory: '.micro-core-cache',
          maxAge: 7 * 24 * 60 * 60 * 1000 // 7天
        },
        
        // 运行时缓存
        runtime: {
          enabled: true,
          strategy: 'stale-while-revalidate',
          maxEntries: 50
        }
      }
    })
  ]
}
```

## 部署配置

### 生产环境构建

```javascript
// 生产环境配置
const productionConfig = {
  mode: 'production',
  
  optimization: {
    minimize: true,
    sideEffects: false,
    usedExports: true,
    
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        default: false,
        vendors: false,
        
        // 微前端运行时
        microCoreRuntime: {
          name: 'micro-core-runtime',
          test: /[\\/]@micro-core[\\/]/,
          priority: 40,
          reuseExistingChunk: true
        }
      }
    }
  },
  
  plugins: [
    new MicroCoreWebpackPlugin({
      // 生产环境优化
      production: {
        // 资源压缩
        compression: {
          enabled: true,
          algorithm: 'gzip',
          threshold: 8192
        },
        
        // 资源哈希
        hash: {
          enabled: true,
          length: 8
        },
        
        // 清理输出目录
        clean: true
      }
    })
  ]
}
```

### CDN 部署配置

```javascript
// CDN 部署配置
module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      deployment: {
        // CDN 配置
        cdn: {
          enabled: true,
          baseUrl: 'https://cdn.example.com',
          
          // 资源上传
          upload: {
            provider: 'aws-s3', // 'aws-s3' | 'aliyun-oss' | 'qcloud-cos'
            bucket: 'my-micro-frontend',
            region: 'us-west-2'
          }
        },
        
        // 版本管理
        versioning: {
          enabled: true,
          strategy: 'timestamp', // 'timestamp' | 'git-hash' | 'semantic'
          format: 'YYYY-MM-DD-HH-mm'
        }
      }
    })
  ]
}
```

## 多环境配置

### 环境配置管理

```javascript
// config/environments.js
const environments = {
  development: {
    apps: [
      {
        name: 'react-app',
        entry: 'http://localhost:3001/remoteEntry.js'
      }
    ],
    devServer: {
      port: 3000,
      hot: true
    }
  },
  
  staging: {
    apps: [
      {
        name: 'react-app',
        entry: 'https://staging-cdn.example.com/react-app/remoteEntry.js'
      }
    ]
  },
  
  production: {
    apps: [
      {
        name: 'react-app',
        entry: 'https://cdn.example.com/react-app/remoteEntry.js'
      }
    ]
  }
}

module.exports = environments[process.env.NODE_ENV || 'development']
```

### 动态配置加载

```javascript
// 动态配置加载
const { MicroCoreWebpackPlugin } = require('@micro-core/webpack-plugin')
const environmentConfig = require('./config/environments')

module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      ...environmentConfig,
      
      // 运行时配置
      runtime: {
        configUrl: '/api/micro-frontend-config',
        fallbackConfig: environmentConfig
      }
    })
  ]
}
```

## 性能监控

### 构建性能分析

```javascript
// 构建性能分析
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin')

const smp = new SpeedMeasurePlugin()

module.exports = smp.wrap({
  plugins: [
    new MicroCoreWebpackPlugin({
      // 性能监控
      performance: {
        enabled: true,
        
        // 构建时间监控
        buildTime: {
          enabled: true,
          threshold: 30000, // 30秒
          alert: true
        },
        
        // 包大小监控
        bundleSize: {
          enabled: true,
          maxSize: 500000, // 500KB
          alert: true
        }
      }
    }),
    
    // 包分析器
    process.env.ANALYZE && new BundleAnalyzerPlugin({
      analyzerMode: 'server',
      openAnalyzer: true
    })
  ].filter(Boolean)
})
```

### 运行时性能监控

```javascript
// 运行时性能监控配置
module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      monitoring: {
        // 性能指标收集
        metrics: {
          enabled: true,
          endpoint: '/api/metrics',
          interval: 30000, // 30秒
          
          // 收集的指标
          collect: [
            'loadTime',      // 加载时间
            'renderTime',    // 渲染时间
            'memoryUsage',   // 内存使用
            'errorRate',     // 错误率
            'userActions'    // 用户行为
          ]
        },
        
        // 错误监控
        errorTracking: {
          enabled: true,
          endpoint: '/api/errors',
          
          // 错误过滤
          filter: (error) => {
            // 过滤掉开发环境的错误
            return process.env.NODE_ENV === 'production'
          }
        }
      }
    })
  ]
}
```

## 最佳实践

### 构建优化建议

1. **合理的代码分割**：按路由、按功能模块进行代码分割
2. **依赖共享**：合理配置共享依赖，避免重复打包
3. **缓存策略**：充分利用构建缓存和浏览器缓存
4. **资源优化**：压缩图片、字体等静态资源
5. **监控告警**：建立完善的构建和运行时监控

### 开发体验优化

```javascript
// 开发体验优化
module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      development: {
        // 快速重建
        fastRefresh: {
          enabled: true,
          overlay: true
        },
        
        // 错误提示
        errorOverlay: {
          enabled: true,
          showWarnings: false
        },
        
        // 开发工具
        devtools: {
          enabled: true,
          inspector: true,
          profiler: true
        }
      }
    })
  ]
}
```

### 团队协作配置

```javascript
// 团队协作配置
module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      // 代码规范
      codeStandards: {
        enabled: true,
        eslint: true,
        prettier: true,
        typescript: true
      },
      
      // 文档生成
      documentation: {
        enabled: true,
        output: 'docs/api',
        format: 'markdown'
      },
      
      // 版本控制
      versionControl: {
        enabled: true,
        autoTag: true,
        changelog: true
      }
    })
  ]
}
```

通过与主流构建工具的深度集成，Micro-Core 让微前端应用的构建变得简单高效，同时提供了丰富的优化选项和监控能力，确保应用在生产环境中的稳定运行。
