/**
 * 共享工具包 - 安全验证工具
 *
 * @description 提供安全验证、输入校验、XSS防护等安全相关工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * 安全配置接口
 */
export interface SecurityConfig {
    /** 是否启用XSS防护 */
    enableXssProtection?: boolean;
    /** 是否启用CSRF防护 */
    enableCsrfProtection?: boolean;
    /** 允许的域名列表 */
    allowedOrigins?: string[];
    /** 允许的协议列表 */
    allowedProtocols?: string[];
    /** 是否启用内容安全策略 */
    enableCsp?: boolean;
    /** CSP配置 */
    cspConfig?: ContentSecurityPolicyConfig;
}
/**
 * 内容安全策略配置
 */
export interface ContentSecurityPolicyConfig {
    /** 默认源 */
    defaultSrc?: string[];
    /** 脚本源 */
    scriptSrc?: string[];
    /** 样式源 */
    styleSrc?: string[];
    /** 图片源 */
    imgSrc?: string[];
    /** 字体源 */
    fontSrc?: string[];
    /** 连接源 */
    connectSrc?: string[];
}
/**
 * 安全验证器类
 */
export declare class SecurityValidator {
    private config;
    constructor(config?: SecurityConfig);
    /**
     * 验证URL是否安全
     * @param url 要验证的URL
     * @returns 是否安全
     */
    validateUrl(url: string): boolean;
    /**
     * 清理HTML内容，防止XSS攻击
     * @param html HTML内容
     * @returns 清理后的HTML
     */
    sanitizeHtml(html: string): string;
    /**
     * 验证输入内容
     * @param input 输入内容
     * @param type 输入类型
     * @returns 是否有效
     */
    validateInput(input: string, type?: 'email' | 'url' | 'text' | 'number'): boolean;
    /**
     * 生成CSP头部
     * @returns CSP字符串
     */
    generateCspHeader(): string;
    /**
     * 验证消息来源
     * @param origin 消息来源
     * @returns 是否可信
     */
    validateOrigin(origin: string): boolean;
    /**
     * 生成随机令牌
     * @param length 令牌长度
     * @returns 随机令牌
     */
    generateToken(length?: number): string;
    /**
     * 更新配置
     * @param newConfig 新配置
     */
    updateConfig(newConfig: Partial<SecurityConfig>): void;
    /**
     * 获取当前配置
     * @returns 当前配置
     */
    getConfig(): SecurityConfig;
}
/**
 * 全局安全验证器实例
 */
export declare const securityValidator: SecurityValidator;
/**
 * 快捷函数：验证URL
 * @param url 要验证的URL
 * @returns 是否安全
 */
export declare function isValidUrl(url: string): boolean;
/**
 * 快捷函数：清理HTML
 * @param html HTML内容
 * @returns 清理后的HTML
 */
export declare function sanitizeHtml(html: string): string;
/**
 * 快捷函数：验证输入
 * @param input 输入内容
 * @param type 输入类型
 * @returns 是否有效
 */
export declare function validateInput(input: string, type?: 'email' | 'url' | 'text' | 'number'): boolean;
/**
 * 快捷函数：验证来源
 * @param origin 消息来源
 * @returns 是否可信
 */
export declare function validateOrigin(origin: string): boolean;
/**
 * 快捷函数：生成令牌
 * @param length 令牌长度
 * @returns 随机令牌
 */
export declare function generateSecureToken(length?: number): string;
//# sourceMappingURL=security-utils.d.ts.map