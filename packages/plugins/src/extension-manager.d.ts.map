{"version": 3, "file": "extension-manager.d.ts", "sourceRoot": "", "sources": ["extension-manager.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAGH,OAAO,KAAK,EAER,eAAe,EACf,oBAAoB,EACvB,MAAM,SAAS,CAAC;AA8BjB;;GAEG;AACH,qBAAa,gBAAiB,SAAQ,YAAY;IAC9C,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAS;IAChC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAA6C;IAC7E,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAwC;IACnE,OAAO,CAAC,QAAQ,CAAC,aAAa,CAA4B;;IAQ1D;;OAEG;IACH,sBAAsB,CAClB,cAAc,EAAE,oBAAoB,EACpC,YAAY,EAAE,MAAM,GACrB,IAAI;IAuCP;;OAEG;IACH,wBAAwB,CAAC,gBAAgB,EAAE,MAAM,GAAG,OAAO;IAiC3D;;OAEG;IACH,iBAAiB,CACb,SAAS,EAAE,eAAe,EAC1B,YAAY,EAAE,MAAM,GACrB,IAAI;IAsDP;;OAEG;IACH,mBAAmB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO;IAqCjD;;OAEG;IACH,iBAAiB,CAAC,gBAAgB,EAAE,MAAM,GAAG,oBAAoB,GAAG,SAAS;IAK7E;;OAEG;IACH,YAAY,CAAC,WAAW,EAAE,MAAM,GAAG,eAAe,GAAG,SAAS;IAK9D;;OAEG;IACH,aAAa,CAAC,gBAAgB,EAAE,MAAM,GAAG,eAAe,EAAE;IAc1D;;OAEG;IACH,YAAY,CAAC,CAAC,GAAG,GAAG,EAAE,gBAAgB,EAAE,MAAM,GAAG,CAAC,EAAE;IAepD;;OAEG;IACH,WAAW,CAAC,CAAC,GAAG,GAAG,EAAE,gBAAgB,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS;IAK7D;;OAEG;IACG,WAAW,CAAC,gBAAgB,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IAsB3E;;OAEG;IACH,iBAAiB,CAAC,gBAAgB,EAAE,MAAM,GAAG,OAAO;IAIpD;;OAEG;IACH,YAAY,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO;IAI1C;;OAEG;IACH,iBAAiB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO;IAyB/C;;OAEG;IACH,mBAAmB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO;IAyBjD;;OAEG;IACH,qBAAqB,IAAI,oBAAoB,EAAE;IAK/C;;OAEG;IACH,gBAAgB,IAAI,eAAe,EAAE;IAKrC;;OAEG;IACH,wBAAwB,CAAC,QAAQ,EAAE,MAAM,GAAG,oBAAoB,EAAE;IAUlE;;OAEG;IACH,mBAAmB,CAAC,QAAQ,EAAE,MAAM,GAAG,eAAe,EAAE;IAUxD;;OAEG;IACH,aAAa,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAsBrC;;OAEG;IACH,QAAQ,IAAI;QACR,oBAAoB,EAAE,MAAM,CAAC;QAC7B,eAAe,EAAE,MAAM,CAAC;QACxB,gBAAgB,EAAE,MAAM,CAAC;QACzB,qBAAqB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC9C,yBAAyB,EAAE,MAAM,CAAC;KACrC;IAgCD;;OAEG;IACH,KAAK,IAAI,IAAI;IAOb;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAW9B;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAUzB;;OAEG;IACH,OAAO,CAAC,+BAA+B;CAgB1C"}