{"name": "@micro-core/builder-parcel", "version": "0.1.0", "description": "Parcel 构建工具适配器，用于微前端应用的零配置构建", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit", "build": "vite build", "dev": "vite build --watch", "test:watch": "vitest"}, "keywords": ["micro-frontend", "parcel", "builder", "微前端"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "devDependencies": {"@parcel/core": "^2.9.0", "typescript": "^5.3.3", "vitest": "^3.2.4"}, "peerDependencies": {"@parcel/core": "^2.9.0"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/builders/builder-parcel"}, "publishConfig": {"access": "public"}, "homepage": "https://micro-core.dev", "bugs": {"url": "https://github.com/echo008/micro-core/issues"}}