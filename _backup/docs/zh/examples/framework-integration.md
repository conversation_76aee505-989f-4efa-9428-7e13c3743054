# 框架集成示例

本文档提供了将不同前端框架集成到 Micro-Core 微前端系统中的完整示例。

## React 应用集成

### 基础 React 应用

```typescript
// src/apps/react-app/index.tsx
import React from 'react'
import { createRoot } from 'react-dom/client'
import { BrowserRouter } from 'react-router-dom'
import App from './App'

// 微前端适配器
class ReactMicroApp {
  private root: any = null
  
  async mount(container: HTMLElement, props: any = {}) {
    const { createRoot } = await import('react-dom/client')
    
    this.root = createRoot(container)
    this.root.render(
      <BrowserRouter basename={props.basename || '/'}>
        <App {...props} />
      </BrowserRouter>
    )
  }
  
  async unmount() {
    if (this.root) {
      this.root.unmount()
      this.root = null
    }
  }
  
  async update(props: any) {
    if (this.root) {
      this.root.render(
        <BrowserRouter basename={props.basename || '/'}>
          <App {...props} />
        </BrowserRouter>
      )
    }
  }
}

// 导出微前端接口
export default new ReactMicroApp()
```

### React 应用组件

```tsx
// src/apps/react-app/App.tsx
import React, { useState, useEffect } from 'react'
import { Routes, Route, Link } from 'react-router-dom'
import { EventBus, GlobalState } from '@micro-core/core'

const App: React.FC<any> = (props) => {
  const [user, setUser] = useState(null)
  const [count, setCount] = useState(0)
  
  useEffect(() => {
    // 监听全局事件
    const handleUserLogin = (userData: any) => {
      setUser(userData)
    }
    
    EventBus.on('user:login', handleUserLogin)
    
    // 订阅全局状态
    const unsubscribe = GlobalState.subscribe('counter', (value) => {
      setCount(value)
    })
    
    return () => {
      EventBus.off('user:login', handleUserLogin)
      unsubscribe()
    }
  }, [])
  
  const handleIncrement = () => {
    const newCount = count + 1
    setCount(newCount)
    GlobalState.set('counter', newCount)
  }
  
  return (
    <div className="react-app">
      <header>
        <h1>React 微应用</h1>
        <nav>
          <Link to="/">首页</Link>
          <Link to="/about">关于</Link>
        </nav>
      </header>
      
      <main>
        <div>用户: {user?.name || '未登录'}</div>
        <div>计数器: {count}</div>
        <button onClick={handleIncrement}>增加</button>
        
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<About />} />
        </Routes>
      </main>
    </div>
  )
}

const Home = () => <div>React 应用首页</div>
const About = () => <div>React 应用关于页面</div>

export default App
```

## Vue 应用集成

### Vue 3 应用

```typescript
// src/apps/vue-app/index.ts
import { createApp, App } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import AppComponent from './App.vue'
import routes from './routes'

class VueMicroApp {
  private app: App | null = null
  private router: any = null
  
  async mount(container: HTMLElement, props: any = {}) {
    const { createApp } = await import('vue')
    const { createRouter, createWebHistory } = await import('vue-router')
    
    // 创建路由
    this.router = createRouter({
      history: createWebHistory(props.basename || '/'),
      routes
    })
    
    // 创建应用
    this.app = createApp(AppComponent, props)
    this.app.use(this.router)
    
    // 挂载应用
    this.app.mount(container)
  }
  
  async unmount() {
    if (this.app) {
      this.app.unmount()
      this.app = null
      this.router = null
    }
  }
  
  async update(props: any) {
    if (this.app) {
      // 更新应用属性
      Object.assign(this.app._props, props)
    }
  }
}

export default new VueMicroApp()
```

### Vue 应用组件

```vue
<!-- src/apps/vue-app/App.vue -->
<template>
  <div class="vue-app">
    <header>
      <h1>Vue 微应用</h1>
      <nav>
        <router-link to="/">首页</router-link>
        <router-link to="/about">关于</router-link>
      </nav>
    </header>
    
    <main>
      <div>用户: {{ user?.name || '未登录' }}</div>
      <div>计数器: {{ count }}</div>
      <button @click="handleIncrement">增加</button>
      
      <router-view />
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { EventBus, GlobalState } from '@micro-core/core'

const user = ref(null)
const count = ref(0)

let unsubscribe: (() => void) | null = null

onMounted(() => {
  // 监听全局事件
  const handleUserLogin = (userData: any) => {
    user.value = userData
  }
  
  EventBus.on('user:login', handleUserLogin)
  
  // 订阅全局状态
  unsubscribe = GlobalState.subscribe('counter', (value) => {
    count.value = value
  })
})

onUnmounted(() => {
  EventBus.off('user:login')
  if (unsubscribe) {
    unsubscribe()
  }
})

const handleIncrement = () => {
  const newCount = count.value + 1
  count.value = newCount
  GlobalState.set('counter', newCount)
}
</script>

<style scoped>
.vue-app {
  padding: 20px;
}

nav a {
  margin-right: 10px;
  text-decoration: none;
  color: #42b883;
}

nav a:hover {
  text-decoration: underline;
}
</style>
```

## Angular 应用集成

### Angular 应用模块

```typescript
// src/apps/angular-app/app.module.ts
import { NgModule } from '@angular/core'
import { BrowserModule } from '@angular/platform-browser'
import { RouterModule } from '@angular/router'
import { AppComponent } from './app.component'
import { HomeComponent } from './home.component'
import { AboutComponent } from './about.component'

@NgModule({
  declarations: [
    AppComponent,
    HomeComponent,
    AboutComponent
  ],
  imports: [
    BrowserModule,
    RouterModule.forRoot([
      { path: '', component: HomeComponent },
      { path: 'about', component: AboutComponent }
    ])
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
```

### Angular 微应用适配器

```typescript
// src/apps/angular-app/index.ts
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic'
import { ApplicationRef } from '@angular/core'
import { AppModule } from './app.module'

class AngularMicroApp {
  private platformRef: any = null
  private moduleRef: any = null
  
  async mount(container: HTMLElement, props: any = {}) {
    const { platformBrowserDynamic } = await import('@angular/platform-browser-dynamic')
    
    // 设置基础路径
    if (props.basename) {
      (window as any).__webpack_public_path__ = props.basename
    }
    
    // 启动 Angular 应用
    this.platformRef = platformBrowserDynamic()
    this.moduleRef = await this.platformRef.bootstrapModule(AppModule)
    
    // 获取应用引用并挂载到容器
    const appRef = this.moduleRef.injector.get(ApplicationRef)
    const componentRef = appRef.bootstrap(AppComponent)
    
    // 将组件插入到指定容器
    container.appendChild(componentRef.location.nativeElement)
  }
  
  async unmount() {
    if (this.moduleRef) {
      this.moduleRef.destroy()
      this.moduleRef = null
    }
    if (this.platformRef) {
      this.platformRef.destroy()
      this.platformRef = null
    }
  }
}

export default new AngularMicroApp()
```

### Angular 应用组件

```typescript
// src/apps/angular-app/app.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core'
import { EventBus, GlobalState } from '@micro-core/core'

@Component({
  selector: 'app-root',
  template: `
    <div class="angular-app">
      <header>
        <h1>Angular 微应用</h1>
        <nav>
          <a routerLink="/">首页</a>
          <a routerLink="/about">关于</a>
        </nav>
      </header>
      
      <main>
        <div>用户: {{ user?.name || '未登录' }}</div>
        <div>计数器: {{ count }}</div>
        <button (click)="handleIncrement()">增加</button>
        
        <router-outlet></router-outlet>
      </main>
    </div>
  `,
  styles: [`
    .angular-app {
      padding: 20px;
    }
    
    nav a {
      margin-right: 10px;
      text-decoration: none;
      color: #dd0031;
    }
    
    nav a:hover {
      text-decoration: underline;
    }
  `]
})
export class AppComponent implements OnInit, OnDestroy {
  user: any = null
  count: number = 0
  
  private unsubscribe?: () => void
  
  ngOnInit() {
    // 监听全局事件
    EventBus.on('user:login', (userData: any) => {
      this.user = userData
    })
    
    // 订阅全局状态
    this.unsubscribe = GlobalState.subscribe('counter', (value: number) => {
      this.count = value
    })
  }
  
  ngOnDestroy() {
    EventBus.off('user:login')
    if (this.unsubscribe) {
      this.unsubscribe()
    }
  }
  
  handleIncrement() {
    const newCount = this.count + 1
    this.count = newCount
    GlobalState.set('counter', newCount)
  }
}
```

## 原生 JavaScript 应用

### 原生 JS 微应用

```typescript
// src/apps/vanilla-app/index.ts
class VanillaMicroApp {
  private container: HTMLElement | null = null
  private eventListeners: Array<[string, Function]> = []
  
  async mount(container: HTMLElement, props: any = {}) {
    this.container = container
    
    // 渲染应用
    this.render(props)
    
    // 绑定事件
    this.bindEvents()
    
    // 监听全局事件
    this.setupGlobalListeners()
  }
  
  async unmount() {
    // 清理事件监听器
    this.eventListeners.forEach(([event, handler]) => {
      EventBus.off(event, handler)
    })
    this.eventListeners = []
    
    // 清空容器
    if (this.container) {
      this.container.innerHTML = ''
      this.container = null
    }
  }
  
  private render(props: any) {
    if (!this.container) return
    
    this.container.innerHTML = `
      <div class="vanilla-app">
        <header>
          <h1>原生 JS 微应用</h1>
          <nav>
            <a href="#/" data-route="/">首页</a>
            <a href="#/about" data-route="/about">关于</a>
          </nav>
        </header>
        
        <main>
          <div id="user-info">用户: 未登录</div>
          <div id="counter">计数器: 0</div>
          <button id="increment-btn">增加</button>
          
          <div id="content">
            <div id="home-page">原生 JS 应用首页</div>
            <div id="about-page" style="display: none;">原生 JS 应用关于页面</div>
          </div>
        </main>
      </div>
    `
  }
  
  private bindEvents() {
    if (!this.container) return
    
    // 路由导航
    const navLinks = this.container.querySelectorAll('[data-route]')
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault()
        const route = (e.target as HTMLElement).getAttribute('data-route')
        this.navigate(route!)
      })
    })
    
    // 增加按钮
    const incrementBtn = this.container.querySelector('#increment-btn')
    incrementBtn?.addEventListener('click', () => {
      this.handleIncrement()
    })
  }
  
  private setupGlobalListeners() {
    // 监听用户登录事件
    const handleUserLogin = (userData: any) => {
      const userInfo = this.container?.querySelector('#user-info')
      if (userInfo) {
        userInfo.textContent = `用户: ${userData.name}`
      }
    }
    
    EventBus.on('user:login', handleUserLogin)
    this.eventListeners.push(['user:login', handleUserLogin])
    
    // 监听计数器变化
    const handleCounterChange = (count: number) => {
      const counter = this.container?.querySelector('#counter')
      if (counter) {
        counter.textContent = `计数器: ${count}`
      }
    }
    
    const unsubscribe = GlobalState.subscribe('counter', handleCounterChange)
    // 注意：这里需要特殊处理 unsubscribe
  }
  
  private navigate(route: string) {
    if (!this.container) return
    
    const homePage = this.container.querySelector('#home-page')
    const aboutPage = this.container.querySelector('#about-page')
    
    if (route === '/') {
      homePage?.setAttribute('style', '')
      aboutPage?.setAttribute('style', 'display: none;')
    } else if (route === '/about') {
      homePage?.setAttribute('style', 'display: none;')
      aboutPage?.setAttribute('style', '')
    }
  }
  
  private handleIncrement() {
    const counter = this.container?.querySelector('#counter')
    if (counter) {
      const currentCount = parseInt(counter.textContent?.split(': ')[1] || '0')
      const newCount = currentCount + 1
      counter.textContent = `计数器: ${newCount}`
      GlobalState.set('counter', newCount)
    }
  }
}

export default new VanillaMicroApp()
```

## 主应用配置

### 应用注册

```typescript
// src/main.ts
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  // 应用配置
  apps: [
    {
      name: 'react-app',
      entry: '/apps/react-app/index.js',
      routes: ['/react/*'],
      props: {
        basename: '/react'
      }
    },
    {
      name: 'vue-app', 
      entry: '/apps/vue-app/index.js',
      routes: ['/vue/*'],
      props: {
        basename: '/vue'
      }
    },
    {
      name: 'angular-app',
      entry: '/apps/angular-app/index.js', 
      routes: ['/angular/*'],
      props: {
        basename: '/angular'
      }
    },
    {
      name: 'vanilla-app',
      entry: '/apps/vanilla-app/index.js',
      routes: ['/vanilla/*'],
      props: {
        basename: '/vanilla'
      }
    }
  ],
  
  // 路由配置
  router: {
    mode: 'history',
    base: '/'
  },
  
  // 沙箱配置
  sandbox: {
    type: 'proxy',
    isolation: {
      css: true,
      js: true
    }
  }
})

// 启动微前端系统
microCore.start()
```

### 主应用 HTML

```html
<!-- public/index.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Micro-Core 框架集成示例</title>
  <style>
    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    }
    
    .main-nav {
      background: #f5f5f5;
      padding: 1rem;
      border-bottom: 1px solid #ddd;
    }
    
    .main-nav a {
      margin-right: 1rem;
      text-decoration: none;
      color: #333;
      font-weight: 500;
    }
    
    .main-nav a:hover {
      color: #1890ff;
    }
    
    #micro-app-container {
      padding: 2rem;
    }
  </style>
</head>
<body>
  <nav class="main-nav">
    <a href="/react">React 应用</a>
    <a href="/vue">Vue 应用</a>
    <a href="/angular">Angular 应用</a>
    <a href="/vanilla">原生 JS 应用</a>
  </nav>
  
  <div id="micro-app-container">
    <!-- 微应用将在这里渲染 -->
  </div>
  
  <script src="/dist/main.js"></script>
</body>
</html>
```

## 构建配置

### Webpack 配置

```javascript
// webpack.config.js
const path = require('path')
const { ModuleFederationPlugin } = require('@module-federation/webpack')

module.exports = {
  mode: 'development',
  
  entry: './src/main.ts',
  
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx']
  },
  
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/
      },
      {
        test: /\.vue$/,
        use: 'vue-loader'
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      }
    ]
  },
  
  plugins: [
    new ModuleFederationPlugin({
      name: 'main_app',
      remotes: {
        'react-app': 'react_app@http://localhost:3001/remoteEntry.js',
        'vue-app': 'vue_app@http://localhost:3002/remoteEntry.js',
        'angular-app': 'angular_app@http://localhost:3003/remoteEntry.js',
        'vanilla-app': 'vanilla_app@http://localhost:3004/remoteEntry.js'
      },
      shared: {
        '@micro-core/core': {
          singleton: true,
          requiredVersion: '^1.0.0'
        }
      }
    })
  ],
  
  devServer: {
    port: 3000,
    historyApiFallback: true,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  }
}
```

## 开发和调试

### 开发环境启动

```json
{
  "scripts": {
    "dev": "concurrently \"npm run dev:main\" \"npm run dev:react\" \"npm run dev:vue\" \"npm run dev:angular\" \"npm run dev:vanilla\"",
    "dev:main": "webpack serve --config webpack.config.js",
    "dev:react": "cd apps/react-app && npm run dev",
    "dev:vue": "cd apps/vue-app && npm run dev", 
    "dev:angular": "cd apps/angular-app && npm run dev",
    "dev:vanilla": "cd apps/vanilla-app && npm run dev",
    "build": "npm run build:main && npm run build:apps",
    "build:main": "webpack --mode production",
    "build:apps": "concurrently \"npm run build:react\" \"npm run build:vue\" \"npm run build:angular\" \"npm run build:vanilla\""
  }
}
```

### 调试配置

```typescript
// src/debug.ts
import { MicroCore } from '@micro-core/core'

// 开发环境调试配置
if (process.env.NODE_ENV === 'development') {
  // 启用调试模式
  MicroCore.enableDebug({
    level: 'verbose',
    showAppBoundaries: true,
    logEvents: true,
    logStateChanges: true
  })
  
  // 添加全局调试工具
  (window as any).__MICRO_CORE_DEBUG__ = {
    getApps: () => microCore.getApps(),
    getGlobalState: () => GlobalState.getAll(),
    emitEvent: (event: string, data?: any) => EventBus.emit(event, data)
  }
}
```

## 最佳实践

### 1. 应用隔离

- 使用沙箱确保应用间的 CSS 和 JS 隔离
- 避免全局变量污染
- 正确处理事件监听器的清理

### 2. 状态管理

- 使用全局状态管理器进行跨应用状态同步
- 本地状态与全局状态保持一致
- 避免状态泄漏

### 3. 路由管理

- 为每个应用配置独立的路由前缀
- 处理路由冲突和嵌套路由
- 支持浏览器前进后退

### 4. 性能优化

- 使用懒加载减少初始包大小
- 共享公共依赖避免重复加载
- 实现应用预加载策略

## 参考资料

- [适配器 API 文档](/api/adapter-api)
- [路由系统文档](/guide/routing)
- [状态管理文档](/api/state-management)
- [构建集成指南](/guide/build-integration)
