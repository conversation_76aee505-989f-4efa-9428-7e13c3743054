import vue from '@vitejs/plugin-vue'
import { defineConfig } from 'vite'

export default defineConfig({
    plugins: [vue()],

    server: {
        port: 3002,
        host: true,
        cors: true,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
    },

    build: {
        outDir: 'dist',
        sourcemap: true,
        minify: 'esbuild',
        target: 'es2020',
        lib: {
            entry: 'src/main.ts',
            name: 'SubAppVue3',
            fileName: 'sub-app-vue3',
            formats: ['umd']
        },
        rollupOptions: {
            external: ['vue', 'vue-router', 'pinia'],
            output: {
                globals: {
                    vue: 'Vue',
                    'vue-router': 'VueRouter',
                    pinia: 'Pinia'
                }
            }
        }
    },

    define: {
        __VUE_OPTIONS_API__: true,
        __VUE_PROD_DEVTOOLS__: false,
        __MICRO_APP_NAME__: JSON.stringify('sub-app-vue3'),
        __POWERED_BY_MICRO_CORE__: true
    },

    resolve: {
        alias: {
            '@': '/src'
        }
    },

    optimizeDeps: {
        include: ['vue', 'vue-router', 'pinia']
    }
})