{"version": 3, "file": "builder-utils.d.ts", "sourceRoot": "", "sources": ["builder-utils.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAEhD;;GAEG;AACH,MAAM,WAAW,WAAW;IACxB,YAAY;IACZ,IAAI,EAAE,WAAW,CAAC;IAClB,WAAW;IACX,KAAK,EAAE,MAAM,CAAC;IACd,WAAW;IACX,MAAM,EAAE,MAAM,CAAC;IACf,WAAW;IACX,IAAI,EAAE,aAAa,GAAG,YAAY,GAAG,MAAM,CAAC;IAC5C,WAAW;IACX,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CACjC;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB;IAC/B,WAAW;IACX,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,eAAe;IACf,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,YAAY;IACZ,YAAY,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CACtC;AAED;;GAEG;AACH,MAAM,WAAW,iBAAkB,SAAQ,WAAW;IAClD,YAAY;IACZ,QAAQ,CAAC,EAAE;QACP,WAAW;QACX,IAAI,EAAE,MAAM,CAAC;QACb,UAAU;QACV,KAAK,EAAE,MAAM,CAAC;QACd,WAAW;QACX,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACjC,WAAW;QACX,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KAChC,CAAC;CACL;AAED;;GAEG;AACH,MAAM,WAAW,WAAW;IACxB,aAAa;IACb,OAAO,EAAE,OAAO,CAAC;IACjB,aAAa;IACb,OAAO,EAAE,WAAW,EAAE,CAAC;IACvB,aAAa;IACb,KAAK,EAAE,UAAU,CAAC;IAClB,WAAW;IACX,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC;IAClB,WAAW;IACX,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;CACvB;AAED;;GAEG;AACH,MAAM,WAAW,WAAW;IACxB,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,IAAI,EAAE,IAAI,GAAG,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;IACtC,cAAc;IACd,OAAO,CAAC,EAAE,OAAO,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,UAAU;IACvB,aAAa;IACb,QAAQ,EAAE,MAAM,CAAC;IACjB,YAAY;IACZ,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW;IACX,SAAS,EAAE,MAAM,CAAC;IAClB,YAAY;IACZ,SAAS,EAAE,MAAM,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAC7B,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW;IACX,KAAK,EAAE,MAAM,CAAC;IACd,WAAW;IACX,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB,WAAW;IACX,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,WAAW;IACX,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC7B,YAAY;IACZ,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAClC;AAED;;;;GAIG;AACH,wBAAgB,cAAc,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,CAQpD;AAED;;;;GAIG;AACH,wBAAgB,mBAAmB,CAAC,MAAM,EAAE,WAAW,GAAG,IAAI,CAY7D;AAED;;;;GAIG;AACH,wBAAgB,gBAAgB,CAAC,OAAO,EAAE;IACtC,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC;IAClB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC7B,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAClC,GAAG,gBAAgB,CAUnB;AAED;;;;;GAKG;AACH,wBAAgB,gBAAgB,CAAC,CAAC,SAAS,WAAW,EAClD,UAAU,EAAE,CAAC,EACb,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC,GAC3B,CAAC,CASH;AAED;;;;GAIG;AACH,wBAAgB,kBAAkB,CAAC,WAAW,EAAE,WAAW,GAAG,OAAO,CA8BpE;AAED;;;;GAIG;AACH,wBAAgB,iBAAiB,CAAC,WAAW,EAAE,WAAW,GAAG,MAAM,GAAG,IAAI,CAuBzE;AAED;;;;;GAKG;AACH,wBAAgB,wBAAwB,CACpC,WAAW,EAAE,WAAW,EACxB,OAAO,EAAE,MAAM,GAChB,iBAAiB,CAWnB;AAED;;;;GAIG;AACH,wBAAgB,gBAAgB,CAAC,WAAW,EAAE,GAAG,GAAG,WAAW,CA4C9D"}