/**
 * @fileoverview 性能监控工具函数
 * @description 提供性能监控相关的工具函数
 * <AUTHOR> <<EMAIL>>
 */
export interface PerformanceMetric {
    startTime: number;
    endTime?: number;
    duration?: number;
}
export interface PerformanceMonitor {
    start(name: string): void;
    end(name: string): void;
    measure<T>(name: string, fn: () => T | Promise<T>): Promise<T>;
    getMetrics(): Record<string, PerformanceMetric>;
    clear(): void;
}
/**
 * 创建性能监控器
 */
export declare function createPerformanceMonitor(): PerformanceMonitor;
//# sourceMappingURL=performance.d.ts.map