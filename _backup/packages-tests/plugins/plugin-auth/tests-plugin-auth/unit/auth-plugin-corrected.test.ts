/**
 * @fileoverview 认证插件单元测试 - 修正版本
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi, afterEach } from 'vitest';
import type { MicroCoreKernel } from '@micro-core/core';
import { AuthPlugin } from '../../src/auth-plugin';
import type { AuthConfig, AuthUser, Role, Permission } from '../../src/types';

// Use global mock kernel
const mockKernel = global.mockKernel as MicroCoreKernel;

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

describe('AuthPlugin - Corrected', () => {
  let authPlugin: AuthPlugin;
  let mockConfig: AuthConfig;

  // Create proper Role and Permission objects
  const createRole = (code: string, name: string): Role => ({
    id: code,
    name,
    code,
    description: `${name} role`,
    permissions: [],
    level: 1
  });

  const createPermission = (code: string, name: string): Permission => ({
    id: code,
    name,
    code,
    description: `${name} permission`,
    resource: 'test',
    action: 'read',
    effect: 'allow'
  });

  const mockUser: AuthUser = {
    id: '1',
    username: 'testuser',
    email: '<EMAIL>',
    roles: [createRole('admin', 'Administrator')],
    permissions: [createPermission('read', 'Read Access')],
    profile: { firstName: 'Test', lastName: 'User' }
  };

  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    sessionStorageMock.getItem.mockReturnValue(null);
    
    mockConfig = {
      tokenKey: 'test-token',
      refreshTokenKey: 'test-refresh-token',
      tokenExpireTime: 3600000, // 1 hour
      autoRefresh: true,
      loginUrl: '/login',
      storage: 'localStorage'
    };
    
    authPlugin = new AuthPlugin(mockConfig);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Plugin Lifecycle', () => {
    it('should install plugin successfully', async () => {
      await authPlugin.install(mockKernel);
      
      expect(mockKernel.registerHook).toHaveBeenCalled();
    });

    it('should uninstall plugin successfully', async () => {
      await authPlugin.install(mockKernel);
      await authPlugin.uninstall();
      
      expect(localStorageMock.removeItem).toHaveBeenCalled();
    });
  });

  describe('Token Management', () => {
    beforeEach(async () => {
      await authPlugin.install(mockKernel);
    });

    it('should get token from storage', () => {
      const token = 'test-token-value';
      localStorageMock.getItem.mockReturnValue(token);
      
      const retrievedToken = authPlugin.getToken();
      expect(retrievedToken).toBe(token);
      expect(localStorageMock.getItem).toHaveBeenCalledWith('test-token');
    });

    it('should return null when no token exists', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const token = authPlugin.getToken();
      expect(token).toBeNull();
    });

    it('should get refresh token from storage', () => {
      const refreshToken = 'test-refresh-token-value';
      localStorageMock.getItem.mockReturnValue(refreshToken);
      
      const retrievedRefreshToken = authPlugin.getRefreshToken();
      expect(retrievedRefreshToken).toBe(refreshToken);
      expect(localStorageMock.getItem).toHaveBeenCalledWith('test-refresh-token');
    });
  });

  describe('Authentication', () => {
    beforeEach(async () => {
      await authPlugin.install(mockKernel);
    });

    it('should login successfully with valid credentials', async () => {
      const mockResponse = {
        success: true,
        data: {
          user: mockUser,
          token: 'new-token',
          refreshToken: 'new-refresh-token'
        }
      };

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const credentials = { username: 'testuser', password: 'password123' };
      const result = await authPlugin.login(credentials);

      expect(global.fetch).toHaveBeenCalled();
      expect(result).toEqual(mockUser);
    });

    it('should handle login failure', async () => {
      const mockResponse = {
        success: false,
        message: 'Invalid credentials'
      };

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const credentials = { username: 'testuser', password: 'wrongpassword' };
      
      await expect(authPlugin.login(credentials)).rejects.toThrow('Invalid credentials');
    });

    it('should logout successfully', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true })
      });

      await authPlugin.logout();

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('test-token');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('test-refresh-token');
    });

    it('should check authentication status', () => {
      // Mock no token
      localStorageMock.getItem.mockReturnValue(null);
      expect(authPlugin.isAuthenticated()).toBe(false);

      // Mock valid token
      localStorageMock.getItem.mockReturnValue('valid-token');
      expect(authPlugin.isAuthenticated()).toBe(true);
    });
  });

  describe('Token Refresh', () => {
    beforeEach(async () => {
      await authPlugin.install(mockKernel);
    });

    it('should refresh token successfully', async () => {
      const mockRefreshResponse = {
        success: true,
        data: {
          token: 'new-token',
          refreshToken: 'new-refresh-token',
          user: mockUser
        }
      };

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockRefreshResponse)
      });

      localStorageMock.getItem.mockReturnValue('old-refresh-token');

      const result = await authPlugin.refreshToken();

      expect(global.fetch).toHaveBeenCalled();
      expect(result).toBe('new-token');
    });

    it('should handle refresh token failure', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('Refresh failed'));
      localStorageMock.getItem.mockReturnValue('invalid-refresh-token');

      const result = await authPlugin.refreshToken();

      expect(result).toBeNull();
    });
  });

  describe('Permission Management', () => {
    beforeEach(async () => {
      await authPlugin.install(mockKernel);
      
      // Mock authenticated user with permissions
      const userWithPermissions: AuthUser = {
        ...mockUser,
        permissions: [
          createPermission('read', 'Read Access'),
          createPermission('write', 'Write Access')
        ]
      };
      
      // Set current user by mocking the private property
      (authPlugin as any).currentUser = userWithPermissions;
    });

    it('should check single permission correctly', () => {
      const readPermission = createPermission('read', 'Read Access');
      expect(authPlugin.hasPermission(readPermission)).toBe(true);
      
      const deletePermission = createPermission('delete', 'Delete Access');
      expect(authPlugin.hasPermission(deletePermission)).toBe(false);
    });

    it('should check permission by string code', () => {
      expect(authPlugin.hasPermission('read')).toBe(true);
      expect(authPlugin.hasPermission('delete')).toBe(false);
    });

    it('should check multiple permissions with hasAllPermissions', () => {
      const permissions = [
        createPermission('read', 'Read Access'),
        createPermission('write', 'Write Access')
      ];
      expect(authPlugin.hasAllPermissions(permissions)).toBe(true);
      
      const permissionsWithMissing = [
        createPermission('read', 'Read Access'),
        createPermission('delete', 'Delete Access')
      ];
      expect(authPlugin.hasAllPermissions(permissionsWithMissing)).toBe(false);
    });

    it('should check multiple permissions with hasAnyPermission', () => {
      const permissions = [
        createPermission('read', 'Read Access'),
        createPermission('delete', 'Delete Access')
      ];
      expect(authPlugin.hasAnyPermission(permissions)).toBe(true);
      
      const noMatchingPermissions = [
        createPermission('delete', 'Delete Access'),
        createPermission('admin', 'Admin Access')
      ];
      expect(authPlugin.hasAnyPermission(noMatchingPermissions)).toBe(false);
    });

    it('should get user permissions', () => {
      const permissions = authPlugin.getUserPermissions();
      expect(permissions).toHaveLength(2);
      expect(permissions[0].code).toBe('read');
      expect(permissions[1].code).toBe('write');
    });
  });

  describe('Role Management', () => {
    beforeEach(async () => {
      await authPlugin.install(mockKernel);
      
      // Mock authenticated user with roles
      const userWithRoles: AuthUser = {
        ...mockUser,
        roles: [
          createRole('admin', 'Administrator'),
          createRole('user', 'Regular User')
        ]
      };
      
      // Set current user by mocking the private property
      (authPlugin as any).currentUser = userWithRoles;
    });

    it('should check single role correctly', () => {
      const adminRole = createRole('admin', 'Administrator');
      expect(authPlugin.hasRole(adminRole)).toBe(true);
      
      const moderatorRole = createRole('moderator', 'Moderator');
      expect(authPlugin.hasRole(moderatorRole)).toBe(false);
    });

    it('should check role by string code', () => {
      expect(authPlugin.hasRole('admin')).toBe(true);
      expect(authPlugin.hasRole('moderator')).toBe(false);
    });

    it('should check multiple roles with hasAllRoles', () => {
      const roles = [
        createRole('admin', 'Administrator'),
        createRole('user', 'Regular User')
      ];
      expect(authPlugin.hasAllRoles(roles)).toBe(true);
      
      const rolesWithMissing = [
        createRole('admin', 'Administrator'),
        createRole('moderator', 'Moderator')
      ];
      expect(authPlugin.hasAllRoles(rolesWithMissing)).toBe(false);
    });

    it('should check multiple roles with hasAnyRole', () => {
      const roles = [
        createRole('admin', 'Administrator'),
        createRole('moderator', 'Moderator')
      ];
      expect(authPlugin.hasAnyRole(roles)).toBe(true);
      
      const noMatchingRoles = [
        createRole('moderator', 'Moderator'),
        createRole('guest', 'Guest')
      ];
      expect(authPlugin.hasAnyRole(noMatchingRoles)).toBe(false);
    });

    it('should get user roles', () => {
      const roles = authPlugin.getUserRoles();
      expect(roles).toHaveLength(2);
      expect(roles[0].code).toBe('admin');
      expect(roles[1].code).toBe('user');
    });
  });

  describe('User Management', () => {
    beforeEach(async () => {
      await authPlugin.install(mockKernel);
    });

    it('should get current user when authenticated', () => {
      // Set current user by mocking the private property
      (authPlugin as any).currentUser = mockUser;
      
      const currentUser = authPlugin.getCurrentUser();
      expect(currentUser).toEqual(mockUser);
    });

    it('should return undefined when no user is authenticated', () => {
      const currentUser = authPlugin.getCurrentUser();
      expect(currentUser).toBeUndefined();
    });
  });

  describe('Error Handling', () => {
    beforeEach(async () => {
      await authPlugin.install(mockKernel);
    });

    it('should handle network errors during login', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));

      const credentials = { username: 'testuser', password: 'password123' };
      
      await expect(authPlugin.login(credentials)).rejects.toThrow('Network error');
    });

    it('should handle HTTP errors during API calls', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      });

      const credentials = { username: 'testuser', password: 'password123' };
      
      await expect(authPlugin.login(credentials)).rejects.toThrow('HTTP 401: Unauthorized');
    });
  });

  describe('Configuration', () => {
    it('should use default configuration when no options provided', () => {
      const defaultPlugin = new AuthPlugin();
      expect(defaultPlugin.name).toBe('auth');
      expect(defaultPlugin.version).toBe('1.0.0');
    });

    it('should merge custom configuration with defaults', () => {
      const customConfig: AuthConfig = {
        tokenKey: 'custom-token',
        storage: 'sessionStorage'
      };
      
      const customPlugin = new AuthPlugin(customConfig);
      expect(customPlugin.name).toBe('auth');
    });
  });
});
