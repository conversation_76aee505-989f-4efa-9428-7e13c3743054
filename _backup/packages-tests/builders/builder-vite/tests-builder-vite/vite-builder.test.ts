/**
 * @fileoverview Vite Builder Tests
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ViteBuilder } from '../src/vite-builder';
import type { ViteBuilderConfig, ViteBuildResult } from '../src/types';

// Mock Vite
const mockViteServer = {
  listen: vi.fn().mockResolvedValue(undefined),
  close: vi.fn().mockResolvedValue(undefined),
  ws: {
    send: vi.fn()
  },
  config: {
    root: '/test',
    base: '/',
    mode: 'development'
  },
  middlewares: {
    use: vi.fn()
  }
};

const mockViteBuild = vi.fn().mockResolvedValue({
  output: [
    {
      type: 'chunk',
      fileName: 'index.js',
      code: 'console.log("test");'
    }
  ]
});

vi.mock('vite', () => ({
  createServer: vi.fn().mockResolvedValue(mockViteServer),
  build: mockViteBuild,
  defineConfig: vi.fn((config) => config),
  loadConfigFromFile: vi.fn().mockResolvedValue({
    config: {},
    configFile: '/test/vite.config.js'
  })
}));

describe('ViteBuilder', () => {
  let builder: ViteBuilder;

  beforeEach(() => {
    builder = new ViteBuilder();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('canHandle', () => {
    it('should return true for valid Vite config', async () => {
      const config: ViteBuilderConfig = {
        id: 'test-build',
        name: 'Test Build',
        root: '/test/project',
        build: {
          outDir: 'dist'
        }
      };

      const result = await builder.canHandle(config);
      expect(result).toBe(true);
    });

    it('should return false for invalid config', async () => {
      const config = {} as ViteBuilderConfig;
      const result = await builder.canHandle(config);
      expect(result).toBe(false);
    });

    it('should return false when root directory does not exist', async () => {
      const config: ViteBuilderConfig = {
        id: 'test-build',
        name: 'Test Build',
        root: '/non-existent-directory'
      };

      const result = await builder.canHandle(config);
      expect(result).toBe(false);
    });
  });

  describe('build', () => {
    it('should build project successfully', async () => {
      const config: ViteBuilderConfig = {
        id: 'test-build',
        name: 'Test Build',
        root: '/test/project',
        build: {
          outDir: 'dist',
          lib: {
            entry: 'src/index.ts',
            name: 'TestLib'
          }
        }
      };

      const result = await builder.build(config);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.buildId).toBe('test-build');
      expect(mockViteBuild).toHaveBeenCalled();
    });

    it('should handle build errors', async () => {
      mockViteBuild.mockRejectedValueOnce(new Error('Build failed'));

      const config: ViteBuilderConfig = {
        id: 'test-build',
        name: 'Test Build',
        root: '/test/project'
      };

      const result = await builder.build(config);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe('Build failed');
    });

    it('should use custom build options', async () => {
      const config: ViteBuilderConfig = {
        id: 'test-build',
        name: 'Test Build',
        root: '/test/project',
        build: {
          outDir: 'custom-dist',
          sourcemap: true,
          minify: false,
          rollupOptions: {
            external: ['vue']
          }
        }
      };

      await builder.build(config);

      expect(mockViteBuild).toHaveBeenCalledWith(
        expect.objectContaining({
          build: expect.objectContaining({
            outDir: 'custom-dist',
            sourcemap: true,
            minify: false,
            rollupOptions: expect.objectContaining({
              external: ['vue']
            })
          })
        })
      );
    });
  });

  describe('serve', () => {
    it('should start dev server successfully', async () => {
      const config: ViteBuilderConfig = {
        id: 'test-serve',
        name: 'Test Serve',
        root: '/test/project',
        server: {
          port: 3000,
          host: 'localhost'
        }
      };

      const result = await builder.serve(config);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.serverId).toBe('test-serve');
      expect(mockViteServer.listen).toHaveBeenCalled();
    });

    it('should handle server start errors', async () => {
      mockViteServer.listen.mockRejectedValueOnce(new Error('Server start failed'));

      const config: ViteBuilderConfig = {
        id: 'test-serve',
        name: 'Test Serve',
        root: '/test/project'
      };

      const result = await builder.serve(config);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe('Server start failed');
    });

    it('should use custom server options', async () => {
      const config: ViteBuilderConfig = {
        id: 'test-serve',
        name: 'Test Serve',
        root: '/test/project',
        server: {
          port: 8080,
          host: '0.0.0.0',
          https: true,
          cors: true
        }
      };

      await builder.serve(config);

      expect(mockViteServer.listen).toHaveBeenCalled();
    });
  });

  describe('stop', () => {
    it('should stop dev server successfully', async () => {
      const config: ViteBuilderConfig = {
        id: 'test-serve',
        name: 'Test Serve',
        root: '/test/project'
      };

      // Start server first
      await builder.serve(config);
      
      // Then stop it
      await builder.stop('test-serve');

      expect(mockViteServer.close).toHaveBeenCalled();
    });

    it('should handle stopping non-existent server', async () => {
      await expect(builder.stop('non-existent')).resolves.not.toThrow();
    });
  });

  describe('watch', () => {
    it('should start watch mode successfully', async () => {
      const config: ViteBuilderConfig = {
        id: 'test-watch',
        name: 'Test Watch',
        root: '/test/project',
        build: {
          watch: {}
        }
      };

      const onChange = vi.fn();
      const result = await builder.watch(config, onChange);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.watcherId).toBe('test-watch');
    });

    it('should handle watch mode errors', async () => {
      mockViteBuild.mockRejectedValueOnce(new Error('Watch failed'));

      const config: ViteBuilderConfig = {
        id: 'test-watch',
        name: 'Test Watch',
        root: '/test/project',
        build: {
          watch: {}
        }
      };

      const onChange = vi.fn();
      const result = await builder.watch(config, onChange);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('getServerInstance', () => {
    it('should return server instance for existing server', async () => {
      const config: ViteBuilderConfig = {
        id: 'test-serve',
        name: 'Test Serve',
        root: '/test/project'
      };

      await builder.serve(config);
      const server = builder.getServerInstance('test-serve');

      expect(server).toBeDefined();
      expect(server?.id).toBe('test-serve');
    });

    it('should return undefined for non-existent server', () => {
      const server = builder.getServerInstance('non-existent');
      expect(server).toBeUndefined();
    });
  });

  describe('getBuildResult', () => {
    it('should return build result for existing build', async () => {
      const config: ViteBuilderConfig = {
        id: 'test-build',
        name: 'Test Build',
        root: '/test/project'
      };

      await builder.build(config);
      const result = builder.getBuildResult('test-build');

      expect(result).toBeDefined();
      expect(result?.buildId).toBe('test-build');
    });

    it('should return undefined for non-existent build', () => {
      const result = builder.getBuildResult('non-existent');
      expect(result).toBeUndefined();
    });
  });

  describe('getAllServerInstances', () => {
    it('should return all server instances', async () => {
      const config1: ViteBuilderConfig = {
        id: 'test-serve-1',
        name: 'Test Serve 1',
        root: '/test/project1'
      };

      const config2: ViteBuilderConfig = {
        id: 'test-serve-2',
        name: 'Test Serve 2',
        root: '/test/project2'
      };

      await builder.serve(config1);
      await builder.serve(config2);

      const servers = builder.getAllServerInstances();
      expect(servers).toHaveLength(2);
      expect(servers.map(s => s.id)).toContain('test-serve-1');
      expect(servers.map(s => s.id)).toContain('test-serve-2');
    });

    it('should return empty array when no servers running', () => {
      const servers = builder.getAllServerInstances();
      expect(servers).toHaveLength(0);
    });
  });

  describe('getAllBuildResults', () => {
    it('should return all build results', async () => {
      const config1: ViteBuilderConfig = {
        id: 'test-build-1',
        name: 'Test Build 1',
        root: '/test/project1'
      };

      const config2: ViteBuilderConfig = {
        id: 'test-build-2',
        name: 'Test Build 2',
        root: '/test/project2'
      };

      await builder.build(config1);
      await builder.build(config2);

      const results = builder.getAllBuildResults();
      expect(results).toHaveLength(2);
      expect(results.map(r => r.buildId)).toContain('test-build-1');
      expect(results.map(r => r.buildId)).toContain('test-build-2');
    });

    it('should return empty array when no builds completed', () => {
      const results = builder.getAllBuildResults();
      expect(results).toHaveLength(0);
    });
  });

  describe('configuration', () => {
    it('should merge user config with default config', async () => {
      const config: ViteBuilderConfig = {
        id: 'test-build',
        name: 'Test Build',
        root: '/test/project',
        plugins: [
          { name: 'custom-plugin', options: {} }
        ],
        define: {
          __VERSION__: '"1.0.0"'
        }
      };

      await builder.build(config);

      expect(mockViteBuild).toHaveBeenCalledWith(
        expect.objectContaining({
          plugins: expect.arrayContaining([
            expect.objectContaining({ name: 'custom-plugin' })
          ]),
          define: expect.objectContaining({
            __VERSION__: '"1.0.0"'
          })
        })
      );
    });

    it('should handle environment-specific config', async () => {
      const config: ViteBuilderConfig = {
        id: 'test-build',
        name: 'Test Build',
        root: '/test/project',
        mode: 'production',
        build: {
          minify: 'terser',
          sourcemap: false
        }
      };

      await builder.build(config);

      expect(mockViteBuild).toHaveBeenCalledWith(
        expect.objectContaining({
          mode: 'production',
          build: expect.objectContaining({
            minify: 'terser',
            sourcemap: false
          })
        })
      );
    });
  });

  describe('plugin system', () => {
    it('should apply micro-core plugin by default', async () => {
      const config: ViteBuilderConfig = {
        id: 'test-build',
        name: 'Test Build',
        root: '/test/project'
      };

      await builder.build(config);

      expect(mockViteBuild).toHaveBeenCalledWith(
        expect.objectContaining({
          plugins: expect.arrayContaining([
            expect.objectContaining({
              name: expect.stringContaining('micro-core')
            })
          ])
        })
      );
    });

    it('should handle custom plugins', async () => {
      const customPlugin = {
        name: 'custom-plugin',
        options: { custom: true }
      };

      const config: ViteBuilderConfig = {
        id: 'test-build',
        name: 'Test Build',
        root: '/test/project',
        plugins: [customPlugin]
      };

      await builder.build(config);

      expect(mockViteBuild).toHaveBeenCalledWith(
        expect.objectContaining({
          plugins: expect.arrayContaining([
            expect.objectContaining({ name: 'custom-plugin' })
          ])
        })
      );
    });
  });

  describe('error handling', () => {
    it('should handle Vite import errors', async () => {
      const { createServer } = await import('vite');
      vi.mocked(createServer).mockRejectedValueOnce(new Error('Vite not found'));

      const config: ViteBuilderConfig = {
        id: 'test-serve',
        name: 'Test Serve',
        root: '/test/project'
      };

      const result = await builder.serve(config);

      expect(result.success).toBe(false);
      expect(result.error?.message).toBe('Vite not found');
    });

    it('should handle invalid configuration', async () => {
      const config: ViteBuilderConfig = {
        id: 'test-build',
        name: 'Test Build',
        root: '/test/project',
        build: {
          // Invalid configuration that would cause Vite to fail
          rollupOptions: {
            input: '/non-existent-file.js'
          }
        }
      };

      mockViteBuild.mockRejectedValueOnce(new Error('Invalid input file'));

      const result = await builder.build(config);

      expect(result.success).toBe(false);
      expect(result.error?.message).toBe('Invalid input file');
    });
  });
});
