/**
 * Iframe 桥接器 - 处理 iframe 沙箱通信
 */

import { AppInstance, EventBusMessage } from './types';

export class IframeBridge {
    private iframes: Map<string, HTMLIFrameElement> = new Map();
    private appInstances: Map<string, AppInstance> = new Map();
    private messageHandlers: Map<string, (message: EventBusMessage) => void> = new Map();

    /**
     * 初始化 iframe 桥接器
     */
    async initialize(): Promise<void> {
        // 监听来自 iframe 的消息
        window.addEventListener('message', this.handleMessage.bind(this));
    }

    /**
     * 创建 iframe 容器
     */
    createIframe(appName: string, url: string, container: HTMLElement): HTMLIFrameElement {
        const iframe = document.createElement('iframe');

        // 设置 iframe 属性
        iframe.src = url;
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.border = 'none';
        iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-forms allow-popups');

        // 添加到容器
        container.appendChild(iframe);

        // 保存 iframe 引用
        this.iframes.set(appName, iframe);

        return iframe;
    }

    /**
     * 向指定应用发送消息
     */
    sendMessage(appName: string, message: EventBusMessage): void {
        const iframe = this.iframes.get(appName);
        if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage(message, '*');
        }
    }

    /**
     * 广播消息到所有应用
     */
    broadcast(message: EventBusMessage): void {
        this.iframes.forEach((iframe, appName) => {
            if (iframe.contentWindow && appName !== message.from) {
                iframe.contentWindow.postMessage(message, '*');
            }
        });
    }

    /**
     * 注册消息处理器
     */
    onMessage(appName: string, handler: (message: EventBusMessage) => void): void {
        this.messageHandlers.set(appName, handler);
    }

    /**
     * 处理来自 iframe 的消息
     */
    private handleMessage(event: MessageEvent): void {
        if (!this.isValidMessage(event.data)) {
            return;
        }

        const message: EventBusMessage = event.data;

        // 如果有指定接收方，直接发送
        if (message.to) {
            this.sendMessage(message.to, message);
        } else {
            // 否则广播给所有应用
            this.broadcast(message);
        }

        // 调用注册的处理器
        const handler = this.messageHandlers.get(message.from);
        if (handler) {
            handler(message);
        }
    }

    /**
     * 验证消息格式
     */
    private isValidMessage(data: any): data is EventBusMessage {
        return (
            data &&
            typeof data === 'object' &&
            typeof data.type === 'string' &&
            typeof data.from === 'string' &&
            typeof data.timestamp === 'number'
        );
    }

    /**
     * 销毁指定应用的 iframe
     */
    destroyIframe(appName: string): void {
        const iframe = this.iframes.get(appName);
        if (iframe) {
            iframe.remove();
            this.iframes.delete(appName);
        }

        this.appInstances.delete(appName);
        this.messageHandlers.delete(appName);
    }

    /**
     * 销毁所有资源
     */
    destroy(): void {
        // 移除事件监听器
        window.removeEventListener('message', this.handleMessage.bind(this));

        // 销毁所有 iframe
        this.iframes.forEach((iframe, appName) => {
            this.destroyIframe(appName);
        });

        // 清空所有映射
        this.iframes.clear();
        this.appInstances.clear();
        this.messageHandlers.clear();
    }

    /**
     * 获取应用实例
     */
    getAppInstance(appName: string): AppInstance | undefined {
        return this.appInstances.get(appName);
    }

    /**
     * 设置应用实例
     */
    setAppInstance(appName: string, instance: AppInstance): void {
        this.appInstances.set(appName, instance);
    }
}