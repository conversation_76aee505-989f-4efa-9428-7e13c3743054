/**
 * @fileoverview Solid.js 微前端子应用入口 - 标准生命周期实现
 * <AUTHOR> <<EMAIL>>
 */

import { render } from 'solid-js/web'
import App from './App'
import { ErrorBoundary, globalErrorHandler } from './utils/error-boundary'
import { performanceMonitor } from './utils/performance'

// 微前端应用属性接口
interface MicroAppProps {
    container?: HTMLElement | string
    basename?: string
    theme?: 'light' | 'dark'
    user?: any
    [key: string]: any
}

// 应用状态管理
let dispose: (() => void) | null = null
let isBootstrapped = false
let isMounted = false
let eventListeners: Array<{ element: any, event: string, handler: any }> = []
let timers: number[] = []

/**
 * 微前端应用启动函数
 * 在应用首次加载时调用，用于初始化应用资源
 */
export async function bootstrap(props: MicroAppProps = {}): Promise<void> {
    try {
        performanceMonitor.startTiming('bootstrap')
        console.log('🚀 Solid.js 子应用开始启动', props)

        // 防止重复启动
        if (isBootstrapped) {
            console.warn('⚠️ Solid.js 子应用已经启动，跳过重复启动')
            return
        }

        // 初始化应用资源
        await initializeResources(props)

        // 设置全局错误处理
        setupErrorHandling()

        // 标记为已启动
        isBootstrapped = true

        const duration = performanceMonitor.endTiming('bootstrap')
        console.log(`✅ Solid.js 子应用启动完成 (${duration.toFixed(2)}ms)`)
    } catch (error) {
        console.error('❌ Solid.js 子应用启动失败:', error)
        globalErrorHandler.captureError(error as Error, 'bootstrap')
        throw error
    }
}

/**
 * 微前端应用挂载函数
 * 将应用挂载到指定容器
 */
export async function mount(props: MicroAppProps = {}): Promise<void> {
    try {
        performanceMonitor.startTiming('mount')
        console.log('🔄 Solid.js 子应用开始挂载', props)

        // 确保应用已启动
        if (!isBootstrapped) {
            await bootstrap(props)
        }

        // 防止重复挂载
        if (isMounted) {
            console.warn('⚠️ Solid.js 子应用已经挂载，先卸载再重新挂载')
            await unmount(props)
        }

        // 获取容器元素
        const container = getContainer(props.container)
        if (!container) {
            throw new Error('找不到挂载容器')
        }

        // 清空容器
        container.innerHTML = ''

        // 创建 Solid.js 应用实例，包含错误边界
        try {
            dispose = render(() => (
                <ErrorBoundary
                    fallback={(error, reset) => (
                        <div style={{
                            padding: '20px',
                            'text-align': 'center',
                            color: '#666'
                        }}>
                            <h2>Solid.js 应用出现错误</h2>
                            <p>{error.message}</p>
                            <button
                                onClick={reset}
                                style={{
                                    padding: '8px 16px',
                                    background: '#007bff',
                                    color: 'white',
                                    border: 'none',
                                    'border-radius': '4px',
                                    cursor: 'pointer',
                                    'margin-right': '10px'
                                }}
                            >
                                重试
                            </button>
                            <button
                                onClick={() => window.location.reload()}
                                style={{
                                    padding: '8px 16px',
                                    background: '#6c757d',
                                    color: 'white',
                                    border: 'none',
                                    'border-radius': '4px',
                                    cursor: 'pointer'
                                }}
                            >
                                重新加载
                            </button>
                        </div>
                    )}
                    onError={(error) => {
                        console.error('Solid.js 应用错误:', error)
                        globalErrorHandler.captureError(new Error(error.message), 'app-render')
                    }}
                >
                    <App
                        microAppProps={props}
                        appInfo={{
                            name: 'sub-app-solid',
                            framework: 'Solid.js',
                            version: '1.8.0',
                            basename: props.basename || '/solid',
                            isMicroFrontend: !!(window as any).__POWERED_BY_MICRO_CORE__
                        }}
                    />
                </ErrorBoundary>
            ), container)
        } catch (error) {
            console.error('Solid.js 应用创建失败，使用降级方案:', error)
            globalErrorHandler.captureError(error as Error, 'app-creation')

            // 降级方案：显示简单的错误页面
            container.innerHTML = `
                <div style="padding: 20px; text-align: center; color: #666;">
                    <h2>Solid.js 应用暂时不可用</h2>
                    <p>应用正在恢复中，请稍后再试</p>
                    <button onclick="window.location.reload()" style="
                        padding: 8px 16px;
                        background: #007bff;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                    ">重新加载</button>
                </div>
            `
            throw error
        }

        // 标记为已挂载
        isMounted = true

        // 触发挂载完成事件
        dispatchLifecycleEvent('mounted', props)

        const duration = performanceMonitor.endTiming('mount')
        performanceMonitor.measureMemoryUsage()
        console.log(`✅ Solid.js 子应用挂载完成 (${duration.toFixed(2)}ms)`)
    } catch (error) {
        console.error('❌ Solid.js 子应用挂载失败:', error)
        globalErrorHandler.captureError(error as Error, 'mount')
        throw error
    }
}

/**
 * 微前端应用卸载函数
 * 清理应用资源和DOM
 */
export async function unmount(props: MicroAppProps = {}): Promise<void> {
    try {
        console.log('🔄 Solid.js 子应用开始卸载', props)

        if (!isMounted) {
            console.warn('⚠️ Solid.js 子应用未挂载，跳过卸载')
            return
        }

        // 销毁 Solid.js 应用
        if (dispose) {
            dispose()
            dispose = null
        }

        // 清理 DOM
        const container = getContainer(props.container)
        if (container) {
            container.innerHTML = ''
        }

        // 清理事件监听器
        cleanupEventListeners()

        // 清理定时器
        cleanupTimers()

        // 标记为未挂载
        isMounted = false

        // 触发卸载完成事件
        dispatchLifecycleEvent('unmounted', props)

        console.log('✅ Solid.js 子应用卸载完成')
    } catch (error) {
        console.error('❌ Solid.js 子应用卸载失败:', error)
        throw error
    }
}

/**
 * 微前端应用更新函数
 * 更新应用属性
 */
export async function update(props: MicroAppProps = {}): Promise<void> {
    try {
        performanceMonitor.startTiming('update')
        console.log('🔄 Solid.js 子应用开始更新', props)

        if (!isMounted) {
            console.warn('⚠️ Solid.js 子应用未挂载，无法更新')
            return
        }

        // 重新挂载应用以应用新属性
        await unmount(props)
        await mount(props)

        const duration = performanceMonitor.endTiming('update')
        console.log(`✅ Solid.js 子应用更新完成 (${duration.toFixed(2)}ms)`)
    } catch (error) {
        console.error('❌ Solid.js 子应用更新失败:', error)
        globalErrorHandler.captureError(error as Error, 'update')
        throw error
    }
}

/**
 * 获取挂载容器
 */
function getContainer(container?: HTMLElement | string): HTMLElement | null {
    if (!container) {
        return document.getElementById('solid-app') || document.getElementById('root') || document.getElementById('app')
    }

    if (typeof container === 'string') {
        return document.querySelector(container)
    }

    return container
}

/**
 * 初始化应用资源
 */
async function initializeResources(props: MicroAppProps): Promise<void> {
    // 初始化主题
    if (props.theme) {
        document.documentElement.setAttribute('data-theme', props.theme)
    }

    // 初始化用户上下文
    if (props.user) {
        // 设置用户相关的全局状态
    }
}

/**
 * 设置全局错误处理
 */
function setupErrorHandling(): void {
    const unhandledRejectionHandler = (event: PromiseRejectionEvent) => {
        console.error('Solid.js 子应用未捕获的 Promise 拒绝:', event.reason)
    }

    const globalErrorHandlerFn = (event: ErrorEvent) => {
        console.error('Solid.js 子应用全局错误:', event.error)
    }

    window.addEventListener('unhandledrejection', unhandledRejectionHandler)
    window.addEventListener('error', globalErrorHandlerFn)

    // 记录事件监听器以便清理
    eventListeners.push(
        { element: window, event: 'unhandledrejection', handler: unhandledRejectionHandler },
        { element: window, event: 'error', handler: globalErrorHandlerFn }
    )
}

/**
 * 清理事件监听器
 */
function cleanupEventListeners(): void {
    eventListeners.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler)
    })
    eventListeners = []
}

/**
 * 清理定时器
 */
function cleanupTimers(): void {
    timers.forEach(timer => clearTimeout(timer))
    timers = []
}

/**
 * 添加定时器到清理列表
 */
export function addTimer(timer: number): void {
    timers.push(timer)
}

/**
 * 触发生命周期事件
 */
function dispatchLifecycleEvent(type: string, props: MicroAppProps): void {
    const event = new CustomEvent(`solid-app:${type}`, {
        detail: { props, timestamp: Date.now() }
    })
    window.dispatchEvent(event)
}

/**
 * 获取应用状态
 */
export function getAppStatus() {
    return {
        name: 'sub-app-solid',
        isBootstrapped,
        isMounted,
        framework: 'Solid.js',
        version: '1.8.0'
    }
}

// 独立运行模式
if (!(window as any).__POWERED_BY_MICRO_CORE__) {
    console.log('🏃 Solid.js 应用以独立模式运行')

    // 等待 DOM 加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            mount({
                container: document.getElementById('root') || document.getElementById('app') || document.body,
                basename: '/',
                theme: 'light'
            }).catch(error => {
                console.error('Solid.js 应用独立模式启动失败:', error)
                globalErrorHandler.captureError(error, 'standalone-mode')
            })
        })
    } else {
        mount({
            container: document.getElementById('root') || document.getElementById('app') || document.body,
            basename: '/',
            theme: 'light'
        }).catch(error => {
            console.error('Solid.js 应用独立模式启动失败:', error)
            globalErrorHandler.captureError(error, 'standalone-mode')
        })
    }
}

// 暴露给微前端框架的接口
declare global {
    interface Window {
        __SOLID_MICRO_APP__?: {
            bootstrap: typeof bootstrap
            mount: typeof mount
            unmount: typeof unmount
            update: typeof update
            getAppStatus: typeof getAppStatus
        }
    }
}

if (typeof window !== 'undefined') {
    (window as any).__SOLID_MICRO_APP__ = {
        bootstrap,
        mount,
        unmount,
        update,
        getAppStatus
    }
}

// 导出生命周期函数供微前端框架使用
export default {
    bootstrap,
    mount,
    unmount,
    update,
    getAppStatus
}