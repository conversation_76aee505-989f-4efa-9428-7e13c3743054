import { createPinia } from 'pinia'
import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import { initMicroCore } from './micro-config'
import routes from './router'

// Error handler for global errors
const globalErrorHandler = (error: Error, context: string) => {
  console.error(`[Global Error] ${context}:`, error)

  // Send error to monitoring service in production
  if (import.meta.env.PROD) {
    // Example: sendToMonitoring(error, context)
  }
}

// Initialize micro-frontend with enhanced configuration
async function initializeApp() {
  try {
    // 创建 Vue 应用
    const app = createApp(App)

    // 创建路由实例
    const router = createRouter({
      history: createWebHistory(),
      routes
    })

    // 创建 Pinia 实例
    const pinia = createPinia()

    // 使用插件
    app.use(router)
    app.use(pinia)

    // Set up Vue error handler
    app.config.errorHandler = (err, instance, info) => {
      console.error('[Vue Error]', err, info)
      globalErrorHandler(err as Error, `Vue: ${info}`)
    }

    // 初始化微前端 with enhanced configuration
    await initMicroCore({
      debug: import.meta.env.DEV,
      logLevel: import.meta.env.DEV ? 'debug' : 'warn',
      errorHandler: globalErrorHandler,
      performanceMonitoring: true,
      maxRetries: 3,
      loadTimeout: 15000
    })

    // 挂载应用
    app.mount('#app')

    console.log('[Main App] Application initialized successfully')

  } catch (error) {
    console.error('[Main App] Failed to initialize application:', error)

    // Show error page or fallback UI
    document.body.innerHTML = `
      <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        color: #721c24;
        background-color: #f8d7da;
        padding: 2rem;
        text-align: center;
      ">
        <h1>应用初始化失败</h1>
        <p>抱歉，主应用无法正常启动。请刷新页面重试。</p>
        <button onclick="window.location.reload()" style="
          background: #dc3545;
          color: white;
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 4px;
          cursor: pointer;
          font-size: 1rem;
          margin-top: 1rem;
        ">刷新页面</button>
        <details style="margin-top: 2rem; text-align: left;">
          <summary>错误详情</summary>
          <pre style="
            background: #f5f5f5;
            padding: 1rem;
            border-radius: 4px;
            overflow: auto;
            margin-top: 1rem;
            font-size: 0.875rem;
          ">${error}</pre>
        </details>
      </div>
    `
  }
}

// Initialize the application
initializeApp()