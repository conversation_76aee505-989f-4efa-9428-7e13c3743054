/**
 * @fileoverview 核心工具集
 * @description 提供微内核工具集合
 * <AUTHOR> <<EMAIL>>
 */
import { CacheManager } from './cache';
import { EventEmitter } from './event';
import { Logger } from './logger';
import { PerformanceMonitor } from './performance';
export interface MicroCoreUtils {
    logger: Logger;
    eventEmitter: EventEmitter;
    cache: CacheManager;
    performance: PerformanceMonitor;
}
/**
 * 创建微内核工具集
 */
export declare function createMicroCoreUtils(namespace?: string): MicroCoreUtils;
//# sourceMappingURL=core-utils.d.ts.map