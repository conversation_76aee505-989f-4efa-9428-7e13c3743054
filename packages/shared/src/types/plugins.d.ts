/**
 * 插件系统类型定义
 *
 * @description 定义微前端插件系统的所有类型接口
 * <AUTHOR> <<EMAIL>>
 */
export type PluginType = 'router' | 'communication' | 'auth' | 'devtools' | 'logger' | 'metrics' | 'sandbox' | 'custom';
export type PluginStatus = 'uninstalled' | 'installed' | 'enabled' | 'disabled' | 'error';
export interface PluginConfig {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 插件类型 */
    type: PluginType;
    /** 插件描述 */
    description?: string;
    /** 插件作者 */
    author?: string;
    /** 插件主页 */
    homepage?: string;
    /** 插件依赖 */
    dependencies?: string[];
    /** 插件选项 */
    options?: Record<string, any>;
    /** 是否启用 */
    enabled?: boolean;
    /** 插件优先级 */
    priority?: number;
}
export interface PluginContext {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 插件配置 */
    config: PluginConfig;
    /** 插件状态 */
    status: PluginStatus;
    /** 插件实例 */
    instance: any;
    /** 安装时间 */
    installedAt: number;
    /** 启用时间 */
    enabledAt?: number;
    /** 最后活动时间 */
    lastActivity: number;
    /** 错误信息 */
    error?: Error;
}
export interface Plugin {
    /** 插件名称 */
    readonly name: string;
    /** 插件版本 */
    readonly version: string;
    /** 插件类型 */
    readonly type: PluginType;
    /** 安装插件 */
    install(context: PluginInstallContext): Promise<void>;
    /** 卸载插件 */
    uninstall(): Promise<void>;
    /** 启用插件 */
    enable(): Promise<void>;
    /** 禁用插件 */
    disable(): Promise<void>;
    /** 获取插件状态 */
    getStatus(): PluginStatus;
    /** 获取插件配置 */
    getConfig(): PluginConfig;
    /** 获取插件统计信息 */
    getStats(): PluginStats;
}
export interface PluginInstallContext {
    /** 微内核实例 */
    kernel: any;
    /** 插件管理器 */
    pluginManager: any;
    /** 事件总线 */
    eventBus: any;
    /** 日志记录器 */
    logger: any;
    /** 插件配置 */
    config: PluginConfig;
}
export interface PluginStats {
    /** 安装时间 */
    installedAt: number;
    /** 启用时间 */
    enabledAt?: number;
    /** 调用次数 */
    callCount: number;
    /** 错误次数 */
    errorCount: number;
    /** 平均执行时间 */
    averageExecutionTime: number;
    /** 内存使用量 */
    memoryUsage: number;
    /** 最后活动时间 */
    lastActivity: number;
}
export interface PluginEvent {
    /** 事件类型 */
    type: 'installed' | 'uninstalled' | 'enabled' | 'disabled' | 'error';
    /** 插件名称 */
    pluginName: string;
    /** 事件数据 */
    data?: any;
    /** 时间戳 */
    timestamp: number;
}
export interface PluginError extends Error {
    /** 错误代码 */
    code: string;
    /** 错误类型 */
    type: 'INSTALL_FAILED' | 'UNINSTALL_FAILED' | 'ENABLE_FAILED' | 'DISABLE_FAILED' | 'EXECUTION_FAILED';
    /** 插件名称 */
    pluginName: string;
    /** 错误详情 */
    details?: Record<string, any>;
}
export interface PluginManagerConfig {
    /** 最大插件数量 */
    maxPlugins: number;
    /** 插件目录 */
    pluginDir?: string;
    /** 是否启用热重载 */
    hotReload?: boolean;
    /** 是否启用性能监控 */
    performanceMonitoring?: boolean;
    /** 插件加载超时时间 */
    loadTimeout?: number;
    /** 默认插件配置 */
    defaultConfig?: Partial<PluginConfig>;
}
export interface PluginManager {
    /** 安装插件 */
    install(plugin: Plugin, config?: PluginConfig): Promise<void>;
    /** 卸载插件 */
    uninstall(name: string): Promise<void>;
    /** 启用插件 */
    enable(name: string): Promise<void>;
    /** 禁用插件 */
    disable(name: string): Promise<void>;
    /** 获取插件 */
    getPlugin(name: string): Plugin | null;
    /** 获取所有插件 */
    getAllPlugins(): Plugin[];
    /** 获取已启用的插件 */
    getEnabledPlugins(): Plugin[];
    /** 检查插件是否存在 */
    hasPlugin(name: string): boolean;
    /** 检查插件是否启用 */
    isPluginEnabled(name: string): boolean;
    /** 获取插件统计信息 */
    getPluginStats(name: string): PluginStats | null;
    /** 获取管理器统计信息 */
    getManagerStats(): PluginManagerStats;
}
export interface PluginManagerStats {
    /** 总插件数量 */
    totalPlugins: number;
    /** 已启用插件数量 */
    enabledPlugins: number;
    /** 已禁用插件数量 */
    disabledPlugins: number;
    /** 错误插件数量 */
    errorPlugins: number;
    /** 平均加载时间 */
    averageLoadTime: number;
    /** 总内存使用量 */
    totalMemoryUsage: number;
    /** 最后更新时间 */
    lastUpdated: number;
}
export interface PluginHooks {
    /** 插件安装前 */
    beforeInstall?: (plugin: Plugin, config: PluginConfig) => Promise<void> | void;
    /** 插件安装后 */
    afterInstall?: (plugin: Plugin, context: PluginContext) => Promise<void> | void;
    /** 插件卸载前 */
    beforeUninstall?: (plugin: Plugin) => Promise<void> | void;
    /** 插件卸载后 */
    afterUninstall?: (pluginName: string) => Promise<void> | void;
    /** 插件启用前 */
    beforeEnable?: (plugin: Plugin) => Promise<void> | void;
    /** 插件启用后 */
    afterEnable?: (plugin: Plugin) => Promise<void> | void;
    /** 插件禁用前 */
    beforeDisable?: (plugin: Plugin) => Promise<void> | void;
    /** 插件禁用后 */
    afterDisable?: (plugin: Plugin) => Promise<void> | void;
}
export interface ExtensionPoint {
    /** 扩展点名称 */
    name: string;
    /** 扩展点描述 */
    description?: string;
    /** 扩展点类型 */
    type: 'sync' | 'async' | 'waterfall' | 'parallel';
    /** 扩展点参数 */
    params?: Record<string, any>;
    /** 扩展点返回值 */
    returns?: any;
}
export interface Extension {
    /** 扩展名称 */
    name: string;
    /** 扩展点名称 */
    extensionPoint: string;
    /** 扩展处理器 */
    handler: (...args: any[]) => any;
    /** 扩展优先级 */
    priority?: number;
    /** 扩展条件 */
    condition?: (...args: any[]) => boolean;
}
export interface PluginRegistry {
    /** 注册插件 */
    register(plugin: Plugin): void;
    /** 注销插件 */
    unregister(name: string): void;
    /** 获取插件 */
    get(name: string): Plugin | null;
    /** 获取所有插件 */
    getAll(): Plugin[];
    /** 检查插件是否存在 */
    has(name: string): boolean;
    /** 清空注册表 */
    clear(): void;
}
export interface PluginLoader {
    /** 加载插件 */
    load(path: string): Promise<Plugin>;
    /** 卸载插件 */
    unload(plugin: Plugin): Promise<void>;
    /** 重新加载插件 */
    reload(plugin: Plugin): Promise<Plugin>;
    /** 检查插件是否有效 */
    validate(plugin: Plugin): boolean;
}
//# sourceMappingURL=plugins.d.ts.map