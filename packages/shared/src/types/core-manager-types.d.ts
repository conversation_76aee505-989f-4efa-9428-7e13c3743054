/**
 * Core 包管理器类型定义
 *
 * @description 微内核架构的管理器类型定义，从core包迁移而来
 * <AUTHOR> <<EMAIL>>
 */
import type { ApplicationInstance, ApplicationStatus, PluginInterface } from './core-types';
/**
 * 微内核状态
 */
export interface MicroCoreStatus {
    /** 是否已初始化 */
    initialized: boolean;
    /** 是否已启动 */
    started: boolean;
    /** 启动时间 */
    startTime?: number;
    /** 运行时间 */
    uptime?: number;
    /** 应用数量 */
    applications: number;
    /** 插件数量 */
    plugins: number;
    /** 管理器数量 */
    managers: number;
}
/**
 * 事件总线接口
 */
export interface EventBus {
    /** 发送事件 */
    emit(event: string, data?: any): void;
    /** 监听事件 */
    on(event: string, handler: (data?: any) => void): void;
    /** 取消监听 */
    off(event: string, handler?: (data?: any) => void): void;
    /** 监听一次 */
    once(event: string, handler: (data?: any) => void): void;
    /** 清空所有监听器 */
    clear(): void;
}
/**
 * 插件管理器接口
 */
export interface PluginManager {
    /** 注册插件 */
    register(plugin: PluginInterface): Promise<void>;
    /** 获取插件 */
    get(name: string): PluginInterface | undefined;
    /** 获取所有插件 */
    getAll(): PluginInterface[];
    /** 启用插件 */
    enable(name: string): Promise<void>;
    /** 禁用插件 */
    disable(name: string): Promise<void>;
    /** 卸载插件 */
    uninstall(name: string): Promise<void>;
    /** 检查插件是否存在 */
    has(name: string): boolean;
}
/**
 * 应用管理器接口
 */
export interface ApplicationManager {
    /** 注册应用 */
    register(config: import('./core-types').ApplicationConfig): Promise<void>;
    /** 获取应用 */
    get(name: string): ApplicationInstance | undefined;
    /** 获取所有应用 */
    getAll(): ApplicationInstance[];
    /** 加载应用 */
    load(name: string): Promise<void>;
    /** 挂载应用 */
    mount(name: string, container?: HTMLElement): Promise<void>;
    /** 卸载应用 */
    unmount(name: string): Promise<void>;
    /** 注销应用 */
    unregister(name: string): Promise<void>;
    /** 检查应用是否存在 */
    has(name: string): boolean;
    /** 获取应用状态 */
    getStatus(name: string): ApplicationStatus | undefined;
}
//# sourceMappingURL=core-manager-types.d.ts.map