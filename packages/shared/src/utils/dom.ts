/**
 * @fileoverview DOM操作工具函数
 * @description 提供各种DOM操作和查询功能
 * <AUTHOR> <<EMAIL>>
 */

import { isBrowser } from './env';

/**
 * 查询DOM元素
 */
export function querySelector(selector: string, context: Document | Element = document): Element | null {
    if (!isBrowser()) return null;
    return context.querySelector(selector);
}

/**
 * 查询所有匹配的DOM元素
 */
export function querySelectorAll(selector: string, context: Document | Element = document): NodeListOf<Element> | [] {
    if (!isBrowser()) return [];
    return context.querySelectorAll(selector);
}

/**
 * 创建DOM元素
 */
export function createElement(tagName: string, attributes?: Record<string, string>, textContent?: string): HTMLElement | null {
    if (!isBrowser()) return null;

    const element = document.createElement(tagName);

    if (attributes) {
        Object.entries(attributes).forEach(([key, value]) => {
            element.setAttribute(key, value);
        });
    }

    if (textContent) {
        element.textContent = textContent;
    }

    return element;
}

/**
 * 添加CSS类
 */
export function addClass(element: Element, className: string): void {
    if (!element || !className) return;
    element.classList.add(className);
}

/**
 * 移除CSS类
 */
export function removeClass(element: Element, className: string): void {
    if (!element || !className) return;
    element.classList.remove(className);
}

/**
 * 切换CSS类
 */
export function toggleClass(element: Element, className: string): boolean {
    if (!element || !className) return false;
    return element.classList.toggle(className);
}

/**
 * 检查是否包含CSS类
 */
export function hasClass(element: Element, className: string): boolean {
    if (!element || !className) return false;
    return element.classList.contains(className);
}

/**
 * 设置元素样式
 */
export function setStyle(element: HTMLElement, styles: Record<string, string>): void {
    if (!element || !styles) return;

    Object.entries(styles).forEach(([property, value]) => {
        element.style.setProperty(property, value);
    });
}

/**
 * 获取元素样式
 */
export function getStyle(element: Element, property: string): string {
    if (!element || !isBrowser()) return '';
    return window.getComputedStyle(element).getPropertyValue(property);
}

/**
 * 添加事件监听器
 */
export function addEventListener(
    element: Element | Window | Document,
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions
): void {
    if (!element) return;
    element.addEventListener(event, handler, options);
}

/**
 * 移除事件监听器
 */
export function removeEventListener(
    element: Element | Window | Document,
    event: string,
    handler: EventListener,
    options?: boolean | EventListenerOptions
): void {
    if (!element) return;
    element.removeEventListener(event, handler, options);
}

/**
 * 获取元素位置信息
 */
export function getBoundingRect(element: Element): DOMRect | null {
    if (!element) return null;
    return element.getBoundingClientRect();
}

/**
 * 检查元素是否在视口中
 */
export function isInViewport(element: Element): boolean {
    if (!element || !isBrowser()) return false;

    const rect = getBoundingRect(element);
    if (!rect) return false;

    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= window.innerHeight &&
        rect.right <= window.innerWidth
    );
}

/**
 * 滚动到元素
 */
export function scrollToElement(element: Element, options?: ScrollIntoViewOptions): void {
    if (!element) return;
    element.scrollIntoView(options);
}