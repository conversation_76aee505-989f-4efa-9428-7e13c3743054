/**
 * @fileoverview 适配器工厂测试
 * @description 测试适配器工厂的功能
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi } from 'vitest'
import { AdapterFactory } from '../adapter-factory'
import { FrameworkType } from '../types'

describe('AdapterFactory', () => {
    let adapterFactory: AdapterFactory

    beforeEach(() => {
        adapterFactory = new AdapterFactory()
    })

    describe('适配器创建', () => {
        it('应该能够创建React适配器', () => {
            const adapter = adapterFactory.createAdapter('test-react', FrameworkType.REACT)
            expect(adapter).toBeDefined()
            expect(adapter.getName()).toBe('test-react')
            expect(adapter.getFrameworkType()).toBe('react')
        })

        it('应该能够创建Vue2适配器', () => {
            const adapter = adapterFactory.createAdapter('test-vue2', FrameworkType.VUE2)
            expect(adapter).toBeDefined()
            expect(adapter.getName()).toBe('test-vue2')
            expect(adapter.getFrameworkType()).toBe('vue2')
        })

        it('应该能够创建Vue3适配器', () => {
            const adapter = adapterFactory.createAdapter('test-vue3', FrameworkType.VUE3)
            expect(adapter).toBeDefined()
            expect(adapter.getName()).toBe('test-vue3')
            expect(adapter.getFrameworkType()).toBe('vue3')
        })

        it('应该能够创建Angular适配器', () => {
            const adapter = adapterFactory.createAdapter('test-angular', FrameworkType.ANGULAR)
            expect(adapter).toBeDefined()
            expect(adapter.getName()).toBe('test-angular')
            expect(adapter.getFrameworkType()).toBe('angular')
        })

        it('应该能够创建Svelte适配器', () => {
            const adapter = adapterFactory.createAdapter('test-svelte', FrameworkType.SVELTE)
            expect(adapter).toBeDefined()
            expect(adapter.getName()).toBe('test-svelte')
            expect(adapter.getFrameworkType()).toBe('svelte')
        })

        it('应该能够创建Solid适配器', () => {
            const adapter = adapterFactory.createAdapter('test-solid', FrameworkType.SOLID)
            expect(adapter).toBeDefined()
            expect(adapter.getName()).toBe('test-solid')
            expect(adapter.getFrameworkType()).toBe('solid')
        })

        it('应该能够创建Lit适配器', () => {
            const adapter = adapterFactory.createAdapter('test-lit', FrameworkType.LIT)
            expect(adapter).toBeDefined()
            expect(adapter.getName()).toBe('test-lit')
            expect(adapter.getFrameworkType()).toBe('lit')
        })

        it('应该能够创建HTML适配器', () => {
            const adapter = adapterFactory.createAdapter('test-html', FrameworkType.HTML)
            expect(adapter).toBeDefined()
            expect(adapter.getName()).toBe('test-html')
            expect(adapter.getFrameworkType()).toBe('html')
        })

        it('应该能够创建原生JS适配器', () => {
            const adapter = adapterFactory.createAdapter('test-vanilla', FrameworkType.VANILLA)
            expect(adapter).toBeDefined()
            expect(adapter.getName()).toBe('test-vanilla')
            expect(adapter.getFrameworkType()).toBe('vanilla')
        })
    })

    describe('适配器管理', () => {
        it('应该能够获取适配器', () => {
            const adapter = adapterFactory.createAdapter('test', FrameworkType.REACT)
            const retrieved = adapterFactory.getAdapter('test')
            expect(retrieved).toBe(adapter)
        })

        it('应该能够检查适配器是否存在', () => {
            adapterFactory.createAdapter('test', FrameworkType.REACT)
            expect(adapterFactory.hasAdapter('test')).toBe(true)
            expect(adapterFactory.hasAdapter('nonexistent')).toBe(false)
        })

        it('应该能够销毁适配器', () => {
            const adapter = adapterFactory.createAdapter('test', FrameworkType.REACT)
            const destroySpy = vi.spyOn(adapter, 'destroy')

            adapterFactory.destroyAdapter('test')

            expect(destroySpy).toHaveBeenCalled()
            expect(adapterFactory.hasAdapter('test')).toBe(false)
        })

        it('应该能够获取所有适配器', () => {
            adapterFactory.createAdapter('test1', FrameworkType.REACT)
            adapterFactory.createAdapter('test2', FrameworkType.VUE3)

            const adapters = adapterFactory.getAllAdapters()
            expect(adapters).toHaveLength(2)
        })

        it('应该能够清理所有适配器', () => {
            adapterFactory.createAdapter('test1', FrameworkType.REACT)
            adapterFactory.createAdapter('test2', FrameworkType.VUE3)

            adapterFactory.clear()

            expect(adapterFactory.getAllAdapters()).toHaveLength(0)
        })
    })

    describe('框架检测', () => {
        it('应该能够自动检测React框架', () => {
            // 模拟React环境
            global.React = { version: '18.0.0' }

            const frameworkType = adapterFactory.detectFramework()
            expect(frameworkType).toBe(FrameworkType.REACT)

            delete global.React
        })

        it('应该能够自动检测Vue3框架', () => {
            // 模拟Vue3环境
            global.Vue = { version: '3.0.0' }

            const frameworkType = adapterFactory.detectFramework()
            expect(frameworkType).toBe(FrameworkType.VUE3)

            delete global.Vue
        })

        it('应该在无法检测时返回HTML框架', () => {
            const frameworkType = adapterFactory.detectFramework()
            expect(frameworkType).toBe(FrameworkType.HTML)
        })
    })

    describe('适配器生命周期', () => {
        it('应该能够初始化适配器', async () => {
            const adapter = adapterFactory.createAdapter('test', FrameworkType.REACT)
            const initSpy = vi.spyOn(adapter, 'initialize')

            await adapter.initialize()

            expect(initSpy).toHaveBeenCalled()
        })

        it('应该能够挂载适配器', async () => {
            const adapter = adapterFactory.createAdapter('test', FrameworkType.REACT)
            const mountSpy = vi.spyOn(adapter, 'mount')

            await adapter.mount(document.createElement('div'))

            expect(mountSpy).toHaveBeenCalled()
        })

        it('应该能够卸载适配器', async () => {
            const adapter = adapterFactory.createAdapter('test', FrameworkType.REACT)
            const unmountSpy = vi.spyOn(adapter, 'unmount')

            await adapter.unmount()

            expect(unmountSpy).toHaveBeenCalled()
        })

        it('应该能够销毁适配器', () => {
            const adapter = adapterFactory.createAdapter('test', FrameworkType.REACT)
            const destroySpy = vi.spyOn(adapter, 'destroy')

            adapter.destroy()

            expect(destroySpy).toHaveBeenCalled()
        })
    })

    describe('错误处理', () => {
        it('应该在创建重复适配器时抛出错误', () => {
            adapterFactory.createAdapter('test', FrameworkType.REACT)

            expect(() => {
                adapterFactory.createAdapter('test', FrameworkType.REACT)
            }).toThrow('适配器 test 已存在')
        })

        it('应该在获取不存在的适配器时返回undefined', () => {
            expect(adapterFactory.getAdapter('nonexistent')).toBeUndefined()
        })

        it('应该在销毁不存在的适配器时不抛出错误', () => {
            expect(() => {
                adapterFactory.destroyAdapter('nonexistent')
            }).not.toThrow()
        })
    })

    describe('兼容性检查', () => {
        it('应该能够检查框架版本兼容性', () => {
            const isCompatible = adapterFactory.checkCompatibility(FrameworkType.REACT, '18.0.0')
            expect(typeof isCompatible).toBe('boolean')
        })

        it('应该能够获取支持的框架列表', () => {
            const supportedFrameworks = adapterFactory.getSupportedFrameworks()
            expect(Array.isArray(supportedFrameworks)).toBe(true)
            expect(supportedFrameworks.length).toBeGreaterThan(0)
        })
    })
})