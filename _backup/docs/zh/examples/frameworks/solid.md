# Solid.js 应用示例

本示例展示如何将 Solid.js 应用集成到 Micro-Core 微前端架构中，包括组件开发、状态管理、路由配置等完整功能。

## 📋 目录

- [项目概述](#项目概述)
- [环境搭建](#环境搭建)
- [基础集成](#基础集成)
- [组件开发](#组件开发)
- [状态管理](#状态管理)
- [路由配置](#路由配置)
- [应用间通信](#应用间通信)
- [构建部署](#构建部署)
- [完整示例](#完整示例)

## 项目概述

Solid.js 是一个声明式、高效且灵活的 JavaScript 库，用于构建用户界面。本示例展示如何将 Solid.js 应用作为微前端应用集成到 Micro-Core 中。

### 🎯 示例特性

- **响应式系统**：利用 Solid.js 的细粒度响应式系统
- **组件复用**：跨应用的组件共享和复用
- **状态同步**：与其他微前端应用的状态同步
- **路由集成**：与主应用路由系统的集成
- **性能优化**：充分利用 Solid.js 的性能优势

## 环境搭建

### 创建 Solid.js 项目

```bash
# 使用官方模板创建项目
npx degit solidjs/templates/ts solid-micro-app
cd solid-micro-app

# 安装依赖
npm install

# 安装 Micro-Core 适配器
npm install @micro-core/adapter-solid
```

### 项目结构

```
solid-micro-app/
├── src/
│   ├── components/          # 组件目录
│   │   ├── UserList.tsx
│   │   ├── UserForm.tsx
│   │   └── shared/
│   ├── pages/              # 页面组件
│   │   ├── Home.tsx
│   │   ├── Users.tsx
│   │   └── Profile.tsx
│   ├── stores/             # 状态管理
│   │   ├── userStore.ts
│   │   └── appStore.ts
│   ├── utils/              # 工具函数
│   ├── App.tsx             # 根组件
│   ├── index.tsx           # 入口文件
│   └── micro-core.ts       # 微前端配置
├── public/
├── package.json
└── vite.config.ts
```

## 基础集成

### 微前端配置

```typescript
// src/micro-core.ts
import { MicroCoreSolidAdapter } from '@micro-core/adapter-solid'
import { render } from 'solid-js/web'
import App from './App'

// 创建适配器实例
const adapter = new MicroCoreSolidAdapter({
  name: 'solid-user-app',
  mount: '#solid-root',
  
  // 生命周期钩子
  async mount(props) {
    console.log('Solid 应用挂载', props)
    
    // 渲染应用
    const dispose = render(() => <App {...props} />, document.getElementById('solid-root')!)
    
    // 返回清理函数
    return dispose
  },
  
  async unmount() {
    console.log('Solid 应用卸载')
  },
  
  async update(props) {
    console.log('Solid 应用更新', props)
  }
})

// 注册微前端应用
adapter.register()

export default adapter
```

### 入口文件配置

```typescript
// src/index.tsx
import { render } from 'solid-js/web'
import App from './App'
import microCoreAdapter from './micro-core'

// 检查是否在微前端环境中运行
if (window.__MICRO_CORE__) {
  // 微前端模式：由适配器控制渲染
  console.log('运行在微前端模式')
} else {
  // 独立模式：直接渲染
  console.log('运行在独立模式')
  render(() => <App />, document.getElementById('root')!)
}
```

## 组件开发

### 用户列表组件

```typescript
// src/components/UserList.tsx
import { Component, For, createSignal, onMount } from 'solid-js'
import { useMicroCore } from '@micro-core/adapter-solid'

interface User {
  id: string
  name: string
  email: string
  role: string
}

const UserList: Component = () => {
  const [users, setUsers] = createSignal<User[]>([])
  const [loading, setLoading] = createSignal(true)
  const microCore = useMicroCore()

  onMount(async () => {
    try {
      // 从其他微应用获取用户数据
      const userData = await microCore.request('user-service', 'getUsers')
      setUsers(userData)
    } catch (error) {
      console.error('获取用户数据失败:', error)
    } finally {
      setLoading(false)
    }
  })

  const handleUserClick = (user: User) => {
    // 发送用户选择事件
    microCore.eventBus.emit('user:selected', user)
    
    // 导航到用户详情页
    microCore.navigate(`/users/${user.id}`)
  }

  return (
    <div class="user-list">
      <h2>用户列表</h2>
      
      {loading() ? (
        <div class="loading">加载中...</div>
      ) : (
        <div class="user-grid">
          <For each={users()}>
            {(user) => (
              <div 
                class="user-card"
                onClick={() => handleUserClick(user)}
              >
                <h3>{user.name}</h3>
                <p>{user.email}</p>
                <span class={`role role-${user.role}`}>
                  {user.role}
                </span>
              </div>
            )}
          </For>
        </div>
      )}
    </div>
  )
}

export default UserList
```

### 用户表单组件

```typescript
// src/components/UserForm.tsx
import { Component, createSignal } from 'solid-js'
import { useMicroCore } from '@micro-core/adapter-solid'

interface UserFormProps {
  userId?: string
  onSave?: (user: any) => void
}

const UserForm: Component<UserFormProps> = (props) => {
  const [formData, setFormData] = createSignal({
    name: '',
    email: '',
    role: 'user'
  })
  const [saving, setSaving] = createSignal(false)
  const microCore = useMicroCore()

  const handleSubmit = async (e: Event) => {
    e.preventDefault()
    setSaving(true)

    try {
      const userData = formData()
      
      if (props.userId) {
        // 更新用户
        await microCore.request('user-service', 'updateUser', {
          id: props.userId,
          ...userData
        })
        microCore.eventBus.emit('user:updated', { id: props.userId, ...userData })
      } else {
        // 创建用户
        const newUser = await microCore.request('user-service', 'createUser', userData)
        microCore.eventBus.emit('user:created', newUser)
      }

      // 调用回调函数
      props.onSave?.(userData)
      
      // 显示成功消息
      microCore.showNotification({
        type: 'success',
        message: props.userId ? '用户更新成功' : '用户创建成功'
      })
      
    } catch (error) {
      console.error('保存用户失败:', error)
      microCore.showNotification({
        type: 'error',
        message: '保存失败，请重试'
      })
    } finally {
      setSaving(false)
    }
  }

  const updateField = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <form class="user-form" onSubmit={handleSubmit}>
      <h2>{props.userId ? '编辑用户' : '创建用户'}</h2>
      
      <div class="form-group">
        <label for="name">姓名</label>
        <input
          id="name"
          type="text"
          value={formData().name}
          onInput={(e) => updateField('name', e.currentTarget.value)}
          required
        />
      </div>

      <div class="form-group">
        <label for="email">邮箱</label>
        <input
          id="email"
          type="email"
          value={formData().email}
          onInput={(e) => updateField('email', e.currentTarget.value)}
          required
        />
      </div>

      <div class="form-group">
        <label for="role">角色</label>
        <select
          id="role"
          value={formData().role}
          onChange={(e) => updateField('role', e.currentTarget.value)}
        >
          <option value="user">普通用户</option>
          <option value="admin">管理员</option>
          <option value="editor">编辑者</option>
        </select>
      </div>

      <div class="form-actions">
        <button type="submit" disabled={saving()}>
          {saving() ? '保存中...' : '保存'}
        </button>
        <button type="button" onClick={() => microCore.goBack()}>
          取消
        </button>
      </div>
    </form>
  )
}

export default UserForm
```

## 状态管理

### 用户状态管理

```typescript
// src/stores/userStore.ts
import { createSignal, createEffect } from 'solid-js'
import { createStore } from 'solid-js/store'
import { useMicroCore } from '@micro-core/adapter-solid'

interface User {
  id: string
  name: string
  email: string
  role: string
}

interface UserState {
  users: User[]
  currentUser: User | null
  loading: boolean
  error: string | null
}

// 创建用户状态
const [userState, setUserState] = createStore<UserState>({
  users: [],
  currentUser: null,
  loading: false,
  error: null
})

// 用户操作
export const userActions = {
  // 加载用户列表
  async loadUsers() {
    setUserState('loading', true)
    setUserState('error', null)
    
    try {
      const microCore = useMicroCore()
      const users = await microCore.request('user-service', 'getUsers')
      setUserState('users', users)
    } catch (error) {
      setUserState('error', error.message)
    } finally {
      setUserState('loading', false)
    }
  },

  // 设置当前用户
  setCurrentUser(user: User | null) {
    setUserState('currentUser', user)
  },

  // 添加用户
  addUser(user: User) {
    setUserState('users', prev => [...prev, user])
  },

  // 更新用户
  updateUser(userId: string, updates: Partial<User>) {
    setUserState('users', user => user.id === userId, updates)
  },

  // 删除用户
  removeUser(userId: string) {
    setUserState('users', prev => prev.filter(user => user.id !== userId))
  }
}

// 监听微前端事件
export const setupUserStoreListeners = () => {
  const microCore = useMicroCore()
  
  // 监听用户创建事件
  microCore.eventBus.on('user:created', (user: User) => {
    userActions.addUser(user)
  })
  
  // 监听用户更新事件
  microCore.eventBus.on('user:updated', (user: User) => {
    userActions.updateUser(user.id, user)
  })
  
  // 监听用户删除事件
  microCore.eventBus.on('user:deleted', (userId: string) => {
    userActions.removeUser(userId)
  })
}

export { userState }
```

### 应用状态管理

```typescript
// src/stores/appStore.ts
import { createSignal } from 'solid-js'
import { createStore } from 'solid-js/store'

interface AppState {
  theme: 'light' | 'dark'
  language: 'zh' | 'en'
  sidebarOpen: boolean
  notifications: Notification[]
}

interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  message: string
  duration?: number
}

// 创建应用状态
const [appState, setAppState] = createStore<AppState>({
  theme: 'light',
  language: 'zh',
  sidebarOpen: true,
  notifications: []
})

// 应用操作
export const appActions = {
  // 切换主题
  toggleTheme() {
    setAppState('theme', prev => prev === 'light' ? 'dark' : 'light')
  },

  // 设置语言
  setLanguage(language: 'zh' | 'en') {
    setAppState('language', language)
  },

  // 切换侧边栏
  toggleSidebar() {
    setAppState('sidebarOpen', prev => !prev)
  },

  // 添加通知
  addNotification(notification: Omit<Notification, 'id'>) {
    const id = Date.now().toString()
    const newNotification = { ...notification, id }
    
    setAppState('notifications', prev => [...prev, newNotification])
    
    // 自动移除通知
    if (notification.duration !== 0) {
      setTimeout(() => {
        appActions.removeNotification(id)
      }, notification.duration || 3000)
    }
  },

  // 移除通知
  removeNotification(id: string) {
    setAppState('notifications', prev => prev.filter(n => n.id !== id))
  }
}

export { appState }
```

## 路由配置

### 路由设置

```typescript
// src/router.tsx
import { Router, Route, Routes } from '@solidjs/router'
import { Component, lazy } from 'solid-js'
import { useMicroCore } from '@micro-core/adapter-solid'

// 懒加载页面组件
const Home = lazy(() => import('./pages/Home'))
const Users = lazy(() => import('./pages/Users'))
const UserDetail = lazy(() => import('./pages/UserDetail'))
const Profile = lazy(() => import('./pages/Profile'))

const AppRouter: Component = () => {
  const microCore = useMicroCore()

  // 路由守卫
  const requireAuth = (component: Component) => {
    return (props: any) => {
      if (!microCore.isAuthenticated()) {
        microCore.navigate('/login')
        return null
      }
      return component(props)
    }
  }

  return (
    <Router>
      <Routes>
        <Route path="/" component={Home} />
        <Route path="/users" component={requireAuth(Users)} />
        <Route path="/users/:id" component={requireAuth(UserDetail)} />
        <Route path="/profile" component={requireAuth(Profile)} />
      </Routes>
    </Router>
  )
}

export default AppRouter
```

### 页面组件

```typescript
// src/pages/Users.tsx
import { Component, onMount } from 'solid-js'
import UserList from '../components/UserList'
import UserForm from '../components/UserForm'
import { userState, userActions, setupUserStoreListeners } from '../stores/userStore'

const Users: Component = () => {
  onMount(() => {
    // 设置事件监听
    setupUserStoreListeners()
    
    // 加载用户数据
    userActions.loadUsers()
  })

  return (
    <div class="users-page">
      <div class="page-header">
        <h1>用户管理</h1>
        <button class="btn-primary" onClick={() => {
          // 打开创建用户对话框
        }}>
          创建用户
        </button>
      </div>

      <div class="page-content">
        {userState.loading ? (
          <div class="loading">加载中...</div>
        ) : userState.error ? (
          <div class="error">错误: {userState.error}</div>
        ) : (
          <UserList />
        )}
      </div>
    </div>
  )
}

export default Users
```

## 应用间通信

### 事件通信

```typescript
// src/utils/communication.ts
import { useMicroCore } from '@micro-core/adapter-solid'
import { createSignal, createEffect } from 'solid-js'

export const useCommunication = () => {
  const microCore = useMicroCore()
  const [messages, setMessages] = createSignal<any[]>([])

  // 发送消息到其他应用
  const sendMessage = (targetApp: string, type: string, data: any) => {
    microCore.sendMessage(targetApp, type, data)
  }

  // 广播消息
  const broadcast = (type: string, data: any) => {
    microCore.eventBus.emit(type, data)
  }

  // 监听消息
  const onMessage = (type: string, handler: (data: any) => void) => {
    microCore.eventBus.on(type, handler)
    
    // 返回取消监听函数
    return () => microCore.eventBus.off(type, handler)
  }

  return {
    sendMessage,
    broadcast,
    onMessage,
    messages
  }
}
```

### 状态同步

```typescript
// src/utils/stateSync.ts
import { createEffect } from 'solid-js'
import { useMicroCore } from '@micro-core/adapter-solid'
import { userState, userActions } from '../stores/userStore'

export const setupStateSync = () => {
  const microCore = useMicroCore()

  // 同步用户状态到全局状态
  createEffect(() => {
    microCore.setState('solid-app.users', userState.users)
  })

  // 监听全局状态变化
  microCore.onStateChange('global.currentUser', (user) => {
    userActions.setCurrentUser(user)
  })

  // 监听其他应用的状态变化
  microCore.onStateChange('react-app.selectedUser', (user) => {
    if (user) {
      userActions.setCurrentUser(user)
    }
  })
}
```

## 构建部署

### Vite 配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import solid from 'vite-plugin-solid'

export default defineConfig({
  plugins: [solid()],
  
  build: {
    // 微前端构建配置
    lib: {
      entry: 'src/index.tsx',
      name: 'SolidMicroApp',
      fileName: 'solid-micro-app',
      formats: ['umd']
    },
    
    rollupOptions: {
      external: ['solid-js', 'solid-js/web'],
      output: {
        globals: {
          'solid-js': 'SolidJS',
          'solid-js/web': 'SolidJSWeb'
        }
      }
    }
  },
  
  server: {
    port: 3003,
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  }
})
```

### 部署脚本

```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "build:micro": "vite build --mode micro",
    "serve:micro": "vite preview --port 3003"
  }
}
```

## 完整示例

### 主应用组件

```typescript
// src/App.tsx
import { Component, onMount } from 'solid-js'
import { Router } from '@solidjs/router'
import { useMicroCore } from '@micro-core/adapter-solid'
import AppRouter from './router'
import { setupStateSync } from './utils/stateSync'
import { appState, appActions } from './stores/appStore'

const App: Component = (props: any) => {
  const microCore = useMicroCore()

  onMount(() => {
    // 设置状态同步
    setupStateSync()
    
    // 监听主题变化
    microCore.onStateChange('global.theme', (theme) => {
      if (theme !== appState.theme) {
        appActions.setLanguage(theme)
      }
    })
    
    // 应用就绪通知
    microCore.eventBus.emit('app:ready', {
      name: 'solid-user-app',
      version: '1.0.0'
    })
  })

  return (
    <div class={`app theme-${appState.theme}`}>
      <header class="app-header">
        <h1>Solid.js 微前端应用</h1>
        <div class="header-actions">
          <button onClick={appActions.toggleTheme}>
            切换主题
          </button>
        </div>
      </header>
      
      <main class="app-main">
        <AppRouter />
      </main>
      
      {/* 通知组件 */}
      <div class="notifications">
        {appState.notifications.map(notification => (
          <div 
            class={`notification notification-${notification.type}`}
            key={notification.id}
          >
            {notification.message}
            <button onClick={() => appActions.removeNotification(notification.id)}>
              ×
            </button>
          </div>
        ))}
      </div>
    </div>
  )
}

export default App
```

### 样式文件

```css
/* src/styles/app.css */
.app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
}

.theme-dark {
  background-color: #1a1a1a;
  color: #fff;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  border-bottom: 1px solid #eee;
}

.theme-dark .app-header {
  border-bottom-color: #333;
}

.user-list {
  padding: 2rem;
}

.user-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.user-card {
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.user-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.theme-dark .user-card {
  border-color: #333;
  background-color: #2a2a2a;
}

.notifications {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
}

.notification {
  padding: 1rem;
  margin-bottom: 0.5rem;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 300px;
}

.notification-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.notification-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
```

## 运行示例

### 启动开发服务器

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 构建生产版本

```bash
# 构建微前端版本
npm run build:micro

# 预览构建结果
npm run serve:micro
```

### 集成到主应用

```typescript
// 在主应用中注册 Solid.js 微应用
microCore.registerApp({
  name: 'solid-user-app',
  entry: 'http://localhost:3003/solid-micro-app.js',
  container: '#solid-container',
  activeWhen: '/solid-app'
})
```

## 相关链接

- [Solid.js 官方文档](https://www.solidjs.com/)
- [Micro-Core 核心文档](/guide/introduction)
- [适配器开发指南](/guide/advanced/adapters)
- [应用间通信](/guide/features/communication)
- [状态管理](/guide/features/state-management)

---

*最后更新: 2024-07-27*