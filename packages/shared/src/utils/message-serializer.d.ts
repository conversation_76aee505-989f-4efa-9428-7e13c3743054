/**
 * @fileoverview 消息序列化器
 * @description 提供多种消息序列化和反序列化方式
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 消息序列化器接口
 */
export interface MessageSerializer {
    serialize(data: any): string | ArrayBuffer;
    deserialize(data: string | ArrayBuffer): any;
}
/**
 * JSON 序列化器
 */
export declare class JsonSerializer implements MessageSerializer {
    serialize(data: any): string;
    deserialize(data: string | ArrayBuffer): any;
}
/**
 * 二进制序列化器
 */
export declare class BinarySerializer implements MessageSerializer {
    serialize(data: any): ArrayBuffer;
    deserialize(data: string | ArrayBuffer): any;
}
/**
 * 压缩序列化器
 */
export declare class CompressedSerializer implements MessageSerializer {
    private baseSerializer;
    constructor(baseSerializer?: MessageSerializer);
    serialize(data: any): string | ArrayBuffer;
    deserialize(data: string | ArrayBuffer): any;
    private compress;
    private decompress;
}
/**
 * 序列化器工厂
 */
export declare class SerializerFactory {
    private static serializers;
    /**
     * 创建序列化器
     */
    static create(type: string): MessageSerializer;
    /**
     * 注册自定义序列化器
     */
    static register(type: string, factory: () => MessageSerializer): void;
    /**
     * 获取可用的序列化器类型
     */
    static getAvailableTypes(): string[];
}
/**
 * 创建序列化器的工厂函数
 */
export declare function createSerializer(type?: 'json' | 'binary' | 'compressed' | 'compressed-binary'): MessageSerializer;
declare const _default: {
    JsonSerializer: typeof JsonSerializer;
    BinarySerializer: typeof BinarySerializer;
    CompressedSerializer: typeof CompressedSerializer;
    SerializerFactory: typeof SerializerFactory;
    createSerializer: typeof createSerializer;
};
export default _default;
//# sourceMappingURL=message-serializer.d.ts.map