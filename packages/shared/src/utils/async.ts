/**
 * @fileoverview 异步工具函数
 * @description 提供防抖、节流等异步处理工具
 * <AUTHOR> <<EMAIL>>
 */

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null

    return function (...args: Parameters<T>) {
        if (timeout) {
            clearTimeout(timeout)
        }
        timeout = setTimeout(() => func.apply(this, args), wait)
    }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
    func: T,
    wait: number
): (...args: Parameters<T>) => void {
    let lastTime = 0

    return function (...args: Parameters<T>) {
        const now = Date.now()
        if (now - lastTime >= wait) {
            lastTime = now
            func.apply(this, args)
        }
    }
}