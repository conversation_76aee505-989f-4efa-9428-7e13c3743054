# Webpack 构建集成

Webpack 构建集成为 Micro-Core 提供了完整的 Webpack 支持，包括模块联邦、代码分割、构建优化等功能。

## 📋 目录

- [集成概述](#集成概述)
- [安装配置](#安装配置)
- [基础配置](#基础配置)
- [模块联邦](#模块联邦)
- [构建优化](#构建优化)
- [最佳实践](#最佳实践)

## 集成概述

Webpack 集成插件为微前端应用提供了完整的构建支持。

### 🎯 主要特性

- **自动配置**：自动配置微前端构建选项
- **模块联邦**：支持 Webpack Module Federation
- **代码分割**：智能代码分割和懒加载
- **构建优化**：生产环境构建优化
- **开发体验**：热更新和开发服务器支持

## 安装配置

### 安装插件

```bash
npm install @micro-core/webpack-plugin
```

### 基础配置

```javascript
// webpack.config.js
const { MicroCoreWebpackPlugin } = require('@micro-core/webpack-plugin')

module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      name: 'my-micro-app',
      entry: './src/index.js',
      
      // 自动配置选项
      autoConfig: {
        umd: true,
        cors: true,
        manifest: true
      },
      
      // 模块联邦配置
      federation: {
        exposes: {
          './App': './src/App',
          './utils': './src/utils'
        },
        shared: {
          react: { singleton: true },
          'react-dom': { singleton: true }
        }
      }
    })
  ]
}
```

## 基础配置

### 主应用配置

```javascript
// 主应用 webpack.config.js
const { MicroCoreWebpackPlugin } = require('@micro-core/webpack-plugin')

module.exports = {
  mode: 'development',
  
  plugins: [
    new MicroCoreWebpackPlugin({
      name: 'main-app',
      type: 'host',
      
      // 远程应用配置
      remotes: {
        'react-app': 'react_app@http://localhost:3001/remoteEntry.js',
        'vue-app': 'vue_app@http://localhost:3002/remoteEntry.js'
      },
      
      // 共享依赖
      shared: {
        react: { singleton: true, eager: true },
        'react-dom': { singleton: true, eager: true }
      }
    })
  ],
  
  devServer: {
    port: 3000,
    hot: true,
    historyApiFallback: true
  }
}
```

### 子应用配置

```javascript
// 子应用 webpack.config.js
const { MicroCoreWebpackPlugin } = require('@micro-core/webpack-plugin')

module.exports = {
  mode: 'development',
  
  plugins: [
    new MicroCoreWebpackPlugin({
      name: 'react-app',
      type: 'remote',
      
      // 暴露的模块
      exposes: {
        './App': './src/App',
        './bootstrap': './src/bootstrap'
      },
      
      // 共享依赖
      shared: {
        react: { singleton: true },
        'react-dom': { singleton: true }
      }
    })
  ],
  
  devServer: {
    port: 3001,
    hot: true,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  }
}
```

## 模块联邦

### 高级模块联邦配置

```javascript
// 高级模块联邦配置
const federationConfig = {
  name: 'advanced-app',
  
  // 暴露模块
  exposes: {
    './App': './src/App',
    './components/Button': './src/components/Button',
    './hooks/useAuth': './src/hooks/useAuth',
    './services/api': './src/services/api'
  },
  
  // 远程模块
  remotes: {
    'shared-components': 'shared@http://localhost:3003/remoteEntry.js',
    'auth-service': 'auth@http://localhost:3004/remoteEntry.js'
  },
  
  // 共享依赖配置
  shared: {
    react: {
      singleton: true,
      requiredVersion: '^18.0.0',
      eager: true
    },
    'react-dom': {
      singleton: true,
      requiredVersion: '^18.0.0',
      eager: true
    },
    '@micro-core/core': {
      singleton: true,
      eager: true
    },
    lodash: {
      singleton: false,
      requiredVersion: '^4.17.0'
    }
  }
}
```

### 动态远程模块

```javascript
// 动态加载远程模块
const loadRemoteModule = async (remoteName, moduleName) => {
  try {
    const container = window[remoteName]
    await container.init(__webpack_share_scopes__.default)
    const factory = await container.get(moduleName)
    return factory()
  } catch (error) {
    console.error(`加载远程模块失败: ${remoteName}/${moduleName}`, error)
    throw error
  }
}

// 使用示例
const RemoteComponent = React.lazy(async () => {
  const module = await loadRemoteModule('remote-app', './Component')
  return { default: module.default || module }
})
```

## 构建优化

### 生产环境优化

```javascript
// webpack.prod.js
const { MicroCoreWebpackPlugin } = require('@micro-core/webpack-plugin')
const TerserPlugin = require('terser-webpack-plugin')
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin')

module.exports = {
  mode: 'production',
  
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true
          }
        }
      }),
      new CssMinimizerPlugin()
    ],
    
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true
        }
      }
    }
  },
  
  plugins: [
    new MicroCoreWebpackPlugin({
      name: 'production-app',
      
      // 生产环境优化
      optimization: {
        minimize: true,
        treeshaking: true,
        bundleAnalyzer: true
      },
      
      // CDN 配置
      cdn: {
        enabled: true,
        url: 'https://cdn.example.com',
        assets: ['js', 'css', 'images']
      }
    })
  ]
}
```

### 代码分割策略

```javascript
// 智能代码分割配置
const splitChunksConfig = {
  chunks: 'all',
  minSize: 20000,
  maxSize: 244000,
  
  cacheGroups: {
    // 框架代码
    framework: {
      test: /[\\/]node_modules[\\/](react|react-dom|vue|@angular)[\\/]/,
      name: 'framework',
      chunks: 'all',
      priority: 40
    },
    
    // 第三方库
    vendor: {
      test: /[\\/]node_modules[\\/]/,
      name: 'vendor',
      chunks: 'all',
      priority: 30
    },
    
    // 公共代码
    common: {
      name: 'common',
      minChunks: 2,
      chunks: 'all',
      priority: 20
    },
    
    // 微前端共享代码
    microCore: {
      test: /[\\/]node_modules[\\/]@micro-core[\\/]/,
      name: 'micro-core',
      chunks: 'all',
      priority: 50
    }
  }
}
```

## 最佳实践

### 1. 配置组织

```javascript
// webpack/base.config.js
const path = require('path')
const { MicroCoreWebpackPlugin } = require('@micro-core/webpack-plugin')

const baseConfig = {
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    alias: {
      '@': path.resolve(__dirname, '../src'),
      '@components': path.resolve(__dirname, '../src/components'),
      '@utils': path.resolve(__dirname, '../src/utils')
    }
  },
  
  module: {
    rules: [
      {
        test: /\.(ts|tsx)$/,
        use: 'ts-loader',
        exclude: /node_modules/
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      },
      {
        test: /\.(png|jpg|gif|svg)$/,
        type: 'asset/resource'
      }
    ]
  }
}

module.exports = baseConfig
```

### 2. 环境配置

```javascript
// webpack/env.config.js
const getEnvConfig = (env) => {
  const configs = {
    development: {
      devtool: 'eval-source-map',
      mode: 'development',
      
      devServer: {
        hot: true,
        port: process.env.PORT || 3000,
        historyApiFallback: true,
        headers: {
          'Access-Control-Allow-Origin': '*'
        }
      }
    },
    
    production: {
      devtool: 'source-map',
      mode: 'production',
      
      optimization: {
        minimize: true,
        sideEffects: false
      }
    },
    
    test: {
      devtool: 'inline-source-map',
      mode: 'development'
    }
  }
  
  return configs[env] || configs.development
}

module.exports = getEnvConfig
```

### 3. 性能监控

```javascript
// webpack/performance.config.js
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin')

const performanceConfig = {
  plugins: [
    // 包大小分析
    process.env.ANALYZE && new BundleAnalyzerPlugin({
      analyzerMode: 'static',
      openAnalyzer: false,
      reportFilename: 'bundle-report.html'
    }),
    
    // 构建速度分析
    process.env.SPEED && new SpeedMeasurePlugin()
  ].filter(Boolean),
  
  // 性能预算
  performance: {
    maxAssetSize: 250000,
    maxEntrypointSize: 250000,
    hints: 'warning'
  }
}

module.exports = performanceConfig
```

### 4. 错误处理

```javascript
// webpack/error-handling.config.js
class MicroCoreErrorPlugin {
  apply(compiler) {
    compiler.hooks.done.tap('MicroCoreErrorPlugin', (stats) => {
      if (stats.hasErrors()) {
        const errors = stats.toJson().errors
        
        // 发送错误到监控系统
        errors.forEach(error => {
          console.error('构建错误:', error)
          
          // 可以集成错误监控服务
          // errorReporter.report(error)
        })
      }
    })
    
    compiler.hooks.failed.tap('MicroCoreErrorPlugin', (error) => {
      console.error('构建失败:', error)
    })
  }
}

module.exports = MicroCoreErrorPlugin
```

### 5. 完整配置示例

```javascript
// webpack.config.js
const path = require('path')
const { merge } = require('webpack-merge')
const { MicroCoreWebpackPlugin } = require('@micro-core/webpack-plugin')
const baseConfig = require('./webpack/base.config')
const getEnvConfig = require('./webpack/env.config')
const performanceConfig = require('./webpack/performance.config')

module.exports = (env, argv) => {
  const mode = argv.mode || 'development'
  const envConfig = getEnvConfig(mode)
  
  return merge(baseConfig, envConfig, performanceConfig, {
    entry: './src/index.ts',
    
    plugins: [
      new MicroCoreWebpackPlugin({
        name: process.env.APP_NAME || 'micro-app',
        
        // 根据环境调整配置
        ...(mode === 'production' ? {
          optimization: {
            minimize: true,
            treeshaking: true
          },
          cdn: {
            enabled: true,
            url: process.env.CDN_URL
          }
        } : {
          devServer: {
            hot: true,
            port: process.env.PORT || 3000
          }
        })
      })
    ]
  })
}
```

## 相关链接

- [Vite 构建集成](/ecosystem/builders/vite)
- [Rollup 构建集成](/ecosystem/builders/rollup)
- [构建优化指南](/guide/best-practices/build)
- [模块联邦文档](https://webpack.js.org/concepts/module-federation/)

---

*最后更新: 2024-07-27*