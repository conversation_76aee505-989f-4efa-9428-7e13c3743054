# 加载器 API (第二部分)

## 缓存系统

Micro-Core 加载器提供了缓存系统，用于缓存加载的资源，提高加载性能。

### 缓存配置

```typescript
// 创建带缓存配置的加载器
const loader = new Loader({
  cache: {
    enabled: true,      // 是否启用缓存
    maxAge: 3600000,    // 缓存最大存活时间，单位为毫秒，默认为 1 小时
    capacity: 100       // 缓存容量，默认为 50
  }
});

// 或者在加载资源时指定缓存配置
loader.loadResource('https://example.com/app1/main.js', {
  cache: {
    enabled: true,
    maxAge: 3600000,
    capacity: 100
  }
});
```

### 缓存管理

```typescript
// 清除所有缓存
loader.clearCache();

// 清除特定资源的缓存
loader.clearCache('https://example.com/app1/main.js');

// 检查资源是否已缓存
const isCached = loader.isCached('https://example.com/app1/main.js');
console.log('资源是否已缓存:', isCached);

// 获取缓存统计信息
const cacheStats = loader.getCacheStats();
console.log('缓存统计信息:', cacheStats);
```

## 错误处理

Micro-Core 加载器提供了多种错误处理机制。

### 超时处理

```typescript
// 设置全局超时时间
const loader = new Loader({
  timeout: 10000 // 10 秒
});

// 设置特定资源的超时时间
loader.loadResource('https://example.com/app1/main.js', {
  timeout: 5000 // 5 秒
});

// 处理超时错误
loader.loadResource('https://example.com/app1/main.js')
  .catch(error => {
    if (error.name === 'TimeoutError') {
      console.error('资源加载超时:', error.message);
      // 处理超时错误
    }
  });
```

### 重试机制

```typescript
// 设置全局重试配置
const loader = new Loader({
  retryCount: 3,    // 重试次数
  retryDelay: 1000  // 重试间隔时间，单位为毫秒
});

// 设置特定资源的重试配置
loader.loadResource('https://example.com/app1/main.js', {
  retryCount: 5,
  retryDelay: 2000
});
```

### 错误事件

```typescript
// 监听错误事件
loader.on('error', (error, resource) => {
  console.error(`资源 ${resource.url} 加载失败:`, error);
  
  // 上报错误
  reportError({
    type: 'resource_load_error',
    url: resource.url,
    message: error.message,
    stack: error.stack
  });
});
```

### 自定义错误处理

```typescript
// 创建带自定义错误处理的加载器
const loader = new Loader({
  onError: (error, resource) => {
    console.error(`资源 ${resource.url} 加载失败:`, error);
    
    // 根据错误类型处理
    if (error.name === 'TimeoutError') {
      // 处理超时错误
      console.warn(`资源 ${resource.url} 加载超时，尝试使用备用地址`);
      
      // 使用备用地址重新加载
      const backupUrl = resource.url.replace('example.com', 'backup-example.com');
      return loader.loadResource(backupUrl, resource.options);
    }
    
    if (error.name === 'NetworkError') {
      // 处理网络错误
      console.warn(`资源 ${resource.url} 网络错误，尝试使用本地缓存`);
      
      // 使用本地缓存
      return getLocalCache(resource.url);
    }
    
    // 其他错误直接抛出
    throw error;
  }
});
```

## 沙箱系统

Micro-Core 加载器提供了沙箱系统，用于隔离微应用的运行环境，防止微应用之间相互影响。

### 沙箱配置

```typescript
// 创建带沙箱配置的加载器
const loader = new Loader({
  sandbox: {
    enabled: true,           // 是否启用沙箱
    strictIsolation: true,   // 是否启用严格隔离
    disableWith: false,      // 是否禁用 with 语句
    useProxy: true           // 是否使用 Proxy
  }
});

// 或者在加载微应用时指定沙箱配置
loader.load({
  name: 'app1',
  entry: 'https://example.com/app1/index.html',
  container: '#app1-container',
  sandbox: {
    enabled: true,
    strictIsolation: true,
    disableWith: false,
    useProxy: true
  }
});
```

### 沙箱环境变量

```typescript
// 设置沙箱环境变量
loader.load({
  name: 'app1',
  entry: 'https://example.com/app1/index.html',
  container: '#app1-container',
  sandbox: {
    enabled: true,
    globals: {
      // 全局变量
      appName: 'App1',
      version: '1.0.0',
      
      // 全局函数
      log: (...args) => console.log('[App1]', ...args),
      
      // 覆盖原生 API
      fetch: (url, options) => {
        console.log(`[App1] 请求: ${url}`);
        return window.fetch(url, options);
      }
    }
  }
});
```

### 沙箱通信

```typescript
// 主应用和微应用之间的通信
loader.load({
  name: 'app1',
  entry: 'https://example.com/app1/index.html',
  container: '#app1-container',
  props: {
    // 传递给微应用的方法
    onMessage: (message) => {
      console.log('收到微应用消息:', message);
      
      // 处理消息
      if (message.type === 'getData') {
        return fetchData(message.params);
      }
      
      if (message.type === 'navigate') {
        router.push(message.path);
        return true;
      }
      
      return null;
    }
  }
});

// 微应用中调用主应用方法
window.microApp.onMessage({
  type: 'getData',
  params: { id: 1 }
}).then(data => {
  console.log('获取数据:', data);
});

window.microApp.onMessage({
  type: 'navigate',
  path: '/home'
});
```

## 生命周期

Micro-Core 加载器提供了完整的生命周期钩子，用于控制微应用的加载、挂载和卸载过程。

### 生命周期钩子

```typescript
// 设置生命周期钩子
loader.load({
  name: 'app1',
  entry: 'https://example.com/app1/index.html',
  container: '#app1-container',
  lifecycle: {
    // 加载前
    beforeLoad: (app) => {
      console.log(`${app.name} 开始加载`);
      
      // 显示加载指示器
      showLoadingIndicator(app.container);
      
      return true; // 返回 false 将阻止加载
    },
    
    // 加载后
    afterLoad: (app) => {
      console.log(`${app.name} 加载完成`);
      
      // 隐藏加载指示器
      hideLoadingIndicator(app.container);
    },
    
    // 挂载前
    beforeMount: (app) => {
      console.log(`${app.name} 开始挂载`);
      
      // 准备容器
      prepareContainer(app.container);
      
      return true; // 返回 false 将阻止挂载
    },
    
    // 挂载后
    afterMount: (app) => {
      console.log(`${app.name} 挂载完成`);
      
      // 应用挂载动画
      applyMountAnimation(app.container);
    },
    
    // 卸载前
    beforeUnmount: (app) => {
      console.log(`${app.name} 开始卸载`);
      
      // 确认是否可以卸载
      const canUnmount = confirmUnmount(app.name);
      
      return canUnmount; // 返回 false 将阻止卸载
    },
    
    // 卸载后
    afterUnmount: (app) => {
      console.log(`${app.name} 卸载完成`);
      
      // 清理容器
      cleanupContainer(app.container);
    },
    
    // 错误处理
    onError: (error, app) => {
      console.error(`${app.name} 发生错误:`, error);
      
      // 显示错误信息
      showErrorMessage(app.container, error);
      
      // 上报错误
      reportError({
        app: app.name,
        message: error.message,
        stack: error.stack
      });
    }
  }
});
```

### 生命周期事件

```typescript
// 监听生命周期事件
loader.on('beforeLoad', (app) => {
  console.log(`${app.name} 开始加载`);
});

loader.on('afterLoad', (app) => {
  console.log(`${app.name} 加载完成`);
});

loader.on('beforeMount', (app) => {
  console.log(`${app.name} 开始挂载`);
});

loader.on('afterMount', (app) => {
  console.log(`${app.name} 挂载完成`);
});

loader.on('beforeUnmount', (app) => {
  console.log(`${app.name} 开始卸载`);
});

loader.on('afterUnmount', (app) => {
  console.log(`${app.name} 卸载完成`);
});

loader.on('error', (error, app) => {
  console.error(`${app.name} 发生错误:`, error);
});
```

## 性能优化

Micro-Core 加载器提供了多种性能优化机制。

### 资源预加载

```typescript
// 预加载资源
loader.prefetch('https://example.com/app1/main.js');

// 预加载多个资源
loader.prefetch([
  'https://example.com/app1/main.js',
  'https://example.com/app1/style.css',
  'https://example.com/app1/config.json'
]);

// 预加载微应用
loader.prefetchApp({
  name: 'app1',
  entry: 'https://example.com/app1/index.html'
});

// 预加载多个微应用
loader.prefetchApps([
  {
    name: 'app1',
    entry: 'https://example.com/app1/index.html'
  },
  {
    name: 'app2',
    entry: 'https://example.com/app2/index.html'
  }
]);
```

### 并行加载

```typescript
// 并行加载多个资源
Promise.all([
  loader.loadResource('https://example.com/app1/main.js'),
  loader.loadResource('https://example.com/app1/style.css'),
  loader.loadResource('https://example.com/app1/config.json')
])
  .then(([jsModule, cssModule, configModule]) => {
    console.log('所有资源加载成功');
  })
  .catch(error => {
    console.error('资源加载失败:', error);
  });

// 并行加载多个微应用
loader.loadAll([
  {
    name: 'app1',
    entry: 'https://example.com/app1/index.html',
    container: '#app1-container'
  },
  {
    name: 'app2',
    entry: 'https://example.com/app2/index.html',
    container: '#app2-container'
  }
])
  .then(apps => {
    console.log('所有微应用加载成功');
  })
  .catch(error => {
    console.error('微应用加载失败:', error);
  });
```

### 资源优先级

```typescript
// 设置资源优先级
loader.loadResource('https://example.com/app1/main.js', {
  priority: 'high'
});

loader.loadResource('https://example.com/app1/non-critical.js', {
  priority: 'low'
});

// 使用 requestIdleCallback 在空闲时间加载低优先级资源
if ('requestIdleCallback' in window) {
  requestIdleCallback(() => {
    loader.loadResource('https://example.com/app1/non-critical.js');
  });
} else {
  setTimeout(() => {
    loader.loadResource('https://example.com/app1/non-critical.js');
  }, 1000);
}
```

### 资源压缩和合并

```typescript
// 加载压缩资源
loader.loadResource('https://example.com/app1/main.min.js');

// 加载合并资源
loader.loadResource('https://example.com/app1/bundle.js');

// 使用 HTTP/2 加载多个小文件
loader.loadResources([
  'https://example.com/app1/module1.js',
  'https://example.com/app1/module2.js',
  'https://example.com/app1/module3.js'
]);
```

## 高级功能

### 依赖管理

```typescript
// 定义微应用依赖
loader.load({
  name: 'app1',
  entry: 'https://example.com/app1/index.html',
  container: '#app1-container',
  dependencies: {
    // 共享库
    'react': '^17.0.0',
    'react-dom': '^17.0.0',
    'lodash': '^4.0.0',
    
    // 其他微应用
    'common-app': '*'
  }
});

// 注册共享库
loader.registerSharedLibrary('react', window.React, '17.0.2');
loader.registerSharedLibrary('react-dom', window.ReactDOM, '17.0.2');
loader.registerSharedLibrary('lodash', window._, '4.17.21');

// 获取共享库
const react = loader.getSharedLibrary('react');
console.log('React 版本:', react.__version);
```

### 资源转换

```typescript
// 注册资源转换器
loader.registerTransformer('js', (code, url, options) => {
  // 转换代码
  return transformCode(code);
});

// 使用转换器加载资源
loader.loadResource('https://example.com/app1/main.js', {
  transform: true
});

// 自定义转换器
const babelTransformer = {
  name: 'babel',
  test: (url) => url.endsWith('.js'),
  transform: (code, url, options) => {
    // 使用 Babel 转换代码
    return Babel.transform(code, {
      presets: ['env'],
      plugins: ['transform-runtime']
    }).code;
  }
};

loader.registerTransformer(babelTransformer);
```

### 资源映射

```typescript
// 设置资源映射
loader.setResourceMap({
  'react': 'https://cdn.example.com/react/17.0.2/react.min.js',
  'react-dom': 'https://cdn.example.com/react-dom/17.0.2/react-dom.min.js',
  'lodash': 'https://cdn.example.com/lodash/4.17.21/lodash.min.js'
});

// 使用资源映射加载资源
loader.loadResource('react')
  .then(react => {
    console.log('React 加载成功:', react);
  });

// 动态资源映射
loader.setResourceResolver((name, version) => {
  return `https://cdn.example.com/${name}/${version}/${name}.min.js`;
});

// 使用资源解析器加载资源
loader.loadResource('vue', { version: '3.2.0' })
  .then(vue => {
    console.log('Vue 加载成功:', vue);
  });
```

### 资源监控

```typescript
// 监控资源加载性能
loader.on('resourceLoad', (resource, performance) => {
  console.log(`资源 ${resource.url} 加载性能:`, {
    duration: performance.duration,
    size: performance.size,
    type: performance.type
  });
  
  // 上报性能数据
  reportPerformance({
    type: 'resource_load',
    url: resource.url,
    duration: performance.duration,
    size: performance.size,
    resourceType: performance.type
  });
});

// 监控资源加载错误
loader.on('resourceError', (error, resource) => {
  console.error(`资源 ${resource.url} 加载失败:`, error);
  
  // 上报错误
  reportError({
    type: 'resource_load_error',
    url: resource.url,
    message: error.message,
    stack: error.stack
  });
});

// 获取资源加载统计信息
const stats = loader.getLoadStats();
console.log('资源加载统计信息:', stats);
```

### 自定义加载器

```typescript
// 创建自定义加载器
const customLoader = {
  name: 'custom',
  
  // 加载资源
  load: (url, options) => {
    console.log(`使用自定义加载器加载资源: ${url}`);
    
    // 自定义加载逻辑
    return new Promise((resolve, reject) => {
      // 实现自定义加载逻辑
      const xhr = new XMLHttpRequest();
      xhr.open('GET', url);
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve(xhr.responseText);
        } else {
          reject(new Error(`加载失败: ${xhr.status} ${xhr.statusText}`));
        }
      };
      xhr.onerror = () => reject(new Error('网络错误'));
      xhr.send();
    });
  }
};

// 使用自定义加载器
loader.load({
  name: 'app1',
  entry: 'https://example.com/app1/index.html',
  container: '#app1-container',
  customLoader
});
```

## 与其他框架集成

### 与 qiankun 集成

```typescript
import { registerMicroApps, start } from 'qiankun';
import { Loader } from '@micro-core/core';

// 创建加载器实例
const loader = new Loader();

// 注册微应用
registerMicroApps([
  {
    name: 'app1',
    entry: '//localhost:8081',
    container: '#app1-container',
    activeRule: '/app1',
    loader: (url) => {
      // 使用 Micro-Core 加载器加载资源
      return loader.loadResource(url);
    }
  },
  {
    name: 'app2',
    entry: '//localhost:8082',
    container: '#app2-container',
    activeRule: '/app2',
    loader: (url) => {
      return loader.loadResource(url);
    }
  }
]);

// 启动 qiankun
start();
```

### 与 wujie (无界) 集成

```typescript
import { bus, setupApp, preloadApp, startApp } from 'wujie';
import { Loader } from '@micro-core/core';

// 创建加载器实例
const loader = new Loader();

// 设置微应用
setupApp({
  name: 'app1',
  url: '//localhost:8081',
  exec: true,
  fetch: (url, options) => {
    // 使用 Micro-Core 加载器加载资源
    return loader.loadResource(url, options);
  }
});

// 预加载微应用
preloadApp({
  name: 'app1'
});

// 启动微应用
startApp({
  name: 'app1',
  container: '#app1-container',
  url: '//localhost:8081'
});
```

## 常见问题与解决方案

### 资源加载失败

**问题**：资源加载失败，无法加载微应用。

**解决方案**：

1. 检查资源 URL 是否正确
2. 检查网络连接是否正常
3. 检查跨域配置是否正确
4. 使用重试机制
5. 使用备用资源

```typescript
// 使用重试机制
loader.loadResource('https://example.com/app1/main.js', {
  retryCount: 3,
  retryDelay: 1000
});

// 使用备用资源
loader.loadResource('https://example.com/app1/main.js')
  .catch(error => {
    console.warn('主资源加载失败，尝试使用备用资源:', error);
    return loader.loadResource('https://backup.example.com/app1/main.js');
  });

// 使用资源映射
loader.setResourceMap({
  'app1/main.js': [
    'https://example.com/app1/main.js',
    'https://backup1.example.com/app1/main.js',
    'https://backup2.example.com/app1/main.js'
  ]
});

loader.loadResource('app1/main.js');
```

### 跨域问题

**问题**：跨域请求被阻止，无法加载资源。

**解决方案**：

1. 配置服务器 CORS 头
2. 使用代理服务器
3. 使用 JSONP 加载脚本

```typescript
// 配置服务器 CORS 头
// Access-Control-Allow-Origin: *

// 使用代理服务器
loader.loadResource('/api/proxy?url=https://example.com/app1/main.js');

// 使用自定义 fetch 函数
loader.loadResource('https://example.com/app1/main.js', {
  fetch: (url, options) => {
    // 将请求转发到代理服务器
    return window.fetch(`/api/proxy?url=${encodeURIComponent(url)}`, options);
  }
});

// 使用 JSONP 加载脚本
loader.registerPlugin({
  name: 'jsonp',
  test: (url) => url.endsWith('.js'),
  load: (url) => {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = url;
      script.onload = () => resolve(window[url.split('/').pop().split('.')[0]]);
      script.onerror = () => reject(new Error(`加载失败: ${url}`));
      document.head.appendChild(script);
    });
  }
});
```

### 沙箱冲突

**问题**：沙箱环境与主应用或其他微应用发生冲突。

**解决方案**：

1. 使用严格隔离模式
2. 配置沙箱白名单
3. 使用自定义代理

```typescript
// 使用严格隔离模式
loader.load({
  name: 'app1',
  entry: 'https://example.com/app1/index.html',
  container: '#app1-container',
  sandbox: {
    enabled: true,
    strictIsolation: true
  }
});

// 配置沙箱白名单
loader.load({
  name: 'app1',
  entry: 'https://example.com/app1/index.html',
  container: '#app1-container',
  sandbox: {
    enabled: true,
    whitelist: ['React', 'ReactDOM', '_', 'moment']
  }
});

// 使用自定义代理
loader.load({
  name: 'app1',
  entry: 'https://example.com/app1/index.html',
  container: '#app1-container',
  sandbox: {
    enabled: true,
    useProxy: true,
    proxyHandler: {
      get: (target, prop) => {
        // 自定义 get 处理
        console.log(`访问属性: ${String(prop)}`);
        return Reflect.get(target, prop);
      },
      set: (target, prop, value) => {
        // 自定义 set 处理
        console.log(`设置属性: ${String(prop)} = ${value}`);
        return Reflect.set(target, prop, value);
      }
    }
  }
});
```

### 资源冲突

**问题**：多个微应用使用不同版本的同一库，导致冲突。

**解决方案**：

1. 使用共享依赖
2. 使用依赖隔离
3. 使用外部依赖

```typescript
// 使用共享依赖
loader.registerSharedLibrary('react', window.React, '17.0.2');
loader.registerSharedLibrary('react-dom', window.ReactDOM, '17.0.2');

// 使用依赖隔离
loader.load({
  name: 'app1',
  entry: 'https://example.com/app1/index.html',
  container: '#app1-container',
  sandbox: {
    enabled: true,
    isolateDependencies: ['jquery', 'lodash']
  }
});

// 使用外部依赖
loader.load({
  name: 'app1',
  entry: 'https://example.com/app1/index.html',
  container: '#app1-container',
  externals: {
    'react': 'React',
    'react-dom': 'ReactDOM',
    'lodash': '_'
  }
});
```

### 性能问题

**问题**：微应用加载性能不佳，影响用户体验。

**解决方案**：

1. 使用资源预加载
2. 使用资源缓存
3. 使用资源压缩和合并
4. 使用并行加载
5. 使用懒加载

```typescript
// 使用资源预加载
loader.prefetch([
  'https://example.com/app1/main.js',
  'https://example.com/app1/style.css'
]);

// 使用资源缓存
loader.loadResource('https://example.com/app1/main.js', {
  cache: {
    enabled: true,
    maxAge: 3600000 // 1小时
  }
});

// 使用资源压缩和合并
loader.loadResource('https://example.com/app1/bundle.min.js');

// 使用并行加载
Promise.all([
  loader.loadResource('https://example.com/app1/main.js'),
  loader.loadResource('https://example.com/app1/style.css')
]);

// 使用懒加载
function loadAppWhenNeeded() {
  // 当需要时才加载微应用
  return loader.load({
    name: 'app1',
    entry: 'https://example.com/app1/index.html',
    container: '#app1-container'
  });
}

// 在用户交互时加载
button.addEventListener('click', () => {
  loadAppWhenNeeded()
    .then(app => app.mount());
});
```

## 总结

Micro-Core 加载器 API 提供了强大而灵活的资源加载和微应用管理功能，适用于微前端架构中的各种场景。通过本文档，你可以了解加载器 API 的基本用法、高级特性以及最佳实践，帮助你构建高效、可靠的微前端应用。

主要特性包括：

1. **灵活的资源加载**：支持加载各种类型的资源，如 JavaScript、CSS、HTML 等
2. **强大的插件系统**：通过插件扩展加载器功能
3. **完善的缓存系统**：提高资源加载性能
4. **沙箱隔离**：隔离微应用运行环境，防止相互影响
5. **生命周期管理**：控制微应用的加载、挂载和卸载过程
6. **错误处理机制**：处理资源加载和执行过程中的错误
7. **性能优化**：提供多种性能优化机制

通过合理使用这些功能，你可以构建出用户体验良好、性能优异的微前端应用。