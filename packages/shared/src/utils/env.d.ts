/**
 * @fileoverview 环境检测工具函数
 * @description 提供各种环境和平台检测功能
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 检查是否为浏览器环境
 */
export declare function isBrowser(): boolean;
/**
 * 检查是否为Node.js环境
 */
export declare function isNode(): boolean;
/**
 * 检查是否为开发环境
 */
export declare function isDevelopment(): boolean;
/**
 * 检查是否为生产环境
 */
export declare function isProduction(): boolean;
/**
 * 检查是否为测试环境
 */
export declare function isTest(): boolean;
/**
 * 获取用户代理信息
 */
export declare function getUserAgent(): string;
/**
 * 检查是否为移动设备
 */
export declare function isMobile(): boolean;
/**
 * 检查是否为iOS设备
 */
export declare function isIOS(): boolean;
/**
 * 检查是否为Android设备
 */
export declare function isAndroid(): boolean;
/**
 * 检查是否支持触摸
 */
export declare function isTouchDevice(): boolean;
/**
 * 获取浏览器信息
 */
export declare function getBrowserInfo(): {
    name: string;
    version: string;
};
/**
 * 获取操作系统信息
 */
export declare function getOSInfo(): {
    name: string;
    version: string;
};
//# sourceMappingURL=env.d.ts.map