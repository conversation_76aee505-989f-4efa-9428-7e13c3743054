/**
 * 序列化器实现
 *
 * @description 消息序列化和反序列化处理
 * <AUTHOR> <<EMAIL>>
 */
type SerializationFormat = 'json' | 'msgpack' | 'protobuf' | 'cbor';
interface SerializationOptions {
    format?: SerializationFormat;
    compress?: boolean;
    validate?: boolean;
}
interface SerializerConfig {
    defaultFormat?: SerializationFormat;
    enableCompression?: boolean;
    enableValidation?: boolean;
    maxSize?: number;
}
interface SerializerMetrics {
    totalSerializations: number;
    totalDeserializations: number;
    totalBytes: number;
    compressionRatio: number;
    averageSerializationTime: number;
    averageDeserializationTime: number;
    errorCount: number;
    lastUpdated: number;
}
/**
 * 序列化器
 * 提供多种格式的序列化和反序列化能力
 */
export declare class Serializer {
    private readonly logger;
    private readonly config;
    private readonly metrics;
    private readonly formatHandlers;
    constructor(config?: SerializerConfig);
    /**
     * 序列化数据
     */
    serialize(data: any, options?: SerializationOptions): string;
    /**
     * 反序列化数据
     */
    deserialize<T = any>(serialized: string, options?: SerializationOptions): T;
    /**
     * 批量序列化
     */
    serializeBatch(dataArray: any[], options?: SerializationOptions): string[];
    /**
     * 批量反序列化
     */
    deserializeBatch<T = any>(serializedArray: string[], options?: SerializationOptions): T[];
    /**
     * 注册格式处理器
     */
    registerFormatHandler(format: SerializationFormat, handler: FormatHandler): void;
    /**
     * 注销格式处理器
     */
    unregisterFormatHandler(format: SerializationFormat): boolean;
    /**
     * 获取支持的格式
     */
    getSupportedFormats(): SerializationFormat[];
    /**
     * 检查格式是否支持
     */
    isFormatSupported(format: SerializationFormat): boolean;
    /**
     * 估算序列化大小
     */
    estimateSize(data: any, format?: SerializationFormat): number;
    /**
     * 获取性能指标
     */
    getMetrics(): SerializerMetrics;
    /**
     * 重置指标
     */
    resetMetrics(): void;
    /**
     * 销毁序列化器
     */
    destroy(): void;
    /**
     * 注册默认格式处理器
     */
    private registerDefaultHandlers;
    /**
     * 获取格式处理器
     */
    private getFormatHandler;
    /**
     * 验证数据
     */
    private validateData;
    /**
     * 验证格式处理器
     */
    private validateFormatHandler;
    /**
     * 压缩数据
     */
    private compress;
    /**
     * 解压缩数据
     */
    private decompress;
    /**
     * 更新序列化指标
     */
    private updateSerializationMetrics;
    /**
     * 更新反序列化指标
     */
    private updateDeserializationMetrics;
    /**
     * 更新指标
     */
    private updateMetrics;
}
/**
 * 格式处理器接口
 */
interface FormatHandler {
    serialize: (data: any, options?: SerializationOptions) => string;
    deserialize: (serialized: string, options?: SerializationOptions) => any;
}
export {};
//# sourceMappingURL=serializer.d.ts.map