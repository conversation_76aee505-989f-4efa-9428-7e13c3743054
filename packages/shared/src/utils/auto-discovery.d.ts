/**
 * 共享工具包 - 自动发现工具
 *
 * @description 自动发现和识别微前端应用
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * 应用发现配置
 */
export interface DiscoveryConfig {
    /** 扫描的根路径 */
    basePath?: string;
    /** 应用清单文件名 */
    manifestFile?: string;
    /** 自动扫描间隔（毫秒） */
    scanInterval?: number;
    /** 是否启用自动发现 */
    enabled?: boolean;
}
/**
 * 应用清单接口
 */
export interface AppManifest {
    name: string;
    version: string;
    entry: string;
    activeWhen: string;
    container?: string;
    framework?: string;
    dependencies?: string[];
    customProps?: Record<string, any>;
}
/**
 * 微应用配置接口
 */
export interface MicroAppConfig {
    name: string;
    entry: string;
    container?: string | HTMLElement;
    activeWhen?: string | ((location: Location) => boolean);
    customProps?: Record<string, any>;
}
/**
 * 自动发现管理器
 */
export declare class AutoDiscovery {
    private config;
    private discoveredApps;
    private scanTimer?;
    private isScanning;
    constructor(config?: DiscoveryConfig);
    /**
     * 启动自动发现
     */
    start(): void;
    /**
     * 停止自动发现
     */
    stop(): void;
    /**
     * 扫描应用
     */
    scan(): Promise<AppManifest[]>;
    /**
     * 获取已发现的应用
     */
    getDiscoveredApps(): AppManifest[];
    /**
     * 将清单转换为应用配置
     */
    manifestToConfig(manifest: AppManifest): MicroAppConfig;
    /**
     * 扫描应用实现
     */
    private scanForApps;
    /**
     * 加载已知的清单文件
     */
    private loadKnownManifests;
    /**
     * 从DOM中扫描应用
     */
    private scanFromDOM;
    /**
     * 从全局配置中扫描应用
     */
    private scanFromGlobalConfig;
    /**
     * 更新已发现的应用列表
     */
    private updateDiscoveredApps;
}
/**
 * 应用发现工具函数
 */
export declare const discoveryUtils: {
    /**
     * 验证应用清单
     */
    validateManifest(manifest: any): manifest is AppManifest;
    /**
     * 创建默认清单
     */
    createDefaultManifest(name: string, entry: string, activeWhen: string): AppManifest;
    /**
     * 从URL中提取应用信息
     */
    extractAppInfoFromUrl(url: string): Partial<AppManifest>;
};
/**
 * 创建自动发现实例
 */
export declare function createAutoDiscovery(config?: DiscoveryConfig): AutoDiscovery;
export default AutoDiscovery;
//# sourceMappingURL=auto-discovery.d.ts.map