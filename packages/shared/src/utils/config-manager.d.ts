/**
 * 共享工具包 - 配置管理工具
 *
 * @description 管理微前端项目的配置加载和管理
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * 配置来源枚举
 */
export declare enum ConfigSource {
    DEFAULT = "default",
    SCRIPT_TAG = "script-tag",
    GLOBAL_VARIABLE = "global-variable",
    LOCAL_STORAGE = "local-storage",
    URL_PARAMS = "url-params",
    REMOTE = "remote"
}
/**
 * 基础配置接口
 */
export interface BaseConfig {
    /** 开发模式 */
    development?: boolean;
    /** 调试模式 */
    debug?: boolean;
    /** 沙箱配置 */
    sandbox?: {
        enabled?: boolean;
        type?: string;
    };
    /** 其他配置 */
    [key: string]: any;
}
/**
 * 配置管理器
 */
export declare class ConfigManager<T extends BaseConfig = BaseConfig> {
    private config;
    private configSources;
    private isLoaded;
    private defaultConfig;
    constructor(defaultConfig?: Partial<T>);
    /**
     * 加载配置
     */
    load(): Promise<T>;
    /**
     * 获取配置
     */
    getConfig(): T;
    /**
     * 更新配置
     */
    updateConfig(updates: Partial<T>): void;
    /**
     * 重新加载配置
     */
    reload(): Promise<T>;
    /**
     * 从各种来源加载配置
     */
    private loadFromSources;
    /**
     * 获取默认配置
     */
    private getDefaultConfig;
    /**
     * 从script标签加载配置
     */
    private loadFromScriptTag;
    /**
     * 从全局变量加载配置
     */
    private loadFromGlobalVariable;
    /**
     * 从localStorage加载配置
     */
    private loadFromLocalStorage;
    /**
     * 从URL参数加载配置
     */
    private loadFromUrlParams;
    /**
     * 从远程加载配置
     */
    private loadFromRemote;
    /**
     * 合并配置
     */
    private mergeConfigs;
    /**
     * 深度合并对象
     */
    private deepMerge;
    /**
     * 检查是否为对象
     */
    private isObject;
    /**
     * 验证配置
     */
    private validateConfig;
    /**
     * 获取配置来源信息
     */
    getConfigSources(): Map<ConfigSource, any>;
}
/**
 * 配置工具函数
 */
export declare const configUtils: {
    /**
     * 验证配置对象
     */
    validateConfig(config: any): config is BaseConfig;
    /**
     * 创建默认配置
     */
    createDefaultConfig(): BaseConfig;
    /**
     * 安全解析JSON
     */
    safeParseJSON<T = any>(jsonString: string, defaultValue: T): T;
};
/**
 * 创建配置管理器
 */
export declare function createConfigManager<T extends BaseConfig = BaseConfig>(defaultConfig?: Partial<T>): ConfigManager<T>;
export default ConfigManager;
//# sourceMappingURL=config-manager.d.ts.map