/**
 * @fileoverview 类型检查工具函数
 * @description 提供各种类型检查和验证功能
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 检查是否为字符串
 */
export declare function isString(value: unknown): value is string;
/**
 * 检查是否为数字
 */
export declare function isNumber(value: unknown): value is number;
/**
 * 检查是否为布尔值
 */
export declare function isBoolean(value: unknown): value is boolean;
/**
 * 检查是否为函数
 */
export declare function isFunction(value: unknown): value is Function;
/**
 * 检查是否为对象
 */
export declare function isObject(value: unknown): value is Record<string, any>;
/**
 * 检查是否为数组
 */
export declare function isArray(value: unknown): value is any[];
/**
 * 检查是否为null
 */
export declare function isNull(value: unknown): value is null;
/**
 * 检查是否为undefined
 */
export declare function isUndefined(value: unknown): value is undefined;
/**
 * 检查是否已定义
 */
export declare function isDefined<T>(value: T | undefined | null): value is T;
/**
 * 检查是否为空值
 */
export declare function isEmpty(value: unknown): boolean;
/**
 * 检查是否为Promise
 */
export declare function isPromise(value: unknown): value is Promise<any>;
/**
 * 检查是否为Date对象
 */
export declare function isDate(value: unknown): value is Date;
/**
 * 检查是否为正则表达式
 */
export declare function isRegExp(value: unknown): value is RegExp;
/**
 * 检查是否为Error对象
 */
export declare function isError(value: unknown): value is Error;
/**
 * 检查是否为DOM元素
 */
export declare function isElement(value: unknown): value is Element;
/**
 * 检查是否为纯对象
 */
export declare function isPlainObject(value: unknown): value is Record<string, any>;
/**
 * 检查是否为原始类型
 */
export declare function isPrimitive(value: unknown): value is string | number | boolean | null | undefined;
/**
 * 获取值的类型
 */
export declare function getType(value: unknown): string;
/**
 * 检查是否为指定类型
 */
export declare function isType(value: unknown, type: string): boolean;
/**
 * 检查对象是否有指定属性
 */
export declare function hasProperty(obj: unknown, prop: string): boolean;
/**
 * 检查是否为有效的URL
 */
export declare function isValidUrl(value: unknown): value is string;
//# sourceMappingURL=type-check.d.ts.map