/**
 * 插件生命周期管理器
 *
 * @description 管理插件的完整生命周期
 * <AUTHOR> <<EMAIL>>
 */
import { EventEmitter } from '@micro-core/shared';
import { BasePlugin } from './base-plugin';
import type { PluginConfig, PluginLifecycleHook, PluginMetrics, PluginState } from './types';
/**
 * 插件实例信息
 */
interface PluginInstance {
    /** 插件ID */
    id: string;
    /** 插件实例 */
    instance: BasePlugin;
    /** 插件状态 */
    state: PluginState;
    /** 插件配置 */
    config: PluginConfig;
    /** 创建时间 */
    createdAt: number;
    /** 最后活动时间 */
    lastActivity: number;
    /** 性能指标 */
    metrics: PluginMetrics;
    /** 错误信息 */
    lastError?: Error;
}
/**
 * 生命周期钩子函数
 */
type LifecycleHookFunction = (pluginId: string, instance: BasePlugin) => Promise<void> | void;
/**
 * 插件生命周期管理器
 */
export declare class PluginLifecycleManager extends EventEmitter {
    private readonly logger;
    private readonly instances;
    private readonly hooks;
    private readonly stateTransitions;
    constructor();
    /**
     * 创建插件实例
     */
    createInstance(pluginId: string, pluginClass: typeof BasePlugin, config?: PluginConfig): Promise<BasePlugin>;
    /**
     * 加载插件
     */
    loadPlugin(pluginId: string): Promise<void>;
    /**
     * 激活插件
     */
    activatePlugin(pluginId: string): Promise<void>;
    /**
     * 停用插件
     */
    deactivatePlugin(pluginId: string): Promise<void>;
    /**
     * 卸载插件
     */
    unloadPlugin(pluginId: string): Promise<void>;
    /**
     * 销毁插件实例
     */
    destroyInstance(pluginId: string): Promise<void>;
    /**
     * 获取插件实例
     */
    getInstance(pluginId: string): BasePlugin | undefined;
    /**
     * 获取插件状态
     */
    getState(pluginId: string): PluginState | undefined;
    /**
     * 获取插件指标
     */
    getMetrics(pluginId: string): PluginMetrics | undefined;
    /**
     * 获取所有插件状态
     */
    getAllStates(): Record<string, PluginState>;
    /**
     * 获取指定状态的插件
     */
    getPluginsByState(state: PluginState): string[];
    /**
     * 检查插件是否存在
     */
    hasInstance(pluginId: string): boolean;
    /**
     * 添加生命周期钩子
     */
    addHook(hook: PluginLifecycleHook, fn: LifecycleHookFunction): void;
    /**
     * 移除生命周期钩子
     */
    removeHook(hook: PluginLifecycleHook, fn: LifecycleHookFunction): boolean;
    /**
     * 更新插件指标
     */
    updateMetrics(pluginId: string, metrics: Partial<PluginMetrics>): void;
    /**
     * 获取状态转换历史
     */
    getStateTransitions(pluginId: string): PluginState[];
    /**
     * 获取所有实例信息
     */
    getAllInstances(): Record<string, PluginInstance>;
    /**
     * 清理所有实例
     */
    cleanup(): Promise<void>;
    /**
     * 获取实例信息
     */
    private getInstanceInfo;
    /**
     * 更新插件状态
     */
    private updateState;
    /**
     * 执行生命周期钩子
     */
    private executeHooks;
    /**
     * 处理插件错误
     */
    private handlePluginError;
    /**
     * 初始化钩子
     */
    private initializeHooks;
}
export {};
//# sourceMappingURL=plugin-lifecycle.d.ts.map