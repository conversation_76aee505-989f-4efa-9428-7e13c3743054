/**
 * @fileoverview React 应用生命周期测试
 * <AUTHOR> <<EMAIL>>
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { bootstrap, getAppStatus, mount, unmount, update } from './index'

// 模拟 DOM 环境
const mockContainer = document.createElement('div')
mockContainer.id = 'test-container'
document.body.appendChild(mockContainer)

describe('React App Lifecycle', () => {
    beforeEach(() => {
        // 清理容器
        mockContainer.innerHTML = ''

        // 重置模块状态
        vi.resetModules()
    })

    afterEach(() => {
        // 清理容器
        mockContainer.innerHTML = ''
    })

    describe('bootstrap', () => {
        it('should bootstrap successfully', async () => {
            await expect(bootstrap()).resolves.toBeUndefined()

            const status = getAppStatus()
            expect(status.isBootstrapped).toBe(true)
            expect(status.name).toBe('sub-app-react')
            expect(status.framework).toBe('React')
        })

        it('should not bootstrap twice', async () => {
            await bootstrap()

            // 第二次调用应该被跳过
            const consoleSpy = vi.spyOn(console, 'warn')
            await bootstrap()

            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining('已经启动，跳过重复启动')
            )
        })
    })

    describe('mount', () => {
        it('should mount successfully', async () => {
            const props = {
                container: mockContainer,
                basename: '/test',
                theme: 'light'
            }

            await mount(props)

            const status = getAppStatus()
            expect(status.isMounted).toBe(true)
            expect(mockContainer.innerHTML).not.toBe('')
        })

        it('should bootstrap automatically if not bootstrapped', async () => {
            const status1 = getAppStatus()
            expect(status1.isBootstrapped).toBe(false)

            await mount({ container: mockContainer })

            const status2 = getAppStatus()
            expect(status2.isBootstrapped).toBe(true)
            expect(status2.isMounted).toBe(true)
        })

        it('should throw error if container not found', async () => {
            await expect(mount({ container: '#non-existent' })).rejects.toThrow(
                '找不到挂载容器'
            )
        })
    })

    describe('unmount', () => {
        it('should unmount successfully', async () => {
            // 先挂载
            await mount({ container: mockContainer })
            expect(getAppStatus().isMounted).toBe(true)

            // 再卸载
            await unmount()
            expect(getAppStatus().isMounted).toBe(false)
            expect(mockContainer.innerHTML).toBe('')
        })

        it('should handle unmount when not mounted', async () => {
            const consoleSpy = vi.spyOn(console, 'warn')
            await unmount()

            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining('未挂载，跳过卸载')
            )
        })
    })

    describe('update', () => {
        it('should update successfully', async () => {
            // 先挂载
            await mount({ container: mockContainer, theme: 'light' })
            expect(getAppStatus().isMounted).toBe(true)

            // 更新
            await update({ container: mockContainer, theme: 'dark' })
            expect(getAppStatus().isMounted).toBe(true)
        })

        it('should handle update when not mounted', async () => {
            const consoleSpy = vi.spyOn(console, 'warn')
            await update()

            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining('未挂载，无法更新')
            )
        })
    })

    describe('getAppStatus', () => {
        it('should return correct status', () => {
            const status = getAppStatus()

            expect(status).toEqual({
                name: 'sub-app-react',
                isBootstrapped: expect.any(Boolean),
                isMounted: expect.any(Boolean),
                framework: 'React',
                version: '18.2.0'
            })
        })
    })
})