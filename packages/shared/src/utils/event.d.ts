/**
 * @fileoverview 事件工具函数
 * @description 提供事件发射器相关的工具函数
 * <AUTHOR> <<EMAIL>>
 */
export interface EventEmitter {
    on(event: string, handler: (data?: any) => void): void;
    off(event: string, handler?: (data?: any) => void): void;
    once(event: string, handler: (data?: any) => void): void;
    emit(event: string, data?: any): void;
    clear(): void;
}
/**
 * 创建事件发射器
 */
export declare function createEventEmitter(): EventEmitter;
//# sourceMappingURL=event.d.ts.map