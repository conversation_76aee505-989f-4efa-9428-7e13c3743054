{"version": 3, "file": "sidecar-types.d.ts", "sourceRoot": "", "sources": ["sidecar-types.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,6BAA6B,CAAC;AAErE;;GAEG;AACH,MAAM,WAAW,aAAa;IAC1B,WAAW;IACX,EAAE,EAAE,MAAM,CAAC;IACX,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,UAAU;IACV,IAAI,EAAE,MAAM,CAAC;IACb,UAAU;IACV,EAAE,EAAE,MAAM,CAAC;IACX,WAAW;IACX,IAAI,CAAC,EAAE,GAAG,CAAC;IACX,UAAU;IACV,SAAS,EAAE,MAAM,CAAC;IAClB,YAAY;IACZ,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAClC;AAED;;GAEG;AACH,MAAM,WAAW,aAAa;IAC1B,WAAW;IACX,MAAM,CAAC,OAAO,EAAE,aAAa,GAAG,OAAO,CAAC;IACxC,WAAW;IACX,KAAK,EAAE,UAAU,EAAE,CAAC;CACvB;AAED;;GAEG;AACH,MAAM,WAAW,UAAU;IACvB,WAAW;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW;IACX,SAAS,EAAE,CAAC,OAAO,EAAE,aAAa,KAAK,OAAO,CAAC;IAC/C,WAAW;IACX,OAAO,EAAE,OAAO,CAAC;CACpB;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IACzB,WAAW;IACX,QAAQ,CAAC,EAAE,aAAa,GAAG,cAAc,GAAG,aAAa,CAAC;IAC1D,WAAW;IACX,MAAM,CAAC,EAAE,MAAM,GAAG,QAAQ,GAAG,YAAY,CAAC;IAC1C,WAAW;IACX,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,WAAW;IACX,UAAU,CAAC,EAAE,iBAAiB,CAAC;IAC/B,YAAY;IACZ,MAAM,CAAC,EAAE,aAAa,CAAC;IACvB,WAAW;IACX,KAAK,CAAC,EAAE;QACJ,WAAW,EAAE,MAAM,CAAC;QACpB,KAAK,EAAE,MAAM,CAAC;QACd,OAAO,EAAE,QAAQ,GAAG,aAAa,CAAC;QAClC,SAAS,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,OAAO,CAAC;KACxC,CAAC;CACL;AAED;;GAEG;AACH,MAAM,WAAW,WAAW;IACxB,YAAY;IACZ,YAAY,EAAE,MAAM,CAAC;IACrB,YAAY;IACZ,gBAAgB,EAAE,MAAM,CAAC;IACzB,YAAY;IACZ,cAAc,EAAE,MAAM,CAAC;IACvB,WAAW;IACX,cAAc,EAAE,MAAM,CAAC;CAC1B;AAED;;GAEG;AACH,MAAM,WAAW,OAAO;IACpB,UAAU;IACV,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5B,SAAS;IACT,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IACzB,WAAW;IACX,IAAI,CAAC,OAAO,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5C,WAAW;IACX,MAAM,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,aAAa,KAAK,IAAI,GAAG,MAAM,IAAI,CAAC;IAC/D,aAAa;IACb,QAAQ,IAAI,WAAW,CAAC;CAC3B;AAED;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACjC,WAAW;IACX,IAAI,EAAE,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACrC,aAAa;IACb,MAAM,EAAE,MAAM,CAAC;IACf,YAAY;IACZ,gBAAgB,EAAE,MAAM,EAAE,CAAC;IAC3B,eAAe;IACf,aAAa,EAAE,OAAO,CAAC;CAC1B;AAED;;GAEG;AACH,MAAM,WAAW,qBAAqB;IAClC,WAAW;IACX,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG,QAAQ,CAAC;IAChC,UAAU;IACV,SAAS,EAAE,MAAM,EAAE,CAAC;IACpB,UAAU;IACV,SAAS,EAAE,MAAM,EAAE,CAAC;IACpB,WAAW;IACX,MAAM,EAAE,OAAO,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,qBAAqB;IAClC,WAAW;IACX,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB,WAAW;IACX,OAAO,EAAE,MAAM,EAAE,CAAC;IAClB,WAAW;IACX,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CACnC;AAED;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACjC,eAAe;IACf,GAAG,EAAE,OAAO,CAAC;IACb,cAAc;IACd,MAAM,EAAE,OAAO,CAAC;IAChB,aAAa;IACb,SAAS,EAAE,MAAM,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,eAAe;IAC5B,WAAW;IACX,KAAK,CAAC,EAAE,oBAAoB,CAAC;IAC7B,WAAW;IACX,MAAM,CAAC,EAAE,qBAAqB,CAAC;IAC/B,aAAa;IACb,MAAM,CAAC,EAAE,qBAAqB,CAAC;IAC/B,WAAW;IACX,KAAK,CAAC,EAAE,oBAAoB,CAAC;CAChC;AAED;;GAEG;AACH,MAAM,WAAW,mBAAmB;IAChC,UAAU;IACV,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5B,SAAS;IACT,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IACzB,WAAW;IACX,OAAO,CAAC,CAAC,GAAG,OAAO,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IAC/C,aAAa;IACb,SAAS,CAAC,CAAC,GAAG,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;IACrD,aAAa;IACb,SAAS,CAAC,CAAC,GAAG,OAAO,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC;IACpD,aAAa;IACb,YAAY,IAAI,IAAI,CAAC;IACrB,aAAa;IACb,mBAAmB,CAAC,OAAO,EAAE,WAAW,GAAG,IAAI,CAAC;IAChD,aAAa;IACb,oBAAoB,CAAC,OAAO,EAAE,WAAW,GAAG,IAAI,CAAC;CACpD;AAED;;GAEG;AACH,MAAM,WAAW,cAAc;IAC3B,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC;IACjC,UAAU,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,KAAK,OAAO,CAAC,CAAC;IACxD,WAAW,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CACrC;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB;IAC/B,WAAW;IACX,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,WAAW;IACX,GAAG,CAAC,EAAE;QACF,IAAI,EAAE,MAAM,CAAC;QACb,OAAO,EAAE,MAAM,CAAC;QAChB,KAAK,EAAE,MAAM,CAAC;QACd,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;KACtB,CAAC;IACF,aAAa;IACb,IAAI,CAAC,EAAE,cAAc,EAAE,CAAC;IACxB,aAAa;IACb,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,YAAY;IACZ,SAAS,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC;IACjC,eAAe;IACf,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,WAAW;IACX,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,YAAY;IACZ,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;IACtC,WAAW;IACX,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,MAAM,aAAa,GAAG,MAAM,GAAG,UAAU,GAAG,SAAS,GAAG,UAAU,GAAG,SAAS,GAAG,OAAO,CAAC;AAE/F;;GAEG;AACH,MAAM,WAAW,YAAY;IACzB,WAAW;IACX,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,WAAW;IACX,MAAM,EAAE,MAAM,CAAC;IACf,YAAY;IACZ,aAAa,EAAE,MAAM,CAAC;IACtB,YAAY;IACZ,YAAY,EAAE,MAAM,CAAC;IACrB,YAAY;IACZ,gBAAgB,EAAE,MAAM,CAAC;IACzB,UAAU;IACV,MAAM,EAAE,MAAM,CAAC;CAClB"}