# 路由 API (第一部分)

Micro-Core 提供了强大的路由系统，用于管理微应用的路由和导航。本文档详细介绍了路由 API 的使用方法和最佳实践。

## 基本概念

在微前端架构中，路由系统负责以下任务：

1. **路由匹配**：根据当前 URL 确定应该激活哪个微应用
2. **路由导航**：在不同微应用之间进行导航
3. **路由状态同步**：保持主应用和微应用之间的路由状态同步
4. **路由拦截**：拦截路由变化，执行权限检查等逻辑

## Router

`Router` 是 Micro-Core 提供的核心路由类，用于创建和管理路由系统。

### 基本用法

```typescript
import { Router } from '@micro-core/core';

// 创建路由实例
const router = new Router({
  mode: 'history',
  base: '/app',
  routes: [
    {
      path: '/',
      name: 'home',
      component: 'home-app'
    },
    {
      path: '/users/:userId',
      name: 'user-detail',
      component: 'user-app'
    },
    {
      path: '/settings',
      name: 'settings',
      component: 'settings-app',
      meta: {
        requiresAuth: true
      }
    }
  ]
});

// 监听路由变化
router.beforeEach((to, from, next) => {
  console.log(`路由从 ${from.path} 变化到 ${to.path}`);
  
  // 检查权限
  if (to.meta.requiresAuth && !isAuthenticated()) {
    // 重定向到登录页
    next('/login');
  } else {
    // 继续导航
    next();
  }
});

// 导航到指定路由
router.push('/users/123');

// 替换当前路由
router.replace('/settings');

// 获取当前路由
const currentRoute = router.currentRoute;
console.log('当前路径:', currentRoute.path);
console.log('路由参数:', currentRoute.params);
console.log('查询参数:', currentRoute.query);
```

### API 参考

#### 构造函数

创建路由实例。

```typescript
constructor(options: RouterOptions)
```

**参数：**
- `options` (RouterOptions): 路由配置选项
  - `mode` (string): 路由模式，可选值为 'hash' 或 'history'，默认为 'history'
  - `base` (string): 应用的基础路径，默认为 '/'
  - `routes` (RouteConfig[]): 路由配置数组
  - `scrollBehavior` (Function): 控制滚动行为的函数
  - `parseQuery` (Function): 自定义查询字符串解析函数
  - `stringifyQuery` (Function): 自定义查询字符串生成函数
  - `fallback` (boolean): 当浏览器不支持 history.pushState 时是否回退到 hash 模式，默认为 true

**示例：**

```typescript
// 基本用法
const router = new Router({
  mode: 'history',
  base: '/app',
  routes: [
    { path: '/', component: 'home-app' },
    { path: '/users/:userId', component: 'user-app' }
  ]
});

// 完整配置
const router = new Router({
  mode: 'history',
  base: '/app',
  routes: [
    {
      path: '/',
      name: 'home',
      component: 'home-app',
      meta: { title: '首页' }
    },
    {
      path: '/users/:userId',
      name: 'user-detail',
      component: 'user-app',
      props: true,
      meta: { title: '用户详情' }
    },
    {
      path: '/settings',
      name: 'settings',
      component: 'settings-app',
      meta: { requiresAuth: true, title: '设置' },
      children: [
        {
          path: 'profile',
          name: 'profile',
          component: 'profile-app',
          meta: { title: '个人资料' }
        },
        {
          path: 'security',
          name: 'security',
          component: 'security-app',
          meta: { title: '安全设置' }
        }
      ]
    },
    {
      path: '*',
      component: 'not-found-app'
    }
  ],
  scrollBehavior: (to, from, savedPosition) => {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { x: 0, y: 0 };
    }
  },
  parseQuery: (query) => {
    // 自定义查询字符串解析
    return customParseQuery(query);
  },
  stringifyQuery: (query) => {
    // 自定义查询字符串生成
    return customStringifyQuery(query);
  },
  fallback: true
});
```

#### push(location, onComplete?, onAbort?)

导航到指定路由。

```typescript
push(location: RawLocation, onComplete?: Function, onAbort?: Function): void
```

**参数：**
- `location` (RawLocation): 目标路由位置，可以是字符串路径或者路由位置对象
- `onComplete` (Function): 可选，导航成功完成时的回调函数
- `onAbort` (Function): 可选，导航中止时的回调函数

**示例：**

```typescript
// 使用字符串路径
router.push('/users/123');

// 使用路由位置对象
router.push({
  path: '/users/123',
  query: { tab: 'profile' }
});

// 使用命名路由
router.push({
  name: 'user-detail',
  params: { userId: '123' },
  query: { tab: 'profile' }
});

// 带回调函数
router.push('/settings', 
  () => {
    console.log('导航成功完成');
    showSettingsUI();
  },
  (error) => {
    console.error('导航被中止:', error);
    showErrorMessage(error.message);
  }
);
```

#### replace(location, onComplete?, onAbort?)

替换当前路由。

```typescript
replace(location: RawLocation, onComplete?: Function, onAbort?: Function): void
```

**参数：**
- `location` (RawLocation): 目标路由位置，可以是字符串路径或者路由位置对象
- `onComplete` (Function): 可选，导航成功完成时的回调函数
- `onAbort` (Function): 可选，导航中止时的回调函数

**示例：**

```typescript
// 使用字符串路径
router.replace('/users/123');

// 使用路由位置对象
router.replace({
  path: '/users/123',
  query: { tab: 'profile' }
});

// 使用命名路由
router.replace({
  name: 'user-detail',
  params: { userId: '123' },
  query: { tab: 'profile' }
});

// 带回调函数
router.replace('/settings', 
  () => {
    console.log('导航成功完成');
    showSettingsUI();
  },
  (error) => {
    console.error('导航被中止:', error);
    showErrorMessage(error.message);
  }
);
```

#### go(n)

导航到历史堆栈中的某个位置。

```typescript
go(n: number): void
```

**参数：**
- `n` (number): 相对于当前页面的历史堆栈位置，正数表示前进，负数表示后退

**示例：**

```typescript
// 后退一步
router.go(-1);

// 前进一步
router.go(1);

// 前进两步
router.go(2);

// 后退两步
router.go(-2);
```

#### back()

后退一步。

```typescript
back(): void
```

**参数：** 无

**示例：**

```typescript
// 后退一步
router.back();

// 在用户点击后退按钮时使用
backButton.addEventListener('click', () => {
  router.back();
});
```

#### forward()

前进一步。

```typescript
forward(): void
```

**参数：** 无

**示例：**

```typescript
// 前进一步
router.forward();

// 在用户点击前进按钮时使用
forwardButton.addEventListener('click', () => {
  router.forward();
});
```

#### beforeEach(guard)

添加全局前置守卫。

```typescript
beforeEach(guard: NavigationGuard): Function
```

**参数：**
- `guard` (NavigationGuard): 导航守卫函数，接收 to、from 和 next 三个参数

**返回值：** 移除已注册守卫的函数

**示例：**

```typescript
// 添加全局前置守卫
const unregister = router.beforeEach((to, from, next) => {
  console.log(`路由从 ${from.path} 变化到 ${to.path}`);
  
  // 检查权限
  if (to.meta.requiresAuth && !isAuthenticated()) {
    // 重定向到登录页
    next('/login');
  } else {
    // 继续导航
    next();
  }
});

// 移除守卫
unregister();

// 权限检查守卫
router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth) {
    if (!isAuthenticated()) {
      // 保存目标路由
      saveTargetRoute(to.fullPath);
      // 重定向到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      });
    } else if (to.meta.roles && !hasRole(to.meta.roles)) {
      // 检查角色权限
      next('/403'); // 无权限页面
    } else {
      // 有权限，继续导航
      next();
    }
  } else {
    // 不需要权限，继续导航
    next();
  }
});

// 进度条守卫
router.beforeEach((to, from, next) => {
  // 启动进度条
  startProgressBar();
  next();
});
```

#### afterEach(hook)

添加全局后置钩子。

```typescript
afterEach(hook: (to: Route, from: Route) => void): Function
```

**参数：**
- `hook` ((to: Route, from: Route) => void): 后置钩子函数，接收 to 和 from 两个参数

**返回值：** 移除已注册钩子的函数

**示例：**

```typescript
// 添加全局后置钩子
const unregister = router.afterEach((to, from) => {
  console.log(`路由导航完成: ${from.path} -> ${to.path}`);
  
  // 更新页面标题
  document.title = to.meta.title || '默认标题';
});

// 移除钩子
unregister();

// 进度条钩子
router.afterEach((to, from) => {
  // 结束进度条
  stopProgressBar();
});

// 分析钩子
router.afterEach((to, from) => {
  // 发送页面浏览事件
  trackPageView({
    path: to.path,
    title: to.meta.title,
    referrer: from.path
  });
});
```

#### beforeResolve(guard)

添加全局解析守卫。

```typescript
beforeResolve(guard: NavigationGuard): Function
```

**参数：**
- `guard` (NavigationGuard): 导航守卫函数，接收 to、from 和 next 三个参数

**返回值：** 移除已注册守卫的函数

**示例：**

```typescript
// 添加全局解析守卫
const unregister = router.beforeResolve((to, from, next) => {
  // 在导航被确认之前，组件已经被解析
  console.log('路由解析中...');
  
  // 异步数据预加载
  if (to.meta.fetchData) {
    loadAsyncData(to.params.id)
      .then(data => {
        // 将数据附加到路由上
        to.meta.data = data;
        next();
      })
      .catch(error => {
        console.error('数据加载失败:', error);
        next(false); // 中止导航
      });
  } else {
    next();
  }
});

// 移除守卫
unregister();
```

#### onError(callback)

注册错误处理回调。

```typescript
onError(callback: (error: Error) => void): Function
```

**参数：**
- `callback` ((error: Error) => void): 错误处理回调函数

**返回值：** 移除已注册回调的函数

**示例：**

```typescript
// 注册错误处理回调
const unregister = router.onError((error) => {
  console.error('路由错误:', error);
  
  // 显示错误通知
  showErrorNotification(`路由导航失败: ${error.message}`);
  
  // 上报错误
  reportError({
    type: 'router_error',
    message: error.message,
    stack: error.stack
  });
});

// 移除回调
unregister();
```

#### addRoutes(routes)

动态添加路由规则。

```typescript
addRoutes(routes: RouteConfig[]): void
```

**参数：**
- `routes` (RouteConfig[]): 路由配置数组

**示例：**

```typescript
// 动态添加路由
router.addRoutes([
  {
    path: '/admin',
    name: 'admin',
    component: 'admin-app',
    meta: { requiresAuth: true, roles: ['admin'] },
    children: [
      {
        path: 'users',
        name: 'admin-users',
        component: 'admin-users-app'
      },
      {
        path: 'settings',
        name: 'admin-settings',
        component: 'admin-settings-app'
      }
    ]
  }
]);

// 基于用户角色动态添加路由
function addRoutesBasedOnUserRole(role) {
  let routes = [];
  
  if (role === 'admin') {
    routes = adminRoutes;
  } else if (role === 'manager') {
    routes = managerRoutes;
  } else {
    routes = userRoutes;
  }
  
  router.addRoutes(routes);
}

// 在用户登录后调用
function onUserLogin(user) {
  addRoutesBasedOnUserRole(user.role);
}
```

#### getMatchedComponents(location?)

返回指定路由匹配的组件数组。

```typescript
getMatchedComponents(location?: RawLocation): Array<string>
```

**参数：**
- `location` (RawLocation): 可选，路由位置，默认为当前路由

**返回值：** 匹配的组件数组

**示例：**

```typescript
// 获取当前路由匹配的组件
const components = router.getMatchedComponents();
console.log('当前路由匹配的组件:', components);

// 获取指定路由匹配的组件
const components = router.getMatchedComponents('/users/123');
console.log('指定路由匹配的组件:', components);

// 预加载组件
function preloadComponents(path) {
  const components = router.getMatchedComponents(path);
  components.forEach(component => {
    // 预加载组件
    loadComponent(component);
  });
}
```

#### resolve(location, current?, append?)

解析目标位置。

```typescript
resolve(location: RawLocation, current?: Route, append?: boolean): {
  location: Location;
  route: Route;
  href: string;
}
```

**参数：**
- `location` (RawLocation): 目标路由位置
- `current` (Route): 可选，当前路由，默认为当前路由
- `append` (boolean): 可选，是否追加路径，默认为 false

**返回值：** 包含解析后的位置、路由和 href 的对象

**示例：**

```typescript
// 解析路由
const resolved = router.resolve('/users/123');
console.log('解析后的 href:', resolved.href);
console.log('解析后的路由:', resolved.route);

// 解析命名路由
const resolved = router.resolve({
  name: 'user-detail',
  params: { userId: '123' }
});
console.log('解析后的 href:', resolved.href);

// 在生成链接时使用
function generateLink(location) {
  const resolved = router.resolve(location);
  return resolved.href;
}

const userLink = generateLink({
  name: 'user-detail',
  params: { userId: '123' }
});
console.log('用户链接:', userLink);