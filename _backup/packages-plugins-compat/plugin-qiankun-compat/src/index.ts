/**
 * qiankun 兼容插件
 * 提供与 qiankun 兼容的 API 和行为
 */

import type { AppConfig, MicroCoreKernel, Plugin } from '@micro-core/core';

/**
 * qiankun 兼容配置选项
 */
export interface QiankunCompatOptions {
    /** 是否启用沙箱 */
    sandbox?: boolean | { strictStyleIsolation?: boolean; experimentalStyleIsolation?: boolean };
    /** 是否启用单例模式 */
    singular?: boolean;
    /** 预加载策略 */
    prefetch?: boolean | 'all' | string[] | ((apps: any[]) => any[]);
    /** 获取公共路径 */
    getPublicPath?: (entry: string) => string;
    /** 获取模板 */
    getTemplate?: (tpl: string) => string;
    /** 是否启用日志 */
    enableLogging?: boolean;
}

/**
 * qiankun 应用配置
 */
export interface QiankunAppConfig {
    /** 应用名称 */
    name: string;
    /** 应用入口 */
    entry: string | { scripts?: string[]; styles?: string[]; html?: string };
    /** 挂载容器 */
    container: string | HTMLElement;
    /** 激活规则 */
    activeRule: string | ((location: Location) => boolean) | Array<string | ((location: Location) => boolean)>;
    /** 应用加载器 */
    loader?: (loading: boolean) => void;
    /** 自定义属性 */
    props?: Record<string, any>;
}

/**
 * qiankun 生命周期函数
 */
export interface QiankunLifecycles {
    beforeLoad?: (app: QiankunAppConfig) => Promise<any> | any;
    beforeMount?: (app: QiankunAppConfig) => Promise<any> | any;
    afterMount?: (app: QiankunAppConfig) => Promise<any> | any;
    beforeUnmount?: (app: QiankunAppConfig) => Promise<any> | any;
    afterUnmount?: (app: QiankunAppConfig) => Promise<any> | any;
}

/**
 * 全局状态管理
 */
class GlobalStateManager {
    private state: Record<string, any> = {};
    private listeners: Array<(state: Record<string, any>, prevState: Record<string, any>) => void> = [];

    /**
     * 设置全局状态
     */
    setGlobalState(state: Record<string, any>): boolean {
        const prevState = { ...this.state };
        this.state = { ...this.state, ...state };

        // 通知所有监听器
        this.listeners.forEach(listener => {
            try {
                listener(this.state, prevState);
            } catch (error) {
                console.error('[QiankunCompat] 全局状态监听器执行错误:', error);
            }
        });

        return true;
    }

    /**
     * 获取全局状态
     */
    getGlobalState(): Record<string, any> {
        return { ...this.state };
    }

    /**
     * 添加状态监听器
     */
    onGlobalStateChange(callback: (state: Record<string, any>, prevState: Record<string, any>) => void): () => void {
        this.listeners.push(callback);

        // 返回取消监听的函数
        return () => {
            const index = this.listeners.indexOf(callback);
            if (index > -1) {
                this.listeners.splice(index, 1);
            }
        };
    }

    /**
     * 清空状态
     */
    clearGlobalState(): void {
        const prevState = { ...this.state };
        this.state = {};

        this.listeners.forEach(listener => {
            try {
                listener(this.state, prevState);
            } catch (error) {
                console.error('[QiankunCompat] 全局状态监听器执行错误:', error);
            }
        });
    }
}

/**
 * qiankun 兼容插件类
 */
export class QiankunCompatPlugin implements Plugin {
    name = 'qiankun-compat';
    version = '1.0.0';

    private kernel?: MicroCoreKernel;
    private options: QiankunCompatOptions;
    private globalStateManager = new GlobalStateManager();
    private registeredApps: Map<string, QiankunAppConfig> = new Map();
    private isStarted = false;

    constructor(options: QiankunCompatOptions = {}) {
        this.options = {
            sandbox: true,
            singular: false,
            prefetch: true,
            enableLogging: false,
            ...options
        };
    }

    /**
     * 安装插件
     */
    install(kernel: MicroCoreKernel): void {
        this.kernel = kernel;

        // 注册 qiankun 兼容 API 到全局
        this.setupGlobalAPI();

        if (this.options.enableLogging) {
            console.log('[QiankunCompatPlugin] qiankun 兼容插件已安装');
        }
    }

    /**
     * 卸载插件
     */
    uninstall(kernel: MicroCoreKernel): void {
        // 清理全局 API
        this.cleanupGlobalAPI();

        // 清空注册的应用
        this.registeredApps.clear();

        // 清空全局状态
        this.globalStateManager.clearGlobalState();

        this.kernel = undefined;

        if (this.options.enableLogging) {
            console.log('[QiankunCompatPlugin] qiankun 兼容插件已卸载');
        }
    }

    /**
     * 设置全局 API
     */
    private setupGlobalAPI(): void {
        // 注册微应用
        (window as any).registerMicroApps = this.registerMicroApps.bind(this);

        // 启动 qiankun
        (window as any).start = this.start.bind(this);

        // 手动加载微应用
        (window as any).loadMicroApp = this.loadMicroApp.bind(this);

        // 初始化全局状态
        (window as any).initGlobalState = this.initGlobalState.bind(this);

        // 设置默认挂载应用
        (window as any).setDefaultMountApp = this.setDefaultMountApp.bind(this);

        // 运行时公共路径配置
        (window as any).runAfterFirstMounted = this.runAfterFirstMounted.bind(this);
    }

    /**
     * 清理全局 API
     */
    private cleanupGlobalAPI(): void {
        delete (window as any).registerMicroApps;
        delete (window as any).start;
        delete (window as any).loadMicroApp;
        delete (window as any).initGlobalState;
        delete (window as any).setDefaultMountApp;
        delete (window as any).runAfterFirstMounted;
    }

    /**
     * 注册微应用
     */
    registerMicroApps(
        apps: QiankunAppConfig[],
        lifeCycles?: QiankunLifecycles
    ): void {
        if (!this.kernel) {
            throw new Error('qiankun 兼容插件未正确安装');
        }

        apps.forEach(app => {
            // 转换为 Micro-Core 应用配置
            const microCoreConfig: AppConfig = {
                name: app.name,
                entry: typeof app.entry === 'string' ? app.entry : app.entry.html || '',
                container: typeof app.container === 'string' ? app.container : app.container.id || 'container',
                activeWhen: this.convertActiveRule(app.activeRule),
                props: app.props || {}
            };

            // 注册到 Micro-Core
            this.kernel.registerApp(microCoreConfig);

            // 保存原始配置
            this.registeredApps.set(app.name, app);

            if (this.options.enableLogging) {
                console.log(`[QiankunCompat] 已注册微应用: ${app.name}`);
            }
        });

        // 设置生命周期钩子
        if (lifeCycles) {
            this.setupLifecycleHooks(lifeCycles);
        }
    }

    /**
     * 启动 qiankun
     */
    start(options?: {
        prefetch?: boolean | 'all' | string[];
        sandbox?: boolean | { strictStyleIsolation?: boolean };
        singular?: boolean;
        urlRerouteOnly?: boolean;
    }): void {
        if (!this.kernel) {
            throw new Error('qiankun 兼容插件未正确安装');
        }

        if (this.isStarted) {
            console.warn('[QiankunCompat] qiankun 已经启动');
            return;
        }

        // 合并配置
        const mergedOptions = { ...this.options, ...options };

        // 启动 Micro-Core
        this.kernel.start();

        this.isStarted = true;

        if (this.options.enableLogging) {
            console.log('[QiankunCompat] qiankun 已启动', mergedOptions);
        }
    }

    /**
     * 手动加载微应用
     */
    loadMicroApp(
        app: QiankunAppConfig,
        configuration?: any
    ): { mount: () => Promise<any>; unmount: () => Promise<any>; update: (props: any) => Promise<any> } {
        if (!this.kernel) {
            throw new Error('qiankun 兼容插件未正确安装');
        }

        const microCoreConfig: AppConfig = {
            name: app.name,
            entry: typeof app.entry === 'string' ? app.entry : app.entry.html || '',
            container: typeof app.container === 'string' ? app.container : app.container.id || 'container',
            activeWhen: () => true, // 手动加载的应用总是激活
            props: app.props || {}
        };

        // 注册并加载应用
        this.kernel.registerApp(microCoreConfig);

        return {
            mount: async () => {
                await this.kernel.loadApp(app.name);
                return this.kernel.mountApp(app.name);
            },
            unmount: async () => {
                return this.kernel.unmountApp(app.name);
            },
            update: async (props: any) => {
                // 更新应用属性
                const currentApp = this.kernel.getApp(app.name);
                if (currentApp) {
                    currentApp.props = { ...currentApp.props, ...props };
                }
                return Promise.resolve();
            }
        };
    }

    /**
     * 初始化全局状态
     */
    initGlobalState(state: Record<string, any> = {}) {
        this.globalStateManager.setGlobalState(state);

        return {
            setGlobalState: this.globalStateManager.setGlobalState.bind(this.globalStateManager),
            onGlobalStateChange: this.globalStateManager.onGlobalStateChange.bind(this.globalStateManager),
            getGlobalState: this.globalStateManager.getGlobalState.bind(this.globalStateManager)
        };
    }

    /**
     * 设置默认挂载应用
     */
    setDefaultMountApp(appLink: string): void {
        if (this.options.enableLogging) {
            console.log(`[QiankunCompat] 设置默认挂载应用: ${appLink}`);
        }
        // 这里可以实现默认应用的逻辑
    }

    /**
     * 第一个微应用 mount 后需要调用的方法
     */
    runAfterFirstMounted(effect: () => void): void {
        if (typeof effect === 'function') {
            // 监听第一个应用挂载完成
            setTimeout(effect, 0);
        }
    }

    /**
     * 转换激活规则
     */
    private convertActiveRule(
        activeRule: string | ((location: Location) => boolean) | Array<string | ((location: Location) => boolean)>
    ): (location: Location) => boolean {
        if (typeof activeRule === 'string') {
            return (location: Location) => location.pathname.startsWith(activeRule);
        }

        if (typeof activeRule === 'function') {
            return activeRule;
        }

        if (Array.isArray(activeRule)) {
            return (location: Location) => {
                return activeRule.some(rule => {
                    if (typeof rule === 'string') {
                        return location.pathname.startsWith(rule);
                    }
                    if (typeof rule === 'function') {
                        return rule(location);
                    }
                    return false;
                });
            };
        }

        return () => false;
    }

    /**
     * 设置生命周期钩子
     */
    private setupLifecycleHooks(lifeCycles: QiankunLifecycles): void {
        if (!this.kernel) return;

        // 这里需要根据 Micro-Core 的实际 API 来实现生命周期钩子
        // 由于当前 kernel 类型定义可能不完整，这里提供一个示例实现

        if (lifeCycles.beforeLoad) {
            // kernel.onBeforeLoad?.(lifeCycles.beforeLoad);
        }

        if (lifeCycles.beforeMount) {
            // kernel.onBeforeMount?.(lifeCycles.beforeMount);
        }

        if (lifeCycles.afterMount) {
            // kernel.onAfterMount?.(lifeCycles.afterMount);
        }

        if (lifeCycles.beforeUnmount) {
            // kernel.onBeforeUnmount?.(lifeCycles.beforeUnmount);
        }

        if (lifeCycles.afterUnmount) {
            // kernel.onAfterUnmount?.(lifeCycles.afterUnmount);
        }
    }
}

/**
 * 创建 qiankun 兼容插件实例
 */
export function createQiankunCompatPlugin(options?: QiankunCompatOptions): QiankunCompatPlugin {
    return new QiankunCompatPlugin(options);
}

/**
 * 默认导出
 */
export default QiankunCompatPlugin;