/**
 * 事件总线系统实现
 *
 * @description 高性能的发布订阅模式事件总线
 * <AUTHOR> <<EMAIL>>
 */
import type { EventBusConfig, EventBusMetrics, EventBusOptions, EventHandler, EventSubscription } from './types';
/**
 * 事件总线实现
 * 提供高性能的发布订阅模式事件系统
 */
export declare class EventBus {
    private readonly logger;
    private readonly config;
    private readonly listeners;
    private readonly onceListeners;
    private readonly wildcardListeners;
    private readonly metrics;
    private readonly eventQueue;
    private isProcessingQueue;
    constructor(config?: EventBusConfig);
    /**
     * 订阅事件
     */
    on(event: string, handler: EventHandler, options?: EventBusOptions): EventSubscription;
    /**
     * 订阅事件（仅触发一次）
     */
    once(event: string, handler: EventHandler, options?: EventBusOptions): EventSubscription;
    /**
     * 取消订阅
     */
    off(subscription: EventSubscription): boolean;
    off(event: string, handler?: EventHandler): boolean;
    /**
     * 发布事件
     */
    emit(event: string, data?: any): Promise<boolean>;
    /**
     * 同步发布事件
     */
    emitSync(event: string, data?: any): boolean;
    /**
     * 获取事件监听器数量
     */
    listenerCount(event: string): number;
    /**
     * 获取所有事件名称
     */
    eventNames(): string[];
    /**
     * 移除所有监听器
     */
    removeAllListeners(event?: string): void;
    /**
     * 获取性能指标
     */
    getMetrics(): EventBusMetrics;
    /**
     * 销毁事件总线
     */
    destroy(): void;
    /**
     * 验证事件名称
     */
    private validateEventName;
    /**
     * 验证处理器
     */
    private validateHandler;
    /**
     * 检查监听器数量限制
     */
    private checkListenerLimit;
    /**
     * 生成订阅ID
     */
    private generateSubscriptionId;
    /**
     * 移除订阅
     */
    private removeSubscription;
    /**
     * 通过事件名和处理器移除订阅
     */
    private removeByEventAndHandler;
    /**
     * 从监听器集合中移除处理器
     */
    private removeHandlerFromListeners;
    /**
     * 获取事件监听器
     */
    private getEventListeners;
    /**
     * 匹配通配符
     */
    private matchWildcard;
    /**
     * 将事件加入队列
     */
    private enqueueEvent;
    /**
     * 处理事件队列
     */
    private processEventQueue;
    /**
     * 处理事件
     */
    private processEvent;
    /**
     * 异步执行监听器
     */
    private executeListenersAsync;
    /**
     * 同步执行监听器
     */
    private executeListenersSync;
    /**
     * 安全执行处理器
     */
    private executeHandlerSafely;
    /**
     * 执行处理器
     */
    private executeHandler;
    /**
     * 移除一次性监听器
     */
    private removeOnceListeners;
    /**
     * 更新事件指标
     */
    private updateEventMetrics;
    /**
     * 更新指标
     */
    private updateMetrics;
}
//# sourceMappingURL=event-bus.d.ts.map