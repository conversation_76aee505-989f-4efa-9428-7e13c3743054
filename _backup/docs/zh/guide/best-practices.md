# 最佳实践

本指南汇总了使用 Micro-Core 开发微前端应用的最佳实践，帮助您构建高质量、可维护的微前端系统。

## 架构设计原则

### 1. 单一职责原则

每个微应用应该专注于一个业务领域：

```typescript
// ✅ 好的设计
const userManagementApp = {
  name: 'user-management',
  routes: ['/users/*'],
  responsibilities: ['用户CRUD', '权限管理', '用户认证']
}

const orderManagementApp = {
  name: 'order-management', 
  routes: ['/orders/*'],
  responsibilities: ['订单处理', '支付管理', '物流跟踪']
}

// ❌ 避免的设计
const monolithApp = {
  name: 'everything-app',
  routes: ['/*'],
  responsibilities: ['用户管理', '订单处理', '商品管理', '支付', '物流...']
}
```

### 2. 松耦合设计

微应用之间应该通过标准化接口通信：

```typescript
// ✅ 通过事件总线通信
import { EventBus } from '@micro-core/core'

// 发送事件
EventBus.emit('user:login', { userId: '123', userName: 'John' })

// 监听事件
EventBus.on('user:login', (userData) => {
  // 处理用户登录事件
})

// ❌ 避免直接调用其他应用的方法
// window.userApp.login() // 紧耦合，难以维护
```

### 3. 渐进式迁移

从单体应用迁移到微前端应该是渐进式的：

```typescript
// 阶段1：路由级别拆分
const migrationPlan = {
  phase1: {
    routes: ['/admin/*'],
    strategy: 'iframe' // 最小风险
  },
  phase2: {
    routes: ['/dashboard/*'],
    strategy: 'sandbox' // 更好的集成
  },
  phase3: {
    routes: ['/users/*', '/orders/*'],
    strategy: 'native' // 完全集成
  }
}
```

## 应用拆分策略

### 按业务域拆分

```typescript
// 电商系统拆分示例
const microApps = [
  {
    name: 'product-catalog',
    domain: '商品管理',
    routes: ['/products/*', '/categories/*'],
    team: 'product-team'
  },
  {
    name: 'user-center',
    domain: '用户中心', 
    routes: ['/profile/*', '/settings/*'],
    team: 'user-team'
  },
  {
    name: 'order-system',
    domain: '订单系统',
    routes: ['/orders/*', '/checkout/*'],
    team: 'order-team'
  }
]
```

### 按技术栈拆分

```typescript
// 技术栈混合示例
const techStackApps = [
  {
    name: 'legacy-admin',
    framework: 'jQuery + Bootstrap',
    strategy: 'iframe', // 隔离遗留系统
    routes: ['/legacy-admin/*']
  },
  {
    name: 'modern-dashboard',
    framework: 'React + Ant Design',
    strategy: 'sandbox',
    routes: ['/dashboard/*']
  },
  {
    name: 'mobile-app',
    framework: 'Vue 3 + Vant',
    strategy: 'native',
    routes: ['/mobile/*']
  }
]
```

## 状态管理最佳实践

### 1. 状态隔离

```typescript
// ✅ 每个应用管理自己的状态
class UserApp {
  private store = new Vuex.Store({
    state: {
      currentUser: null,
      permissions: []
    }
  })
  
  // 通过事件共享必要状态
  onUserLogin(user) {
    this.store.commit('setCurrentUser', user)
    EventBus.emit('user:login', { userId: user.id })
  }
}

// ❌ 避免全局状态污染
// window.globalState = {} // 容易造成状态混乱
```

### 2. 共享状态管理

```typescript
// 使用 Micro-Core 的状态管理
import { GlobalState } from '@micro-core/core'

// 定义共享状态
const sharedState = GlobalState.create({
  user: {
    id: null,
    name: '',
    permissions: []
  },
  theme: 'light',
  locale: 'zh-CN'
})

// 应用中使用共享状态
class OrderApp {
  mounted() {
    // 订阅用户状态变化
    sharedState.subscribe('user', (user) => {
      this.updateUserContext(user)
    })
  }
}
```

## 路由管理

### 1. 路由规划

```typescript
// ✅ 清晰的路由规划
const routeConfig = {
  '/': 'main-app',           // 主应用
  '/users/*': 'user-app',    // 用户管理
  '/orders/*': 'order-app',  // 订单管理
  '/admin/*': 'admin-app',   // 后台管理
  '/mobile/*': 'mobile-app'  // 移动端
}

// ❌ 避免路由冲突
const badRouteConfig = {
  '/admin/*': 'admin-app',
  '/admin/users/*': 'user-app' // 冲突！
}
```

### 2. 路由守卫

```typescript
// 全局路由守卫
import { Router } from '@micro-core/plugin-router'

Router.beforeEach(async (to, from, next) => {
  // 权限检查
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login')
    return
  }
  
  // 预加载应用
  if (to.meta.preload) {
    await preloadApp(to.meta.app)
  }
  
  next()
})
```

## 样式隔离

### 1. CSS 隔离策略

```typescript
// 配置样式隔离
const sandboxConfig = {
  css: {
    isolation: 'scoped', // 'scoped' | 'shadow' | 'prefix'
    prefix: 'micro-app-', // 前缀隔离
    excludes: [
      'antd', // 排除第三方库
      'element-ui'
    ]
  }
}
```

### 2. 样式最佳实践

```css
/* ✅ 使用 CSS Modules */
.container {
  padding: 16px;
}

.title {
  font-size: 24px;
  color: #333;
}

/* ✅ 使用 CSS-in-JS */
const styles = {
  container: {
    padding: '16px'
  },
  title: {
    fontSize: '24px',
    color: '#333'
  }
}

/* ❌ 避免全局样式污染 */
.container { /* 可能影响其他应用 */
  padding: 16px !important;
}
```

## 性能优化

### 1. 应用预加载

```typescript
// 智能预加载策略
const preloadConfig = {
  strategy: 'predictive', // 'eager' | 'lazy' | 'predictive'
  rules: [
    {
      condition: 'user-role:admin',
      apps: ['admin-app', 'analytics-app']
    },
    {
      condition: 'route-frequency:high',
      apps: ['dashboard-app']
    }
  ]
}
```

### 2. 资源优化

```typescript
// 共享依赖配置
const sharedDependencies = {
  'react': '^18.0.0',
  'react-dom': '^18.0.0',
  'antd': '^5.0.0',
  '@micro-core/core': '^1.0.0'
}

// Webpack 配置
module.exports = {
  externals: sharedDependencies,
  optimization: {
    splitChunks: {
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        }
      }
    }
  }
}
```

## 错误处理

### 1. 全局错误处理

```typescript
// 配置全局错误处理
import { ErrorHandler } from '@micro-core/core'

ErrorHandler.configure({
  // 应用加载错误
  onAppLoadError: (error, appName) => {
    console.error(`应用 ${appName} 加载失败:`, error)
    // 显示降级UI
    showFallbackUI(appName)
  },
  
  // 运行时错误
  onRuntimeError: (error, appName) => {
    console.error(`应用 ${appName} 运行时错误:`, error)
    // 错误上报
    reportError(error, appName)
  }
})
```

### 2. 应用级错误边界

```typescript
// React 错误边界
class AppErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false }
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true }
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('应用错误:', error, errorInfo)
    // 错误上报
    reportError(error, this.props.appName)
  }
  
  render() {
    if (this.state.hasError) {
      return <FallbackComponent />
    }
    
    return this.props.children
  }
}
```

## 测试策略

### 1. 单元测试

```typescript
// 应用单元测试
import { render, screen } from '@testing-library/react'
import { MicroApp } from './micro-app'

describe('MicroApp', () => {
  it('should render correctly', () => {
    render(<MicroApp />)
    expect(screen.getByText('Welcome')).toBeInTheDocument()
  })
  
  it('should handle events correctly', () => {
    const mockHandler = jest.fn()
    EventBus.on('test-event', mockHandler)
    
    render(<MicroApp />)
    EventBus.emit('test-event', { data: 'test' })
    
    expect(mockHandler).toHaveBeenCalledWith({ data: 'test' })
  })
})
```

### 2. 集成测试

```typescript
// 微应用集成测试
import { MicroCore } from '@micro-core/core'
import { createTestEnvironment } from '@micro-core/test-utils'

describe('Micro Apps Integration', () => {
  let microCore
  
  beforeEach(() => {
    const testEnv = createTestEnvironment()
    microCore = new MicroCore(testEnv.config)
  })
  
  it('should load and communicate between apps', async () => {
    // 加载应用
    await microCore.loadApp('user-app')
    await microCore.loadApp('order-app')
    
    // 测试应用间通信
    const userApp = microCore.getApp('user-app')
    const orderApp = microCore.getApp('order-app')
    
    userApp.emit('user:login', { userId: '123' })
    
    // 验证订单应用接收到事件
    expect(orderApp.currentUser.id).toBe('123')
  })
})
```

## 部署策略

### 1. 独立部署

```yaml
# 应用独立部署配置
version: '3.8'
services:
  user-app:
    build: ./apps/user-app
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
      
  order-app:
    build: ./apps/order-app  
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=production
      
  main-app:
    build: ./apps/main-app
    ports:
      - "3000:3000"
    environment:
      - USER_APP_URL=http://user-app:3000
      - ORDER_APP_URL=http://order-app:3000
```

### 2. CDN 部署

```typescript
// CDN 资源配置
const cdnConfig = {
  baseUrl: 'https://cdn.example.com/micro-apps/',
  apps: {
    'user-app': {
      version: '1.2.3',
      entry: 'user-app/1.2.3/index.js'
    },
    'order-app': {
      version: '2.1.0', 
      entry: 'order-app/2.1.0/index.js'
    }
  }
}
```

## 监控和调试

### 1. 性能监控

```typescript
// 性能监控配置
import { PerformanceMonitor } from '@micro-core/core'

PerformanceMonitor.configure({
  // 应用加载时间监控
  trackAppLoad: true,
  
  // 路由切换监控
  trackRouteChange: true,
  
  // 内存使用监控
  trackMemoryUsage: true,
  
  // 自定义指标
  customMetrics: [
    'user-interaction-time',
    'api-response-time'
  ]
})
```

### 2. 调试工具

```typescript
// 开发环境调试配置
const debugConfig = {
  devtools: true,
  logging: {
    level: 'debug',
    categories: ['router', 'sandbox', 'communication']
  },
  
  // 可视化调试面板
  debugPanel: {
    enabled: true,
    position: 'bottom-right'
  }
}
```

## 团队协作

### 1. 开发规范

```typescript
// 应用接口规范
interface MicroAppInterface {
  // 必需方法
  mount(container: HTMLElement): Promise<void>
  unmount(): Promise<void>
  
  // 可选方法
  update?(props: any): Promise<void>
  
  // 生命周期钩子
  beforeMount?(): Promise<void>
  afterMount?(): Promise<void>
  beforeUnmount?(): Promise<void>
  afterUnmount?(): Promise<void>
}
```

### 2. 版本管理

```json
{
  "name": "@company/user-app",
  "version": "1.2.3",
  "microApp": {
    "name": "user-app",
    "entry": "./dist/index.js",
    "dependencies": {
      "@micro-core/core": "^1.0.0",
      "react": "^18.0.0"
    },
    "exports": {
      "mount": "./dist/mount.js",
      "unmount": "./dist/unmount.js"
    }
  }
}
```

## 常见陷阱和解决方案

### 1. 内存泄漏

```typescript
// ✅ 正确的事件清理
class MicroApp {
  private eventListeners = []
  
  mount() {
    const handler = this.handleEvent.bind(this)
    EventBus.on('global-event', handler)
    this.eventListeners.push(['global-event', handler])
  }
  
  unmount() {
    // 清理事件监听器
    this.eventListeners.forEach(([event, handler]) => {
      EventBus.off(event, handler)
    })
    this.eventListeners = []
  }
}
```

### 2. 样式冲突

```typescript
// ✅ 使用样式隔离
const sandboxConfig = {
  css: {
    isolation: 'scoped',
    prefix: 'app-prefix-'
  }
}

// ✅ 或使用 Shadow DOM
const shadowConfig = {
  css: {
    isolation: 'shadow'
  }
}
```

### 3. 全局变量污染

```typescript
// ✅ 使用沙箱隔离
const proxyConfig = {
  sandbox: {
    type: 'proxy',
    isolation: ['window', 'document', 'location']
  }
}
```

## 参考资料

- [架构设计指南](/guide/architecture)
- [性能优化指南](/guide/performance)
- [安全最佳实践](/guide/security)
- [故障排除指南](/guide/troubleshooting)
