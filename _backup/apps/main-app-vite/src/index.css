/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.6;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
        'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f8f9fa;
    color: #333;
}

#root {
    min-height: 100vh;
}

/* 通用样式 */
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 600;
    line-height: 1.2;
}

p {
    margin-bottom: 1rem;
}

a {
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #764ba2;
}

button {
    font-family: inherit;
    cursor: pointer;
    transition: all 0.3s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 微应用容器通用样式 */
[data-micro-app] {
    width: 100%;
    min-height: 400px;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* 工具类 */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-1 {
    margin-bottom: 0.5rem;
}

.mb-2 {
    margin-bottom: 1rem;
}

.mb-3 {
    margin-bottom: 1.5rem;
}

.mb-4 {
    margin-bottom: 2rem;
}

.mt-1 {
    margin-top: 0.5rem;
}

.mt-2 {
    margin-top: 1rem;
}

.mt-3 {
    margin-top: 1.5rem;
}

.mt-4 {
    margin-top: 2rem;
}

.p-1 {
    padding: 0.5rem;
}

.p-2 {
    padding: 1rem;
}

.p-3 {
    padding: 1.5rem;
}

.p-4 {
    padding: 2rem;
}

.d-flex {
    display: flex;
}

.align-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.gap-1 {
    gap: 0.5rem;
}

.gap-2 {
    gap: 1rem;
}

.gap-3 {
    gap: 1.5rem;
}

.gap-4 {
    gap: 2rem;
}

/* 响应式工具类 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 576px) {
    .container {
        padding: 0 1.5rem;
    }
}

@media (min-width: 768px) {
    .container {
        padding: 0 2rem;
    }
}

@media (min-width: 992px) {
    .container {
        padding: 0 2.5rem;
    }
}

@media (min-width: 1200px) {
    .container {
        padding: 0 3rem;
    }
}