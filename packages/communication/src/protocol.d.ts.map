{"version": 3, "file": "protocol.d.ts", "sourceRoot": "", "sources": ["protocol.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,KAAK,EACR,WAAW,EACX,cAAc,EACd,eAAe,EACf,eAAe,EACf,eAAe,EAElB,MAAM,oBAAoB,CAAC;AAG5B;;;GAGG;AACH,qBAAa,QAAQ;IACjB,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAS;IAChC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAiB;IACxC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAgD;IACzE,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAkB;IAC1C,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAyB;IACtD,OAAO,CAAC,iBAAiB,CAAS;gBAEtB,MAAM,GAAE,cAAmB;IA8BvC;;OAEG;IACH,eAAe,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,GAAG,MAAM;IA2B3E;;OAEG;IACH,iBAAiB,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO;IA+BvE;;OAEG;IACH,aAAa,CACT,IAAI,EAAE,WAAW,EACjB,OAAO,EAAE,GAAG,EACZ,OAAO,GAAE;QACL,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KAC7B,GACP,eAAe;IAqClB;;OAEG;IACG,cAAc,CAAC,OAAO,EAAE,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC;IAkChE;;OAEG;IACG,eAAe,CAAC,QAAQ,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;IAkBtE;;OAEG;IACH,gBAAgB,CAAC,OAAO,EAAE,eAAe,GAAG,MAAM;IAuBlD;;OAEG;IACH,kBAAkB,CAAC,IAAI,EAAE,MAAM,GAAG,eAAe;IA8BjD;;OAEG;IACH,eAAe,CAAC,WAAW,CAAC,EAAE,WAAW,GAAG,MAAM;IAYlD;;OAEG;IACH,wBAAwB,IAAI,WAAW,EAAE;IAIzC;;OAEG;IACH,UAAU,IAAI,eAAe;IAK7B;;OAEG;IACH,KAAK,IAAI,IAAI;IAWb;;OAEG;IACH,OAAO,IAAI,IAAI;IASf;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAM3B;;OAEG;IACH,OAAO,CAAC,eAAe;IAMvB;;OAEG;IACH,OAAO,CAAC,eAAe;IA4CvB;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAiBzB;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAIzB;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAIzB;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAkB5B;;OAEG;IACH,OAAO,CAAC,cAAc;IAUtB;;OAEG;YACW,mBAAmB;IAoBjC;;OAEG;YACW,aAAa;IAwD3B;;OAEG;IACH,OAAO,CAAC,aAAa;CAGxB"}