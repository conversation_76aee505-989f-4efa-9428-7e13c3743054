# @micro-core/plugin-sandbox-proxy

基于 Proxy 的沙箱隔离插件，为微前端应用提供强大的运行时隔离能力。

## 功能特性

- 🛡️ **全局变量隔离**: 通过 Proxy 代理实现全局变量的完全隔离
- 🎨 **样式隔离**: CSS 样式作用域隔离，防止样式污染
- 📝 **DOM 隔离**: DOM 操作隔离，确保应用间不相互影响
- 🔧 **事件隔离**: 事件监听器隔离，防止事件冲突
- 💾 **存储隔离**: localStorage/sessionStorage 隔离
- 🌐 **History 隔离**: 浏览器历史记录隔离
- ⚡ **高性能**: 基于 Proxy 的轻量级实现
- 🔄 **动态切换**: 支持沙箱的动态创建和销毁

## 安装

```bash
pnpm add @micro-core/plugin-sandbox-proxy
```

## 基础用法

### 插件注册

```typescript
import { MicroCoreKernel } from '@micro-core/core';
import { ProxySandboxPlugin } from '@micro-core/plugin-sandbox-proxy';

const kernel = new MicroCoreKernel();

// 注册代理沙箱插件
await kernel.installPlugin(new ProxySandboxPlugin({
  enableStyleIsolation: true,
  enableEventIsolation: true,
  enableStorageIsolation: true,
  enableHistoryIsolation: true,
  strictMode: true,
  enableDebug: false
}));
```

### 创建沙箱

```typescript
// 创建应用沙箱
const sandbox = await proxySandboxPlugin.createSandbox('app1', {
  enableStyleIsolation: true,
  enableEventIsolation: true,
  globalWhitelist: ['console', 'setTimeout', 'setInterval'],
  stylePrefix: 'micro-app-1'
});

// 激活沙箱
await sandbox.activate();

// 在沙箱中执行代码
sandbox.execScript(`
  window.myGlobalVar = 'Hello from App1';
  console.log('App1 is running in sandbox');
`);

// 停用沙箱
await sandbox.deactivate();
```

### 样式隔离

```typescript
// 启用样式隔离
const sandbox = await proxySandboxPlugin.createSandbox('app1', {
  enableStyleIsolation: true,
  stylePrefix: 'micro-app-1',
  styleIsolationMode: 'scoped' // 'scoped' | 'shadow-dom'
});

// 样式会自动添加前缀
// 原始: .header { color: red; }
// 隔离后: .micro-app-1 .header { color: red; }
```

### 事件隔离

```typescript
// 启用事件隔离
const sandbox = await proxySandboxPlugin.createSandbox('app1', {
  enableEventIsolation: true,
  eventIsolationMode: 'capture' // 'capture' | 'bubble'
});

// 在沙箱中添加事件监听器
sandbox.execScript(`
  document.addEventListener('click', function(e) {
    console.log('Click event in App1');
    // 事件不会影响其他应用
  });
`);
```

## 配置选项

```typescript
interface ProxySandboxConfig {
  // 基础配置
  strictMode?: boolean;                 // 严格模式
  enableDebug?: boolean;                // 调试模式
  
  // 隔离功能开关
  enableStyleIsolation?: boolean;       // 样式隔离
  enableEventIsolation?: boolean;       // 事件隔离
  enableStorageIsolation?: boolean;     // 存储隔离
  enableHistoryIsolation?: boolean;     // 历史记录隔离
  enableDOMIsolation?: boolean;         // DOM 隔离
  
  // 全局变量配置
  globalWhitelist?: string[];           // 全局变量白名单
  globalBlacklist?: string[];           // 全局变量黑名单
  
  // 样式隔离配置
  stylePrefix?: string;                 // 样式前缀
  styleIsolationMode?: 'scoped' | 'shadow-dom';  // 样式隔离模式
  
  // 事件隔离配置
  eventIsolationMode?: 'capture' | 'bubble';     // 事件隔离模式
  
  // 存储隔离配置
  storagePrefix?: string;               // 存储前缀
  
  // 性能配置
  enableLazyLoading?: boolean;          // 懒加载
  maxSandboxCount?: number;             // 最大沙箱数量
}
```

## API 参考

### ProxySandboxPlugin

#### `createSandbox(appId: string, config?: SandboxConfig): Promise<ProxySandbox>`
创建新的代理沙箱

#### `getSandbox(appId: string): ProxySandbox | null`
获取指定应用的沙箱

#### `destroySandbox(appId: string): Promise<void>`
销毁指定应用的沙箱

#### `getAllSandboxes(): Map<string, ProxySandbox>`
获取所有沙箱实例

### ProxySandbox

#### `activate(): Promise<void>`
激活沙箱

#### `deactivate(): Promise<void>`
停用沙箱

#### `execScript(script: string): any`
在沙箱中执行脚本

#### `setGlobalProperty(key: string, value: any): void`
设置沙箱全局属性

#### `getGlobalProperty(key: string): any`
获取沙箱全局属性

#### `addStyleIsolation(css: string): void`
添加样式隔离

#### `removeStyleIsolation(): void`
移除样式隔离

## 高级用法

### 自定义代理处理器

```typescript
const sandbox = await proxySandboxPlugin.createSandbox('app1', {
  customProxyHandler: {
    get(target, property, receiver) {
      // 自定义属性获取逻辑
      if (property === 'customAPI') {
        return () => console.log('Custom API called');
      }
      return Reflect.get(target, property, receiver);
    },
    
    set(target, property, value, receiver) {
      // 自定义属性设置逻辑
      if (property.startsWith('_')) {
        console.warn('Setting private property:', property);
      }
      return Reflect.set(target, property, value, receiver);
    }
  }
});
```

### 沙箱通信

```typescript
// 沙箱间通信
const sandbox1 = await proxySandboxPlugin.createSandbox('app1');
const sandbox2 = await proxySandboxPlugin.createSandbox('app2');

// 设置通信桥梁
sandbox1.setGlobalProperty('sendMessage', (message) => {
  sandbox2.receiveMessage('app1', message);
});

sandbox2.setGlobalProperty('receiveMessage', (from, message) => {
  console.log(`Message from ${from}:`, message);
});
```

### 资源预加载

```typescript
// 预加载沙箱资源
await proxySandboxPlugin.preloadSandbox('app1', {
  scripts: ['/app1/main.js', '/app1/vendor.js'],
  styles: ['/app1/main.css'],
  enableCache: true
});
```

## 性能优化

### 1. 懒加载沙箱

```typescript
const plugin = new ProxySandboxPlugin({
  enableLazyLoading: true,
  maxSandboxCount: 5  // 限制同时存在的沙箱数量
});
```

### 2. 沙箱池管理

```typescript
// 使用沙箱池复用沙箱实例
const sandboxPool = proxySandboxPlugin.getSandboxPool();

// 获取可复用的沙箱
const sandbox = await sandboxPool.acquire('app1');

// 使用完毕后释放
await sandboxPool.release(sandbox);
```

### 3. 内存优化

```typescript
// 定期清理未使用的沙箱
setInterval(() => {
  proxySandboxPlugin.cleanupInactiveSandboxes();
}, 60000); // 每分钟清理一次
```

## 安全考虑

### 1. 全局变量控制

```typescript
const sandbox = await proxySandboxPlugin.createSandbox('app1', {
  globalWhitelist: [
    'console', 'setTimeout', 'setInterval', 'clearTimeout', 'clearInterval'
  ],
  globalBlacklist: [
    'eval', 'Function', 'XMLHttpRequest'
  ],
  strictMode: true
});
```

### 2. 脚本执行限制

```typescript
// 限制脚本执行权限
sandbox.setExecutionPolicy({
  allowEval: false,
  allowFunction: false,
  allowDynamicImport: false,
  maxExecutionTime: 5000  // 5秒超时
});
```

## 调试和监控

### 1. 调试模式

```typescript
const plugin = new ProxySandboxPlugin({
  enableDebug: true
});

// 监听沙箱事件
plugin.on('sandbox:created', (sandbox) => {
  console.log('Sandbox created:', sandbox.appId);
});

plugin.on('sandbox:activated', (sandbox) => {
  console.log('Sandbox activated:', sandbox.appId);
});

plugin.on('sandbox:error', (error, sandbox) => {
  console.error('Sandbox error:', error, sandbox.appId);
});
```

### 2. 性能监控

```typescript
// 获取沙箱性能指标
const metrics = sandbox.getPerformanceMetrics();
console.log('Memory usage:', metrics.memoryUsage);
console.log('Execution time:', metrics.executionTime);
console.log('API calls:', metrics.apiCalls);
```

## 故障排除

### 常见问题

**Q: 沙箱中的全局变量泄露到主应用**
A: 检查 `globalWhitelist` 配置，确保只允许必要的全局变量。

**Q: 样式隔离不生效**
A: 确保启用了 `enableStyleIsolation` 并设置了正确的 `stylePrefix`。

**Q: 沙箱性能问题**
A: 启用懒加载和沙箱池，限制同时存在的沙箱数量。

### 调试技巧

```typescript
// 启用详细日志
const sandbox = await proxySandboxPlugin.createSandbox('app1', {
  enableDebug: true,
  logLevel: 'verbose'
});

// 检查沙箱状态
console.log('Sandbox state:', sandbox.getState());
console.log('Global properties:', sandbox.getGlobalProperties());
```

## 兼容性

- ✅ Chrome 49+
- ✅ Firefox 18+
- ✅ Safari 10+
- ✅ Edge 12+
- ❌ IE (不支持 Proxy)

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个插件。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基于 Proxy 的沙箱隔离
- 支持样式、事件、存储隔离
- 支持自定义代理处理器
- 支持性能监控和调试
