/**
 * 隔离管理器实现
 *
 * @description 统一管理所有隔离能力
 * <AUTHOR> <<EMAIL>>
 */
import type { IsolationConfig, Isolator, SandboxContext } from '../types';
/**
 * 隔离管理器
 * 负责协调和管理所有隔离器
 */
export declare class IsolationManager {
    private readonly logger;
    private readonly isolators;
    private config?;
    private initialized;
    constructor();
    /**
     * 初始化隔离管理器
     */
    initialize(config: IsolationConfig): Promise<void>;
    /**
     * 应用所有隔离
     */
    applyIsolation(context: SandboxContext): Promise<void>;
    /**
     * 移除所有隔离
     */
    removeIsolation(context: SandboxContext): Promise<void>;
    /**
     * 回滚隔离（在应用失败时使用）
     */
    rollbackIsolation(context: SandboxContext): Promise<void>;
    /**
     * 获取隔离器
     */
    getIsolator(name: string): Isolator | undefined;
    /**
     * 获取所有隔离器
     */
    getAllIsolators(): Isolator[];
    /**
     * 获取启用的隔离器
     */
    getEnabledIsolators(): Isolator[];
    /**
     * 检查隔离器是否启用
     */
    isIsolatorEnabled(name: string): boolean;
    /**
     * 获取隔离状态
     */
    getIsolationStatus(): Record<string, boolean>;
    /**
     * 清理资源
     */
    cleanup(): Promise<void>;
    /**
     * 初始化所有隔离器
     */
    private initializeIsolators;
    /**
     * 注册隔离器
     */
    private registerIsolator;
    /**
     * 验证配置
     */
    private validateConfig;
}
//# sourceMappingURL=isolation-manager.d.ts.map