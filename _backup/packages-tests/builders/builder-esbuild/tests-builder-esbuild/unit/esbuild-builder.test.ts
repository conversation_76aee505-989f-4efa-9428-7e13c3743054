/**
 * @fileoverview ESBuild Builder Unit Tests
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import type { BaseBuilderConfig, DevServerConfig } from '@micro-core/shared/types';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { EsbuildBuilder } from '../../src/esbuild-builder';
import type { EsbuildBuilderConfig, EsbuildBuilderOptions } from '../../src/types';

// Mock ESBuild
vi.mock('esbuild', () => ({
  build: vi.fn(),
  serve: vi.fn(),
  context: vi.fn()
}));

// Mock shared utilities
vi.mock('../../../shared', () => ({
  Logger: {
    info: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn()
  },
  PerformanceMonitor: {
    startTimer: vi.fn().mockReturnValue('timer-id'),
    endTimer: vi.fn().mockReturnValue(1000)
  },
  ConfigMerger: {
    deepMerge: vi.fn().mockImplementation((base, override) => ({ ...base, ...override }))
  }
}));

describe('EsbuildBuilder', () => {
  let builder: EsbuildBuilder;
  let mockConfig: BaseBuilderConfig;

  beforeEach(() => {
    builder = new EsbuildBuilder({
      mode: 'development',
      entry: './src/index.ts',
      outDir: './dist'
    });

    mockConfig = {
      entry: './src/index.ts',
      outDir: './dist',
      mode: 'development'
    };

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Constructor', () => {
    it('should initialize with default options', () => {
      const defaultBuilder = new EsbuildBuilder();
      expect(defaultBuilder.name).toBe('esbuild');
      expect(defaultBuilder.version).toBe('1.0.0');
    });

    it('should initialize with custom options', () => {
      const options: EsbuildBuilderOptions = {
        mode: 'production',
        entry: './custom/entry.ts',
        outDir: './custom/dist'
      };
      const customBuilder = new EsbuildBuilder(options);
      expect(customBuilder.name).toBe('esbuild');
      expect(customBuilder.version).toBe('1.0.0');
    });
  });

  describe('createBuilderConfig', () => {
    it('should create basic ESBuild configuration', () => {
      const esbuildConfig = (builder as any).createBuilderConfig(mockConfig);

      expect(esbuildConfig).toMatchObject({
        entryPoints: ['./src/index.ts'],
        outdir: './dist',
        bundle: true,
        platform: 'browser',
        target: 'es2020',
        format: 'esm'
      });
    });

    it('should handle production mode configuration', () => {
      const prodConfig = { ...mockConfig, mode: 'production' as const };
      const esbuildConfig = (builder as any).createBuilderConfig(prodConfig);

      expect(esbuildConfig.minify).toBe(true);
      expect(esbuildConfig.sourcemap).toBe(true);
    });

    it('should handle custom ESBuild configuration', () => {
      const configWithCustom: EsbuildBuilderConfig = {
        ...mockConfig,
        esbuildConfig: {
          target: 'es2018',
          format: 'cjs',
          external: ['react']
        }
      };

      const esbuildConfig = (builder as any).createBuilderConfig(configWithCustom);
      expect(esbuildConfig.target).toBe('es2018');
      expect(esbuildConfig.format).toBe('cjs');
      expect(esbuildConfig.external).toEqual(['react']);
    });

    it('should handle loader configuration', () => {
      const configWithLoaders: EsbuildBuilderConfig = {
        ...mockConfig,
        loader: {
          '.png': 'file',
          '.svg': 'text'
        }
      };

      const esbuildConfig = (builder as any).createBuilderConfig(configWithLoaders);
      expect(esbuildConfig.loader).toEqual({
        '.png': 'file',
        '.svg': 'text'
      });
    });
  });

  describe('executeBuild', () => {
    it('should execute successful build', async () => {
      const mockBuildResult = {
        metafile: {
          outputs: {
            'dist/index.js': { bytes: 1000 },
            'dist/style.css': { bytes: 500 }
          }
        }
      };

      const { build } = await import('esbuild');
      vi.mocked(build).mockResolvedValue(mockBuildResult as any);

      const esbuildConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(esbuildConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(2);
      expect(result.stats.duration).toBe(1000);
      expect(result.stats.totalSize).toBe(1500);
    });

    it('should handle build errors', async () => {
      const buildError = new Error('Build failed');
      const { build } = await import('esbuild');
      vi.mocked(build).mockRejectedValue(buildError);

      const esbuildConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(esbuildConfig);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].message).toBe('Build failed');
    });

    it('should handle build warnings', async () => {
      const mockBuildResult = {
        warnings: [
          { text: 'Warning 1', location: null },
          { text: 'Warning 2', location: null }
        ],
        metafile: {
          outputs: {
            'dist/index.js': { bytes: 1000 }
          }
        }
      };

      const { build } = await import('esbuild');
      vi.mocked(build).mockResolvedValue(mockBuildResult as any);

      const esbuildConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(esbuildConfig);

      expect(result.success).toBe(true);
      expect(result.warnings).toHaveLength(2);
      expect(result.stats.warnings).toBe(2);
    });
  });

  describe('startDevServer', () => {
    it('should start development server successfully', async () => {
      const mockContext = {
        serve: vi.fn().mockResolvedValue({
          host: 'localhost',
          port: 3000
        }),
        dispose: vi.fn()
      };

      const { context } = await import('esbuild');
      vi.mocked(context).mockResolvedValue(mockContext as any);

      const esbuildConfig = (builder as any).createBuilderConfig(mockConfig);
      const devServerConfig: DevServerConfig = {
        port: 3000,
        host: 'localhost'
      };

      const server = await (builder as any).startDevServer(esbuildConfig, devServerConfig);

      expect(server).toBeDefined();
      expect(mockContext.serve).toHaveBeenCalledWith({
        servedir: './dist',
        port: 3000,
        host: 'localhost'
      });
    });

    it('should handle dev server start errors', async () => {
      const serverError = new Error('Failed to start server');
      const { context } = await import('esbuild');
      vi.mocked(context).mockRejectedValue(serverError);

      const esbuildConfig = (builder as any).createBuilderConfig(mockConfig);

      await expect((builder as any).startDevServer(esbuildConfig)).rejects.toThrow('Failed to start server');
    });

    it('should use default dev server configuration', async () => {
      const mockContext = {
        serve: vi.fn().mockResolvedValue({
          host: 'localhost',
          port: 8080
        }),
        dispose: vi.fn()
      };

      const { context } = await import('esbuild');
      vi.mocked(context).mockResolvedValue(mockContext as any);

      const esbuildConfig = (builder as any).createBuilderConfig(mockConfig);
      await (builder as any).startDevServer(esbuildConfig);

      expect(mockContext.serve).toHaveBeenCalledWith({
        servedir: './dist',
        port: 8080,
        host: 'localhost'
      });
    });
  });

  describe('stopDevServer', () => {
    it('should stop development server', async () => {
      const mockServer = {
        dispose: vi.fn().mockResolvedValue(undefined)
      };

      await (builder as any).stopDevServer(mockServer);
      expect(mockServer.dispose).toHaveBeenCalled();
    });

    it('should handle server stop errors', async () => {
      const mockServer = {
        dispose: vi.fn().mockRejectedValue(new Error('Stop failed'))
      };

      await expect((builder as any).stopDevServer(mockServer)).rejects.toThrow('Stop failed');
    });
  });

  describe('Build Integration', () => {
    it('should perform complete build lifecycle', async () => {
      const mockBuildResult = {
        metafile: {
          outputs: {
            'dist/index.js': { bytes: 2000 }
          }
        }
      };

      const { build } = await import('esbuild');
      vi.mocked(build).mockResolvedValue(mockBuildResult as any);

      const result = await builder.build(mockConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(1);
      expect(result.outputs[0].fileName).toBe('index.js');
      expect(result.outputs[0].size).toBe(2000);
    });

    it('should handle multiple entry points', async () => {
      const multiEntryConfig = {
        ...mockConfig,
        entry: {
          main: './src/index.ts',
          worker: './src/worker.ts'
        }
      };

      const mockBuildResult = {
        metafile: {
          outputs: {
            'dist/main.js': { bytes: 1500 },
            'dist/worker.js': { bytes: 800 }
          }
        }
      };

      const { build } = await import('esbuild');
      vi.mocked(build).mockResolvedValue(mockBuildResult as any);

      const result = await builder.build(multiEntryConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(2);
      expect(result.stats.totalSize).toBe(2300);
    });
  });

  describe('Performance Tests', () => {
    it('should handle large number of files efficiently', async () => {
      const largeOutputs: Record<string, { bytes: number }> = {};
      for (let i = 0; i < 1000; i++) {
        largeOutputs[`dist/chunk-${i}.js`] = { bytes: 1000 };
      }

      const mockBuildResult = {
        metafile: { outputs: largeOutputs }
      };

      const { build } = await import('esbuild');
      vi.mocked(build).mockResolvedValue(mockBuildResult as any);

      const startTime = Date.now();
      const esbuildConfig = (builder as any).createBuilderConfig(mockConfig);
      const result = await (builder as any).executeBuild(esbuildConfig);
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(result.outputs).toHaveLength(1000);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle rapid successive builds', async () => {
      const mockBuildResult = {
        metafile: {
          outputs: {
            'dist/index.js': { bytes: 1000 }
          }
        }
      };

      const { build } = await import('esbuild');
      vi.mocked(build).mockResolvedValue(mockBuildResult as any);

      const promises = Array.from({ length: 10 }, () => builder.build(mockConfig));
      const results = await Promise.all(promises);

      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid configuration gracefully', async () => {
      const invalidConfig = {
        ...mockConfig,
        entry: '', // Invalid empty entry
        outDir: '' // Invalid empty output
      };

      // Should not throw, but may produce warnings or handle gracefully
      expect(() => (builder as any).createBuilderConfig(invalidConfig)).not.toThrow();
    });

    it('should handle build process interruption', async () => {
      const { build } = await import('esbuild');
      vi.mocked(build).mockImplementation(() => {
        return new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Build interrupted')), 100);
        });
      });

      const result = await builder.build(mockConfig);
      expect(result.success).toBe(false);
      expect(result.errors[0].message).toBe('Build interrupted');
    });
  });

  describe('Configuration Validation', () => {
    it('should validate entry point configuration', () => {
      const configWithStringEntry = { ...mockConfig, entry: './src/main.ts' };
      const esbuildConfig = (builder as any).createBuilderConfig(configWithStringEntry);
      expect(esbuildConfig.entryPoints).toEqual(['./src/main.ts']);
    });

    it('should validate output configuration', () => {
      const configWithCustomOut = { ...mockConfig, outDir: './custom-dist' };
      const esbuildConfig = (builder as any).createBuilderConfig(configWithCustomOut);
      expect(esbuildConfig.outdir).toBe('./custom-dist');
    });

    it('should handle external dependencies', () => {
      const configWithExternals: EsbuildBuilderConfig = {
        ...mockConfig,
        external: ['react', 'react-dom']
      };

      const esbuildConfig = (builder as any).createBuilderConfig(configWithExternals);
      expect(esbuildConfig.external).toEqual(['react', 'react-dom']);
    });
  });
});
