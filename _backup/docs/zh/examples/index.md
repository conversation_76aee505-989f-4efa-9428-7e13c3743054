# 示例概览

本章节提供了丰富的 Micro-Core 使用示例，从基础用法到复杂的实战场景，帮助您快速上手和深入理解微前端架构。

## 示例分类

### 🚀 基础示例

适合初学者，展示 Micro-Core 的基本功能和用法。

- [基本使用](/examples/basic/) - 最简单的微前端应用示例
- [React 应用](/examples/frameworks/react) - React 微应用集成示例
- [Vue 应用](/examples/frameworks/vue) - Vue 微应用集成示例
- [Angular 应用](/examples/frameworks/angular) - Angular 微应用集成示例

### 🔧 进阶示例

展示更复杂的功能和最佳实践。

- [多应用协作](/examples/advanced/multi-app) - 多个微应用协同工作
- [应用间通信](/examples/advanced/communication) - 不同应用间的数据交换
- [共享状态](/examples/advanced/shared-state) - 全局状态管理
- [动态路由](/examples/advanced/dynamic-routing) - 动态路由配置和管理

### 🏢 实战案例

真实业务场景的完整解决方案。

- [电商平台](/examples/real-world/ecommerce) - 完整的电商微前端架构
- [管理后台](/examples/real-world/admin-dashboard) - 企业级管理系统
- [内容管理系统](/examples/real-world/cms) - CMS 系统微前端改造
- [数据可视化](/examples/real-world/data-visualization) - 大屏数据展示系统

## 快速开始

### 1. 克隆示例仓库

```bash
git clone https://github.com/micro-core/examples.git
cd examples
```

### 2. 安装依赖

```bash
# 使用 pnpm（推荐）
pnpm install

# 或使用 npm
npm install

# 或使用 yarn
yarn install
```

### 3. 运行示例

```bash
# 运行基础示例
pnpm run dev:basic

# 运行 React 示例
pnpm run dev:react

# 运行 Vue 示例
pnpm run dev:vue

# 运行完整示例
pnpm run dev:full
```

## 示例结构

每个示例都包含以下结构：

```
example-name/
├── README.md           # 示例说明
├── package.json        # 依赖配置
├── main-app/          # 主应用
│   ├── src/
│   ├── public/
│   └── vite.config.ts
├── micro-apps/        # 微应用
│   ├── app1/
│   ├── app2/
│   └── ...
└── shared/            # 共享资源
    ├── types/
    ├── utils/
    └── components/
```

## 在线演示

您可以在以下平台查看在线演示：

- [CodeSandbox](https://codesandbox.io/s/micro-core-examples) - 在线编辑和运行
- [StackBlitz](https://stackblitz.com/github/micro-core/examples) - 快速预览
- [GitHub Pages](https://micro-core.github.io/examples/) - 静态演示

## 贡献示例

我们欢迎社区贡献更多的示例！如果您有好的示例想要分享：

1. Fork [示例仓库](https://github.com/micro-core/examples)
2. 创建新的示例目录
3. 添加完整的代码和文档
4. 提交 Pull Request

### 示例贡献指南

- **命名规范**: 使用清晰的英文名称，如 `react-vue-integration`
- **文档完整**: 包含详细的 README.md 说明
- **代码规范**: 遵循项目的代码风格
- **可运行**: 确保示例可以正常运行
- **注释清晰**: 关键代码添加中文注释

## 常见问题

### Q: 示例无法运行怎么办？

A: 请检查以下几点：
1. Node.js 版本是否满足要求（>= 16.0.0）
2. 依赖是否正确安装
3. 端口是否被占用
4. 查看控制台错误信息

### Q: 如何修改示例配置？

A: 每个示例的配置文件通常在：
- `micro-core.config.ts` - 微前端配置
- `vite.config.ts` - 构建配置
- `package.json` - 依赖和脚本配置

### Q: 示例可以用于生产环境吗？

A: 示例主要用于学习和参考，生产环境使用前请：
1. 完善错误处理
2. 添加安全配置
3. 优化性能
4. 添加监控和日志

## 学习路径

建议按以下顺序学习示例：

1. **基础入门** (1-2天)
   - 基本使用示例
   - 单个框架集成

2. **功能探索** (3-5天)
   - 多应用协作
   - 应用间通信
   - 状态管理

3. **深入实践** (1-2周)
   - 完整项目案例
   - 性能优化
   - 部署配置

4. **高级应用** (持续学习)
   - 自定义插件
   - 复杂业务场景
   - 最佳实践

## 相关资源

- [官方文档](/guide/introduction) - 完整的使用指南
- [API 参考](/api/core) - 详细的 API 文档
- [最佳实践](/guide/best-practices/architecture) - 架构设计建议
- [社区讨论](https://github.com/micro-core/micro-core/discussions) - 技术交流

## 反馈和建议

如果您对示例有任何建议或发现问题，请：

- 在 [GitHub Issues](https://github.com/micro-core/examples/issues) 中提交问题
- 在 [Discussions](https://github.com/micro-core/micro-core/discussions) 中参与讨论
- 通过 [邮件](mailto:<EMAIL>) 联系我们

让我们一起构建更好的微前端生态！