# 插件系统

Micro-Core 采用 100% 插件化架构设计，所有功能都通过插件系统实现。这种设计使得框架具有极高的可扩展性和灵活性，开发者可以根据具体需求选择和组合不同的插件。

## 核心理念

### 微内核架构

Micro-Core 的核心只包含最基础的插件加载和管理功能，所有其他功能都通过插件提供：

```typescript
import { MicroCore } from '@micro-core/core';
import { RouterPlugin } from '@micro-core/plugin-router';
import { SandboxPlugin } from '@micro-core/plugin-sandbox';
import { CommunicationPlugin } from '@micro-core/plugin-communication';

const microCore = new MicroCore({
  plugins: [
    new RouterPlugin(),
    new SandboxPlugin({ type: 'proxy' }),
    new CommunicationPlugin()
  ]
});
```

### 插件生命周期

每个插件都遵循标准的生命周期管理：

```typescript
interface Plugin {
  name: string;
  version: string;
  install(kernel: MicroCoreKernel): void;
  uninstall(kernel: MicroCoreKernel): void;
  beforeMount?(app: MicroApp): void;
  afterMount?(app: MicroApp): void;
  beforeUnmount?(app: MicroApp): void;
  afterUnmount?(app: MicroApp): void;
}
```

## 官方插件生态

### 核心插件

#### 1. 路由插件 (@micro-core/plugin-router)

提供微前端应用的路由管理功能：

```typescript
import { RouterPlugin } from '@micro-core/plugin-router';

const routerPlugin = new RouterPlugin({
  mode: 'history', // 'hash' | 'history' | 'memory'
  base: '/',
  routes: [
    {
      path: '/app1/*',
      name: 'app1',
      component: () => import('./apps/app1')
    }
  ]
});
```

**核心功能**：
- 路由匹配和导航
- 路由守卫和权限控制
- 路由缓存和预加载
- 嵌套路由支持

#### 2. 沙箱插件 (@micro-core/plugin-sandbox)

提供多种沙箱隔离策略：

```typescript
import { SandboxPlugin } from '@micro-core/plugin-sandbox';

// Proxy 沙箱（推荐）
const proxySandbox = new SandboxPlugin({
  type: 'proxy',
  strictIsolation: true,
  globalWhitelist: ['console', 'setTimeout']
});

// Iframe 沙箱（强隔离）
const iframeSandbox = new SandboxPlugin({
  type: 'iframe',
  sandbox: 'allow-scripts allow-same-origin'
});
```

**支持的沙箱类型**：
- **Proxy 沙箱**: 基于 Proxy 的 JavaScript 隔离
- **Iframe 沙箱**: 基于 iframe 的完全隔离
- **WebComponent 沙箱**: 基于 Web Components 的样式隔离
- **DefineProperty 沙箱**: 基于 Object.defineProperty 的兼容方案
- **Namespace 沙箱**: 基于命名空间的轻量隔离
- **Federation 沙箱**: 基于模块联邦的隔离方案

#### 3. 通信插件 (@micro-core/plugin-communication)

提供应用间通信能力：

```typescript
import { CommunicationPlugin } from '@micro-core/plugin-communication';

const communicationPlugin = new CommunicationPlugin({
  enableEventBus: true,
  enableGlobalState: true,
  enableDirectCommunication: true,
  middleware: [
    // 通信中间件
    (message, next) => {
      console.log('Communication:', message);
      next();
    }
  ]
});
```

**通信方式**：
- **EventBus**: 基于事件的发布订阅通信
- **GlobalState**: 全局状态共享
- **Direct Communication**: 直接方法调用
- **Message Channel**: 基于 MessageChannel 的通信

#### 4. 状态管理插件 (@micro-core/plugin-state)

提供全局状态管理：

```typescript
import { StatePlugin } from '@micro-core/plugin-state';

const statePlugin = new StatePlugin({
  initialState: {
    user: null,
    theme: 'light'
  },
  middleware: [
    // 状态中间件
    (action, state, next) => {
      console.log('State change:', action);
      next();
    }
  ],
  persistence: {
    key: 'micro-core-state',
    storage: 'localStorage'
  }
});
```

### 适配器插件

#### 1. React 适配器 (@micro-core/plugin-adapter-react)

```typescript
import { ReactAdapterPlugin } from '@micro-core/plugin-adapter-react';

const reactAdapter = new ReactAdapterPlugin({
  version: '18.x', // 支持 16.8+, 17.x, 18.x
  enableStrictMode: true,
  enableConcurrentFeatures: true
});
```

#### 2. Vue 适配器 (@micro-core/plugin-adapter-vue)

```typescript
import { VueAdapterPlugin } from '@micro-core/plugin-adapter-vue';

// Vue 3 适配器
const vue3Adapter = new VueAdapterPlugin({
  version: '3.x',
  enableCompositionAPI: true
});

// Vue 2 适配器
const vue2Adapter = new VueAdapterPlugin({
  version: '2.7+',
  enableCompositionAPI: false
});
```

#### 3. Angular 适配器 (@micro-core/plugin-adapter-angular)

```typescript
import { AngularAdapterPlugin } from '@micro-core/plugin-adapter-angular';

const angularAdapter = new AngularAdapterPlugin({
  version: '12+',
  enableIvy: true,
  zonejs: true
});
```

### 构建工具插件

#### 1. Vite 插件 (@micro-core/plugin-builder-vite)

```typescript
import { ViteBuilderPlugin } from '@micro-core/plugin-builder-vite';

const viteBuilder = new ViteBuilderPlugin({
  configFile: 'vite.config.ts',
  mode: 'development',
  optimizeDeps: {
    include: ['@micro-core/core']
  }
});
```

#### 2. Webpack 插件 (@micro-core/plugin-builder-webpack)

```typescript
import { WebpackBuilderPlugin } from '@micro-core/plugin-builder-webpack';

const webpackBuilder = new WebpackBuilderPlugin({
  configFile: 'webpack.config.js',
  mode: 'development',
  federation: {
    name: 'host',
    remotes: {
      app1: 'app1@http://localhost:3001/remoteEntry.js'
    }
  }
});
```

### 加载器插件

#### 1. 智能预加载插件 (@micro-core/plugin-loader-prefetch)

```typescript
import { PrefetchLoaderPlugin } from '@micro-core/plugin-loader-prefetch';

const prefetchLoader = new PrefetchLoaderPlugin({
  strategy: 'viewport', // 'viewport' | 'route' | 'idle'
  threshold: 0.1, // 视口阈值
  maxConcurrent: 3, // 最大并发数
  cache: {
    maxAge: 300000, // 5分钟
    maxSize: 50 // 最大缓存数量
  }
});
```

#### 2. Worker 加载器插件 (@micro-core/plugin-loader-worker)

```typescript
import { WorkerLoaderPlugin } from '@micro-core/plugin-loader-worker';

const workerLoader = new WorkerLoaderPlugin({
  maxWorkers: 4,
  timeout: 30000,
  fallback: true // 降级到主线程
});
```

### 兼容性插件

#### 1. qiankun 兼容插件 (@micro-core/plugin-qiankun-compat)

```typescript
import { QiankunCompatPlugin } from '@micro-core/plugin-qiankun-compat';

const qiankunCompat = new QiankunCompatPlugin({
  sandbox: 'proxy', // 使用 Proxy 沙箱
  prefetch: true,
  singular: false
});

// 兼容 qiankun API
import { registerMicroApps, start } from '@micro-core/plugin-qiankun-compat';

registerMicroApps([
  {
    name: 'react-app',
    entry: '//localhost:3000',
    container: '#subapp-viewport',
    activeRule: '/react',
  },
]);

start();
```

#### 2. wujie 兼容插件 (@micro-core/plugin-wujie-compat)

```typescript
import { WujieCompatPlugin } from '@micro-core/plugin-wujie-compat';

const wujieCompat = new WujieCompatPlugin({
  sandbox: 'iframe', // 使用 iframe 沙箱
  alive: true,
  degrade: false
});

// 兼容 wujie API
import { startApp } from '@micro-core/plugin-wujie-compat';

startApp({
  name: 'vue-app',
  url: '//localhost:8080',
  el: '#subapp-container',
  alive: true,
});
```

## 插件开发指南

### 创建自定义插件

#### 1. 基础插件结构

```typescript
import { Plugin, MicroCoreKernel, MicroApp } from '@micro-core/core';

export class CustomPlugin implements Plugin {
  name = 'custom-plugin';
  version = '1.0.0';
  
  private kernel: MicroCoreKernel | null = null;
  private options: CustomPluginOptions;
  
  constructor(options: CustomPluginOptions = {}) {
    this.options = options;
  }
  
  install(kernel: MicroCoreKernel): void {
    this.kernel = kernel;
    
    // 注册插件功能
    kernel.registerHook('beforeMount', this.beforeMount.bind(this));
    kernel.registerHook('afterMount', this.afterMount.bind(this));
    
    // 扩展内核 API
    kernel.extend('customMethod', this.customMethod.bind(this));
  }
  
  uninstall(kernel: MicroCoreKernel): void {
    // 清理插件资源
    kernel.unregisterHook('beforeMount', this.beforeMount);
    kernel.unregisterHook('afterMount', this.afterMount);
    kernel.remove('customMethod');
    
    this.kernel = null;
  }
  
  private async beforeMount(app: MicroApp): Promise<void> {
    // 应用挂载前的处理逻辑
    console.log(`Before mount: ${app.name}`);
  }
  
  private async afterMount(app: MicroApp): Promise<void> {
    // 应用挂载后的处理逻辑
    console.log(`After mount: ${app.name}`);
  }
  
  private customMethod(params: any): any {
    // 自定义方法实现
    return this.options.handler?.(params);
  }
}

interface CustomPluginOptions {
  handler?: (params: any) => any;
}
```

#### 2. 插件配置和选项

```typescript
export interface PluginOptions {
  // 基础配置
  enabled?: boolean;
  priority?: number;
  
  // 生命周期配置
  hooks?: {
    beforeInstall?: () => void;
    afterInstall?: () => void;
    beforeUninstall?: () => void;
    afterUninstall?: () => void;
  };
  
  // 自定义配置
  [key: string]: any;
}

export class BasePlugin implements Plugin {
  protected options: PluginOptions;
  
  constructor(options: PluginOptions = {}) {
    this.options = {
      enabled: true,
      priority: 0,
      ...options
    };
  }
  
  get enabled(): boolean {
    return this.options.enabled ?? true;
  }
  
  get priority(): number {
    return this.options.priority ?? 0;
  }
}
```

#### 3. 插件间通信

```typescript
export class CommunicatingPlugin extends BasePlugin {
  install(kernel: MicroCoreKernel): void {
    // 监听其他插件的事件
    kernel.on('plugin:router:changed', this.onRouteChange.bind(this));
    
    // 发送插件事件
    kernel.emit('plugin:custom:ready', { plugin: this.name });
  }
  
  private onRouteChange(route: any): void {
    // 响应路由变化
    console.log('Route changed:', route);
  }
}
```

### 插件测试

#### 1. 单元测试

```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import { MicroCoreKernel } from '@micro-core/core';
import { CustomPlugin } from './custom-plugin';

describe('CustomPlugin', () => {
  let kernel: MicroCoreKernel;
  let plugin: CustomPlugin;
  
  beforeEach(() => {
    kernel = new MicroCoreKernel();
    plugin = new CustomPlugin();
  });
  
  it('should install correctly', () => {
    plugin.install(kernel);
    
    expect(kernel.hasPlugin('custom-plugin')).toBe(true);
    expect(kernel.customMethod).toBeDefined();
  });
  
  it('should handle app lifecycle', async () => {
    plugin.install(kernel);
    
    const app = {
      name: 'test-app',
      entry: 'http://localhost:3000'
    };
    
    await kernel.mountApp(app);
    
    // 验证插件逻辑
    expect(/* 插件行为验证 */).toBe(true);
  });
});
```

#### 2. 集成测试

```typescript
import { describe, it, expect } from 'vitest';
import { MicroCore } from '@micro-core/core';
import { CustomPlugin } from './custom-plugin';
import { RouterPlugin } from '@micro-core/plugin-router';

describe('CustomPlugin Integration', () => {
  it('should work with other plugins', async () => {
    const microCore = new MicroCore({
      plugins: [
        new RouterPlugin(),
        new CustomPlugin()
      ]
    });
    
    await microCore.start();
    
    // 验证插件协作
    expect(microCore.isRunning()).toBe(true);
  });
});
```

## 插件配置和管理

### 插件配置

```typescript
import { MicroCore } from '@micro-core/core';

const microCore = new MicroCore({
  plugins: [
    // 基础配置
    {
      plugin: RouterPlugin,
      options: {
        mode: 'history',
        base: '/micro-frontend/'
      }
    },
    
    // 条件加载
    process.env.NODE_ENV === 'development' && {
      plugin: DevToolsPlugin,
      options: { port: 8080 }
    },
    
    // 动态配置
    {
      plugin: SandboxPlugin,
      options: () => ({
        type: window.location.search.includes('iframe') ? 'iframe' : 'proxy'
      })
    }
  ].filter(Boolean)
});
```

### 插件优先级

```typescript
const microCore = new MicroCore({
  plugins: [
    { plugin: RouterPlugin, priority: 100 },      // 最高优先级
    { plugin: SandboxPlugin, priority: 50 },      // 中等优先级
    { plugin: CommunicationPlugin, priority: 10 } // 低优先级
  ]
});
```

### 插件热更新

```typescript
// 动态添加插件
await microCore.addPlugin(new CustomPlugin());

// 动态移除插件
await microCore.removePlugin('custom-plugin');

// 重新加载插件
await microCore.reloadPlugin('custom-plugin', newOptions);
```

## 最佳实践

### 1. 插件设计原则

- **单一职责**: 每个插件只负责一个特定功能
- **松耦合**: 插件间通过事件或接口通信，避免直接依赖
- **可配置**: 提供丰富的配置选项，满足不同使用场景
- **向后兼容**: 保持 API 稳定性，谨慎处理破坏性变更

### 2. 性能优化

```typescript
export class OptimizedPlugin extends BasePlugin {
  private cache = new Map();
  
  install(kernel: MicroCoreKernel): void {
    // 延迟初始化
    kernel.registerHook('beforeMount', this.lazyInit.bind(this));
  }
  
  private async lazyInit(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
      this.initialized = true;
    }
  }
  
  // 缓存计算结果
  private computeExpensiveOperation(input: any): any {
    const cacheKey = JSON.stringify(input);
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    const result = this.doExpensiveOperation(input);
    this.cache.set(cacheKey, result);
    
    return result;
  }
}
```

### 3. 错误处理

```typescript
export class RobustPlugin extends BasePlugin {
  install(kernel: MicroCoreKernel): void {
    try {
      this.doInstall(kernel);
    } catch (error) {
      console.error(`Failed to install ${this.name}:`, error);
      
      // 发送错误事件
      kernel.emit('plugin:error', {
        plugin: this.name,
        error,
        phase: 'install'
      });
      
      // 可选：回滚操作
      this.rollback(kernel);
    }
  }
  
  private rollback(kernel: MicroCoreKernel): void {
    // 清理已注册的功能
    // 恢复之前的状态
  }
}
```

### 4. 调试和监控

```typescript
export class DebuggablePlugin extends BasePlugin {
  private debug = require('debug')(`micro-core:plugin:${this.name}`);
  
  install(kernel: MicroCoreKernel): void {
    this.debug('Installing plugin...');
    
    // 性能监控
    const startTime = performance.now();
    
    this.doInstall(kernel);
    
    const endTime = performance.now();
    this.debug(`Installation completed in ${endTime - startTime}ms`);
    
    // 注册调试命令
    if (process.env.NODE_ENV === 'development') {
      (window as any).__MICRO_CORE_DEBUG__ = {
        ...(window as any).__MICRO_CORE_DEBUG__,
        [this.name]: {
          getState: () => this.getDebugState(),
          reload: () => this.reload()
        }
      };
    }
  }
}
```

## 社区插件

### 推荐插件

- **@micro-core/plugin-analytics**: 应用分析和监控
- **@micro-core/plugin-i18n**: 国际化支持
- **@micro-core/plugin-theme**: 主题系统
- **@micro-core/plugin-auth**: 身份认证和授权
- **@micro-core/plugin-cache**: 智能缓存管理
- **@micro-core/plugin-logger**: 日志管理
- **@micro-core/plugin-performance**: 性能监控

### 插件开发资源

- [插件开发模板](https://github.com/micro-core/plugin-template)
- [插件开发指南](https://micro-core.dev/guide/plugin-development)
- [插件 API 文档](https://micro-core.dev/api/plugin)
- [社区插件列表](https://github.com/micro-core/awesome-plugins)

通过 Micro-Core 的插件系统，开发者可以构建高度定制化和可扩展的微前端应用，满足各种复杂的业务需求。
