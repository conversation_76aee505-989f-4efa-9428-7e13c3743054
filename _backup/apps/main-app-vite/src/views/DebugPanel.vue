<template>
  <div class="debug-panel">
    <div class="debug-header">
      <h2>🐛 微前端调试面板</h2>
      <div class="debug-actions">
        <button @click="refreshData" class="refresh-btn">🔄 刷新</button>
        <button @click="clearLogs" class="clear-btn">🗑️清空日志</button>
      </div>
    </div>

    <div class="debug-content">
      <!-- 系统概览 -->
      <div class="debug-section">
        <h3>📊 系统概览</h3>
        <div class="overview-grid">
          <div class="overview-card">
            <div class="card-title">注册应用</div>
            <div class="card-value">{{ allApps.length }}</div>
          </div>
          <div class="overview-card">
            <div class="card-title">已加载应用</div>
            <div class="card-value">{{ loadedApps.length }}</div>
          </div>
          <div class="overview-card">
            <div class="card-title">活跃应用</div>
            <div class="card-value">{{ activeApp || '无' }}</div>
          </div>
          <div class="overview-card">
            <div class="card-title">平均加载时间</div>
            <div class="card-value">{{ averageLoadTime }}ms</div>
          </div>
        </div>
      </div>

      <!-- 应用列表 -->
      <div class="debug-section">
        <h3>📱 应用列表</h3>
        <div class="app-list">
          <div 
            v-for="app in allApps" 
            :key="app.name"
            class="app-item"
            :class="{ 
              'app-loaded': loadedApps.includes(app.name),
              'app-active': activeApp === app.name 
            }"
          >
            <div class="app-info">
              <div class="app-name">{{ app.name }}</div>
              <div class="app-entry">{{ app.entry }}</div>
              <div class="app-container">容器: {{ app.container }}</div>
            </div>
            <div class="app-status">
              <span :class="['status-dot', getAppStatusClass(app.name)]"></span>
              {{ getAppStatus(app.name) }}
            </div>
            <div class="app-actions">
              <button 
                v-if="!loadedApps.includes(app.name)"
                @click="loadApp(app.name)"
                class="load-btn"
              >
                加载
              </button>
              <button 
                v-else
                @click="unloadApp(app.name)"
                class="unload-btn"
              >
                卸载
              </button>
              <button @click="navigateToApp(app.name)" class="nav-btn">
                导航
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 性能指标 -->
      <div class="debug-section">
        <h3>⚡ 性能指标</h3>
        <div class="performance-grid">
          <div 
            v-for="[key, value] in performanceMetrics" 
            :key="key"
            class="performance-item"
          >
            <div class="metric-name">{{ formatMetricName(key) }}</div>
            <div class="metric-value">{{ formatMetricValue(key, value) }}</div>
          </div>
        </div>
      </div>

      <!-- 插件状态 -->
      <div class="debug-section">
        <h3>🔌 插件状态</h3>
        <div class="plugin-list">
          <div 
            v-for="plugin in enabledPlugins" 
            :key="plugin.name"
            class="plugin-item"
          >
            <div class="plugin-info">
              <div class="plugin-name">{{ plugin.name }}</div>
              <div class="plugin-status">
                <span class="status-dot status-success"></span>
                已启用
              </div>
            </div>
            <div class="plugin-actions">
              <button @click="disablePlugin(plugin.name)" class="disable-btn">
                禁用
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 日志面板 -->
      <div class="debug-section">
        <h3>📝 系统日志</h3>
        <div class="log-controls">
          <select v-model="logLevel" @change="filterLogs">
            <option value="all">所有级别</option>
            <option value="error">错误</option>
            <option value="warn">警告</option>
            <option value="info">信息</option>
            <option value="debug">调试</option>
          </select>
          <button @click="exportLogs" class="export-btn">导出日志</button>
        </div>
        <div class="log-container">
          <div 
            v-for="(log, index) in filteredLogs" 
            :key="index"
            :class="['log-entry', `log-${log.level}`]"
          >
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <span class="log-context">{{ log.context }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  getMicroCoreManager, 
  getAllApplications, 
  getLoadedApplications, 
  getPerformanceMetrics,
  loadApp as loadApplication,
  unloadApp as unloadApplication
} from '../micro-config'

const router = useRouter()

// 响应式数据
const allApps = ref<any[]>([])
const loadedApps = ref<string[]>([])
const activeApp = ref<string>('')
const performanceMetrics = ref<Map<string, number>>(new Map())
const enabledPlugins = ref<any[]>([])
const logs = ref<any[]>([])
const filteredLogs = ref<any[]>([])
const logLevel = ref('all')

// 计算属性
const averageLoadTime = computed(() => {
  const loadTimes = Array.from(performanceMetrics.value.entries())
    .filter(([key]) => key.endsWith('-load-time'))
    .map(([, value]) => value)
  
  if (loadTimes.length === 0) return 0
  return Math.round(loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length)
})

// 刷新数据
const refreshData = () => {
  try {
    allApps.value = getAllApplications()
    loadedApps.value = getLoadedApplications()
    performanceMetrics.value = getPerformanceMetrics()
    
    const manager = getMicroCoreManager()
    if (manager) {
      enabledPlugins.value = manager.getPluginManager().getEnabled()
    }
    
    // 获取当前活跃应用
    const currentPath = router.currentRoute.value.path
    const appRoutes = [
      { path: '/react-app', name: 'react-app' },
      { path: '/vue3-app', name: 'vue3-app' },
      { path: '/vue2-app', name: 'vue2-app' },
      { path: '/angular-app', name: 'angular-app' },
      { path: '/svelte-app', name: 'svelte-app' },
      { path: '/solid-app', name: 'solid-app' },
      { path: '/html-app', name: 'html-app' }
    ]
    
    const currentApp = appRoutes.find(app => currentPath.startsWith(app.path))
    activeApp.value = currentApp?.name || ''
    
  } catch (error) {
    console.error('Failed to refresh debug data:', error)
  }
}

// 获取应用状态
const getAppStatus = (appName: string) => {
  if (loadedApps.value.includes(appName)) {
    return activeApp.value === appName ? '活跃' : '已加载'
  }
  return '未加载'
}

const getAppStatusClass = (appName: string) => {
  if (activeApp.value === appName) return 'status-active'
  if (loadedApps.value.includes(appName)) return 'status-loaded'
  return 'status-unloaded'
}

// 应用操作
const loadApp = async (appName: string) => {
  try {
    await loadApplication(appName)
    refreshData()
  } catch (error) {
    console.error(`Failed to load app ${appName}:`, error)
  }
}

const unloadApp = async (appName: string) => {
  try {
    await unloadApplication(appName)
    refreshData()
  } catch (error) {
    console.error(`Failed to unload app ${appName}:`, error)
  }
}

const navigateToApp = (appName: string) => {
  router.push(`/${appName}`)
}

// 插件操作
const disablePlugin = (pluginName: string) => {
  const manager = getMicroCoreManager()
  if (manager) {
    manager.getPluginManager().disable(pluginName)
    refreshData()
  }
}

// 格式化指标
const formatMetricName = (key: string) => {
  return key.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatMetricValue = (key: string, value: number) => {
  if (key.includes('time')) {
    return `${value}ms`
  }
  return value.toString()
}

// 日志操作
const clearLogs = () => {
  logs.value = []
  filteredLogs.value = []
}

const filterLogs = () => {
  if (logLevel.value === 'all') {
    filteredLogs.value = logs.value
  } else {
    filteredLogs.value = logs.value.filter(log => log.level === logLevel.value)
  }
}

const exportLogs = () => {
  const logData = filteredLogs.value.map(log => 
    `[${formatTime(log.timestamp)}] [${log.level.toUpperCase()}] [${log.context}] ${log.message}`
  ).join('\n')
  
  const blob = new Blob([logData], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `micro-core-logs-${new Date().toISOString().slice(0, 19)}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 模拟日志收集
const collectLogs = () => {
  // 这里应该从实际的日志系统收集日志
  // 现在只是模拟一些日志条目
  const sampleLogs = [
    { timestamp: Date.now(), level: 'info', context: 'MicroCore', message: 'System initialized' },
    { timestamp: Date.now() - 1000, level: 'debug', context: 'Router', message: 'Route changed to /debug' },
    { timestamp: Date.now() - 2000, level: 'warn', context: 'Communication', message: 'Plugin communication timeout' }
  ]
  
  logs.value = sampleLogs
  filterLogs()
}

onMounted(() => {
  refreshData()
  collectLogs()
  
  // 定期刷新数据
  const refreshInterval = setInterval(refreshData, 5000)
  
  onUnmounted(() => {
    clearInterval(refreshInterval)
  })
})
</script>

<style scoped>
.debug-panel {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--border-color);
}

.debug-header h2 {
  margin: 0;
  color: var(--primary-color);
}

.debug-actions {
  display: flex;
  gap: 1rem;
}

.refresh-btn, .clear-btn {
  background: var(--button-bg);
  color: var(--button-text);
  border: 1px solid var(--border-color);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.refresh-btn:hover, .clear-btn:hover {
  background: var(--button-hover-bg);
}

.debug-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.debug-section {
  background: var(--container-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
}

.debug-section h3 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: 1.2rem;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.overview-card {
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 1rem;
  text-align: center;
}

.card-title {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.card-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
}

.app-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.app-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 1rem;
  transition: all 0.3s;
}

.app-item.app-loaded {
  border-color: var(--primary-color);
  background: rgba(66, 184, 131, 0.05);
}

.app-item.app-active {
  border-color: #28a745;
  background: rgba(40, 167, 69, 0.1);
}

.app-info {
  flex: 1;
}

.app-name {
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.app-entry, .app-container {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.app-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 1rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-active {
  background: #28a745;
}

.status-loaded {
  background: var(--primary-color);
}

.status-unloaded {
  background: #6c757d;
}

.app-actions {
  display: flex;
  gap: 0.5rem;
}

.load-btn, .unload-btn, .nav-btn, .disable-btn {
  background: var(--button-bg);
  color: var(--button-text);
  border: 1px solid var(--border-color);
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.3s;
}

.load-btn:hover, .nav-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.unload-btn:hover, .disable-btn:hover {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.performance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.75rem;
}

.metric-name {
  color: var(--text-secondary);
}

.metric-value {
  font-weight: bold;
  color: var(--primary-color);
}

.plugin-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.plugin-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.75rem;
}

.plugin-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.plugin-name {
  font-weight: bold;
  color: var(--text-color);
}

.plugin-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.status-success {
  background: #28a745;
}

.log-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.log-controls select {
  background: var(--bg-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  padding: 0.5rem;
  border-radius: 4px;
}

.export-btn {
  background: var(--button-bg);
  color: var(--button-text);
  border: 1px solid var(--border-color);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.export-btn:hover {
  background: var(--button-hover-bg);
}

.log-container {
  background: #1a1a1a;
  color: #e0e0e0;
  border-radius: 4px;
  padding: 1rem;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
}

.log-entry {
  display: flex;
  gap: 1rem;
  padding: 0.25rem 0;
  border-bottom: 1px solid #333;
}

.log-time {
  color: #888;
  min-width: 80px;
}

.log-level {
  min-width: 60px;
  font-weight: bold;
}

.log-error .log-level {
  color: #ff6b6b;
}

.log-warn .log-level {
  color: #ffd93d;
}

.log-info .log-level {
  color: #6bcf7f;
}

.log-debug .log-level {
  color: #74c0fc;
}

.log-context {
  color: #a78bfa;
  min-width: 100px;
}

.log-message {
  flex: 1;
  color: #e0e0e0;
}

@media (max-width: 768px) {
  .debug-panel {
    padding: 1rem;
  }
  
  .debug-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .overview-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .app-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .app-actions {
    align-self: stretch;
    justify-content: space-between;
  }
}
</style>