/**
 * 微前端核心类型定义
 *
 * @description 微前端架构核心类型定义 - 从 core 包迁移
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 应用状态
 */
export type ApplicationStatus = 'registered' | 'loading' | 'loaded' | 'mounting' | 'mounted' | 'unmounting' | 'error';
/**
 * 框架类型
 */
export type FrameworkType = 'react' | 'vue2' | 'vue3' | 'angular' | 'svelte' | 'solid' | 'lit' | 'html' | 'vanilla';
/**
 * 应用配置
 */
export interface ApplicationConfig {
    /** 应用名称 */
    name: string;
    /** 应用入口 */
    entry?: string;
    /** 应用框架类型 */
    framework?: FrameworkType;
    /** 挂载容器 */
    container?: string | HTMLElement;
    /** 应用属性 */
    props?: Record<string, any>;
    /** 沙箱配置 */
    sandbox?: boolean | Record<string, any>;
    /** 生命周期钩子 */
    lifecycle?: Partial<ApplicationLifecycleHooks>;
    /** 自定义配置 */
    [key: string]: any;
}
/**
 * 应用配置类型别名（兼容性）
 */
export type AppConfig = ApplicationConfig;
/**
 * 应用实例
 */
export interface ApplicationInstance {
    /** 应用名称 */
    name: string;
    /** 应用配置 */
    config: ApplicationConfig;
    /** 应用状态 */
    status: ApplicationStatus;
    /** 创建时间 */
    createdAt: number;
    /** 更新时间 */
    updatedAt: number;
    /** 挂载容器 */
    container?: HTMLElement;
    /** 沙箱实例（通过插件提供） */
    sandbox?: any;
    /** 适配器实例（通过插件提供） */
    adapter?: any;
    /** 最后错误 */
    lastError?: Error;
}
/**
 * 应用生命周期钩子
 */
export interface ApplicationLifecycleHooks {
    /** 加载前 */
    beforeLoad?: (instance: ApplicationInstance) => Promise<void> | void;
    /** 加载后 */
    afterLoad?: (instance: ApplicationInstance) => Promise<void> | void;
    /** 挂载前 */
    beforeMount?: (instance: ApplicationInstance) => Promise<void> | void;
    /** 挂载后 */
    afterMount?: (instance: ApplicationInstance) => Promise<void> | void;
    /** 卸载前 */
    beforeUnmount?: (instance: ApplicationInstance) => Promise<void> | void;
    /** 卸载后 */
    afterUnmount?: (instance: ApplicationInstance) => Promise<void> | void;
    /** 注销前 */
    beforeUnregister?: (instance: ApplicationInstance) => Promise<void> | void;
    /** 注销后 */
    afterUnregister?: (instance: ApplicationInstance) => Promise<void> | void;
}
/**
 * 系统生命周期钩子
 */
export interface LifecycleHooks {
    /** 初始化前 */
    beforeInit?: () => Promise<void> | void;
    /** 初始化后 */
    afterInit?: () => Promise<void> | void;
    /** 启动前 */
    beforeStart?: () => Promise<void> | void;
    /** 启动后 */
    afterStart?: () => Promise<void> | void;
    /** 停止前 */
    beforeStop?: () => Promise<void> | void;
    /** 停止后 */
    afterStop?: () => Promise<void> | void;
    /** 销毁前 */
    beforeDestroy?: () => Promise<void> | void;
    /** 销毁后 */
    afterDestroy?: () => Promise<void> | void;
}
/**
 * 插件接口
 */
export interface PluginInterface {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 插件描述 */
    description?: string;
    /** 插件依赖 */
    dependencies?: string[];
    /** 插件安装方法 */
    install(core: any): Promise<void> | void;
    /** 插件初始化方法 */
    initialize?(core: any): Promise<void> | void;
    /** 插件启动方法 */
    start?(core: any): Promise<void> | void;
    /** 插件停止方法 */
    stop?(core: any): Promise<void> | void;
    /** 插件销毁方法 */
    destroy?(core: any): Promise<void> | void;
    /** 应用加载钩子 */
    loadApplication?(instance: ApplicationInstance): Promise<void> | void;
    /** 应用挂载钩子 */
    mountApplication?(instance: ApplicationInstance, container: HTMLElement): Promise<void> | void;
    /** 应用卸载钩子 */
    unmountApplication?(instance: ApplicationInstance): Promise<void> | void;
    /** 应用注销钩子 */
    unregisterApplication?(instance: ApplicationInstance): Promise<void> | void;
    /** 应用发现钩子 */
    discoverApplications?(): Promise<void> | void;
}
/**
 * 管理器接口
 */
export interface ManagerInterface {
    /** 管理器名称 */
    name: string;
    /** 管理器版本 */
    version: string;
    /** 初始化管理器 */
    initialize?(): Promise<void> | void;
    /** 启动管理器 */
    start?(): Promise<void> | void;
    /** 停止管理器 */
    stop?(): Promise<void> | void;
    /** 销毁管理器 */
    destroy?(): Promise<void> | void;
}
/**
 * 微前端核心配置
 */
export interface MicroCoreConfig {
    /** 是否自动发现应用 */
    autoDiscovery?: boolean;
    /** 生命周期钩子 */
    lifecycle?: Partial<LifecycleHooks>;
}
/**
 * 微前端核心实例
 */
export interface MicroCoreInstance {
    /** 初始化系统 */
    initialize(): Promise<void>;
    /** 启动系统 */
    start(): Promise<void>;
    /** 注册插件 */
    registerPlugin(plugin: PluginInterface): Promise<void>;
    /** 注册管理器 */
    registerManager(name: string, manager: ManagerInterface): void;
    /** 获取管理器 */
    getManager<T extends ManagerInterface>(name: string): T | undefined;
    /** 注册应用 */
    registerApplication(config: ApplicationConfig): Promise<void>;
    /** 加载应用 */
    loadApplication(name: string): Promise<void>;
    /** 挂载应用 */
    mountApplication(name: string, container?: HTMLElement): Promise<void>;
    /** 卸载应用 */
    unmountApplication(name: string): Promise<void>;
    /** 注销应用 */
    unregisterApplication(name: string): Promise<void>;
    /** 获取应用实例 */
    getApplication(name: string): ApplicationInstance | undefined;
    /** 获取所有应用 */
    getAllApplications(): ApplicationInstance[];
    /** 获取应用状态 */
    getApplicationStatus(name: string): string | undefined;
    /** 检查应用是否存在 */
    hasApplication(name: string): boolean;
    /** 获取系统状态 */
    getStatus(): {
        initialized: boolean;
        started: boolean;
        applications: number;
        plugins: number;
    };
    /** 停止系统 */
    stop(): Promise<void>;
    /** 销毁系统 */
    destroy(): Promise<void>;
}
/**
 * 应用事件类型
 */
export type ApplicationEventType = 'application:registered' | 'application:loaded' | 'application:mounted' | 'application:unmounted' | 'application:unregistered' | 'application:error';
/**
 * 应用事件数据
 */
export interface ApplicationEventData {
    /** 事件类型 */
    type: ApplicationEventType;
    /** 应用名称 */
    name: string;
    /** 应用实例 */
    instance?: ApplicationInstance;
    /** 应用配置 */
    config?: ApplicationConfig;
    /** 挂载容器 */
    container?: HTMLElement;
    /** 操作类型 */
    operation?: string;
    /** 错误信息 */
    error?: Error;
    /** 事件时间戳 */
    timestamp: number;
}
/**
 * 系统事件类型
 */
export type SystemEventType = 'system:initialized' | 'system:started' | 'system:stopped' | 'system:destroyed' | 'system:error' | 'plugin:registered' | 'plugin:initialized' | 'plugin:started' | 'plugin:stopped' | 'plugin:destroyed';
/**
 * 系统事件数据
 */
export interface SystemEventData {
    /** 事件类型 */
    type: SystemEventType;
    /** 系统状态 */
    status?: any;
    /** 插件信息 */
    plugin?: PluginInterface;
    /** 错误信息 */
    error?: Error;
    /** 事件时间戳 */
    timestamp: number;
}
/**
 * 路由配置
 */
export interface RouteConfig {
    /** 路由路径 */
    path: string;
    /** 应用名称 */
    app: string;
    /** 是否精确匹配 */
    exact?: boolean;
    /** 路由参数 */
    params?: Record<string, any>;
}
/**
 * 路由匹配结果
 */
export interface RouteMatch {
    /** 匹配的路由配置 */
    route: RouteConfig;
    /** 路由参数 */
    params: Record<string, any>;
    /** 查询参数 */
    query: Record<string, any>;
}
/**
 * 应用激活条件
 */
export type ActiveWhen = string | RegExp | ((location: Location) => boolean) | Array<string | RegExp | ((location: Location) => boolean)>;
/**
 * 应用加载函数
 */
export type LoadFunction = () => Promise<{
    mount: (props: any) => Promise<void>;
    unmount: (props: any) => Promise<void>;
    bootstrap?: (props: any) => Promise<void>;
    update?: (props: any) => Promise<void>;
}>;
/**
 * 应用注册配置
 */
export interface AppRegistrationConfig {
    /** 应用名称 */
    name: string;
    /** 应用激活条件 */
    activeWhen: ActiveWhen;
    /** 应用加载函数 */
    app: LoadFunction | ApplicationConfig;
    /** 自定义属性 */
    customProps?: Record<string, any> | (() => Record<string, any>);
}
/**
 * 核心错误处理器类型（避免与其他ErrorHandler冲突）
 */
export type CoreErrorHandler = (error: Error, errorInfo: {
    /** 错误来源 */
    source: 'application' | 'system' | 'plugin' | 'adapter' | 'sandbox' | 'communication';
    /** 应用名称（如果适用） */
    appName?: string;
    /** 操作类型 */
    operation?: string;
    /** 额外上下文 */
    context?: any;
}) => void;
/**
 * 性能监控配置
 */
export interface PerformanceConfig {
    /** 是否启用性能监控 */
    enabled?: boolean;
    /** 采样率 */
    sampleRate?: number;
    /** 性能指标收集间隔（毫秒） */
    collectInterval?: number;
    /** 性能数据上报地址 */
    reportUrl?: string;
    /** 自定义性能指标 */
    customMetrics?: string[];
}
/**
 * 性能指标
 */
export interface PerformanceMetrics {
    /** 应用名称 */
    appName?: string;
    /** 加载时间 */
    loadTime?: number;
    /** 挂载时间 */
    mountTime?: number;
    /** 卸载时间 */
    unmountTime?: number;
    /** 内存使用量 */
    memoryUsage?: number;
    /** CPU使用率 */
    cpuUsage?: number;
    /** 网络请求数 */
    networkRequests?: number;
    /** 错误次数 */
    errorCount?: number;
    /** 自定义指标 */
    customMetrics?: Record<string, number>;
    /** 时间戳 */
    timestamp: number;
}
/**
 * 开发工具配置
 */
export interface DevToolsConfig {
    /** 是否启用开发工具 */
    enabled?: boolean;
    /** 开发工具面板位置 */
    position?: 'top' | 'bottom' | 'left' | 'right';
    /** 是否显示性能面板 */
    showPerformance?: boolean;
    /** 是否显示应用面板 */
    showApplications?: boolean;
    /** 是否显示通信面板 */
    showCommunication?: boolean;
    /** 是否显示沙箱面板 */
    showSandbox?: boolean;
    /** 是否显示插件面板 */
    showPlugins?: boolean;
}
/**
 * 缓存配置
 */
export interface CacheConfig {
    /** 是否启用缓存 */
    enabled?: boolean;
    /** 缓存策略 */
    strategy?: 'memory' | 'localStorage' | 'sessionStorage' | 'indexedDB';
    /** 缓存过期时间（毫秒） */
    expireTime?: number;
    /** 最大缓存大小 */
    maxSize?: number;
    /** 缓存键前缀 */
    keyPrefix?: string;
}
/**
 * 安全配置
 */
export interface SecurityConfig {
    /** 是否启用CSP */
    enableCSP?: boolean;
    /** CSP策略 */
    cspPolicy?: string;
    /** 允许的域名列表 */
    allowedDomains?: string[];
    /** 是否启用HTTPS检查 */
    enforceHTTPS?: boolean;
    /** 是否启用完整性检查 */
    enableIntegrityCheck?: boolean;
}
//# sourceMappingURL=core-types.d.ts.map