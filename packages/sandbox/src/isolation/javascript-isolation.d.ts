/**
 * JavaScript 隔离实现
 *
 * @description 实现 JavaScript 执行环境的隔离
 * <AUTHOR> <<EMAIL>>
 */
import type { IsolationConfig, Isolator, SandboxContext } from '../types';
/**
 * JavaScript 隔离器
 * 负责隔离 JavaScript 执行环境
 */
export declare class JavaScriptIsolation implements Isolator {
    readonly name = "JavaScript\u9694\u79BB\u5668";
    enabled: boolean;
    private readonly logger;
    private config?;
    constructor();
    /**
     * 初始化隔离器
     */
    initialize(config: IsolationConfig): Promise<void>;
    /**
     * 应用隔离
     */
    apply(context: SandboxContext): Promise<void>;
    /**
     * 移除隔离
     */
    remove(context: SandboxContext): Promise<void>;
    /**
     * 清理资源
     */
    cleanup(): Promise<void>;
    /**
     * 应用全局变量隔离
     */
    private applyGlobalVariableIsolation;
    /**
     * 应用原生 API 隔离
     */
    private applyNativeAPIIsolation;
    /**
     * 恢复原始环境
     */
    private restoreOriginalEnvironment;
    /**
     * 检查变量是否被允许访问
     */
    isVariableAllowed(variableName: string): boolean;
    /**
     * 检查 API 是否被允许访问
     */
    isAPIAllowed(apiName: string): boolean;
}
//# sourceMappingURL=javascript-isolation.d.ts.map