/**
 * 核心工厂函数
 *
 * @description 提供创建核心系统组件的工厂函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import type { EventBus, EventBusConfig, GlobalState, GlobalStateConfig, MicroCoreError } from '../types/core-types';
/**
 * 创建事件总线
 */
export declare function createEventBus(config?: EventBusConfig): EventBus;
/**
 * 创建全局状态管理器
 */
export declare function createGlobalState(config?: GlobalStateConfig): GlobalState;
/**
 * 创建错误对象
 */
export declare function createError(code: number, message: string, details?: any, context?: string): MicroCoreError;
//# sourceMappingURL=core-factories.d.ts.map