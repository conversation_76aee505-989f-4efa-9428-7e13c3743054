# wujie API 对照表

本文档提供了从 wujie 迁移到 Micro-Core 的详细 API 对照表，帮助开发者快速完成迁移。

## 主应用 API 对照

### 应用注册和启动

#### wujie

```javascript
import { setupApp, startApp } from 'wujie';

// 预加载应用
setupApp({
  name: 'vue3',
  url: 'http://localhost:3000/',
  exec: true,
  fetch: (url, options) => window.fetch(url, options),
  props: {
    jump: (name) => {
      startApp({ name });
    }
  }
});

// 启动应用
startApp({
  name: 'vue3',
  url: 'http://localhost:3000/',
  el: '#container',
  props: {
    data: 'hello world'
  }
});
```

#### Micro-Core

```typescript
import { MicroCoreKernel } from '@micro-core/core';

const kernel = new MicroCoreKernel();

// 注册应用
kernel.registerApplication({
  name: 'vue3',
  entry: 'http://localhost:3000/',
  container: '#container',
  activeWhen: '/vue3',
  props: {
    data: 'hello world',
    jump: (name) => {
      kernel.mountApplication(name);
    }
  },
  prefetch: true // 预加载
});

// 启动系统
kernel.start();

// 手动启动特定应用
await kernel.mountApplication('vue3');
```

### 应用销毁

#### wujie

```javascript
import { destroyApp } from 'wujie';

// 销毁应用
destroyApp('vue3');
```

#### Micro-Core

```typescript
// 卸载应用
await kernel.unmountApplication('vue3');

// 完全销毁应用
await kernel.unregisterApplication('vue3');
```

### 应用预加载

#### wujie

```javascript
import { preloadApp } from 'wujie';

// 预加载应用
preloadApp({
  name: 'vue3',
  url: 'http://localhost:3000/'
});
```

#### Micro-Core

```typescript
// 预加载应用
await kernel.prefetchApplication('vue3', {
  strategy: 'idle',
  priority: 'high'
});

// 或在注册时配置预加载
kernel.registerApplication({
  name: 'vue3',
  entry: 'http://localhost:3000/',
  container: '#container',
  activeWhen: '/vue3',
  prefetch: {
    strategy: 'immediate',
    priority: 'high'
  }
});
```

### 生命周期钩子

#### wujie

```javascript
startApp({
  name: 'vue3',
  url: 'http://localhost:3000/',
  el: '#container',
  beforeLoad: (appWindow) => {
    console.log('应用开始加载', appWindow);
  },
  beforeMount: (appWindow) => {
    console.log('应用开始挂载', appWindow);
  },
  afterMount: (appWindow) => {
    console.log('应用挂载完成', appWindow);
  },
  beforeUnmount: (appWindow) => {
    console.log('应用开始卸载', appWindow);
  },
  afterUnmount: (appWindow) => {
    console.log('应用卸载完成', appWindow);
  },
  activated: (appWindow) => {
    console.log('应用激活', appWindow);
  },
  deactivated: (appWindow) => {
    console.log('应用失活', appWindow);
  }
});
```

#### Micro-Core

```typescript
kernel.registerApplication({
  name: 'vue3',
  entry: 'http://localhost:3000/',
  container: '#container',
  activeWhen: '/vue3',
  lifecycle: {
    beforeLoad: async (app) => {
      console.log('应用开始加载', app);
    },
    afterLoad: async (app) => {
      console.log('应用加载完成', app);
    },
    beforeMount: async (app) => {
      console.log('应用开始挂载', app);
    },
    afterMount: async (app) => {
      console.log('应用挂载完成', app);
    },
    beforeUnmount: async (app) => {
      console.log('应用开始卸载', app);
    },
    afterUnmount: async (app) => {
      console.log('应用卸载完成', app);
    }
  }
});

// 监听应用激活/失活
kernel.on('application:activate', (app) => {
  console.log('应用激活', app);
});

kernel.on('application:deactivate', (app) => {
  console.log('应用失活', app);
});
```

### 沙箱配置

#### wujie

```javascript
startApp({
  name: 'vue3',
  url: 'http://localhost:3000/',
  el: '#container',
  alive: true, // 保活模式
  exec: true,  // 是否执行 JS
  fetch: (url, options) => window.fetch(url, options),
  replace: (code) => code.replace('old', 'new'),
  plugins: [
    {
      htmlLoader: (code) => code,
      jsLoader: (code) => code,
      cssLoader: (code) => code
    }
  ]
});
```

#### Micro-Core

```typescript
import { IframeSandboxPlugin } from '@micro-core/plugin-sandbox-iframe';

// 配置沙箱插件
kernel.use(IframeSandboxPlugin, {
  allowScripts: true,
  allowSameOrigin: false,
  keepAlive: true // 保活模式
});

kernel.registerApplication({
  name: 'vue3',
  entry: 'http://localhost:3000/',
  container: '#container',
  activeWhen: '/vue3',
  sandbox: {
    type: 'iframe',
    keepAlive: true,
    plugins: [
      {
        name: 'code-transformer',
        transform: {
          html: (code) => code,
          js: (code) => code.replace('old', 'new'),
          css: (code) => code
        }
      }
    ]
  }
});
```

## 通信 API 对照

### 主子应用通信

#### wujie

```javascript
// 主应用
import { bus } from 'wujie';

// 监听子应用事件
bus.$on('vue3', (event) => {
  console.log('收到子应用事件:', event);
});

// 向子应用发送事件
bus.$emit('vue3', { type: 'message', data: 'hello' });

// 子应用
window.$wujie?.bus.$emit('message', { data: 'from child' });

window.$wujie?.bus.$on('message', (data) => {
  console.log('收到主应用消息:', data);
});
```

#### Micro-Core

```typescript
// 主应用
import { EventBus } from '@micro-core/core';

// 监听子应用事件
EventBus.on('vue3:message', (event) => {
  console.log('收到子应用事件:', event);
});

// 向子应用发送事件
EventBus.emit('main:message', { type: 'message', data: 'hello' });

// 子应用
import { EventBus } from '@micro-core/core';

EventBus.emit('vue3:message', { data: 'from child' });

EventBus.on('main:message', (data) => {
  console.log('收到主应用消息:', data);
});
```

### Props 传递

#### wujie

```javascript
// 主应用
startApp({
  name: 'vue3',
  url: 'http://localhost:3000/',
  el: '#container',
  props: {
    userInfo: { id: 1, name: 'John' },
    theme: 'dark'
  }
});

// 子应用获取 props
const props = window.$wujie?.props;
console.log(props.userInfo, props.theme);
```

#### Micro-Core

```typescript
// 主应用
kernel.registerApplication({
  name: 'vue3',
  entry: 'http://localhost:3000/',
  container: '#container',
  activeWhen: '/vue3',
  props: {
    userInfo: { id: 1, name: 'John' },
    theme: 'dark'
  }
});

// 子应用获取 props (在生命周期函数中)
export const mount = VueAdapter.mount(async (props) => {
  const { userInfo, theme } = props;
  console.log(userInfo, theme);
});
```

### 全局状态管理

#### wujie

```javascript
// wujie 没有内置全局状态管理，需要通过 props 或事件通信实现

// 主应用
let globalState = { user: null, theme: 'light' };

const updateGlobalState = (newState) => {
  globalState = { ...globalState, ...newState };
  // 通知所有子应用
  bus.$emit('vue3', { type: 'state-change', state: globalState });
};

// 子应用
window.$wujie?.bus.$on('state-change', (data) => {
  console.log('全局状态更新:', data.state);
});
```

#### Micro-Core

```typescript
// 主应用
import { GlobalState } from '@micro-core/core';

// 设置全局状态
GlobalState.set('user', null);
GlobalState.set('theme', 'light');

// 监听状态变化
GlobalState.watch('user', (newUser, oldUser) => {
  console.log('用户状态变化:', newUser);
});

// 更新状态
GlobalState.set('user', { id: 1, name: 'John' });

// 子应用
import { GlobalState } from '@micro-core/core';

// 获取全局状态
const user = GlobalState.get('user');
const theme = GlobalState.get('theme');

// 监听状态变化
GlobalState.watch('theme', (newTheme) => {
  console.log('主题变化:', newTheme);
});

// 更新全局状态
GlobalState.set('theme', 'dark');
```

## 路由 API 对照

### 路由同步

#### wujie

```javascript
// wujie 通过 sync 参数同步路由
startApp({
  name: 'vue3',
  url: 'http://localhost:3000/',
  el: '#container',
  sync: true, // 同步路由
  prefix: { 'vue3': '/vue3' } // 路由前缀
});

// 手动同步路由
import { bus } from 'wujie';

bus.$emit('vue3', { 
  type: 'route-change', 
  path: '/new-path' 
});
```

#### Micro-Core

```typescript
import { RouterPlugin } from '@micro-core/plugin-router';

// 配置路由插件
kernel.use(RouterPlugin, {
  mode: 'history',
  syncRoutes: true // 自动同步路由
});

kernel.registerApplication({
  name: 'vue3',
  entry: 'http://localhost:3000/',
  container: '#container',
  activeWhen: '/vue3',
  routing: {
    basename: '/vue3',
    sync: true // 启用路由同步
  }
});

// 手动路由导航
import { Router } from '@micro-core/plugin-router';

const router = Router.getInstance();
await router.push('/vue3/new-path');
```

### 路由守卫

#### wujie

```javascript
// wujie 没有内置路由守卫，需要在子应用中实现
// 或通过事件通信实现

// 主应用监听路由变化
bus.$on('vue3', (event) => {
  if (event.type === 'route-before-change') {
    // 路由守卫逻辑
    if (!checkPermission(event.to)) {
      bus.$emit('vue3', { type: 'route-block' });
      return;
    }
    bus.$emit('vue3', { type: 'route-allow' });
  }
});
```

#### Micro-Core

```typescript
import { Router } from '@micro-core/plugin-router';

const router = Router.getInstance();

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  console.log(`导航从 ${from.path} 到 ${to.path}`);
  
  // 检查权限
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login');
    return;
  }
  
  next();
});

// 应用级路由守卫
kernel.registerApplication({
  name: 'vue3',
  entry: 'http://localhost:3000/',
  container: '#container',
  activeWhen: '/vue3',
  routing: {
    beforeEnter: async (to, from) => {
      // 进入应用前的检查
      return checkAppPermission('vue3');
    }
  }
});
```

## 微应用适配对照

### Vue 3 微应用

#### wujie

```javascript
// main.js
import { createApp } from 'vue';
import App from './App.vue';

let app;

// wujie 环境下的生命周期
if (window.__WUJIE) {
  let instance;
  
  window.__WUJIE_MOUNT = () => {
    instance = createApp(App);
    instance.mount('#app');
  };
  
  window.__WUJIE_UNMOUNT = () => {
    instance?.unmount();
  };
} else {
  // 独立运行
  createApp(App).mount('#app');
}
```

#### Micro-Core

```typescript
// main.ts
import { createApp, App as VueApp } from 'vue';
import { VueAdapter } from '@micro-core/adapter-vue3';
import App from './App.vue';

let app: VueApp | null = null;

export const mount = VueAdapter.mount(async (props) => {
  const { container } = props;
  app = createApp(App);
  app.mount(container);
  return app;
});

export const unmount = VueAdapter.unmount(async () => {
  if (app) {
    app.unmount();
    app = null;
  }
});

// 独立运行
if (!window.__MICRO_CORE__) {
  app = createApp(App);
  app.mount('#app');
}
```

### React 微应用

#### wujie

```javascript
// index.js
import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';

// wujie 环境下的生命周期
if (window.__WUJIE) {
  let root;
  
  window.__WUJIE_MOUNT = () => {
    root = ReactDOM.render(<App />, document.getElementById('root'));
  };
  
  window.__WUJIE_UNMOUNT = () => {
    ReactDOM.unmountComponentAtNode(document.getElementById('root'));
  };
} else {
  // 独立运行
  ReactDOM.render(<App />, document.getElementById('root'));
}
```

#### Micro-Core

```typescript
// index.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import { ReactAdapter } from '@micro-core/adapter-react';
import App from './App';

let root: ReactDOM.Root | null = null;

export const mount = ReactAdapter.mount(async (props) => {
  const { container } = props;
  root = ReactDOM.createRoot(container);
  root.render(<App />);
  return root;
});

export const unmount = ReactAdapter.unmount(async () => {
  if (root) {
    root.unmount();
    root = null;
  }
});

// 独立运行
if (!window.__MICRO_CORE__) {
  root = ReactDOM.createRoot(document.getElementById('root')!);
  root.render(<App />);
}
```

## 构建配置对照

### Vite 配置

#### wujie

```javascript
// vite.config.js
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 3000,
    cors: true,
    origin: 'http://localhost:3000'
  },
  build: {
    target: 'esnext',
    minify: false,
    cssCodeSplit: false,
    rollupOptions: {
      external: ['vue'],
      output: {
        globals: {
          vue: 'Vue'
        }
      }
    }
  }
});
```

#### Micro-Core

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { microCoreVitePlugin } from '@micro-core/builder-vite';

export default defineConfig({
  plugins: [
    vue(),
    microCoreVitePlugin({
      name: 'vue3-app',
      entry: './src/main.ts',
      exposes: {
        './App': './src/App.vue'
      }
    })
  ],
  server: {
    port: 3000,
    cors: true
  },
  build: {
    rollupOptions: {
      external: ['vue'],
      output: {
        globals: {
          vue: 'Vue'
        }
      }
    }
  }
});
```

### Webpack 配置

#### wujie

```javascript
// webpack.config.js
module.exports = {
  entry: './src/main.js',
  mode: 'development',
  devServer: {
    port: 3000,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  },
  output: {
    publicPath: 'http://localhost:3000/'
  },
  externals: {
    vue: 'Vue'
  }
};
```

#### Micro-Core

```javascript
// webpack.config.js
const { MicroCoreWebpackPlugin } = require('@micro-core/builder-webpack');

module.exports = {
  plugins: [
    new MicroCoreWebpackPlugin({
      name: 'vue3-app',
      entry: './src/main.js',
      exposes: {
        './App': './src/App.vue'
      }
    })
  ],
  devServer: {
    port: 3000,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  },
  externals: {
    vue: 'Vue'
  }
};
```

## 迁移检查清单

### 主应用迁移

- [ ] 替换 `setupApp`/`startApp` 为 `kernel.registerApplication`
- [ ] 替换 `destroyApp` 为 `kernel.unmountApplication`
- [ ] 替换 `preloadApp` 为 `kernel.prefetchApplication`
- [ ] 迁移生命周期钩子
- [ ] 迁移沙箱配置
- [ ] 迁移通信机制 (bus -> EventBus/GlobalState)
- [ ] 迁移路由配置

### 微应用迁移

- [ ] 移除 `window.__WUJIE_MOUNT`/`window.__WUJIE_UNMOUNT`
- [ ] 使用适配器包装生命周期函数
- [ ] 替换 `window.$wujie?.props` 为生命周期参数
- [ ] 替换 `window.$wujie?.bus` 为 `EventBus`
- [ ] 更新构建配置
- [ ] 测试独立运行模式

### 构建配置迁移

- [ ] 更新 Vite/Webpack 配置
- [ ] 添加 Micro-Core 构建插件
- [ ] 更新 package.json 依赖
- [ ] 更新部署脚本

## 常见迁移问题

### 1. 沙箱隔离差异

**wujie 问题:**
```javascript
// wujie 使用 iframe 沙箱，可能导致一些 API 不可用
startApp({
  name: 'app',
  url: 'http://localhost:3000/',
  el: '#container',
  alive: true
});
```

**Micro-Core 解决:**
```typescript
// 提供多种沙箱策略选择
kernel.registerApplication({
  name: 'app',
  entry: 'http://localhost:3000/',
  container: '#container',
  activeWhen: '/app',
  sandbox: {
    type: 'proxy', // 或 'iframe', 'webcomponent'
    keepAlive: true
  }
});
```

### 2. 通信机制差异

**wujie 问题:**
```javascript
// 只能通过事件总线通信
bus.$emit('app', { type: 'message', data: 'hello' });
```

**Micro-Core 解决:**
```typescript
// 多种通信方式
EventBus.emit('app:message', 'hello'); // 事件通信
GlobalState.set('message', 'hello');   // 状态共享
ApplicationCommunicator.sendMessage('app', 'hello'); // 直接通信
```

### 3. 生命周期差异

**wujie 问题:**
```javascript
// 生命周期函数挂载在 window 对象上
window.__WUJIE_MOUNT = () => {};
window.__WUJIE_UNMOUNT = () => {};
```

**Micro-Core 解决:**
```typescript
// 标准的模块导出
export const mount = ReactAdapter.mount(async (props) => {});
export const unmount = ReactAdapter.unmount(async () => {});
```

## 性能对比

| 特性 | wujie | Micro-Core | 优势 |
|------|-------|------------|------|
| 沙箱性能 | 中等 (iframe) | 高 (多策略) | 更灵活的沙箱选择 |
| 通信性能 | 中等 | 高 | 多种高效通信方式 |
| 内存占用 | 高 (iframe) | 低 | 更好的资源管理 |
| 加载速度 | 中等 | 快 | 优化的加载策略 |
| 开发体验 | 中等 | 优秀 | 更好的 TypeScript 支持 |
| 路由管理 | 基础 | 强大 | 统一的路由管理系统 |

## 总结

Micro-Core 相比 wujie 提供了：

1. **更灵活的沙箱策略** - 不仅限于 iframe，支持多种沙箱类型
2. **更强大的通信系统** - 事件总线、全局状态、直接通信多种方式
3. **更完善的路由管理** - 统一的路由系统和路由守卫
4. **更好的开发体验** - 完整的 TypeScript 支持和开发工具
5. **更高的性能** - 优化的加载和运行时性能
6. **更标准的架构** - 基于标准的模块化设计

迁移建议：
1. 优先迁移简单的微应用进行验证
2. 充分利用 Micro-Core 的多种沙箱策略
3. 使用更强大的通信和状态管理能力
4. 逐步优化应用架构和性能
