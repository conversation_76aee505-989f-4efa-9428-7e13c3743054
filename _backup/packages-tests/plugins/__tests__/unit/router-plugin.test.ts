/**
 * @fileoverview 路由插件测试
 * <AUTHOR> <<EMAIL>>
 */

import type { MicroCoreKernel } from '@micro-core/core';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { RouterPlugin } from '../plugin-router/src';

// Mock浏览器API
Object.defineProperty(window, 'location', {
    value: {
        pathname: '/',
        search: '',
        hash: '',
        href: 'http://localhost:3000/'
    },
    writable: true
});

Object.defineProperty(window, 'history', {
    value: {
        pushState: vi.fn(),
        replaceState: vi.fn(),
        back: vi.fn(),
        forward: vi.fn(),
        go: vi.fn()
    },
    writable: true
});

describe('RouterPlugin', () => {
    let routerPlugin: RouterPlugin;
    let mockKernel: MicroCoreKernel;

    beforeEach(() => {
        // 重置location
        window.location.pathname = '/';
        window.location.search = '';
        window.location.hash = '';

        // 创建mock内核
        mockKernel = {
            registerPlugin: vi.fn(),
            unregisterPlugin: vi.fn(),
            on: vi.fn(),
            off: vi.fn(),
            emit: vi.fn()
        } as any;

        routerPlugin = new RouterPlugin({
            mode: 'history',
            base: '/',
            enableSync: true,
            syncDelay: 0
        });
    });

    describe('插件初始化', () => {
        it('应该正确初始化路由插件', () => {
            expect(routerPlugin.name).toBe('router');
            expect(routerPlugin.version).toBe('0.1.0');
        });

        it('应该使用默认配置', () => {
            const defaultPlugin = new RouterPlugin();
            expect(defaultPlugin.getMode()).toBe('history');
        });

        it('应该使用自定义配置', () => {
            const customPlugin = new RouterPlugin({
                mode: 'hash',
                base: '/app',
                enableSync: false
            });
            expect(customPlugin.getMode()).toBe('hash');
        });
    });

    describe('插件安装', () => {
        it('应该成功安装插件', () => {
            routerPlugin.install(mockKernel);

            expect(mockKernel.registerPlugin).toHaveBeenCalledWith(routerPlugin);
            expect(mockKernel.on).toHaveBeenCalledWith('app:registered', expect.any(Function));
            expect(mockKernel.on).toHaveBeenCalledWith('app:unmounted', expect.any(Function));
        });

        it('应该创建历史记录适配器', () => {
            routerPlugin.install(mockKernel);
            expect(routerPlugin['historyAdapter']).toBeDefined();
        });

        it('应该在启用同步时创建路由同步器', () => {
            const syncPlugin = new RouterPlugin({ enableSync: true });
            syncPlugin.install(mockKernel);
            expect(syncPlugin['routerSync']).toBeDefined();
        });

        it('应该在禁用同步时不创建路由同步器', () => {
            const noSyncPlugin = new RouterPlugin({ enableSync: false });
            noSyncPlugin.install(mockKernel);
            expect(noSyncPlugin['routerSync']).toBeUndefined();
        });
    });

    describe('插件卸载', () => {
        beforeEach(() => {
            routerPlugin.install(mockKernel);
        });

        it('应该成功卸载插件', () => {
            routerPlugin.uninstall(mockKernel);

            expect(mockKernel.unregisterPlugin).toHaveBeenCalledWith('router');
            expect(routerPlugin['routerSync']).toBeUndefined();
            expect(routerPlugin['historyAdapter']).toBeUndefined();
        });
    });

    describe('路由导航', () => {
        beforeEach(() => {
            routerPlugin.install(mockKernel);
        });

        it('应该支持路由导航', () => {
            const pushStateSpy = vi.spyOn(window.history, 'pushState');

            routerPlugin.navigate('/test-path');

            expect(pushStateSpy).toHaveBeenCalledWith(null, '', '/test-path');
        });

        it('应该支持替换路由', () => {
            const replaceStateSpy = vi.spyOn(window.history, 'replaceState');

            routerPlugin.navigate('/test-path', true);

            expect(replaceStateSpy).toHaveBeenCalledWith(null, '', '/test-path');
        });

        it('应该获取当前路径', () => {
            window.location.pathname = '/current-path';
            expect(routerPlugin.getCurrentPath()).toBe('/current-path');
        });
    });

    describe('应用注册处理', () => {
        beforeEach(() => {
            routerPlugin.install(mockKernel);
        });

        it('应该处理应用注册事件', () => {
            const mockApp = {
                name: 'test-app',
                activeRule: '/test-app'
            };

            // 模拟应用注册事件
            const registeredCallback = (mockKernel.on as any).mock.calls
                .find((call: any) => call[0] === 'app:registered')[1];

            expect(() => registeredCallback(mockApp)).not.toThrow();
        });

        it('应该处理应用卸载事件', () => {
            const mockApp = {
                name: 'test-app',
                activeRule: '/test-app'
            };

            // 模拟应用卸载事件
            const unmountedCallback = (mockKernel.on as any).mock.calls
                .find((call: any) => call[0] === 'app:unmounted')[1];

            expect(() => unmountedCallback(mockApp)).not.toThrow();
        });
    });

    describe('路由模式', () => {
        it('应该支持history模式', () => {
            const historyPlugin = new RouterPlugin({ mode: 'history' });
            expect(historyPlugin.getMode()).toBe('history');
        });

        it('应该支持hash模式', () => {
            const hashPlugin = new RouterPlugin({ mode: 'hash' });
            expect(hashPlugin.getMode()).toBe('hash');
        });

        it('应该支持memory模式', () => {
            const memoryPlugin = new RouterPlugin({ mode: 'memory' });
            expect(memoryPlugin.getMode()).toBe('memory');
        });
    });

    describe('插件状态', () => {
        beforeEach(() => {
            routerPlugin.install(mockKernel);
        });

        it('应该获取插件状态', () => {
            const status = routerPlugin.getStatus();

            expect(status.mode).toBe('history');
            expect(status.currentPath).toBe('/');
            expect(status.syncEnabled).toBe(true);
            expect(typeof status.registeredApps).toBe('number');
        });
    });

    describe('路由同步', () => {
        it('应该在启用同步时同步路由变化', () => {
            const syncPlugin = new RouterPlugin({
                enableSync: true,
                syncDelay: 0
            });
            syncPlugin.install(mockKernel);

            // 模拟路由变化
            window.location.pathname = '/new-path';

            // 触发popstate事件
            const popstateEvent = new PopStateEvent('popstate', {
                state: { path: '/new-path' }
            });
            window.dispatchEvent(popstateEvent);

            expect(syncPlugin.getCurrentPath()).toBe('/new-path');
        });

        it('应该支持延迟同步', (done) => {
            const syncPlugin = new RouterPlugin({
                enableSync: true,
                syncDelay: 50
            });
            syncPlugin.install(mockKernel);

            // 模拟快速路由变化
            syncPlugin.navigate('/path1');
            syncPlugin.navigate('/path2');
            syncPlugin.navigate('/path3');

            // 验证延迟同步
            setTimeout(() => {
                expect(syncPlugin.getCurrentPath()).toBe('/path3');
                done();
            }, 100);
        });
    });

    describe('错误处理', () => {
        it('应该处理导航错误', () => {
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });

            // 模拟导航错误
            vi.spyOn(window.history, 'pushState').mockImplementation(() => {
                throw new Error('导航失败');
            });

            routerPlugin.install(mockKernel);

            expect(() => routerPlugin.navigate('/error-path')).not.toThrow();

            consoleSpy.mockRestore();
        });

        it('应该处理插件销毁错误', () => {
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });

            routerPlugin.install(mockKernel);

            // 模拟销毁错误
            routerPlugin['routerSync'] = {
                destroy: vi.fn(() => {
                    throw new Error('销毁失败');
                })
            } as any;

            expect(() => routerPlugin.uninstall(mockKernel)).not.toThrow();

            consoleSpy.mockRestore();
        });
    });

    describe('浏览器兼容性', () => {
        it('应该在不支持pushState时降级', () => {
            // 模拟不支持pushState的浏览器
            const originalPushState = window.history.pushState;
            delete (window.history as any).pushState;

            const fallbackPlugin = new RouterPlugin({ mode: 'history' });
            fallbackPlugin.install(mockKernel);

            expect(() => fallbackPlugin.navigate('/test')).not.toThrow();

            // 恢复pushState
            window.history.pushState = originalPushState;
        });

        it('应该处理location对象不可用的情况', () => {
            const originalLocation = window.location;
            delete (window as any).location;

            const plugin = new RouterPlugin();
            plugin.install(mockKernel);

            expect(plugin.getCurrentPath()).toBe('/');

            // 恢复location
            window.location = originalLocation;
        });
    });
});