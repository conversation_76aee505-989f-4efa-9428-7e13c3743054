/**
 * HTML Adapter Tests
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { HtmlAdapter } from '../src/html-adapter';
import type { HtmlAdapterConfig, HtmlAppInstance } from '../src/types';

// Mock fetch for loading HTML content
global.fetch = vi.fn();

describe('HtmlAdapter', () => {
  let adapter: HtmlAdapter;
  let mockContainer: HTMLElement;
  let mockConfig: HtmlAdapterConfig;

  beforeEach(() => {
    adapter = new HtmlAdapter();
    
    // Setup DOM
    mockContainer = document.createElement('div');
    mockContainer.id = 'test-app';
    document.body.appendChild(mockContainer);

    mockConfig = {
      id: 'test-html-app',
      name: 'Test HTML App',
      html: {
        content: '<div class="app"><h1>Test HTML App</h1><p>Hello World</p></div>',
        scripts: [
          'console.log("App loaded");'
        ],
        styles: [
          '.app { padding: 20px; background: #f0f0f0; }'
        ]
      }
    };

    // Reset mocks
    vi.clearAllMocks();
    (global.fetch as any).mockResolvedValue({
      ok: true,
      text: () => Promise.resolve(mockConfig.html!.content!)
    });
  });

  afterEach(() => {
    // Cleanup DOM
    if (mockContainer.parentNode) {
      document.body.removeChild(mockContainer);
    }
    vi.restoreAllMocks();
  });

  describe('初始化', () => {
    it('应该正确初始化适配器', () => {
      expect(adapter.name).toBe('html-adapter');
      expect(adapter.version).toBe('1.0.0');
      expect(adapter.supportedVersions).toEqual(['*']);
    });
  });

  describe('canHandle', () => {
    it('应该能够处理有效的 HTML 配置', async () => {
      const result = await adapter.canHandle(mockConfig);
      expect(result).toBe(true);
    });

    it('应该能够处理 URL 配置', async () => {
      const urlConfig = {
        ...mockConfig,
        html: {
          url: 'https://example.com/app.html'
        }
      };

      const result = await adapter.canHandle(urlConfig);
      expect(result).toBe(true);
    });

    it('应该拒绝无效配置', async () => {
      const invalidConfig = {
        id: 'test',
        name: 'test'
        // 缺少 HTML 配置
      };

      const result = await adapter.canHandle(invalidConfig as HtmlAdapterConfig);
      expect(result).toBe(false);
    });

    it('应该处理边界情况', async () => {
      expect(await adapter.canHandle(null as any)).toBe(false);
      expect(await adapter.canHandle(undefined as any)).toBe(false);
      expect(await adapter.canHandle({} as any)).toBe(false);
    });
  });

  describe('load', () => {
    it('应该成功加载 HTML 应用', async () => {
      const appInstance = await adapter.load(mockConfig);

      expect(appInstance).toBeDefined();
      expect(appInstance.id).toBe(mockConfig.id);
      expect(appInstance.name).toBe(mockConfig.name);
      expect(appInstance.status).toBe('loaded');
      expect(appInstance.htmlContent).toBe(mockConfig.html!.content);
    });

    it('应该从 URL 加载 HTML 内容', async () => {
      const urlConfig = {
        ...mockConfig,
        html: {
          url: 'https://example.com/app.html'
        }
      };

      const appInstance = await adapter.load(urlConfig);

      expect(global.fetch).toHaveBeenCalledWith('https://example.com/app.html');
      expect(appInstance.htmlContent).toBe(mockConfig.html!.content);
    });

    it('应该处理加载错误', async () => {
      (global.fetch as any).mockRejectedValueOnce(new Error('Network error'));

      const urlConfig = {
        ...mockConfig,
        html: {
          url: 'https://example.com/app.html'
        }
      };

      await expect(adapter.load(urlConfig)).rejects.toThrow('Network error');
    });

    it('应该处理 HTTP 错误', async () => {
      (global.fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      });

      const urlConfig = {
        ...mockConfig,
        html: {
          url: 'https://example.com/app.html'
        }
      };

      await expect(adapter.load(urlConfig)).rejects.toThrow('Failed to load HTML');
    });
  });

  describe('mount', () => {
    let appInstance: HtmlAppInstance;

    beforeEach(async () => {
      appInstance = await adapter.load(mockConfig);
    });

    it('应该成功挂载应用', async () => {
      await adapter.mount(appInstance.id, mockContainer);

      expect(appInstance.status).toBe('mounted');
      expect(appInstance.container).toBe(mockContainer);
      expect(mockContainer.innerHTML).toContain('Test HTML App');
      expect(mockContainer.innerHTML).toContain('Hello World');
    });

    it('应该注入样式', async () => {
      await adapter.mount(appInstance.id, mockContainer);

      const styleElements = document.querySelectorAll('style[data-html-adapter]');
      expect(styleElements.length).toBeGreaterThan(0);
      
      const styleContent = Array.from(styleElements)
        .map(el => el.textContent)
        .join('');
      expect(styleContent).toContain('.app { padding: 20px; background: #f0f0f0; }');
    });

    it('应该执行脚本', async () => {
      const consoleSpy = vi.spyOn(console, 'log');
      
      await adapter.mount(appInstance.id, mockContainer);

      expect(consoleSpy).toHaveBeenCalledWith('App loaded');
    });

    it('应该处理挂载错误', async () => {
      const errorConfig = {
        ...mockConfig,
        html: {
          content: '<div>Test</div>',
          scripts: ['throw new Error("Script error");']
        }
      };

      const errorInstance = await adapter.load(errorConfig);
      
      // 脚本错误不应该阻止挂载
      await expect(adapter.mount(errorInstance.id, mockContainer)).resolves.not.toThrow();
    });
  });

  describe('unmount', () => {
    let appInstance: HtmlAppInstance;

    beforeEach(async () => {
      appInstance = await adapter.load(mockConfig);
      await adapter.mount(appInstance.id, mockContainer);
    });

    it('应该成功卸载应用', async () => {
      await adapter.unmount(appInstance.id);

      expect(appInstance.status).toBe('unmounted');
      expect(appInstance.container).toBeUndefined();
      expect(mockContainer.innerHTML).toBe('');
    });

    it('应该清理注入的样式', async () => {
      await adapter.unmount(appInstance.id);

      const styleElements = document.querySelectorAll('style[data-html-adapter]');
      expect(styleElements.length).toBe(0);
    });

    it('应该处理重复卸载', async () => {
      await adapter.unmount(appInstance.id);
      
      // 第二次卸载不应该抛出错误
      await expect(adapter.unmount(appInstance.id)).resolves.not.toThrow();
    });
  });

  describe('destroy', () => {
    let appInstance: HtmlAppInstance;

    beforeEach(async () => {
      appInstance = await adapter.load(mockConfig);
    });

    it('应该成功销毁应用', async () => {
      await adapter.destroy(appInstance.id);

      expect(adapter.getAppInstance(appInstance.id)).toBeUndefined();
    });

    it('应该在销毁前卸载已挂载的应用', async () => {
      await adapter.mount(appInstance.id, mockContainer);
      await adapter.destroy(appInstance.id);

      expect(mockContainer.innerHTML).toBe('');
    });
  });

  describe('内容处理', () => {
    it('应该处理相对路径的资源', async () => {
      const configWithRelativePaths = {
        ...mockConfig,
        html: {
          content: '<img src="./images/logo.png" alt="Logo">',
          baseUrl: 'https://example.com/app/'
        }
      };

      const appInstance = await adapter.load(configWithRelativePaths);
      await adapter.mount(appInstance.id, mockContainer);

      expect(mockContainer.innerHTML).toContain('src="https://example.com/app/images/logo.png"');
    });

    it('应该支持模板变量替换', async () => {
      const configWithTemplate = {
        ...mockConfig,
        html: {
          content: '<div>{{title}}</div><p>{{message}}</p>',
          data: {
            title: 'Dynamic Title',
            message: 'Dynamic Message'
          }
        }
      };

      const appInstance = await adapter.load(configWithTemplate);
      await adapter.mount(appInstance.id, mockContainer);

      expect(mockContainer.innerHTML).toContain('Dynamic Title');
      expect(mockContainer.innerHTML).toContain('Dynamic Message');
    });
  });

  describe('安全性', () => {
    it('应该清理危险的脚本标签', async () => {
      const dangerousConfig = {
        ...mockConfig,
        html: {
          content: '<div>Safe content</div><script>alert("XSS")</script>',
          sanitize: true
        }
      };

      const appInstance = await adapter.load(dangerousConfig);
      await adapter.mount(appInstance.id, mockContainer);

      expect(mockContainer.innerHTML).toContain('Safe content');
      expect(mockContainer.innerHTML).not.toContain('<script>');
    });

    it('应该允许禁用内容清理', async () => {
      const unsafeConfig = {
        ...mockConfig,
        html: {
          content: '<div>Content</div><script>console.log("allowed")</script>',
          sanitize: false
        }
      };

      const appInstance = await adapter.load(unsafeConfig);
      await adapter.mount(appInstance.id, mockContainer);

      expect(mockContainer.innerHTML).toContain('<script>');
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内完成加载', async () => {
      const startTime = Date.now();
      await adapter.load(mockConfig);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(500); // 500ms内完成
    });

    it('应该在合理时间内完成挂载', async () => {
      const appInstance = await adapter.load(mockConfig);
      
      const startTime = Date.now();
      await adapter.mount(appInstance.id, mockContainer);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(200); // 200ms内完成
    });
  });
});
