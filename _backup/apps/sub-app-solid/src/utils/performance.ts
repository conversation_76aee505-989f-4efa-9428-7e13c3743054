/**
 * @fileoverview Solid.js 性能优化工具
 * <AUTHOR> <<EMAIL>>
 */

import { createMemo, createSignal } from 'solid-js'

export interface PerformanceMetrics {
    mountTime: number
    renderTime: number
    updateTime: number
    memoryUsage?: number
}

export class PerformanceMonitor {
    private static instance: PerformanceMonitor
    private metrics: PerformanceMetrics = {
        mountTime: 0,
        renderTime: 0,
        updateTime: 0
    }
    private startTimes: Map<string, number> = new Map()

    static getInstance(): PerformanceMonitor {
        if (!PerformanceMonitor.instance) {
            PerformanceMonitor.instance = new PerformanceMonitor()
        }
        return PerformanceMonitor.instance
    }

    public startTiming(operation: string): void {
        this.startTimes.set(operation, performance.now())
    }

    public endTiming(operation: string): number {
        const startTime = this.startTimes.get(operation)
        if (!startTime) return 0

        const duration = performance.now() - startTime
        this.startTimes.delete(operation)

        switch (operation) {
            case 'mount':
                this.metrics.mountTime = duration
                break
            case 'render':
                this.metrics.renderTime = duration
                break
            case 'update':
                this.metrics.updateTime = duration
                break
        }

        return duration
    }

    public getMetrics(): PerformanceMetrics {
        return { ...this.metrics }
    }

    public logMetrics(): void {
        console.log('Solid.js 应用性能指标:', this.metrics)
    }

    public measureMemoryUsage(): void {
        if ('memory' in performance) {
            const memory = (performance as any).memory
            this.metrics.memoryUsage = memory.usedJSHeapSize
        }
    }
}

export const performanceMonitor = PerformanceMonitor.getInstance()

// Solid.js 特定的性能优化 Hook
export function createOptimizedSignal<T>(initialValue: T, equals?: (prev: T, next: T) => boolean) {
    return createSignal(initialValue, { equals })
}

export function createDeferredMemo<T>(fn: () => T, _deps?: any[]) {
    return createMemo(() => {
        // 使用 requestIdleCallback 延迟计算
        return new Promise<T>((resolve) => {
            if ('requestIdleCallback' in window) {
                requestIdleCallback(() => resolve(fn()))
            } else {
                setTimeout(() => resolve(fn()), 0)
            }
        })
    })
}

// 虚拟滚动工具
export function createVirtualList<T>(items: () => T[], itemHeight: number, containerHeight: number) {
    const [scrollTop, setScrollTop] = createSignal(0)

    const visibleRange = createMemo(() => {
        const start = Math.floor(scrollTop() / itemHeight)
        const end = Math.min(start + Math.ceil(containerHeight / itemHeight) + 1, items().length)
        return { start, end }
    })

    const visibleItems = createMemo(() => {
        const range = visibleRange()
        return items().slice(range.start, range.end).map((item, index) => ({
            item,
            index: range.start + index
        }))
    })

    const totalHeight = createMemo(() => items().length * itemHeight)

    return {
        visibleItems,
        totalHeight,
        setScrollTop,
        scrollTop
    }
}

// 资源预加载工具
export class ResourcePreloader {
    private static preloadedResources = new Set<string>()

    static preloadScript(src: string): Promise<void> {
        if (this.preloadedResources.has(src)) {
            return Promise.resolve()
        }

        return new Promise((resolve, reject) => {
            const link = document.createElement('link')
            link.rel = 'preload'
            link.as = 'script'
            link.href = src
            link.onload = () => {
                this.preloadedResources.add(src)
                resolve()
            }
            link.onerror = reject
            document.head.appendChild(link)
        })
    }

    static preloadStyle(href: string): Promise<void> {
        if (this.preloadedResources.has(href)) {
            return Promise.resolve()
        }

        return new Promise((resolve, reject) => {
            const link = document.createElement('link')
            link.rel = 'preload'
            link.as = 'style'
            link.href = href
            link.onload = () => {
                this.preloadedResources.add(href)
                resolve()
            }
            link.onerror = reject
            document.head.appendChild(link)
        })
    }
}

// 懒加载组件
export function createLazyComponent<T>(loader: () => Promise<{ default: T }>) {
    const [component, setComponent] = createSignal<T | null>(null)
    const [loading, setLoading] = createSignal(true)
    const [error, setError] = createSignal<Error | null>(null)

    const load = async () => {
        try {
            setLoading(true)
            const module = await loader()
            setComponent(() => module.default)
        } catch (err) {
            setError(err as Error)
        } finally {
            setLoading(false)
        }
    }

    return {
        component,
        loading,
        error,
        load
    }
}