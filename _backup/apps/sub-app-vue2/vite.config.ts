import { createVuePlugin } from '@vitejs/plugin-vue2'
import { defineConfig } from 'vite'

export default defineConfig({
    plugins: [createVuePlugin()],

    server: {
        port: 3007,
        host: true,
        cors: true,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
    },

    build: {
        outDir: 'dist',
        sourcemap: true,
        minify: 'esbuild',
        target: 'es2020',
        lib: {
            entry: 'src/main.js',
            name: 'SubAppVue2',
            fileName: 'sub-app-vue2',
            formats: ['umd']
        },
        rollupOptions: {
            external: ['vue', 'vue-router'],
            output: {
                globals: {
                    vue: 'Vue',
                    'vue-router': 'VueRouter'
                }
            }
        }
    },

    define: {
        __MICRO_APP_NAME__: JSON.stringify('sub-app-vue2'),
        __POWERED_BY_MICRO_CORE__: true
    },

    resolve: {
        alias: {
            '@': '/src'
        }
    },

    optimizeDeps: {
        include: ['vue', 'vue-router']
    }
})