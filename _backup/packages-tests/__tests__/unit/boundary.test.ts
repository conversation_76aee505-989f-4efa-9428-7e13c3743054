/**
 * @fileoverview 边界测试 - 极端情况和错误输入测试
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { EventBus } from '../src/communication/event-bus';
import { AppRegistry } from '../src/runtime/app-registry';
import { MicroCoreKernel } from '../src/runtime/kernel';
import { LifecycleManager } from '../src/runtime/lifecycle-manager';
import { PluginSystem } from '../src/runtime/plugin-system';
import type { AppConfig } from '../src/types';

describe('边界测试 - 极端情况和错误输入', () => {
    let kernel: MicroCoreKernel;
    let eventBus: EventBus;
    let appRegistry: AppRegistry;
    let lifecycleManager: LifecycleManager;
    let pluginSystem: PluginSystem;

    beforeEach(() => {
        eventBus = new EventBus();
        appRegistry = new AppRegistry(eventBus);
        lifecycleManager = new LifecycleManager(eventBus);
        pluginSystem = new PluginSystem();
        kernel = new MicroCoreKernel({});
    });

    afterEach(() => {
        try {
            if (eventBus) {
                eventBus.destroy();
            }
        } catch (error) {
            // 忽略销毁错误
        }
    });

    describe('EventBus 边界测试', () => {
        it('应该处理空事件名称', () => {
            expect(() => {
                eventBus.on('', vi.fn());
            }).not.toThrow();
            
            expect(() => {
                eventBus.emit('', {});
            }).not.toThrow();
        });

        it('应该处理 null/undefined 监听器', () => {
            expect(() => {
                eventBus.on('test', null as any);
            }).not.toThrow();
            
            expect(() => {
                eventBus.on('test', undefined as any);
            }).not.toThrow();
        });

        it('应该处理大量参数的事件', () => {
            const mockListener = vi.fn();
            eventBus.on('test', mockListener);
            
            const args = new Array(1000).fill(0).map((_, i) => ({ index: i }));
            
            expect(() => {
                eventBus.emit('test', ...args);
            }).not.toThrow();
            
            expect(mockListener).toHaveBeenCalledWith(...args);
        });

        it('应该处理循环事件发布', () => {
            let callCount = 0;
            const maxCalls = 100;
            
            const recursiveListener = () => {
                callCount++;
                if (callCount < maxCalls) {
                    eventBus.emit('recursive-event');
                }
            };
            
            eventBus.on('recursive-event', recursiveListener);
            
            expect(() => {
                eventBus.emit('recursive-event');
            }).not.toThrow();
            
            expect(callCount).toBe(maxCalls);
        });

        it('应该处理监听器中的异常', () => {
            const errorListener = vi.fn(() => {
                throw new Error('Listener error');
            });
            const normalListener = vi.fn();
            
            eventBus.on('error-test', errorListener);
            eventBus.on('error-test', normalListener);
            
            expect(() => {
                eventBus.emit('error-test');
            }).not.toThrow();
            
            expect(errorListener).toHaveBeenCalled();
            expect(normalListener).toHaveBeenCalled();
        });
    });

    describe('AppRegistry 边界测试', () => {
        it('应该处理无效的应用配置', () => {
            const invalidConfigs = [
                null,
                undefined,
                {},
                { name: '' },
                { name: 'test' }, // 缺少必需字段
                { name: 'test', entry: '', activeWhen: '', container: '' }
            ];

            invalidConfigs.forEach((config, index) => {
                expect(() => {
                    appRegistry.register(config as any);
                }).toThrow();
            });
        });

        it('应该处理特殊字符的应用名称', () => {
            const specialNames = [
                'app-with-dashes',
                'app_with_underscores',
                'app.with.dots',
                'app@with@symbols',
                'app with spaces',
                '中文应用名',
                'app123',
                '123app'
            ];

            specialNames.forEach(name => {
                const config: AppConfig = {
                    name,
                    entry: 'http://localhost:3000/app.js',
                    activeWhen: '/test',
                    container: '#app'
                };

                expect(() => {
                    appRegistry.register(config);
                }).not.toThrow();
                
                expect(appRegistry.has(name)).toBe(true);
                appRegistry.unregister(name);
            });
        });

        it('应该处理不存在的应用查询', () => {
            expect(appRegistry.get('non-existent-app')).toBeNull();
            expect(appRegistry.has('non-existent-app')).toBe(false);
            
            expect(() => {
                appRegistry.unregister('non-existent-app');
            }).toThrow();
        });

        it('应该处理大量应用注册和查询', () => {
            const appCount = 10000;
            
            // 注册大量应用
            for (let i = 0; i < appCount; i++) {
                appRegistry.register({
                    name: `bulk-app-${i}`,
                    entry: `http://localhost:3000/app-${i}.js`,
                    activeWhen: `/bulk-${i}`,
                    container: `#bulk-${i}`
                });
            }
            
            expect(appRegistry.getAll()).toHaveLength(appCount);
            
            // 随机查询
            for (let i = 0; i < 1000; i++) {
                const randomIndex = Math.floor(Math.random() * appCount);
                const app = appRegistry.get(`bulk-app-${randomIndex}`);
                expect(app).toBeDefined();
            }
        });
    });

    describe('LifecycleManager 边界测试', () => {
        it('应该处理无效的应用实例', async () => {
            const invalidApps = [null, undefined, {}, { name: '' }];

            for (const app of invalidApps) {
                const result = await lifecycleManager.bootstrap(app as any);
                expect(result.success).toBe(false);
                expect(result.error).toBeDefined();
            }
        });

        it('应该处理生命周期钩子中的异常', async () => {
            const appConfig: AppConfig = {
                name: 'error-app',
                entry: 'http://localhost:3000/error-app.js',
                activeWhen: '/error',
                container: '#error'
            };

            appRegistry.register(appConfig);
            const app = appRegistry.get(appConfig.name);

            if (app) {
                // 模拟生命周期钩子抛出异常
                app.lifecycle = {
                    bootstrap: vi.fn().mockRejectedValue(new Error('Bootstrap failed')),
                    mount: vi.fn().mockRejectedValue(new Error('Mount failed')),
                    unmount: vi.fn().mockRejectedValue(new Error('Unmount failed'))
                };

                const bootstrapResult = await lifecycleManager.bootstrap(app);
                expect(bootstrapResult.success).toBe(false);
                expect(bootstrapResult.error?.message).toContain('Bootstrap failed');

                const mountResult = await lifecycleManager.mount(app);
                expect(mountResult.success).toBe(false);
                expect(mountResult.error?.message).toContain('Mount failed');

                const unmountResult = await lifecycleManager.unmount(app);
                expect(unmountResult.success).toBe(false);
                expect(unmountResult.error?.message).toContain('Unmount failed');
            }
        });

        it('应该处理超时的生命周期操作', async () => {
            const appConfig: AppConfig = {
                name: 'timeout-app',
                entry: 'http://localhost:3000/timeout-app.js',
                activeWhen: '/timeout',
                container: '#timeout'
            };

            appRegistry.register(appConfig);
            const app = appRegistry.get(appConfig.name);

            if (app) {
                // 模拟超时的生命周期钩子
                app.lifecycle = {
                    bootstrap: vi.fn().mockImplementation(() => 
                        new Promise(resolve => setTimeout(resolve, 10000)) // 10秒超时
                    )
                };

                const startTime = Date.now();
                const result = await lifecycleManager.bootstrap(app);
                const duration = Date.now() - startTime;

                // 应该在合理时间内返回（不应该等待10秒）
                expect(duration).toBeLessThan(6000);
                expect(result.success).toBe(true); // 或者根据实际超时处理逻辑调整
            }
        });
    });

    describe('PluginSystem 边界测试', () => {
        it('应该处理无效的插件配置', () => {
            const invalidPlugins = [
                null,
                undefined,
                {},
                { name: '' },
                { name: 'test' }, // 缺少 version 和 install
                { name: 'test', version: '' },
                { name: 'test', version: '1.0.0' }, // 缺少 install 方法
                { name: 'test', version: '1.0.0', install: 'not-a-function' }
            ];

            invalidPlugins.forEach(plugin => {
                expect(() => {
                    pluginSystem.install(plugin as any);
                }).toThrow();
            });
        });

        it('应该处理插件安装过程中的异常', () => {
            const errorPlugin = {
                name: 'error-plugin',
                version: '1.0.0',
                install: vi.fn(() => {
                    throw new Error('Install failed');
                })
            };

            expect(() => {
                pluginSystem.install(errorPlugin);
            }).toThrow('Install failed');
            
            // 插件不应该被标记为已安装
            expect(pluginSystem.has(errorPlugin.name)).toBe(false);
        });

        it('应该处理插件卸载过程中的异常', () => {
            const errorPlugin = {
                name: 'uninstall-error-plugin',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn(() => {
                    throw new Error('Uninstall failed');
                })
            };

            pluginSystem.install(errorPlugin);
            expect(pluginSystem.has(errorPlugin.name)).toBe(true);

            expect(() => {
                pluginSystem.uninstall(errorPlugin.name);
            }).toThrow('Uninstall failed');
            
            // 插件仍然应该被标记为已安装（卸载失败）
            expect(pluginSystem.has(errorPlugin.name)).toBe(true);
        });

        it('应该处理大量插件的安装和卸载', () => {
            const pluginCount = 1000;
            const plugins: any[] = [];

            // 创建大量插件
            for (let i = 0; i < pluginCount; i++) {
                plugins.push({
                    name: `mass-plugin-${i}`,
                    version: '1.0.0',
                    install: vi.fn(),
                    uninstall: vi.fn()
                });
            }

            // 批量安装
            expect(() => {
                plugins.forEach(plugin => {
                    pluginSystem.install(plugin);
                });
            }).not.toThrow();

            // 验证所有插件都已安装
            plugins.forEach(plugin => {
                expect(pluginSystem.has(plugin.name)).toBe(true);
            });

            // 批量卸载
            expect(() => {
                plugins.forEach(plugin => {
                    pluginSystem.uninstall(plugin.name);
                });
            }).not.toThrow();

            // 验证所有插件都已卸载
            plugins.forEach(plugin => {
                expect(pluginSystem.has(plugin.name)).toBe(false);
            });
        });
    });

    describe('MicroCoreKernel 边界测试', () => {
        it('应该处理无效的配置', () => {
            const invalidConfigs = [
                null,
                undefined,
                'invalid-config',
                123,
                []
            ];

            invalidConfigs.forEach(config => {
                expect(() => {
                    new MicroCoreKernel(config as any);
                }).not.toThrow(); // 应该使用默认配置
            });
        });

        it('应该处理重复的启动和停止操作', async () => {
            await kernel.start();
            
            // 重复启动不应该出错
            await expect(kernel.start()).resolves.not.toThrow();
            
            await kernel.stop();
            
            // 重复停止不应该出错
            await expect(kernel.stop()).resolves.not.toThrow();
        });

        it('应该处理销毁后的操作', () => {
            kernel.destroy();
            
            // 销毁后的操作应该抛出错误或安全处理
            expect(() => {
                kernel.use({
                    name: 'test-plugin',
                    version: '1.0.0',
                    install: vi.fn()
                });
            }).toThrow();
        });
    });

    describe('内存和资源边界测试', () => {
        it('应该处理内存不足的情况', () => {
            // 模拟内存压力测试
            const largeObjects: any[] = [];
            
            try {
                // 创建大量对象直到内存压力
                for (let i = 0; i < 100000; i++) {
                    largeObjects.push({
                        id: i,
                        data: new Array(1000).fill(`data-${i}`),
                        timestamp: Date.now()
                    });
                    
                    // 每1000个对象测试一次核心功能
                    if (i % 1000 === 0) {
                        const testApp: AppConfig = {
                            name: `memory-test-${i}`,
                            entry: 'http://localhost:3000/memory-test.js',
                            activeWhen: '/memory-test',
                            container: '#memory-test'
                        };
                        
                        appRegistry.register(testApp);
                        expect(appRegistry.has(testApp.name)).toBe(true);
                        appRegistry.unregister(testApp.name);
                    }
                }
            } catch (error) {
                // 内存不足时应该能够优雅处理
                console.log('内存压力测试触发异常（预期行为）:', error.message);
            }
            
            // 清理
            largeObjects.length = 0;
        });
    });
});
