# 基础示例概览

本章节提供了 Micro-Core 微前端框架的基础示例，帮助开发者快速理解和上手微前端开发。

## 🎯 示例概述

### 示例分类

| 示例类型 | 难度 | 技术栈 | 说明 |
|----------|------|--------|------|
| Hello World | ⭐ | 原生 JS | 最简单的微前端示例 |
| 多应用集成 | ⭐⭐ | React + Vue | 不同框架应用集成 |
| 状态共享 | ⭐⭐⭐ | React + Redux | 跨应用状态管理 |
| 路由协调 | ⭐⭐⭐ | Vue Router | 统一路由管理 |
| 通信机制 | ⭐⭐⭐⭐ | 多框架 | 应用间通信 |

## 🚀 Hello World 示例

### 项目结构

```
hello-world-example/
├── main-app/                 # 主应用
│   ├── index.html
│   ├── main.js
│   └── style.css
├── micro-app-1/             # 微应用 1
│   ├── index.js
│   └── style.css
├── micro-app-2/             # 微应用 2
│   ├── index.js
│   └── style.css
└── package.json
```

### 主应用实现

```html
<!-- main-app/index.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Micro-Core Hello World</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div id="app">
    <header class="header">
      <h1>Micro-Core Hello World</h1>
      <nav class="nav">
        <button onclick="loadApp('app1')" class="nav-btn">应用 1</button>
        <button onclick="loadApp('app2')" class="nav-btn">应用 2</button>
      </nav>
    </header>
    
    <main id="micro-container" class="container">
      <div class="welcome">
        <h2>欢迎使用 Micro-Core</h2>
        <p>点击上方按钮加载微应用</p>
      </div>
    </main>
  </div>
  
  <script src="https://unpkg.com/@micro-core/core@latest/dist/index.umd.js"></script>
  <script src="main.js"></script>
</body>
</html>
```

```javascript
// main-app/main.js
const { MicroCore } = window.MicroCore;

// 初始化微前端核心
const microCore = new MicroCore({
  container: '#micro-container'
});

// 注册微应用
microCore.registerApp({
  name: 'app1',
  entry: './micro-app-1/index.js',
  activeRule: '/app1',
  container: '#micro-container'
});

microCore.registerApp({
  name: 'app2',
  entry: './micro-app-2/index.js',
  activeRule: '/app2',
  container: '#micro-container'
});

// 启动微前端系统
microCore.start();

// 全局函数：加载应用
window.loadApp = function(appName) {
  microCore.loadApp(appName);
  
  // 更新 URL
  history.pushState(null, '', `/${appName}`);
  
  // 更新导航状态
  document.querySelectorAll('.nav-btn').forEach(btn => {
    btn.classList.remove('active');
  });
  event.target.classList.add('active');
};

// 监听浏览器后退/前进
window.addEventListener('popstate', () => {
  const path = location.pathname;
  if (path === '/app1') {
    loadApp('app1');
  } else if (path === '/app2') {
    loadApp('app2');
  }
});
```

```css
/* main-app/style.css */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
  margin-bottom: 1rem;
  font-size: 2rem;
  font-weight: 300;
}

.nav {
  display: flex;
  gap: 1rem;
}

.nav-btn {
  background: rgba(255,255,255,0.2);
  border: 1px solid rgba(255,255,255,0.3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: rgba(255,255,255,0.3);
  transform: translateY(-2px);
}

.nav-btn.active {
  background: rgba(255,255,255,0.4);
  border-color: rgba(255,255,255,0.6);
}

.container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 2rem;
  min-height: 500px;
}

.welcome {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.welcome h2 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.8rem;
  font-weight: 400;
}

.welcome p {
  color: #666;
  font-size: 1.1rem;
}

/* 微应用容器样式 */
.micro-app {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  padding: 2rem;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
```

### 微应用实现

```javascript
// micro-app-1/index.js
(function() {
  'use strict';
  
  // 微应用生命周期函数
  let appContainer = null;
  
  // 挂载函数
  window.microApp1 = {
    mount: function(container) {
      console.log('微应用 1 正在挂载...');
      
      appContainer = container;
      
      // 创建应用内容
      const appContent = `
        <div class="micro-app" id="micro-app-1">
          <div class="app-header">
            <h2>🎉 微应用 1</h2>
            <span class="app-badge">原生 JavaScript</span>
          </div>
          
          <div class="app-content">
            <p>这是一个使用原生 JavaScript 开发的微应用示例。</p>
            
            <div class="feature-list">
              <div class="feature-item">
                <h3>✨ 特性展示</h3>
                <ul>
                  <li>独立开发和部署</li>
                  <li>技术栈无关</li>
                  <li>运行时集成</li>
                  <li>样式隔离</li>
                </ul>
              </div>
              
              <div class="feature-item">
                <h3>🔧 交互功能</h3>
                <button onclick="microApp1.showMessage()" class="action-btn">
                  显示消息
                </button>
                <button onclick="microApp1.sendEvent()" class="action-btn">
                  发送事件
                </button>
              </div>
            </div>
            
            <div class="stats">
              <div class="stat-item">
                <span class="stat-label">挂载时间</span>
                <span class="stat-value" id="mount-time"></span>
              </div>
              <div class="stat-item">
                <span class="stat-label">运行状态</span>
                <span class="stat-value status-active">运行中</span>
              </div>
            </div>
          </div>
        </div>
      `;
      
      container.innerHTML = appContent;
      
      // 记录挂载时间
      document.getElementById('mount-time').textContent = new Date().toLocaleTimeString();
      
      // 加载样式
      this.loadStyles();
      
      console.log('微应用 1 挂载完成');
    },
    
    unmount: function() {
      console.log('微应用 1 正在卸载...');
      
      if (appContainer) {
        appContainer.innerHTML = '';
        appContainer = null;
      }
      
      // 清理样式
      this.removeStyles();
      
      console.log('微应用 1 卸载完成');
    },
    
    // 显示消息
    showMessage: function() {
      alert('来自微应用 1 的消息！');
    },
    
    // 发送事件
    sendEvent: function() {
      // 发送自定义事件
      const event = new CustomEvent('micro-app-event', {
        detail: {
          from: 'micro-app-1',
          message: '这是来自微应用 1 的事件',
          timestamp: new Date().toISOString()
        }
      });
      
      window.dispatchEvent(event);
      
      // 显示反馈
      const feedback = document.createElement('div');
      feedback.className = 'event-feedback';
      feedback.textContent = '事件已发送！';
      document.getElementById('micro-app-1').appendChild(feedback);
      
      setTimeout(() => {
        feedback.remove();
      }, 2000);
    },
    
    // 加载样式
    loadStyles: function() {
      const style = document.createElement('style');
      style.id = 'micro-app-1-styles';
      style.textContent = `
        .app-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
          padding-bottom: 1rem;
          border-bottom: 2px solid #e1e5e9;
        }
        
        .app-header h2 {
          color: #2c3e50;
          font-size: 1.5rem;
          margin: 0;
        }
        
        .app-badge {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 0.25rem 0.75rem;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: 500;
        }
        
        .feature-list {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 2rem;
          margin: 2rem 0;
        }
        
        .feature-item h3 {
          color: #34495e;
          margin-bottom: 1rem;
          font-size: 1.1rem;
        }
        
        .feature-item ul {
          list-style: none;
          padding: 0;
        }
        
        .feature-item li {
          padding: 0.5rem 0;
          color: #7f8c8d;
          position: relative;
          padding-left: 1.5rem;
        }
        
        .feature-item li:before {
          content: "→";
          position: absolute;
          left: 0;
          color: #3498db;
          font-weight: bold;
        }
        
        .action-btn {
          background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
          color: white;
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 6px;
          cursor: pointer;
          margin: 0.5rem 0.5rem 0.5rem 0;
          transition: all 0.3s ease;
          font-size: 0.9rem;
        }
        
        .action-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }
        
        .stats {
          display: flex;
          gap: 2rem;
          margin-top: 2rem;
          padding-top: 2rem;
          border-top: 1px solid #ecf0f1;
        }
        
        .stat-item {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }
        
        .stat-label {
          font-size: 0.8rem;
          color: #95a5a6;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
        
        .stat-value {
          font-weight: 600;
          color: #2c3e50;
        }
        
        .status-active {
          color: #27ae60;
        }
        
        .event-feedback {
          position: fixed;
          top: 20px;
          right: 20px;
          background: #27ae60;
          color: white;
          padding: 1rem 1.5rem;
          border-radius: 6px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          animation: slideIn 0.3s ease;
          z-index: 1000;
        }
        
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        
        @media (max-width: 768px) {
          .feature-list {
            grid-template-columns: 1fr;
            gap: 1rem;
          }
          
          .stats {
            flex-direction: column;
            gap: 1rem;
          }
        }
      `;
      
      document.head.appendChild(style);
    },
    
    // 移除样式
    removeStyles: function() {
      const style = document.getElementById('micro-app-1-styles');
      if (style) {
        style.remove();
      }
    }
  };
  
  // 监听全局事件
  window.addEventListener('micro-app-event', function(event) {
    if (event.detail.from !== 'micro-app-1') {
      console.log('微应用 1 收到事件:', event.detail);
    }
  });
  
})();
```

```javascript
// micro-app-2/index.js
(function() {
  'use strict';
  
  let appContainer = null;
  let updateTimer = null;
  
  window.microApp2 = {
    mount: function(container) {
      console.log('微应用 2 正在挂载...');
      
      appContainer = container;
      
      const appContent = `
        <div class="micro-app" id="micro-app-2">
          <div class="app-header">
            <h2>🚀 微应用 2</h2>
            <span class="app-badge">动态内容</span>
          </div>
          
          <div class="app-content">
            <p>这是一个展示动态内容和实时更新的微应用示例。</p>
            
            <div class="dashboard">
              <div class="card">
                <h3>📊 实时数据</h3>
                <div class="metric">
                  <span class="metric-label">当前时间</span>
                  <span class="metric-value" id="current-time"></span>
                </div>
                <div class="metric">
                  <span class="metric-label">随机数值</span>
                  <span class="metric-value" id="random-value"></span>
                </div>
                <div class="metric">
                  <span class="metric-label">访问计数</span>
                  <span class="metric-value" id="visit-count">1</span>
                </div>
              </div>
              
              <div class="card">
                <h3>🎮 交互控制</h3>
                <div class="controls">
                  <button onclick="microApp2.toggleUpdates()" class="control-btn" id="toggle-btn">
                    暂停更新
                  </button>
                  <button onclick="microApp2.resetCounter()" class="control-btn">
                    重置计数
                  </button>
                  <button onclick="microApp2.broadcastMessage()" class="control-btn">
                    广播消息
                  </button>
                </div>
              </div>
            </div>
            
            <div class="activity-log">
              <h3>📝 活动日志</h3>
              <div id="log-container" class="log-container">
                <div class="log-entry">应用已启动</div>
              </div>
            </div>
          </div>
        </div>
      `;
      
      container.innerHTML = appContent;
      
      this.loadStyles();
      this.startUpdates();
      this.addLogEntry('微应用 2 挂载完成');
      
      console.log('微应用 2 挂载完成');
    },
    
    unmount: function() {
      console.log('微应用 2 正在卸载...');
      
      this.stopUpdates();
      
      if (appContainer) {
        appContainer.innerHTML = '';
        appContainer = null;
      }
      
      this.removeStyles();
      
      console.log('微应用 2 卸载完成');
    },
    
    startUpdates: function() {
      updateTimer = setInterval(() => {
        this.updateData();
      }, 1000);
    },
    
    stopUpdates: function() {
      if (updateTimer) {
        clearInterval(updateTimer);
        updateTimer = null;
      }
    },
    
    updateData: function() {
      const timeElement = document.getElementById('current-time');
      const randomElement = document.getElementById('random-value');
      
      if (timeElement) {
        timeElement.textContent = new Date().toLocaleTimeString();
      }
      
      if (randomElement) {
        randomElement.textContent = Math.floor(Math.random() * 1000);
      }
    },
    
    toggleUpdates: function() {
      const toggleBtn = document.getElementById('toggle-btn');
      
      if (updateTimer) {
        this.stopUpdates();
        toggleBtn.textContent = '开始更新';
        this.addLogEntry('数据更新已暂停');
      } else {
        this.startUpdates();
        toggleBtn.textContent = '暂停更新';
        this.addLogEntry('数据更新已恢复');
      }
    },
    
    resetCounter: function() {
      const counterElement = document.getElementById('visit-count');
      if (counterElement) {
        counterElement.textContent = '1';
        this.addLogEntry('访问计数已重置');
      }
    },
    
    broadcastMessage: function() {
      const event = new CustomEvent('micro-app-event', {
        detail: {
          from: 'micro-app-2',
          message: '这是来自微应用 2 的广播消息',
          timestamp: new Date().toISOString(),
          data: {
            currentTime: new Date().toLocaleTimeString(),
            randomValue: Math.floor(Math.random() * 1000)
          }
        }
      });
      
      window.dispatchEvent(event);
      this.addLogEntry('广播消息已发送');
    },
    
    addLogEntry: function(message) {
      const logContainer = document.getElementById('log-container');
      if (logContainer) {
        const entry = document.createElement('div');
        entry.className = 'log-entry';
        entry.innerHTML = `
          <span class="log-time">${new Date().toLocaleTimeString()}</span>
          <span class="log-message">${message}</span>
        `;
        
        logContainer.insertBefore(entry, logContainer.firstChild);
        
        // 限制日志条数
        const entries = logContainer.querySelectorAll('.log-entry');
        if (entries.length > 10) {
          entries[entries.length - 1].remove();
        }
      }
    },
    
    loadStyles: function() {
      const style = document.createElement('style');
      style.id = 'micro-app-2-styles';
      style.textContent = `
        #micro-app-2 .app-badge {
          background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .dashboard {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 2rem;
          margin: 2rem 0;
        }
        
        .card {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 1.5rem;
          border: 1px solid #e9ecef;
        }
        
        .card h3 {
          color: #495057;
          margin-bottom: 1rem;
          font-size: 1.1rem;
        }
        
        .metric {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.75rem 0;
          border-bottom: 1px solid #dee2e6;
        }
        
        .metric:last-child {
          border-bottom: none;
        }
        
        .metric-label {
          color: #6c757d;
          font-size: 0.9rem;
        }
        
        .metric-value {
          font-weight: 600;
          color: #212529;
          font-family: 'Courier New', monospace;
        }
        
        .controls {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
        }
        
        .control-btn {
          background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
          color: white;
          border: none;
          padding: 0.75rem 1rem;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 0.9rem;
        }
        
        .control-btn:hover {
          transform: translateY(-1px);
          box-shadow: 0 3px 8px rgba(40, 167, 69, 0.3);
        }
        
        .activity-log {
          margin-top: 2rem;
        }
        
        .activity-log h3 {
          color: #495057;
          margin-bottom: 1rem;
          font-size: 1.1rem;
        }
        
        .log-container {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 1rem;
          max-height: 200px;
          overflow-y: auto;
          border: 1px solid #e9ecef;
        }
        
        .log-entry {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.5rem 0;
          border-bottom: 1px solid #dee2e6;
          font-size: 0.9rem;
        }
        
        .log-entry:last-child {
          border-bottom: none;
        }
        
        .log-time {
          color: #6c757d;
          font-family: 'Courier New', monospace;
          font-size: 0.8rem;
        }
        
        .log-message {
          color: #495057;
        }
        
        @media (max-width: 768px) {
          .dashboard {
            grid-template-columns: 1fr;
            gap: 1rem;
          }
        }
      `;
      
      document.head.appendChild(style);
    },
    
    removeStyles: function() {
      const style = document.getElementById('micro-app-2-styles');
      if (style) {
        style.remove();
      }
    }
  };
  
  // 监听全局事件
  window.addEventListener('micro-app-event', function(event) {
    if (event.detail.from !== 'micro-app-2' && window.microApp2) {
      window.microApp2.addLogEntry(`收到来自 ${event.detail.from} 的消息`);
    }
  });
  
})();
```

### 运行示例

1. **克隆或下载示例代码**
2. **启动本地服务器**：
   ```bash
   # 使用 Python
   python -m http.server 8080
   
   # 或使用 Node.js
   npx serve .
   ```
3. **访问应用**：打开浏览器访问 `http://localhost:8080`

### 示例特性

- ✅ **零配置启动** - 无需复杂的构建配置
- ✅ **技术栈无关** - 使用原生 JavaScript 实现
- ✅ **生命周期管理** - 完整的挂载/卸载流程
- ✅ **样式隔离** - 每个微应用独立的样式
- ✅ **事件通信** - 应用间事件通信机制
- ✅ **响应式设计** - 适配移动端设备

这个基础示例展示了 Micro-Core 的核心概念和基本用法，是学习微前端开发的理想起点。
