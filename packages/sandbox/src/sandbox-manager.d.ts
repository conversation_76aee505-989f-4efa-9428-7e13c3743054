/**
 * 沙箱管理器实现
 *
 * @description 统一管理所有沙箱实例的生命周期和资源
 * <AUTHOR> <<EMAIL>>
 */
import type { SandboxConfig, SandboxContext, SandboxManagerConfig, SandboxPerformanceMetrics } from '@micro-core/shared';
import { EventEmitter } from '@micro-core/shared';
/**
 * 沙箱管理器类
 * 负责沙箱的创建、管理和资源清理
 */
export declare class SandboxManager extends EventEmitter {
    private readonly config;
    private readonly logger;
    private readonly factory;
    private readonly sandboxes;
    private readonly performanceMetrics;
    private cleanupTimer?;
    constructor(config?: Partial<SandboxManagerConfig>);
    /**
     * 创建沙箱
     */
    createSandbox(config?: Partial<SandboxConfig>): Promise<SandboxContext>;
    /**
     * 获取沙箱
     */
    getSandbox(sandboxId: string): SandboxContext | undefined;
    /**
     * 激活沙箱
     */
    activateSandbox(sandboxId: string): Promise<void>;
    /**
     * 停用沙箱
     */
    deactivateSandbox(sandboxId: string): Promise<void>;
    /**
     * 销毁沙箱
     */
    destroySandbox(sandboxId: string): Promise<void>;
    /**
     * 执行代码
     */
    executeInSandbox(sandboxId: string, code: string): Promise<any>;
    /**
     * 获取所有沙箱
     */
    getAllSandboxes(): SandboxContext[];
    /**
     * 获取活跃沙箱
     */
    getActiveSandboxes(): SandboxContext[];
    /**
     * 获取性能指标
     */
    getPerformanceMetrics(sandboxId?: string): SandboxPerformanceMetrics | Map<string, SandboxPerformanceMetrics> | undefined;
    /**
     * 清理非活跃沙箱
     */
    cleanupInactiveSandboxes(maxIdleTime?: number): Promise<void>;
    /**
     * 销毁管理器
     */
    destroy(): Promise<void>;
    /**
     * 初始化性能监控
     */
    private initializePerformanceMonitoring;
    /**
     * 发送沙箱事件
     */
    private emitSandboxEvent;
    /**
     * 设置清理定时器
     */
    private setupCleanupTimer;
    /**
     * 设置错误处理
     */
    private setupErrorHandling;
}
//# sourceMappingURL=sandbox-manager.d.ts.map