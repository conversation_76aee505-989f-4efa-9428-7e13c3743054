/**
 * @fileoverview 工具函数统一导出
 * @description 提供所有工具函数的统一入口
 * <AUTHOR> <<EMAIL>>
 */

// 核心工具集
export { createMicroCoreUtils } from './core-utils'
export type { MicroCoreUtils } from './core-utils'

// 日志工具
export { createLogger } from './logger'
export type { Logger } from './logger'

// 异步工具 - 从async.ts导出
export { parallel, retry, sequential, sleep, timeout } from './async'

// 函数工具 - 从function.ts导出
export {
    compose, curry, debounce as functionDebounce,
    throttle as functionThrottle, identity, memoize, negate, once, partial, pipe
} from './function'

// 对象工具
export { deepClone, deepMerge } from './object'

// 类型检查工具
export {
    getType, hasProperty, isArray, isBoolean, isDate, isDefined, isElement, isEmpty, isError, isFunction, isNull, isNumber, isObject, isPlainObject,
    isPrimitive, isPromise, isRegExp, isString, isType, isUndefined, isValidUrl
} from './type-check'

// 字符串工具
export { formatBytes, generateId } from './string'

// URL工具
export { parseUrl } from './url'

// 验证工具
export { validateConfig } from './validation'
export type { ValidationSchema } from './validation'

// 事件工具
export { createEventEmitter } from './event'
export type { EventEmitter } from './event'

// Promise工具
export { createPromiseWithResolvers } from './promise'
export type { PromiseWithResolvers } from './promise'

// 重试工具
export { createRetryFunction } from './retry'
export type { RetryOptions } from './retry'

// 缓存工具
export { createCacheManager } from './cache'
export type { CacheManager } from './cache'

// 性能监控工具
export { createPerformanceMonitor } from './performance'
export type { PerformanceMetric, PerformanceMonitor } from './performance'

// 为了向后兼容，提供debounce和throttle的默认导出（来自async.ts）
export { debounce, throttle } from './async'
