/**
 * @fileoverview 日志系统
 * @description 提供统一的日志记录功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * 日志级别枚举
 */
export declare enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3,
    SILENT = 4
}
/**
 * 日志记录器接口
 */
export interface ILogger {
    debug(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
}
/**
 * 日志记录器类
 */
export declare class Logger implements ILogger {
    private namespace;
    private level;
    constructor(namespace: string, level?: LogLevel);
    /**
     * 调试日志
     */
    debug(message: string, ...args: any[]): void;
    /**
     * 信息日志
     */
    info(message: string, ...args: any[]): void;
    /**
     * 警告日志
     */
    warn(message: string, ...args: any[]): void;
    /**
     * 错误日志
     */
    error(message: string, ...args: any[]): void;
    /**
     * 设置日志级别
     */
    setLevel(level: LogLevel): void;
    /**
     * 获取日志级别
     */
    getLevel(): LogLevel;
}
/**
 * 创建日志记录器
 */
export declare function createLogger(namespace: string, level?: LogLevel): Logger;
/**
 * 默认日志记录器
 */
export declare const logger: Logger;
//# sourceMappingURL=logger.d.ts.map