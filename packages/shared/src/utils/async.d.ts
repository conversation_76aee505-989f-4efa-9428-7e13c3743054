/**
 * @fileoverview 异步工具函数
 * @description 提供防抖、节流等异步处理工具
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 防抖函数
 */
export declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
/**
 * 节流函数
 */
export declare function throttle<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
//# sourceMappingURL=async.d.ts.map