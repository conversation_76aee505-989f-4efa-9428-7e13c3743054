/**
 * 共享工具包 - 资源加载工具函数
 *
 * @description 提供资源加载、解析、缓存等工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import type { ResourceType } from '../constants';
/**
 * 资源信息接口
 */
export interface ResourceInfo {
    /** 资源类型 */
    type: ResourceType;
    /** 资源URL */
    url: string | null;
    /** 内联内容 */
    content: string | null;
    /** 是否为内联资源 */
    inline: boolean;
    /** 加载状态 */
    status: 'loading' | 'loaded' | 'error';
    /** 错误信息 */
    error?: Error;
    /** 加载时间 */
    loadTime?: number;
    /** 资源大小 */
    size?: number;
}
/**
 * 加载选项
 */
export interface LoadOptions {
    /** 超时时间（毫秒） */
    timeout?: number;
    /** 是否启用缓存 */
    cache?: boolean;
    /** 请求凭据 */
    credentials?: RequestCredentials;
    /** 重试次数 */
    retries?: number;
    /** 重试延迟（毫秒） */
    retryDelay?: number;
    /** 完整性校验 */
    integrity?: string;
}
/**
 * 带超时的 fetch 请求
 * @param url 请求URL
 * @param options 加载选项
 * @returns Promise<Response>
 */
export declare function fetchWithTimeout(url: string, options?: LoadOptions): Promise<Response>;
/**
 * 带重试的资源加载
 * @param url 资源URL
 * @param options 加载选项
 * @returns Promise<string>
 */
export declare function loadResourceWithRetry(url: string, options?: LoadOptions): Promise<string>;
/**
 * 解析 HTML 中的资源
 * @param html HTML内容
 * @param baseUrl 基础URL
 * @returns 资源信息数组
 */
export declare function parseHtmlResources(html: string, baseUrl: string): ResourceInfo[];
/**
 * 解析相对 URL
 * @param url 相对或绝对URL
 * @param baseUrl 基础URL
 * @returns 绝对URL
 */
export declare function resolveUrl(url: string, baseUrl: string): string;
/**
 * 动态加载脚本
 * @param url 脚本URL
 * @param options 加载选项
 * @returns Promise<void>
 */
export declare function loadScript(url: string, options?: LoadOptions): Promise<void>;
/**
 * 动态加载样式
 * @param url 样式URL
 * @param options 加载选项
 * @returns Promise<void>
 */
export declare function loadStyle(url: string, options?: LoadOptions): Promise<void>;
/**
 * 预加载资源
 * @param urls 资源URL数组
 * @param options 加载选项
 * @returns Promise<void>
 */
export declare function preloadResources(urls: string[], options?: LoadOptions): Promise<void>;
/**
 * 获取资源缓存统计信息
 * @returns 缓存统计信息
 */
export declare function getResourceCacheStats(): {
    size: number;
    totalSize: number;
    keys: string[];
};
/**
 * 清除资源缓存
 */
export declare function clearResourceCache(): void;
/**
 * 检查资源是否已缓存
 * @param url 资源URL
 * @returns 是否已缓存
 */
export declare function isResourceCached(url: string): boolean;
/**
 * 获取资源类型
 * @param url 资源URL
 * @returns 资源类型
 */
export declare function getResourceType(url: string): ResourceType;
//# sourceMappingURL=resource-utils.d.ts.map