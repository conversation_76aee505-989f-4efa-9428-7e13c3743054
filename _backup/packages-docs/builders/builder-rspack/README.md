# @micro-core/builder-rspack

A next-generation Rspack builder implementation for the Micro-Core ecosystem, providing Rust-powered bundling with Webpack compatibility.

## 🚀 Features

- **Rust-Powered Performance**: Built on Rspack's high-performance Rust core
- **Webpack Compatibility**: Drop-in replacement for most Webpack configurations
- **Module Federation**: Built-in support for micro-frontend architecture
- **Hot Module Replacement**: Fast development with HMR support
- **TypeScript Integration**: First-class TypeScript support
- **Tree Shaking**: Advanced dead code elimination
- **Code Splitting**: Intelligent chunk splitting strategies
- **Plugin Ecosystem**: Compatible with most Webpack plugins

## 📦 Installation

```bash
pnpm add @micro-core/builder-rspack
# or
npm install @micro-core/builder-rspack
# or
yarn add @micro-core/builder-rspack
```

## 🔧 Quick Start

```typescript
import { RspackBuilder } from '@micro-core/builder-rspack';

// Create builder instance
const builder = new RspackBuilder({
  mode: 'development',
  entry: './src/index.ts',
  outDir: './dist'
});

// Build project
const result = await builder.build({
  entry: './src/index.ts',
  outDir: './dist',
  mode: 'production'
});

console.log('Build completed:', result.success);
```

## ⚙️ Configuration

### Basic Configuration

```typescript
const builder = new RspackBuilder({
  mode: 'development',
  entry: './src/index.ts',
  outDir: './dist',
  target: 'web'
});
```

### Advanced Configuration

```typescript
const builder = new RspackBuilder({
  mode: 'production',
  entry: {
    main: './src/index.ts',
    vendor: './src/vendor.ts'
  },
  outDir: './dist',
  rspackConfig: {
    module: {
      rules: [
        {
          test: /\.tsx?$/,
          use: {
            loader: 'builtin:swc-loader',
            options: {
              sourceMap: true,
              jsc: {
                parser: {
                  syntax: 'typescript',
                  tsx: true
                },
                transform: {
                  react: {
                    runtime: 'automatic'
                  }
                }
              }
            }
          }
        }
      ]
    },
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all'
          }
        }
      }
    }
  }
});
```

## 🔌 API Reference

### RspackBuilder Class

#### Constructor

```typescript
constructor(options: RspackBuilderOptions)
```

**Parameters:**
- `options.mode`: Build mode ('development' | 'production')
- `options.entry`: Entry point(s) for the build
- `options.outDir`: Output directory
- `options.target`: Build target ('web' | 'node' | 'webworker')
- `options.rspackConfig`: Custom Rspack configuration

#### Methods

##### `build(config: RspackBuilderConfig): Promise<BuildResult>`

Execute the build process.

```typescript
const result = await builder.build({
  entry: './src/index.ts',
  outDir: './dist',
  mode: 'production'
});
```

##### `serve(config: RspackBuilderConfig, devServerConfig?: DevServerConfig): Promise<any>`

Start the development server.

```typescript
const server = await builder.serve({
  entry: './src/index.ts',
  mode: 'development'
}, {
  port: 3000,
  host: 'localhost',
  hot: true
});
```

##### `stop(): Promise<void>`

Stop the development server.

```typescript
await builder.stop();
```

### Configuration Types

#### RspackBuilderConfig

```typescript
interface RspackBuilderConfig extends BaseBuilderConfig {
  target?: 'web' | 'node' | 'webworker';
  rspackConfig?: RspackConfig;
  plugins?: RspackPlugin[];
  rules?: RuleSetRule[];
  alias?: Record<string, string>;
  externals?: ExternalsElement;
}
```

## 🏗️ Advanced Usage

### Module Federation

```typescript
const builder = new RspackBuilder({
  mode: 'production',
  entry: './src/index.ts',
  rspackConfig: {
    plugins: [
      new ModuleFederationPlugin({
        name: 'shell',
        filename: 'remoteEntry.js',
        remotes: {
          mfe1: 'mfe1@http://localhost:3001/remoteEntry.js'
        },
        shared: {
          react: { singleton: true },
          'react-dom': { singleton: true }
        }
      })
    ]
  }
});
```

### SWC Integration

```typescript
const builder = new RspackBuilder({
  rspackConfig: {
    module: {
      rules: [
        {
          test: /\.tsx?$/,
          use: {
            loader: 'builtin:swc-loader',
            options: {
              jsc: {
                parser: {
                  syntax: 'typescript',
                  tsx: true,
                  decorators: true
                },
                transform: {
                  legacyDecorator: true,
                  decoratorMetadata: true
                }
              }
            }
          }
        }
      ]
    }
  }
});
```

### CSS Processing

```typescript
const builder = new RspackBuilder({
  rspackConfig: {
    module: {
      rules: [
        {
          test: /\.css$/,
          use: [
            'builtin:lightningcss-loader',
            {
              loader: 'postcss-loader',
              options: {
                postcssOptions: {
                  plugins: ['autoprefixer']
                }
              }
            }
          ]
        }
      ]
    }
  }
});
```

## 🧪 Testing

```bash
# Run tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Run integration tests
pnpm test:integration
```

## 📊 Performance Tips

1. **Use Built-in Loaders**: Leverage Rspack's built-in loaders for maximum performance
2. **Enable Persistent Caching**: Use filesystem caching for faster rebuilds
3. **Optimize Bundle Splitting**: Configure intelligent chunk splitting
4. **Tree Shaking**: Enable advanced tree shaking optimizations

```typescript
const builder = new RspackBuilder({
  rspackConfig: {
    cache: {
      type: 'filesystem'
    },
    optimization: {
      usedExports: true,
      sideEffects: false,
      splitChunks: {
        chunks: 'all',
        minSize: 20000,
        maxSize: 244000
      }
    }
  }
});
```

## 🔍 Troubleshooting

### Common Issues

**Build performance issues:**
```typescript
// Enable parallel processing
rspackConfig: {
  experiments: {
    incrementalRebuild: true
  }
}
```

**Module resolution errors:**
```typescript
// Configure resolve options
rspackConfig: {
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  }
}
```

**HMR not working:**
```typescript
// Ensure HMR is properly configured
devServerConfig: {
  hot: true,
  liveReload: true
}
```

## 🔄 Migration Guide

### From Webpack

Most Webpack configurations work directly with Rspack:

```typescript
// Webpack config (webpack.config.js)
module.exports = {
  entry: './src/index.js',
  module: {
    rules: [
      {
        test: /\.js$/,
        use: 'babel-loader'
      }
    ]
  }
};

// Rspack equivalent
const builder = new RspackBuilder({
  entry: './src/index.js',
  rspackConfig: {
    module: {
      rules: [
        {
          test: /\.js$/,
          use: 'builtin:swc-loader' // Use built-in SWC loader for better performance
        }
      ]
    }
  }
});
```

## 📄 License

MIT © 2025 Micro-Core

## 🤝 Contributing

Contributions are welcome! Please read our contributing guidelines and submit pull requests to our repository.

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/echo008/micro-core/issues)
- 📖 Documentation: [Micro-Core Docs](https://micro-core.dev)
