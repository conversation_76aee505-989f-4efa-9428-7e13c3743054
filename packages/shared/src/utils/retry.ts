/**
 * @fileoverview 重试工具函数
 * @description 提供函数重试相关的工具函数
 * <AUTHOR> <<EMAIL>>
 */

export interface RetryOptions {
    maxAttempts: number
    delay?: number
    backoff?: 'linear' | 'exponential'
}

/**
 * 创建重试函数
 */
export function createRetryFunction<T extends (...args: any[]) => any>(
    fn: T,
    options: RetryOptions
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
    const { maxAttempts, delay = 1000, backoff = 'linear' } = options

    return async (...args: Parameters<T>): Promise<ReturnType<T>> => {
        let lastError: any

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                return await fn(...args)
            } catch (error) {
                lastError = error

                if (attempt === maxAttempts) {
                    throw error
                }

                // 计算延迟时间
                let currentDelay = delay
                if (backoff === 'exponential') {
                    currentDelay = delay * Math.pow(2, attempt - 1)
                }

                await new Promise(resolve => setTimeout(resolve, currentDelay))
            }
        }

        throw lastError
    }
}