/**
 * @fileoverview Sidecar 相关类型定义
 * @description 提供边车模式相关的类型定义
 * <AUTHOR> <<EMAIL>>
 */
import type { MessageSerializer } from '../utils/message-serializer';
/**
 * 桥接消息接口
 */
export interface BridgeMessage {
    /** 消息ID */
    id: string;
    /** 消息类型 */
    type: string;
    /** 发送方 */
    from: string;
    /** 接收方 */
    to: string;
    /** 消息数据 */
    data?: any;
    /** 时间戳 */
    timestamp: number;
    /** 消息元数据 */
    metadata?: Record<string, any>;
}
/**
 * 消息过滤器接口
 */
export interface MessageFilter {
    /** 过滤函数 */
    filter(message: BridgeMessage): boolean;
    /** 过滤规则 */
    rules: FilterRule[];
}
/**
 * 过滤规则
 */
export interface FilterRule {
    /** 规则名称 */
    name: string;
    /** 规则条件 */
    condition: (message: BridgeMessage) => boolean;
    /** 是否启用 */
    enabled: boolean;
}
/**
 * 桥接配置
 */
export interface BridgeConfig {
    /** 通信协议 */
    protocol?: 'postMessage' | 'sharedWorker' | 'customEvent';
    /** 消息格式 */
    format?: 'json' | 'binary' | 'compressed';
    /** 超时时间 */
    timeout?: number;
    /** 序列化器 */
    serializer?: MessageSerializer;
    /** 消息过滤器 */
    filter?: MessageFilter;
    /** 重试配置 */
    retry?: {
        maxAttempts: number;
        delay: number;
        backoff: 'linear' | 'exponential';
        condition: (error: Error) => boolean;
    };
}
/**
 * 桥接统计信息
 */
export interface BridgeStats {
    /** 发送消息数 */
    messagesSent: number;
    /** 接收消息数 */
    messagesReceived: number;
    /** 失败消息数 */
    failedMessages: number;
    /** 平均延迟 */
    averageLatency: number;
}
/**
 * 桥接器接口
 */
export interface IBridge {
    /** 初始化 */
    initialize(): Promise<void>;
    /** 销毁 */
    destroy(): Promise<void>;
    /** 发送消息 */
    send(message: BridgeMessage): Promise<void>;
    /** 监听消息 */
    listen(callback: (message: BridgeMessage) => void): () => void;
    /** 获取统计信息 */
    getStats(): BridgeStats;
}
/**
 * 样式隔离配置
 */
export interface StyleIsolationConfig {
    /** 隔离模式 */
    mode: 'scoped' | 'shadow' | 'prefix';
    /** CSS 前缀 */
    prefix: string;
    /** 排除选择器 */
    excludeSelectors: string[];
    /** 是否隔离全局样式 */
    isolateGlobal: boolean;
}
/**
 * 脚本隔离配置
 */
export interface ScriptIsolationConfig {
    /** 隔离模式 */
    mode: 'proxy' | 'vm' | 'iframe';
    /** 白名单 */
    whitelist: string[];
    /** 黑名单 */
    blacklist: string[];
    /** 严格模式 */
    strict: boolean;
}
/**
 * 全局变量隔离配置
 */
export interface GlobalIsolationConfig {
    /** 共享变量 */
    shared: string[];
    /** 私有变量 */
    private: string[];
    /** 变量映射 */
    mapping: Record<string, string>;
}
/**
 * 事件隔离配置
 */
export interface EventIsolationConfig {
    /** DOM 事件隔离 */
    dom: boolean;
    /** 自定义事件隔离 */
    custom: boolean;
    /** 事件命名空间 */
    namespace: string;
}
/**
 * 隔离配置
 */
export interface IsolationConfig {
    /** 样式隔离 */
    style?: StyleIsolationConfig;
    /** 脚本隔离 */
    script?: ScriptIsolationConfig;
    /** 全局变量隔离 */
    global?: GlobalIsolationConfig;
    /** 事件隔离 */
    event?: EventIsolationConfig;
}
/**
 * 隔离容器接口
 */
export interface IIsolationContainer {
    /** 初始化 */
    initialize(): Promise<void>;
    /** 销毁 */
    destroy(): Promise<void>;
    /** 执行代码 */
    execute<T = unknown>(code: string): Promise<T>;
    /** 设置全局变量 */
    setGlobal<T = unknown>(name: string, value: T): void;
    /** 获取全局变量 */
    getGlobal<T = unknown>(name: string): T | undefined;
    /** 清理全局变量 */
    clearGlobals(): void;
    /** 应用样式隔离 */
    applyStyleIsolation(element: HTMLElement): void;
    /** 移除样式隔离 */
    removeStyleIsolation(element: HTMLElement): void;
}
/**
 * 微应用配置接口
 */
export interface MicroAppConfig {
    name: string;
    entry: string;
    container?: string | HTMLElement;
    activeWhen?: string | ((location: Location) => boolean);
    customProps?: Record<string, any>;
}
/**
 * Sidecar 初始化选项
 */
export interface SidecarInitOptions {
    /** 应用名称 */
    name?: string;
    /** 应用信息 */
    app?: {
        name: string;
        version: string;
        entry: string;
        [key: string]: any;
    };
    /** 应用配置列表 */
    apps?: MicroAppConfig[];
    /** 是否自动启动 */
    autoStart?: boolean;
    /** 容器选择器 */
    container?: string | HTMLElement;
    /** 是否自动发现应用 */
    autoDiscovery?: boolean;
    /** 调试模式 */
    debug?: boolean;
    /** 错误处理器 */
    errorHandler?: (error: Error) => void;
    /** 其他配置 */
    [key: string]: any;
}
/**
 * Sidecar 状态
 */
export type SidecarStatus = 'idle' | 'starting' | 'running' | 'stopping' | 'stopped' | 'error';
/**
 * Sidecar 统计信息
 */
export interface SidecarStats {
    /** 启动时间 */
    startTime?: number;
    /** 运行时间 */
    uptime: number;
    /** 代理请求数 */
    proxyRequests: number;
    /** 消息发送数 */
    messagesSent: number;
    /** 消息接收数 */
    messagesReceived: number;
    /** 错误数 */
    errors: number;
}
//# sourceMappingURL=sidecar-types.d.ts.map