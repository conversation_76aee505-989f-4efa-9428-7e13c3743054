/**
 * @fileoverview 字符串处理工具
 * @description 提供各种字符串操作和转换功能
 * <AUTHOR> <<EMAIL>>
 */
/**
 * 首字母大写
 */
export declare function capitalize(str: string): string;
/**
 * 转换为驼峰命名
 */
export declare function camelCase(str: string): string;
/**
 * 转换为短横线命名
 */
export declare function kebabCase(str: string): string;
/**
 * 转换为下划线命名
 */
export declare function snakeCase(str: string): string;
/**
 * 转换为帕斯卡命名
 */
export declare function pascalCase(str: string): string;
/**
 * 生成唯一ID
 */
export declare function generateId(prefix?: string, length?: number): string;
/**
 * 格式化字节数
 */
export declare function formatBytes(bytes: number, decimals?: number): string;
/**
 * 截断字符串
 */
export declare function truncate(str: string, length: number, suffix?: string): string;
/**
 * 移除字符串两端空白
 */
export declare function trim(str: string): string;
/**
 * 移除字符串左端空白
 */
export declare function trimStart(str: string): string;
/**
 * 移除字符串右端空白
 */
export declare function trimEnd(str: string): string;
/**
 * 填充字符串左侧
 */
export declare function padStart(str: string, length: number, fillString?: string): string;
/**
 * 填充字符串右侧
 */
export declare function padEnd(str: string, length: number, fillString?: string): string;
/**
 * 重复字符串
 */
export declare function repeat(str: string, count: number): string;
/**
 * 反转字符串
 */
export declare function reverse(str: string): string;
/**
 * 检查字符串是否以指定前缀开始
 */
export declare function startsWith(str: string, searchString: string, position?: number): boolean;
/**
 * 检查字符串是否以指定后缀结束
 */
export declare function endsWith(str: string, searchString: string, length?: number): boolean;
/**
 * 检查字符串是否包含指定子串
 */
export declare function includes(str: string, searchString: string, position?: number): boolean;
/**
 * 替换字符串中的所有匹配项
 */
export declare function replaceAll(str: string, searchValue: string | RegExp, replaceValue: string): string;
/**
 * 分割字符串为数组
 */
export declare function split(str: string, separator: string | RegExp, limit?: number): string[];
/**
 * 转换为小写
 */
export declare function toLowerCase(str: string): string;
/**
 * 转换为大写
 */
export declare function toUpperCase(str: string): string;
/**
 * 获取字符串长度
 */
export declare function length(str: string): number;
/**
 * 检查字符串是否为空
 */
export declare function isEmpty(str: string): boolean;
/**
 * 检查字符串是否为空白
 */
export declare function isBlank(str: string): boolean;
/**
 * 转义HTML字符
 */
export declare function escapeHtml(str: string): string;
/**
 * 反转义HTML字符
 */
export declare function unescapeHtml(str: string): string;
/**
 * 生成随机字符串
 */
export declare function randomString(length?: number, chars?: string): string;
/**
 * 计算字符串的哈希值
 */
export declare function hash(str: string): number;
/**
 * 模板字符串插值
 */
export declare function template(str: string, data: Record<string, any>): string;
//# sourceMappingURL=string.d.ts.map