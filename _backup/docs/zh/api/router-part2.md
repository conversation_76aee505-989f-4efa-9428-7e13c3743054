# 路由 API (第二部分)

## 路由配置

### 路由模式

Micro-Core 路由支持两种模式：

1. **Hash 模式**：使用 URL 的 hash 部分（`#` 后面的部分）来模拟完整的 URL
2. **History 模式**：使用 HTML5 History API 来实现真实的 URL

```typescript
// Hash 模式
const router = new Router({
  mode: 'hash',
  routes: [...]
});
// URL 示例: https://example.com/#/users/123

// History 模式
const router = new Router({
  mode: 'history',
  routes: [...]
});
// URL 示例: https://example.com/users/123
```

### 路由匹配

路由匹配支持多种模式：

1. **静态路径**：完全匹配指定的路径
2. **动态路径参数**：匹配路径的一部分，并将其作为参数
3. **嵌套路由**：通过父子关系定义嵌套的路由结构
4. **命名视图**：同时显示多个视图
5. **通配符**：匹配任意路径

```typescript
const router = new Router({
  routes: [
    // 静态路径
    { path: '/home', component: 'home-app' },
    
    // 动态路径参数
    { path: '/users/:userId', component: 'user-app' },
    
    // 可选参数
    { path: '/posts/:postId?', component: 'post-app' },
    
    // 多参数
    { path: '/categories/:categoryId/products/:productId', component: 'product-app' },
    
    // 嵌套路由
    {
      path: '/settings',
      component: 'settings-app',
      children: [
        { path: 'profile', component: 'profile-app' },
        { path: 'security', component: 'security-app' }
      ]
    },
    
    // 命名视图
    {
      path: '/dashboard',
      components: {
        default: 'dashboard-main-app',
        sidebar: 'dashboard-sidebar-app',
        header: 'dashboard-header-app'
      }
    },
    
    // 通配符
    { path: '*', component: 'not-found-app' }
  ]
});
```

### 路由元信息

路由元信息允许你将任意信息附加到路由上：

```typescript
const router = new Router({
  routes: [
    {
      path: '/admin',
      component: 'admin-app',
      meta: {
        requiresAuth: true,
        roles: ['admin'],
        title: '管理后台',
        transition: 'fade',
        keepAlive: true
      }
    }
  ]
});

// 在导航守卫中使用元信息
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title || '默认标题';
  
  // 检查权限
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login');
  } else if (to.meta.roles && !hasRole(to.meta.roles)) {
    next('/403');
  } else {
    next();
  }
});
```

### 路由传参

路由传参有多种方式：

1. **动态路径参数**：通过路径传递参数
2. **查询参数**：通过 URL 查询字符串传递参数
3. **Props 传参**：将路由参数作为组件的 props

```typescript
const router = new Router({
  routes: [
    // 动态路径参数
    {
      path: '/users/:userId',
      component: 'user-app'
    },
    
    // Props 传参 (布尔模式)
    {
      path: '/products/:productId',
      component: 'product-app',
      props: true // 将路由参数作为组件的 props
    },
    
    // Props 传参 (对象模式)
    {
      path: '/about',
      component: 'about-app',
      props: { version: '1.0.0' } // 传递静态值
    },
    
    // Props 传参 (函数模式)
    {
      path: '/search',
      component: 'search-app',
      props: (route) => ({
        query: route.query.q,
        page: Number(route.query.page) || 1,
        filters: route.query.filters
      })
    }
  ]
});

// 导航时传递参数
router.push({
  path: '/users/123',
  query: { tab: 'profile', mode: 'edit' }
});

// 在组件中访问参数
// 路径参数: this.$route.params.userId
// 查询参数: this.$route.query.tab, this.$route.query.mode
```

## 导航守卫

导航守卫用于控制导航的行为，例如权限检查、数据预加载等。

### 全局守卫

全局守卫应用于所有路由导航：

```typescript
// 全局前置守卫
router.beforeEach((to, from, next) => {
  console.log(`导航开始: ${from.path} -> ${to.path}`);
  
  // 权限检查
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login');
  } else {
    next();
  }
});

// 全局解析守卫
router.beforeResolve((to, from, next) => {
  console.log('路由解析中...');
  
  // 数据预加载
  if (to.meta.fetchData) {
    loadData()
      .then(() => next())
      .catch(() => next(false));
  } else {
    next();
  }
});

// 全局后置钩子
router.afterEach((to, from) => {
  console.log(`导航完成: ${from.path} -> ${to.path}`);
  
  // 更新页面标题
  document.title = to.meta.title || '默认标题';
  
  // 发送分析事件
  trackPageView(to.path);
});
```

### 路由独享守卫

路由独享守卫只应用于特定路由：

```typescript
const router = new Router({
  routes: [
    {
      path: '/admin',
      component: 'admin-app',
      beforeEnter: (to, from, next) => {
        // 检查是否是管理员
        if (isAdmin()) {
          next();
        } else {
          next('/403');
        }
      }
    }
  ]
});
```

### 组件内守卫

组件内守卫在组件内部定义：

```typescript
const UserComponent = {
  template: '...',
  
  // 组件进入前
  beforeRouteEnter(to, from, next) {
    // 在渲染该组件的对应路由被确认前调用
    // 此时组件实例还未被创建，不能访问 this
    
    // 加载用户数据
    fetchUser(to.params.userId)
      .then(user => {
        // 通过回调访问组件实例
        next(vm => {
          vm.user = user;
        });
      })
      .catch(error => {
        next(false);
      });
  },
  
  // 组件更新前
  beforeRouteUpdate(to, from, next) {
    // 在当前路由改变，但该组件被复用时调用
    // 可以访问 this
    
    // 保存滚动位置
    const scrollPosition = this.saveScrollPosition();
    
    // 更新用户数据
    this.fetchUser(to.params.userId)
      .then(() => {
        next();
        // 恢复滚动位置
        this.$nextTick(() => {
          this.restoreScrollPosition(scrollPosition);
        });
      })
      .catch(error => {
        next(false);
      });
  },
  
  // 组件离开前
  beforeRouteLeave(to, from, next) {
    // 在导航离开该组件的对应路由时调用
    // 可以访问 this
    
    // 检查是否有未保存的更改
    if (this.hasUnsavedChanges) {
      // 显示确认对话框
      const confirmed = window.confirm('有未保存的更改，确定要离开吗？');
      if (confirmed) {
        next();
      } else {
        next(false);
      }
    } else {
      next();
    }
  }
};
```

## 路由懒加载

路由懒加载允许你按需加载路由组件，提高应用性能：

```typescript
const router = new Router({
  routes: [
    {
      path: '/',
      component: 'home-app' // 直接加载
    },
    {
      path: '/users',
      component: () => loadMicroApp('users-app') // 懒加载
    },
    {
      path: '/settings',
      component: () => {
        // 显示加载指示器
        showLoadingIndicator();
        
        // 加载组件
        return loadMicroApp('settings-app')
          .finally(() => {
            // 隐藏加载指示器
            hideLoadingIndicator();
          });
      }
    },
    {
      path: '/admin',
      component: () => {
        // 带超时的懒加载
        return Promise.race([
          loadMicroApp('admin-app'),
          new Promise((_, reject) => {
            setTimeout(() => reject(new Error('加载超时')), 5000);
          })
        ]);
      }
    }
  ]
});
```

## 滚动行为

控制页面滚动行为：

```typescript
const router = new Router({
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置，则恢复到保存的位置
    if (savedPosition) {
      return savedPosition;
    }
    
    // 如果有哈希，则滚动到锚点
    if (to.hash) {
      return { selector: to.hash };
    }
    
    // 否则滚动到顶部
    return { x: 0, y: 0 };
  }
});

// 更复杂的滚动行为
const router = new Router({
  scrollBehavior(to, from, savedPosition) {
    // 延迟滚动
    return new Promise((resolve) => {
      setTimeout(() => {
        if (savedPosition) {
          resolve(savedPosition);
        } else if (to.hash) {
          resolve({ selector: to.hash, behavior: 'smooth' });
        } else {
          resolve({ x: 0, y: 0, behavior: 'smooth' });
        }
      }, 500);
    });
  }
});
```

## 路由过渡效果

为路由切换添加过渡效果：

```typescript
// 在主应用中
function renderApp(route) {
  const app = document.getElementById('app');
  
  // 应用过渡效果
  const transition = route.meta.transition || 'fade';
  app.className = `transition-${transition}-enter`;
  
  // 加载微应用
  loadMicroApp(route.component)
    .then(microApp => {
      // 渲染微应用
      microApp.mount(app);
      
      // 完成过渡
      setTimeout(() => {
        app.className = `transition-${transition}-enter-active`;
      }, 20);
      
      setTimeout(() => {
        app.className = '';
      }, 300);
    });
}

// 监听路由变化
router.afterEach((to, from) => {
  renderApp(to);
});
```

## 路由模式

### History 模式

使用 HTML5 History API 实现真实的 URL：

```typescript
const router = new Router({
  mode: 'history',
  base: '/app',
  routes: [...]
});
```

**注意事项：**

1. 需要服务器配置支持，所有路由都应该返回同一个 HTML 文件
2. 服务器配置示例：

```nginx
# Nginx 配置
location /app/ {
  try_files $uri $uri/ /app/index.html;
}
```

```apache
# Apache 配置
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /app/
  RewriteRule ^index\.html$ - [L]
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule . /app/index.html [L]
</IfModule>
```

### Hash 模式

使用 URL 的 hash 部分来模拟完整的 URL：

```typescript
const router = new Router({
  mode: 'hash',
  routes: [...]
});
```

**优点：**

1. 不需要服务器配置支持
2. 兼容性更好，适用于所有浏览器

**缺点：**

1. URL 不够美观，包含 `#` 符号
2. 对 SEO 不友好

## 微前端路由集成

### 主应用与微应用路由同步

在微前端架构中，主应用和微应用之间的路由同步是关键：

```typescript
// 主应用中
const mainRouter = new Router({
  mode: 'history',
  base: '/',
  routes: [...]
});

// 注册微应用
registerMicroApp({
  name: 'user-app',
  entry: '//localhost:8081',
  container: '#micro-container',
  activeRule: '/users',
  props: {
    // 传递路由实例给微应用
    mainRouter
  }
});

// 微应用中
export async function bootstrap({ mainRouter }) {
  // 创建微应用路由
  const microRouter = new Router({
    mode: 'history',
    base: '/users',
    routes: [...]
  });
  
  // 监听主应用路由变化
  mainRouter.beforeEach((to, from, next) => {
    // 如果路由属于当前微应用
    if (to.path.startsWith('/users')) {
      // 同步到微应用路由
      const microPath = to.path.replace('/users', '');
      microRouter.push(microPath);
    }
    next();
  });
  
  // 监听微应用路由变化
  microRouter.beforeEach((to, from, next) => {
    // 同步到主应用路由
    const mainPath = '/users' + to.path;
    mainRouter.push(mainPath);
    next();
  });
}
```

### 路由隔离

为了避免路由冲突，可以使用路由隔离：

```typescript
// 主应用中
const mainRouter = new Router({
  mode: 'history',
  base: '/',
  routes: [...]
});

// 微应用中
export async function bootstrap() {
  // 创建隔离的微应用路由
  const microRouter = new Router({
    mode: 'hash', // 使用 hash 模式避免与主应用冲突
    base: '/',
    routes: [...]
  });
}
```

### 路由通信

主应用和微应用之间的路由通信：

```typescript
// 主应用中
const mainRouter = new Router({
  mode: 'history',
  base: '/',
  routes: [...]
});

// 发布路由变化事件
mainRouter.afterEach((to, from) => {
  window.dispatchEvent(new CustomEvent('main-route-change', {
    detail: { to, from }
  }));
});

// 微应用中
export async function bootstrap() {
  // 创建微应用路由
  const microRouter = new Router({
    mode: 'history',
    base: '/users',
    routes: [...]
  });
  
  // 监听主应用路由变化事件
  window.addEventListener('main-route-change', (event) => {
    const { to, from } = event.detail;
    console.log('主应用路由变化:', to.path);
    
    // 处理路由变化
    if (to.path.startsWith('/users')) {
      const microPath = to.path.replace('/users', '');
      microRouter.push(microPath);
    }
  });
  
  // 发布微应用路由变化事件
  microRouter.afterEach((to, from) => {
    window.dispatchEvent(new CustomEvent('micro-route-change', {
      detail: { to, from, appName: 'user-app' }
    }));
  });
}

// 主应用中监听微应用路由变化
window.addEventListener('micro-route-change', (event) => {
  const { to, from, appName } = event.detail;
  console.log(`微应用 ${appName} 路由变化:`, to.path);
});
```

## 最佳实践

### 路由组织

按照功能模块组织路由：

```typescript
// 用户模块路由
const userRoutes = [
  {
    path: '/users',
    component: 'user-list-app',
    children: [
      { path: ':userId', component: 'user-detail-app' },
      { path: ':userId/edit', component: 'user-edit-app' }
    ]
  }
];

// 产品模块路由
const productRoutes = [
  {
    path: '/products',
    component: 'product-list-app',
    children: [
      { path: ':productId', component: 'product-detail-app' },
      { path: ':productId/edit', component: 'product-edit-app' }
    ]
  }
];

// 设置模块路由
const settingRoutes = [
  {
    path: '/settings',
    component: 'settings-app',
    meta: { requiresAuth: true },
    children: [
      { path: 'profile', component: 'profile-app' },
      { path: 'security', component: 'security-app' },
      { path: 'notifications', component: 'notifications-app' }
    ]
  }
];

// 合并所有路由
const routes = [
  { path: '/', component: 'home-app' },
  ...userRoutes,
  ...productRoutes,
  ...settingRoutes,
  { path: '*', component: 'not-found-app' }
];

// 创建路由实例
const router = new Router({
  mode: 'history',
  routes
});
```

### 权限控制

基于角色的路由权限控制：

```typescript
// 路由权限配置
const routePermissions = {
  '/admin': ['admin'],
  '/users/manage': ['admin', 'manager'],
  '/settings/security': ['admin']
};

// 权限检查函数
function hasPermission(route, userRoles) {
  if (!routePermissions[route]) {
    return true; // 没有权限配置，默认允许访问
  }
  
  return routePermissions[route].some(role => userRoles.includes(role));
}

// 全局前置守卫
router.beforeEach((to, from, next) => {
  const userRoles = getUserRoles(); // 获取用户角色
  
  if (hasPermission(to.path, userRoles)) {
    next(); // 有权限，继续导航
  } else {
    next('/403'); // 无权限，重定向到 403 页面
  }
});

// 动态生成路由
function generateRoutesBasedOnRoles(roles) {
  const accessibleRoutes = allRoutes.filter(route => {
    if (route.meta && route.meta.roles) {
      return route.meta.roles.some(role => roles.includes(role));
    }
    return true; // 没有 roles 配置，默认允许访问
  });
  
  return accessibleRoutes;
}

// 在用户登录后动态添加路由
function addRoutesAfterLogin(user) {
  const accessibleRoutes = generateRoutesBasedOnRoles(user.roles);
  router.addRoutes(accessibleRoutes);
}
```

### 路由缓存

缓存路由组件以提高性能：

```typescript
// 在主应用中
const cachedComponents = new Map();

function loadCachedComponent(name) {
  if (cachedComponents.has(name)) {
    return Promise.resolve(cachedComponents.get(name));
  }
  
  return loadMicroApp(name)
    .then(component => {
      cachedComponents.set(name, component);
      return component;
    });
}

const router = new Router({
  routes: [
    {
      path: '/users',
      component: () => loadCachedComponent('users-app'),
      meta: { keepAlive: true }
    },
    {
      path: '/products',
      component: () => loadCachedComponent('products-app'),
      meta: { keepAlive: true }
    },
    {
      path: '/settings',
      component: () => loadCachedComponent('settings-app'),
      meta: { keepAlive: false } // 不缓存
    }
  ]
});

// 在路由变化时处理缓存
router.beforeEach((to, from, next) => {
  // 如果离开的路由不需要缓存
  if (from.meta && from.meta.keepAlive === false) {
    // 清除缓存
    const componentName = from.matched[0].components.default.name;
    if (cachedComponents.has(componentName)) {
      cachedComponents.delete(componentName);
    }
  }
  
  next();
});
```

### 路由分析

跟踪路由变化以进行分析：

```typescript
// 路由分析中间件
router.afterEach((to, from) => {
  // 记录路由变化
  trackRouteChange({
    from: from.path,
    to: to.path,
    timestamp: Date.now(),
    duration: performance.now() - routeStartTime,
    userId: getCurrentUserId()
  });
  
  // 页面浏览事件
  trackPageView({
    path: to.path,
    title: to.meta.title,
    referrer: from.path
  });
});

// 在路由开始前记录时间
let routeStartTime = 0;
router.beforeEach((to, from, next) => {
  routeStartTime = performance.now();
  next();
});

// 错误跟踪
router.onError((error) => {
  // 记录路由错误
  trackRouteError({
    error: error.message,
    stack: error.stack,
    path: router.currentRoute.path
  });
});
```

### 路由测试

测试路由配置和导航守卫：

```typescript
// 路由配置测试
describe('Router Configuration', () => {
  let router;
  
  beforeEach(() => {
    router = new Router({
      mode: 'abstract', // 用于测试的模式
      routes: [...]
    });
  });
  
  test('should match home route', () => {
    router.push('/');
    expect(router.currentRoute.matched[0].path).toBe('/');
    expect(router.currentRoute.matched[0].component).toBe('home-app');
  });
  
  test('should match user detail route with params', () => {
    router.push('/users/123');
    expect(router.currentRoute.params.userId).toBe('123');
    expect(router.currentRoute.matched[0].path).toBe('/users/:userId');
  });
});

// 导航守卫测试
describe('Navigation Guards', () => {
  let router;
  let authService;
  
  beforeEach(() => {
    authService = {
      isAuthenticated: jest.fn(),
      hasRole: jest.fn()
    };
    
    router = new Router({
      mode: 'abstract',
      routes: [...]
    });
    
    router.beforeEach((to, from, next) => {
      if (to.meta.requiresAuth && !authService.isAuthenticated()) {
        next('/login');
      } else if (to.meta.roles && !authService.hasRole(to.meta.roles)) {
        next('/403');
      } else {
        next();
      }
    });
  });
  
  test('should redirect to login when route requires auth and user is not authenticated', () => {
    authService.isAuthenticated.mockReturnValue(false);
    
    router.push('/settings');
    expect(router.currentRoute.path).toBe('/login');
  });
  
  test('should allow navigation when user is authenticated', () => {
    authService.isAuthenticated.mockReturnValue(true);
    
    router.push('/settings');
    expect(router.currentRoute.path).toBe('/settings');
  });
});
```

## 与其他框架集成

### 与 qiankun 集成

```typescript
// 主应用中
import { registerMicroApps, start } from 'qiankun';
import { Router } from '@micro-core/core';

// 创建路由实例
const router = new Router({
  mode: 'history',
  routes: [...]
});

// 注册微应用
registerMicroApps([
  {
    name: 'user-app',
    entry: '//localhost:8081',
    container: '#micro-container',
    activeRule: '/users',
    props: {
      mainRouter: router // 传递路由实例给微应用
    }
  },
  {
    name: 'product-app',
    entry: '//localhost:8082',
    container: '#micro-container',
    activeRule: '/products',
    props: {
      mainRouter: router
    }
  }
]);

// 启动 qiankun
start();

// 微应用中
export async function bootstrap(props) {
  const { mainRouter } = props;
  
  // 创建微应用路由
  const microRouter = new Router({
    mode: 'history',
    base: '/users', // 与 activeRule 保持一致
    routes: [...]
  });
  
  // 路由同步
  setupRouterSync(mainRouter, microRouter);
}

function setupRouterSync(mainRouter, microRouter) {
  // 从主应用路由同步到微应用路由
  mainRouter.afterEach((to) => {
    if (to.path.startsWith('/users')) {
      const microPath = to.path.replace('/users', '') || '/';
      if (microRouter.currentRoute.path !== microPath) {
        microRouter.push(microPath);
      }
    }
  });
  
  // 从微应用路由同步到主应用路由
  microRouter.afterEach((to) => {
    const mainPath = '/users' + (to.path === '/' ? '' : to.path);
    if (mainRouter.currentRoute.path !== mainPath) {
      mainRouter.push(mainPath);
    }
  });
}
```

### 与 wujie (无界) 集成

```typescript
// 主应用中
import { bus, setupApp, preloadApp, startApp } from 'wujie';
import { Router } from '@micro-core/core';

// 创建路由实例
const router = new Router({
  mode: 'history',
  routes: [...]
});

// 设置微应用
setupApp({
  name: 'user-app',
  url: '//localhost:8081',
  exec: true,
  props: {
    mainRouter: router
  }
});

// 预加载微应用
preloadApp({
  name: 'user-app'
});

// 路由守卫中启动微应用
router.beforeEach((to, from, next) => {
  if (to.path.startsWith('/users')) {
    // 启动微应用
    startApp({
      name: 'user-app',
      container: '#micro-container',
      url: '//localhost:8081',
      props: {
        mainRouter: router,
        currentPath: to.path
      }
    });
  }
  next();
});

// 路由通信
router.afterEach((to, from) => {
  if (to.path.startsWith('/users')) {
    // 通过 bus 发送路由变化事件
    bus.$emit('main-route-change', {
      to,
      from
    });
  }
});

// 微应用中
export function bootstrap(props) {
  const { mainRouter, currentPath } = props;
  
  // 创建微应用路由
  const microRouter = new Router({
    mode: 'history',
    base: '/users',
    routes: [...]
  });
  
  // 初始化路由
  if (currentPath && currentPath.startsWith('/users')) {
    const microPath = currentPath.replace('/users', '') || '/';
    microRouter.push(microPath);
  }
  
  // 监听主应用路由变化
  window.$wujie.bus.$on('main-route-change', ({ to, from }) => {
    if (to.path.startsWith('/users')) {
      const microPath = to.path.replace('/users', '') || '/';
      if (microRouter.currentRoute.path !== microPath) {
        microRouter.push(microPath);
      }
    }
  });
  
  // 微应用路由变化同步到主应用
  microRouter.afterEach((to, from) => {
    const mainPath = '/users' + (to.path === '/' ? '' : to.path);
    if (mainRouter.currentRoute.path !== mainPath) {
      mainRouter.push(mainPath);
    }
  });
}
```

## 常见问题与解决方案

### 路由冲突

**问题**：主应用和微应用的路由发生冲突。

**解决方案**：

1. 使用不同的路由模式：主应用使用 history