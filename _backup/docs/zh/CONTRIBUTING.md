# 贡献指南

感谢您对 Micro-Core 项目的关注和贡献！本指南将帮助您了解如何参与项目开发，包括代码贡献、文档改进、问题报告等。

## 📋 目录

- [贡献方式](#贡献方式)
- [开发环境搭建](#开发环境搭建)
- [代码贡献流程](#代码贡献流程)
- [代码规范](#代码规范)
- [测试指南](#测试指南)
- [文档贡献](#文档贡献)
- [问题报告](#问题报告)
- [社区参与](#社区参与)

## 贡献方式

### 🎯 贡献类型

我们欢迎以下类型的贡献：

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core 贡献生态                          │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   代码贡献       │    │   文档贡献       │    │   社区贡献       ││
│  │                 │    │                 │    │                 ││
│  │ • Bug 修复      │    │ • API 文档      │    │ • 问题解答      ││
│  │ • 新功能开发    │    │ • 使用指南      │    │ • 经验分享      ││
│  │ • 性能优化      │    │ • 示例代码      │    │ • 插件开发      ││
│  │ • 测试用例      │    │ • 翻译工作      │    │ • 模板贡献      ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│           │                       │                       │       │
│           └───────────────────────┼───────────────────────┘       │
│                                   │                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    贡献流程                                 │ │
│  │  Fork → Clone → Branch → Code → Test → PR → Review        │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 📊 贡献统计

| 贡献类型 | 难度 | 时间投入 | 影响范围 | 奖励积分 |
|----------|------|----------|----------|----------|
| Bug 修复 | ⭐⭐ | 2-8 小时 | 局部 | 10-50 |
| 新功能开发 | ⭐⭐⭐⭐ | 1-4 周 | 全局 | 100-500 |
| 文档改进 | ⭐ | 1-4 小时 | 用户体验 | 5-20 |
| 测试用例 | ⭐⭐⭐ | 4-16 小时 | 质量保证 | 20-100 |
| 性能优化 | ⭐⭐⭐⭐⭐ | 1-2 周 | 全局 | 200-1000 |

## 开发环境搭建

### 1. 系统要求

```bash
# Node.js 版本要求
node >= 18.0.0
npm >= 8.0.0
pnpm >= 8.0.0 (推荐)

# Git 版本要求
git >= 2.20.0
```

### 2. 克隆项目

```bash
# 1. Fork 项目到你的 GitHub 账户
# 2. 克隆你的 Fork
git clone https://github.com/YOUR_USERNAME/micro-core.git
cd micro-core

# 3. 添加上游仓库
git remote add upstream https://github.com/micro-core/micro-core.git

# 4. 验证远程仓库
git remote -v
```

### 3. 安装依赖

```bash
# 使用 pnpm (推荐)
pnpm install

# 或使用 npm
npm install

# 或使用 yarn
yarn install
```

### 4. 开发环境配置

```bash
# 复制环境配置文件
cp .env.example .env.local

# 编辑环境变量
vim .env.local
```

```bash
# .env.local 示例配置
NODE_ENV=development
DEBUG=micro-core:*
LOG_LEVEL=debug

# 开发服务器配置
DEV_SERVER_PORT=3000
DEV_SERVER_HOST=localhost

# 测试配置
TEST_TIMEOUT=30000
TEST_COVERAGE_THRESHOLD=80
```

### 5. 启动开发服务器

```bash
# 启动所有服务
pnpm dev

# 启动特定包的开发服务器
pnpm dev:core
pnpm dev:plugins
pnpm dev:adapters

# 启动示例项目
pnpm dev:examples
```

### 6. 验证环境

```bash
# 运行测试
pnpm test

# 运行 lint 检查
pnpm lint

# 构建项目
pnpm build

# 检查类型
pnpm type-check
```

## 代码贡献流程

### 1. 创建功能分支

```bash
# 同步最新代码
git checkout main
git pull upstream main

# 创建功能分支
git checkout -b feature/your-feature-name

# 或修复分支
git checkout -b fix/issue-number-description
```

### 2. 分支命名规范

```bash
# 功能开发
feature/add-vue3-adapter
feature/improve-router-performance

# Bug 修复
fix/memory-leak-in-sandbox
fix/issue-123-route-navigation

# 文档更新
docs/update-api-reference
docs/add-migration-guide

# 重构
refactor/simplify-event-bus
refactor/extract-common-utils

# 测试
test/add-unit-tests-for-core
test/improve-integration-coverage
```

### 3. 开发和测试

```bash
# 开发过程中持续测试
pnpm test:watch

# 运行特定测试
pnpm test packages/core
pnpm test packages/plugins

# 运行端到端测试
pnpm test:e2e

# 检查代码覆盖率
pnpm test:coverage
```

### 4. 代码提交

```bash
# 添加变更文件
git add .

# 提交代码 (遵循 Conventional Commits)
git commit -m "feat: add Vue 3 adapter support"
git commit -m "fix: resolve memory leak in proxy sandbox"
git commit -m "docs: update API documentation"

# 推送到你的 Fork
git push origin feature/your-feature-name
```

### 5. 创建 Pull Request

1. **访问 GitHub 页面**：打开你的 Fork 页面
2. **创建 PR**：点击 "New Pull Request"
3. **填写 PR 模板**：

```markdown
## 📝 变更描述

简要描述这个 PR 的目的和实现方式。

## 🔗 相关 Issue

- Closes #123
- Related to #456

## 🧪 测试

- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成
- [ ] 性能测试通过

## 📋 检查清单

- [ ] 代码遵循项目规范
- [ ] 添加了必要的测试
- [ ] 更新了相关文档
- [ ] 通过了所有 CI 检查

## 🖼️ 截图/演示

如果适用，请提供截图或演示视频。

## 💭 其他说明

任何其他需要说明的内容。
```

### 6. 代码审查

```typescript
// 代码审查要点
const reviewChecklist = {
  functionality: [
    '功能是否按预期工作',
    '是否处理了边界情况',
    '错误处理是否完善'
  ],
  
  codeQuality: [
    '代码是否清晰易读',
    '是否遵循项目规范',
    '是否有重复代码'
  ],
  
  performance: [
    '是否有性能问题',
    '内存使用是否合理',
    '是否影响加载速度'
  ],
  
  testing: [
    '测试覆盖率是否足够',
    '测试用例是否有效',
    '是否测试了边界情况'
  ],
  
  documentation: [
    'API 文档是否更新',
    '代码注释是否充分',
    '变更日志是否更新'
  ]
}
```

## 代码规范

### 1. TypeScript 规范

```typescript
// ✅ 推荐的 TypeScript 代码风格

// 接口定义
interface MicroAppConfig {
  name: string
  entry: string
  activeWhen: string | ((location: Location) => boolean)
  container?: string | HTMLElement
  props?: Record<string, any>
}

// 类定义
class MicroCore {
  private apps: Map<string, MicroApp> = new Map()
  private currentApp: string | null = null
  
  constructor(private config: MicroCoreConfig) {
    this.validateConfig(config)
  }
  
  // 公共方法
  public async registerApp(config: MicroAppConfig): Promise<void> {
    if (this.apps.has(config.name)) {
      throw new Error(`App ${config.name} already registered`)
    }
    
    const app = new MicroApp(config)
    this.apps.set(config.name, app)
  }
  
  // 私有方法
  private validateConfig(config: MicroCoreConfig): void {
    if (!config.apps || config.apps.length === 0) {
      throw new Error('At least one app must be configured')
    }
  }
}

// 函数定义
export function createMicroCore(config: MicroCoreConfig): MicroCore {
  return new MicroCore(config)
}

// 常量定义
export const DEFAULT_CONFIG: Partial<MicroCoreConfig> = {
  sandbox: {
    css: true,
    js: true
  },
  prefetch: true
} as const
```

### 2. 命名规范

```typescript
// 文件命名：kebab-case
// micro-core.ts
// event-bus.ts
// router-plugin.ts

// 类名：PascalCase
class EventBus {}
class RouterPlugin {}
class MicroCore {}

// 接口名：PascalCase，以 I 开头（可选）
interface AppConfig {}
interface IEventBus {}

// 变量名：camelCase
const appName = 'my-app'
const isActive = true
const userConfig = {}

// 常量名：SCREAMING_SNAKE_CASE
const MAX_RETRY_COUNT = 3
const DEFAULT_TIMEOUT = 5000
const APP_STATUS = {
  LOADING: 'loading',
  LOADED: 'loaded',
  MOUNTING: 'mounting',
  MOUNTED: 'mounted'
} as const

// 函数名：camelCase
function loadApp() {}
function registerEventHandler() {}
function validateConfig() {}

// 私有成员：下划线前缀
class MyClass {
  private _privateProperty: string
  private _privateMethod(): void {}
}
```

## 测试指南

### 1. 测试结构

```
tests/
├── unit/                 # 单元测试
│   ├── core/
│   ├── plugins/
│   └── adapters/
├── integration/          # 集成测试
│   ├── app-loading/
│   ├── communication/
│   └── routing/
├── e2e/                  # 端到端测试
│   ├── examples/
│   └── scenarios/
├── fixtures/             # 测试数据
└── utils/               # 测试工具
```

### 2. 单元测试示例

```typescript
// packages/core/tests/micro-core.test.ts
import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { MicroCore } from '../src/micro-core'

describe('MicroCore', () => {
  let microCore: MicroCore
  
  beforeEach(() => {
    microCore = new MicroCore({
      apps: [
        {
          name: 'test-app',
          entry: 'http://localhost:3001',
          activeWhen: '/test'
        }
      ]
    })
  })
  
  afterEach(() => {
    microCore.destroy()
  })
  
  describe('registerApp', () => {
    it('should register app successfully', async () => {
      const config = {
        name: 'new-app',
        entry: 'http://localhost:3002',
        activeWhen: '/new'
      }
      
      await microCore.registerApp(config)
      
      expect(microCore.getApp('new-app')).toBeDefined()
    })
    
    it('should throw error when registering duplicate app', async () => {
      const config = {
        name: 'test-app',
        entry: 'http://localhost:3002',
        activeWhen: '/duplicate'
      }
      
      await expect(microCore.registerApp(config))
        .rejects
        .toThrow('App test-app already registered')
    })
  })
  
  describe('loadApp', () => {
    it('should load app successfully', async () => {
      const loadSpy = vi.fn()
      microCore.on('app:load:success', loadSpy)
      
      await microCore.loadApp('test-app')
      
      expect(loadSpy).toHaveBeenCalledWith(
        expect.objectContaining({ name: 'test-app' })
      )
    })
    
    it('should handle load failure gracefully', async () => {
      const errorSpy = vi.fn()
      microCore.on('app:load:error', errorSpy)
      
      await expect(microCore.loadApp('non-existent-app'))
        .rejects
        .toThrow('App non-existent-app not found')
      
      expect(errorSpy).toHaveBeenCalled()
    })
  })
})
```

## 联系我们

如果您有任何问题或建议，请通过以下方式联系我们：

- 📧 **邮箱**: <EMAIL>
- 🐙 **GitHub**: [提交 Issue](https://github.com/micro-core/micro-core/issues)
- 💬 **Discord**: [加入讨论](https://discord.gg/micro-core)

感谢您对 Micro-Core 项目的支持和贡献！让我们一起构建更好的微前端生态系统。

---

## 行为准则

请注意，参与本项目即表示您同意遵守我们的[行为准则](CODE_OF_CONDUCT.md)。我们致力于为所有人提供友好、安全和包容的环境。