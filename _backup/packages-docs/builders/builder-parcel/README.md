# @micro-core/builder-parcel

A zero-configuration Parcel builder implementation for the Micro-Core ecosystem, providing effortless bundling with automatic optimization.

## 🚀 Features

- **Zero Configuration**: Works out of the box with sensible defaults
- **Universal Asset Support**: Handles all file types automatically
- **Hot Module Replacement**: Lightning-fast development experience
- **Automatic Code Splitting**: Intelligent bundle optimization
- **Tree Shaking**: Built-in dead code elimination
- **Source Maps**: Full debugging support
- **Multi-Target Builds**: Support for multiple output formats
- **Plugin Ecosystem**: Extensible with Parcel plugins

## 📦 Installation

```bash
pnpm add @micro-core/builder-parcel
# or
npm install @micro-core/builder-parcel
# or
yarn add @micro-core/builder-parcel
```

## 🔧 Quick Start

```typescript
import { ParcelBuilder } from '@micro-core/builder-parcel';

// Create builder instance
const builder = new ParcelBuilder({
  mode: 'development',
  entry: './src/index.html',
  outDir: './dist'
});

// Build project
const result = await builder.build({
  entry: './src/index.html',
  outDir: './dist',
  mode: 'production'
});

console.log('Build completed:', result.success);
```

## ⚙️ Configuration

### Basic Configuration

```typescript
const builder = new ParcelBuilder({
  mode: 'development',
  entry: './src/index.html',
  outDir: './dist',
  target: 'browser'
});
```

### Advanced Configuration

```typescript
const builder = new ParcelBuilder({
  mode: 'production',
  entry: {
    main: './src/index.html',
    worker: './src/worker.js'
  },
  outDir: './dist',
  parcelConfig: {
    targets: {
      main: {
        distDir: './dist',
        engines: {
          browsers: ['> 1%', 'last 2 versions']
        }
      }
    },
    transformers: {
      '*.{ts,tsx}': ['@parcel/transformer-typescript-types']
    },
    optimizers: {
      '*.js': ['@parcel/optimizer-terser'],
      '*.css': ['@parcel/optimizer-cssnano']
    }
  }
});
```

## 🔌 API Reference

### ParcelBuilder Class

#### Constructor

```typescript
constructor(options: ParcelBuilderOptions)
```

**Parameters:**
- `options.mode`: Build mode ('development' | 'production')
- `options.entry`: Entry point(s) for the build
- `options.outDir`: Output directory
- `options.target`: Build target ('browser' | 'node' | 'electron-main' | 'electron-renderer')
- `options.parcelConfig`: Custom Parcel configuration

#### Methods

##### `build(config: ParcelBuilderConfig): Promise<BuildResult>`

Execute the build process.

```typescript
const result = await builder.build({
  entry: './src/index.html',
  outDir: './dist',
  mode: 'production'
});
```

##### `serve(config: ParcelBuilderConfig, devServerConfig?: DevServerConfig): Promise<any>`

Start the development server.

```typescript
const server = await builder.serve({
  entry: './src/index.html',
  mode: 'development'
}, {
  port: 3000,
  host: 'localhost',
  hot: true
});
```

##### `stop(): Promise<void>`

Stop the development server.

```typescript
await builder.stop();
```

### Configuration Types

#### ParcelBuilderConfig

```typescript
interface ParcelBuilderConfig extends BaseBuilderConfig {
  target?: 'browser' | 'node' | 'electron-main' | 'electron-renderer';
  parcelConfig?: ParcelConfig;
  plugins?: string[];
  transformers?: Record<string, string[]>;
  optimizers?: Record<string, string[]>;
}
```

## 🏗️ Advanced Usage

### Multi-Target Builds

```typescript
const builder = new ParcelBuilder({
  entry: './src/index.ts',
  parcelConfig: {
    targets: {
      modern: {
        distDir: './dist/modern',
        engines: {
          browsers: ['Chrome >= 80', 'Firefox >= 74']
        }
      },
      legacy: {
        distDir: './dist/legacy',
        engines: {
          browsers: ['> 1%']
        }
      }
    }
  }
});
```

### Custom Transformers

```typescript
const builder = new ParcelBuilder({
  parcelConfig: {
    transformers: {
      '*.vue': ['@parcel/transformer-vue'],
      '*.scss': ['@parcel/transformer-sass'],
      '*.svg': ['@parcel/transformer-svg-react']
    }
  }
});
```

### Environment Variables

```typescript
const builder = new ParcelBuilder({
  parcelConfig: {
    env: {
      NODE_ENV: 'production',
      API_URL: 'https://api.example.com'
    }
  }
});
```

### Asset Processing

```typescript
const builder = new ParcelBuilder({
  parcelConfig: {
    optimizers: {
      '*.{jpg,jpeg,png}': ['@parcel/optimizer-imagemin'],
      '*.svg': ['@parcel/optimizer-svgo'],
      '*.js': ['@parcel/optimizer-terser']
    }
  }
});
```

## 🧪 Testing

```bash
# Run tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Run integration tests
pnpm test:integration
```

## 📊 Performance Tips

1. **Use .parcelrc**: Configure transformers and optimizers for your needs
2. **Enable Caching**: Parcel's built-in caching speeds up rebuilds
3. **Optimize Images**: Use image optimization plugins
4. **Bundle Analysis**: Use @parcel/reporter-bundle-analyzer

```typescript
const builder = new ParcelBuilder({
  parcelConfig: {
    reporters: ['@parcel/reporter-bundle-analyzer'],
    optimizers: {
      '*.{jpg,jpeg,png}': ['@parcel/optimizer-imagemin']
    }
  }
});
```

## 🔍 Troubleshooting

### Common Issues

**Build fails with TypeScript errors:**
```json
// .parcelrc
{
  "extends": "@parcel/config-default",
  "transformers": {
    "*.{ts,tsx}": ["@parcel/transformer-typescript-types"]
  }
}
```

**Asset resolution issues:**
```typescript
// Use explicit imports
import imageUrl from './image.png';
import './styles.css';
```

**Development server not hot reloading:**
```typescript
// Ensure HMR is enabled
devServerConfig: {
  hot: true,
  port: 3000
}
```

## 🔄 Migration Guide

### From Parcel CLI

```bash
# Before
parcel src/index.html --dist-dir dist

# After (Micro-Core)
```

```typescript
const builder = new ParcelBuilder({
  entry: './src/index.html',
  outDir: './dist'
});
```

### From Other Bundlers

```typescript
// Easy migration with zero configuration
const builder = new ParcelBuilder({
  entry: './src/index.html', // or .js, .ts, etc.
  outDir: './dist',
  mode: 'production'
});
```

## 📄 License

MIT © 2025 Micro-Core

## 🤝 Contributing

Contributions are welcome! Please read our contributing guidelines and submit pull requests to our repository.

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/echo008/micro-core/issues)
- 📖 Documentation: [Micro-Core Docs](https://micro-core.dev)
