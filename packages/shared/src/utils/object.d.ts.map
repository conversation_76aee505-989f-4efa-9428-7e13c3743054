{"version": 3, "file": "object.d.ts", "sourceRoot": "", "sources": ["object.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH;;GAEG;AACH,wBAAgB,SAAS,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAkBtC;AAED;;GAEG;AACH,wBAAgB,SAAS,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAgB/F;AAED;;GAEG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,SAAS,MAAM,CAAC,EACjE,GAAG,EAAE,CAAC,EACN,IAAI,EAAE,CAAC,EAAE,GACV,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAQZ;AAED;;GAEG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,SAAS,MAAM,CAAC,EACjE,GAAG,EAAE,CAAC,EACN,IAAI,EAAE,CAAC,EAAE,GACV,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAMZ;AAED;;GAEG;AACH,wBAAgB,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,CAAC,GAAG,CAAC,CAYxE;AAED;;GAEG;AACH,wBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,IAAI,CAa5D;AAED;;GAEG;AACH,wBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAYnD;AAED;;GAEG;AACH,wBAAgB,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAmBrD;AAED;;GAEG;AACH,wBAAgB,aAAa,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,MAAM,SAAK,EAAE,SAAS,SAAM,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAiBzG;AAED;;GAEG;AACH,wBAAgB,eAAe,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,SAAS,SAAM,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAU9F;AAED;;GAEG;AACH,wBAAgB,OAAO,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EACjD,GAAG,EAAE,CAAC,EACN,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,MAAM,GAC5C,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAWrB;AAED;;GAEG;AACH,wBAAgB,SAAS,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,EACtD,GAAG,EAAE,CAAC,EACN,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC,GACvC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAUpB;AAED;;GAEG;AACH,wBAAgB,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAUvE;AAGD;;GAEG;AACH,wBAAgB,OAAO,CAAC,CAAC,EACrB,KAAK,EAAE,CAAC,EAAE,EACV,SAAS,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,MAAM,GAC/B,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CASxB"}