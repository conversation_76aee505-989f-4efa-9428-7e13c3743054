{"version": 3, "file": "communication-plugin.d.ts", "sourceRoot": "", "sources": ["communication-plugin.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAGH,KAAK,YAAY,EACpB,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAa3C,UAAU,oBAAoB;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,GAAG,CAAC;IACV,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,OAAO,CAAC,EAAE,MAAM,CAAC;CACpB;AAED,UAAU,cAAc;IACpB,CAAC,OAAO,EAAE,oBAAoB,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;CACzD;AAED,UAAU,aAAa;IACnB,CAAC,OAAO,EAAE,oBAAoB,GAAG,OAAO,CAAC;CAC5C;AAED,UAAU,eAAe;IACrB,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC;IAC1C,WAAW,IAAI,GAAG,CAAC;CACtB;AAED;;GAEG;AACH,UAAU,yBAA0B,SAAQ,YAAY;IACpD,iBAAiB;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,eAAe;IACf,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,gBAAgB;IAChB,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B,eAAe;IACf,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,WAAW;IACX,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,eAAe;IACf,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B,aAAa;IACb,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,eAAe;IACf,aAAa,CAAC,EAAE,MAAM,CAAC;CAC1B;AAED;;GAEG;AACH,UAAU,aAAa;IACnB,WAAW;IACX,EAAE,EAAE,MAAM,CAAC;IACX,WAAW;IACX,OAAO,EAAE,oBAAoB,CAAC;IAC9B,WAAW;IACX,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU;IACV,MAAM,EAAE,MAAM,CAAC;IACf,UAAU;IACV,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,WAAW;IACX,MAAM,EAAE,SAAS,GAAG,MAAM,GAAG,WAAW,GAAG,QAAQ,CAAC;IACpD,WAAW;IACX,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW;IACX,KAAK,CAAC,EAAE,MAAM,CAAC;CAClB;AAsCD;;;GAGG;AACH,qBAAa,mBAAoB,SAAQ,UAAU;IAC/C,WAAW;IACX,SAAS,CAAC,MAAM,CAAC,EAAE,eAAe,CAAC;IAEnC,aAAa;IACb,OAAO,CAAC,UAAU,CAAsC;IAExD,eAAe;IACf,OAAO,CAAC,QAAQ,CAAsC;IAEtD,WAAW;IACX,OAAO,CAAC,YAAY,CAAuB;IAE3C,WAAW;IACX,OAAO,CAAC,QAAQ,CAA2C;IAE3D,WAAW;IACX,OAAO,CAAC,cAAc,CAAuB;IAE7C,aAAa;IACb,OAAO,CAAC,eAAe,CAAoC;IAE3D,YAAY;IACZ,OAAO,CAAC,aAAa,CAAuB;gBAEhC,MAAM,GAAE,yBAA8B;IA0BlD;;OAEG;cACa,SAAS,CAAC,MAAM,EAAE,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC;IAwCjE;;OAEG;cACa,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC;IAoB5C;;OAEG;IACG,WAAW,CACb,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,GAAG,EACT,MAAM,CAAC,EAAE,MAAM,EACf,OAAO,GAAE;QACL,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,UAAU,CAAC,EAAE,OAAO,CAAC;QACrB,SAAS,CAAC,EAAE,OAAO,CAAC;KAClB,GACP,OAAO,CAAC,MAAM,CAAC;IA4DlB;;OAEG;IACG,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,GAAE,GAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;IAInF;;OAEG;IACH,iBAAiB,CACb,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE,cAAc,EACvB,OAAO,GAAE;QACL,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,MAAM,CAAC,EAAE,aAAa,CAAC;QACvB,QAAQ,CAAC,EAAE,MAAM,CAAC;KAChB,GACP,MAAM;IA0BT;;OAEG;IACH,oBAAoB,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO;IAmBhD;;OAEG;IACH,cAAc,CACV,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE,cAAc,EACvB,OAAO,GAAE,GAAQ,GAClB,MAAM;IAaT;;OAEG;IACH,aAAa,CACT,IAAI,EAAE,MAAM,EACZ,IAAI,GAAE,WAAW,GAAG,SAAS,GAAG,WAAyB,GAC1D,MAAM;IAuBT;;OAEG;IACH,WAAW,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO;IAsB3D;;OAEG;IACH,YAAY,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO;IAuB5D;;OAEG;IACH,WAAW,IAAI,KAAK,CAAC;QACjB,IAAI,EAAE,MAAM,CAAC;QACb,IAAI,EAAE,MAAM,CAAC;QACb,gBAAgB,EAAE,MAAM,CAAC;QACzB,SAAS,EAAE,MAAM,CAAC;KACrB,CAAC;IASF;;OAEG;IACH,iBAAiB,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,aAAa,EAAE;IAKlD;;OAEG;IACH,YAAY,IAAI,IAAI;IAKpB;;OAEG;IACH,eAAe,CAAC,MAAM,EAAE,aAAa,GAAG,IAAI;IAK5C;;OAEG;IACH,kBAAkB,CAAC,MAAM,EAAE,aAAa,GAAG,OAAO;IAUlD;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAI9B;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAK7B;;OAEG;YACW,mBAAmB;IAYjC;;OAEG;YACW,cAAc;IAoE5B;;OAEG;IACH,OAAO,CAAC,UAAU;IAWlB;;OAEG;IACH,OAAO,CAAC,YAAY;IASpB;;OAEG;YACW,sBAAsB;IAqCpC;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAS1B;;OAEG;YACW,WAAW;IAMzB;;OAEG;YACW,WAAW;IAMzB;;OAEG;YACW,YAAY;IAM1B;;OAEG;YACW,cAAc;IAM5B;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAKzB;;OAEG;YACW,qBAAqB;IAYnC;;OAEG;YACW,kBAAkB;IAShC;;OAEG;IACM,QAAQ,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;CAa3C"}