/**
 * @fileoverview 沙箱系统类型定义
 * @description 定义沙箱系统的核心类型和接口
 * <AUTHOR> <<EMAIL>>
 */

/**
 * 沙箱选项配置
 */
export interface SandboxOptions {
    /** 是否启用严格模式 */
    strict?: boolean
    /** 是否启用CSS隔离 */
    cssIsolation?: boolean
    /** 是否启用JavaScript隔离 */
    jsIsolation?: boolean
    /** 是否启用全局变量隔离 */
    globalIsolation?: boolean
    /** 自定义全局变量白名单 */
    globalWhitelist?: string[]
    /** 自定义全局变量黑名单 */
    globalBlacklist?: string[]
    /** 沙箱超时时间（毫秒） */
    timeout?: number
    /** 是否启用调试模式 */
    debug?: boolean
}

/**
 * 沙箱上下文
 */
export interface SandboxContext {
    /** 沙箱窗口对象 */
    window: WindowProxy | Window
    /** 文档对象 */
    document: Document
    /** 位置对象 */
    location: Location
    /** 历史对象 */
    history: History
    /** 导航器对象 */
    navigator: Navigator
}

/**
 * 沙箱策略接口
 */
export interface SandboxStrategy {
    /** 获取沙箱名称 */
    getName(): string

    /** 获取沙箱类型 */
    getType(): string

    /** 检查沙箱是否激活 */
    isActive(): boolean

    /** 激活沙箱 */
    activate(): void

    /** 停用沙箱 */
    deactivate(): void

    /** 获取沙箱上下文 */
    getContext(): SandboxContext

    /** 执行脚本 */
    execScript(script: string): any

    /** 销毁沙箱 */
    destroy(): void
}

/**
 * 沙箱类型枚举
 */
export enum SandboxType {
    PROXY = 'proxy',
    DEFINE_PROPERTY = 'defineProperty',
    IFRAME = 'iframe',
    WEB_COMPONENT = 'webComponent',
    NAMESPACE = 'namespace',
    FEDERATION = 'federation'
}

/**
 * 沙箱状态枚举
 */
export enum SandboxStatus {
    IDLE = 'idle',
    ACTIVE = 'active',
    DESTROYED = 'destroyed'
}

/**
 * CSS隔离策略
 */
export interface CSSIsolationStrategy {
    /** 隔离CSS样式 */
    isolate(css: string, scopeId: string): string

    /** 清理CSS隔离 */
    cleanup(scopeId: string): void
}

/**
 * JavaScript隔离策略
 */
export interface JavaScriptIsolationStrategy {
    /** 隔离JavaScript代码 */
    isolate(js: string, context: SandboxContext): string

    /** 执行隔离的JavaScript代码 */
    execute(isolatedJs: string, context: SandboxContext): any
}

/**
 * 全局变量隔离策略
 */
export interface GlobalVariableIsolationStrategy {
    /** 创建隔离的全局变量环境 */
    createIsolatedGlobals(whitelist?: string[], blacklist?: string[]): Record<string, any>

    /** 恢复全局变量环境 */
    restoreGlobals(): void
}

/**
 * 沙箱事件类型
 */
export interface SandboxEvents {
    /** 沙箱创建事件 */
    'sandbox:created': { name: string; type: string }

    /** 沙箱激活事件 */
    'sandbox:activated': { name: string; type: string }

    /** 沙箱停用事件 */
    'sandbox:deactivated': { name: string; type: string }

    /** 沙箱销毁事件 */
    'sandbox:destroyed': { name: string; type: string }

    /** 沙箱错误事件 */
    'sandbox:error': { name: string; type: string; error: Error }
}

/**
 * 沙箱工厂接口
 */
export interface SandboxFactory {
    /** 创建沙箱实例 */
    createSandbox(type: SandboxType, name: string, options?: SandboxOptions): SandboxStrategy

    /** 获取支持的沙箱类型 */
    getSupportedTypes(): SandboxType[]

    /** 检查沙箱类型是否支持 */
    isTypeSupported(type: SandboxType): boolean
}

/**
 * 沙箱管理器接口
 */
export interface SandboxManager {
    /** 创建沙箱 */
    createSandbox(type: SandboxType, name: string, options?: SandboxOptions): SandboxStrategy

    /** 获取沙箱 */
    getSandbox(name: string): SandboxStrategy | undefined

    /** 获取所有沙箱 */
    getAllSandboxes(): Map<string, SandboxStrategy>

    /** 激活沙箱 */
    activateSandbox(name: string): void

    /** 停用沙箱 */
    deactivateSandbox(name: string): void

    /** 销毁沙箱 */
    destroySandbox(name: string): void

    /** 销毁所有沙箱 */
    destroyAllSandboxes(): void

    /** 获取活跃的沙箱 */
    getActiveSandboxes(): SandboxStrategy[]
}

/**
 * 隔离管理器接口
 */
export interface IsolationManager {
    /** 启用CSS隔离 */
    enableCSSIsolation(scopeId: string): void

    /** 禁用CSS隔离 */
    disableCSSIsolation(scopeId: string): void

    /** 启用JavaScript隔离 */
    enableJSIsolation(context: SandboxContext): void

    /** 禁用JavaScript隔离 */
    disableJSIsolation(): void

    /** 启用全局变量隔离 */
    enableGlobalIsolation(whitelist?: string[], blacklist?: string[]): void

    /** 禁用全局变量隔离 */
    disableGlobalIsolation(): void
}