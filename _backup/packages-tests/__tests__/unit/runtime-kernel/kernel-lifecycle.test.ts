/**
 * @fileoverview MicroCoreKernel 生命周期管理测试
 * @description 测试内核的启动、停止、销毁等生命周期功能
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { MicroCoreError } from '../../../src/errors';
import { MicroCoreKernel } from '../../../src/runtime/kernel';
import type { MicroCoreOptions } from '../../../src/types';

// Mock 依赖模块
vi.mock('../../../src/communication/event-bus');
vi.mock('../../../src/runtime/app-registry');
vi.mock('../../../src/runtime/lifecycle-manager');
vi.mock('../../../src/utils');

describe('MicroCoreKernel 生命周期管理', () => {
    let kernel: MicroCoreKernel;
    let mockEventBus: any;
    let mockAppRegistry: any;
    let mockLifecycleManager: any;
    let mockLogger: any;

    beforeEach(() => {
        // 创建模拟对象
        mockEventBus = {
            emit: vi.fn(),
            on: vi.fn(),
            off: vi.fn(),
            clear: vi.fn()
        };

        mockAppRegistry = {
            register: vi.fn(),
            unregister: vi.fn(),
            get: vi.fn(),
            getAll: vi.fn(),
            getByStatus: vi.fn(),
            updateStatus: vi.fn(),
            setError: vi.fn(),
            clear: vi.fn()
        };

        mockLifecycleManager = {
            bootstrap: vi.fn(),
            mount: vi.fn(),
            unmount: vi.fn(),
            clearHooks: vi.fn()
        };

        mockLogger = {
            debug: vi.fn(),
            info: vi.fn(),
            warn: vi.fn(),
            error: vi.fn()
        };

        // Mock 构造函数
        const { EventBus } = require('../../../src/communication/event-bus');
        const { AppRegistry } = require('../../../src/runtime/app-registry');
        const { LifecycleManager } = require('../../../src/runtime/lifecycle-manager');
        const { createLogger } = require('../../../src/utils');

        EventBus.mockImplementation(() => mockEventBus);
        AppRegistry.mockImplementation(() => mockAppRegistry);
        LifecycleManager.mockImplementation(() => mockLifecycleManager);
        createLogger.mockReturnValue(mockLogger);

        kernel = new MicroCoreKernel();
    });

    afterEach(() => {
        vi.clearAllMocks();
        vi.resetModules();
    });

    describe('构造函数', () => {
        it('应该使用默认配置创建内核实例', () => {
            const newKernel = new MicroCoreKernel();
            expect(newKernel).toBeInstanceOf(MicroCoreKernel);
            expect(mockLogger.info).toHaveBeenCalledWith('微前端内核初始化完成');
        });

        it('应该使用自定义配置创建内核实例', () => {
            const config: MicroCoreOptions = {
                development: true,
                logLevel: 'DEBUG',
                defaultSandbox: 'iframe'
            };

            const newKernel = new MicroCoreKernel(config);
            expect(newKernel).toBeInstanceOf(MicroCoreKernel);
        });

        it('应该合并默认配置和自定义配置', () => {
            const config: MicroCoreOptions = {
                development: true
            };

            const newKernel = new MicroCoreKernel(config);
            expect(newKernel).toBeInstanceOf(MicroCoreKernel);
        });

        it('应该处理空配置对象', () => {
            const newKernel = new MicroCoreKernel({});
            expect(newKernel).toBeInstanceOf(MicroCoreKernel);
        });
    });

    describe('start', () => {
        it('应该成功启动内核', async () => {
            await kernel.start();

            expect(mockLogger.info).toHaveBeenCalledWith('启动微前端内核...');
            expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:starting');
            expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:started');
            expect(mockLogger.info).toHaveBeenCalledWith('微前端内核启动完成');
            expect(kernel.isKernelStarted()).toBe(true);
        });

        it('应该忽略重复启动请求', async () => {
            await kernel.start();
            mockLogger.warn.mockClear();

            await kernel.start();

            expect(mockLogger.warn).toHaveBeenCalledWith('内核已启动，忽略重复启动请求');
        });

        it('应该处理启动失败的情况', async () => {
            const error = new Error('启动失败');
            mockEventBus.emit.mockImplementation((event: string) => {
                if (event === 'kernel:starting') {
                    throw error;
                }
            });

            await expect(kernel.start()).rejects.toThrow(MicroCoreError);
            expect(mockLogger.error).toHaveBeenCalledWith('内核启动失败:', error);
        });

        it('应该在内核销毁后抛出错误', async () => {
            await kernel.destroy();

            await expect(kernel.start()).rejects.toThrow(MicroCoreError);
        });

        it('应该处理字符串类型的启动错误', async () => {
            mockEventBus.emit.mockImplementation((event: string) => {
                if (event === 'kernel:starting') {
                    throw 'string error';
                }
            });

            await expect(kernel.start()).rejects.toThrow(MicroCoreError);
        });
    });

    describe('stop', () => {
        it('应该成功停止内核', async () => {
            // 先启动内核
            await kernel.start();

            // 模拟已挂载的应用
            const mountedApps: any[] = [
                {
                    name: 'mounted-app',
                    status: 'MOUNTED',
                    config: {
                        name: 'mounted-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                }
            ];
            mockAppRegistry.getByStatus.mockReturnValue(mountedApps);

            await kernel.stop();

            expect(mockLogger.info).toHaveBeenCalledWith('停止微前端内核...');
            expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:stopping');
            expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:stopped');
            expect(mockLogger.info).toHaveBeenCalledWith('微前端内核停止完成');
            expect(kernel.isKernelStarted()).toBe(false);
        });

        it('应该忽略未启动时的停止请求', async () => {
            await kernel.stop();

            expect(mockLogger.warn).toHaveBeenCalledWith('内核未启动，忽略停止请求');
        });

        it('应该处理停止失败的情况', async () => {
            await kernel.start();

            const error = new Error('停止失败');
            mockEventBus.emit.mockImplementation((event: string) => {
                if (event === 'kernel:stopping') {
                    throw error;
                }
            });

            await expect(kernel.stop()).rejects.toThrow(MicroCoreError);
            expect(mockLogger.error).toHaveBeenCalledWith('内核停止失败:', error);
        });

        it('应该在内核销毁后抛出错误', async () => {
            await kernel.destroy();

            await expect(kernel.stop()).rejects.toThrow(MicroCoreError);
        });

        it('应该处理字符串类型的停止错误', async () => {
            await kernel.start();

            mockEventBus.emit.mockImplementation((event: string) => {
                if (event === 'kernel:stopping') {
                    throw 'string error';
                }
            });

            await expect(kernel.stop()).rejects.toThrow(MicroCoreError);
        });
    });

    describe('destroy', () => {
        it('应该成功销毁内核', async () => {
            await kernel.start();
            await kernel.destroy();

            expect(mockLogger.info).toHaveBeenCalledWith('销毁微前端内核...');
            expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:destroying');
            expect(mockAppRegistry.clear).toHaveBeenCalled();
            expect(mockLifecycleManager.clearHooks).toHaveBeenCalled();
            expect(mockEventBus.clear).toHaveBeenCalled();
            expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:destroyed');
            expect(mockLogger.info).toHaveBeenCalledWith('微前端内核销毁完成');
            expect(kernel.isKernelDestroyed()).toBe(true);
        });

        it('应该忽略重复销毁请求', async () => {
            await kernel.destroy();
            mockLogger.warn.mockClear();

            await kernel.destroy();

            expect(mockLogger.warn).toHaveBeenCalledWith('内核已销毁，忽略重复销毁请求');
        });

        it('应该处理销毁失败的情况', async () => {
            const error = new Error('销毁失败');
            mockAppRegistry.clear.mockImplementation(() => {
                throw error;
            });

            await kernel.destroy();

            expect(mockLogger.error).toHaveBeenCalledWith('内核销毁失败:', error);
            expect(kernel.isKernelDestroyed()).toBe(true); // 即使失败也应该标记为已销毁
        });

        it('应该在销毁前先停止内核', async () => {
            await kernel.start();
            expect(kernel.isKernelStarted()).toBe(true);

            await kernel.destroy();

            expect(kernel.isKernelStarted()).toBe(false);
            expect(kernel.isKernelDestroyed()).toBe(true);
        });
    });

    describe('状态检查方法', () => {
        describe('isKernelStarted', () => {
            it('应该正确返回内核启动状态', async () => {
                expect(kernel.isKernelStarted()).toBe(false);

                await kernel.start();
                expect(kernel.isKernelStarted()).toBe(true);

                await kernel.stop();
                expect(kernel.isKernelStarted()).toBe(false);
            });
        });

        describe('isKernelDestroyed', () => {
            it('应该正确返回内核销毁状态', async () => {
                expect(kernel.isKernelDestroyed()).toBe(false);

                await kernel.destroy();
                expect(kernel.isKernelDestroyed()).toBe(true);
            });
        });
    });

    describe('内存泄漏防护', () => {
        it('应该在销毁后清理所有引用', async () => {
            // 注册一些应用和插件
            const appConfig: any = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app',
                activeWhen: '/test'
            };
            kernel.registerApplication(appConfig);

            const plugin: any = {
                name: 'test-plugin',
                version: '1.0.0'
            };
            kernel.use(plugin);

            await kernel.start();
            await kernel.destroy();

            // 验证清理工作
            expect(mockAppRegistry.clear).toHaveBeenCalled();
            expect(mockLifecycleManager.clearHooks).toHaveBeenCalled();
            expect(mockEventBus.clear).toHaveBeenCalled();
            expect(kernel.isKernelDestroyed()).toBe(true);
        });

        it('应该正确处理重复操作', async () => {
            // 重复启动
            await kernel.start();
            await kernel.start();
            expect(mockLogger.warn).toHaveBeenCalledWith('内核已启动，忽略重复启动请求');

            // 重复停止
            await kernel.stop();
            await kernel.stop();
            expect(mockLogger.warn).toHaveBeenCalledWith('内核未启动，忽略停止请求');

            // 重复销毁
            await kernel.destroy();
            await kernel.destroy();
            expect(mockLogger.warn).toHaveBeenCalledWith('内核已销毁，忽略重复销毁请求');
        });
    });

    describe('私有方法测试', () => {
        it('应该正确处理路由监听', async () => {
            await kernel.start();
            expect(mockLogger.debug).toHaveBeenCalledWith('启动路由监听');

            await kernel.stop();
            expect(mockLogger.debug).toHaveBeenCalledWith('停止路由监听');
        });
    });
});