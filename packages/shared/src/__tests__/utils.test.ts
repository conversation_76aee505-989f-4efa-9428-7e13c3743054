/**
 * 工具函数测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import {
    createCacheManager,
    createEventEmitter,
    createLogger,
    createMicroCoreUtils,
    createPerformanceMonitor,
    createPromiseWithResolvers,
    createRetryFunction,
    debounce,
    deepClone,
    deepMerge,
    formatBytes,
    generateId,
    isPlainObject,
    parseUrl,
    throttle,
    validateConfig
} from '../utils'

describe('工具函数', () => {
    describe('createLogger', () => {
        it('应该创建日志记录器', () => {
            const logger = createLogger('test')
            expect(logger).toHaveProperty('info')
            expect(logger).toHaveProperty('warn')
            expect(logger).toHaveProperty('error')
            expect(logger).toHaveProperty('debug')
        })

        it('应该能够记录不同级别的日志', () => {
            const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { })
            const logger = createLogger('test')

            logger.info('info message')
            logger.warn('warn message')
            logger.error('error message')
            logger.debug('debug message')

            expect(consoleSpy).toHaveBeenCalled()
            consoleSpy.mockRestore()
        })
    })

    describe('createMicroCoreUtils', () => {
        it('应该创建微内核工具集', () => {
            const utils = createMicroCoreUtils()
            expect(utils).toHaveProperty('logger')
            expect(utils).toHaveProperty('eventEmitter')
            expect(utils).toHaveProperty('cache')
            expect(utils).toHaveProperty('performance')
        })
    })

    describe('debounce', () => {
        beforeEach(() => {
            vi.useFakeTimers()
        })

        afterEach(() => {
            vi.useRealTimers()
        })

        it('应该延迟执行函数', () => {
            const fn = vi.fn()
            const debouncedFn = debounce(fn, 100)

            debouncedFn()
            expect(fn).not.toHaveBeenCalled()

            vi.advanceTimersByTime(100)
            expect(fn).toHaveBeenCalledTimes(1)
        })

        it('应该在多次调用时只执行最后一次', () => {
            const fn = vi.fn()
            const debouncedFn = debounce(fn, 100)

            debouncedFn()
            debouncedFn()
            debouncedFn()

            vi.advanceTimersByTime(100)
            expect(fn).toHaveBeenCalledTimes(1)
        })
    })

    describe('throttle', () => {
        beforeEach(() => {
            vi.useFakeTimers()
        })

        afterEach(() => {
            vi.useRealTimers()
        })

        it('应该限制函数执行频率', () => {
            const fn = vi.fn()
            const throttledFn = throttle(fn, 100)

            throttledFn()
            throttledFn()
            throttledFn()

            expect(fn).toHaveBeenCalledTimes(1)

            vi.advanceTimersByTime(100)
            throttledFn()

            expect(fn).toHaveBeenCalledTimes(2)
        })
    })

    describe('deepClone', () => {
        it('应该深度克隆对象', () => {
            const obj = {
                a: 1,
                b: {
                    c: 2,
                    d: [3, 4, { e: 5 }]
                }
            }

            const cloned = deepClone(obj)

            expect(cloned).toEqual(obj)
            expect(cloned).not.toBe(obj)
            expect(cloned.b).not.toBe(obj.b)
            expect(cloned.b.d).not.toBe(obj.b.d)
        })

        it('应该处理循环引用', () => {
            const obj: any = { a: 1 }
            obj.self = obj

            const cloned = deepClone(obj)

            expect(cloned.a).toBe(1)
            expect(cloned.self).toBe(cloned)
        })

        it('应该处理特殊类型', () => {
            const date = new Date()
            const regex = /test/g
            const obj = { date, regex, null: null, undefined: undefined }

            const cloned = deepClone(obj)

            expect(cloned.date).toEqual(date)
            expect(cloned.date).not.toBe(date)
            expect(cloned.regex).toEqual(regex)
            expect(cloned.regex).not.toBe(regex)
            expect(cloned.null).toBeNull()
            expect(cloned.undefined).toBeUndefined()
        })
    })

    describe('deepMerge', () => {
        it('应该深度合并对象', () => {
            const obj1 = { a: 1, b: { c: 2 } }
            const obj2 = { b: { d: 3 }, e: 4 }

            const merged = deepMerge(obj1, obj2)

            expect(merged).toEqual({
                a: 1,
                b: { c: 2, d: 3 },
                e: 4
            })
        })

        it('应该处理数组合并', () => {
            const obj1 = { arr: [1, 2] }
            const obj2 = { arr: [3, 4] }

            const merged = deepMerge(obj1, obj2)

            expect(merged.arr).toEqual([3, 4])
        })
    })

    describe('isPlainObject', () => {
        it('应该正确识别普通对象', () => {
            expect(isPlainObject({})).toBe(true)
            expect(isPlainObject({ a: 1 })).toBe(true)
            expect(isPlainObject(Object.create(null))).toBe(true)
        })

        it('应该正确识别非普通对象', () => {
            expect(isPlainObject([])).toBe(false)
            expect(isPlainObject(new Date())).toBe(false)
            expect(isPlainObject(/test/)).toBe(false)
            expect(isPlainObject(null)).toBe(false)
            expect(isPlainObject(undefined)).toBe(false)
            expect(isPlainObject('string')).toBe(false)
            expect(isPlainObject(123)).toBe(false)
        })
    })

    describe('generateId', () => {
        it('应该生成唯一ID', () => {
            const id1 = generateId()
            const id2 = generateId()

            expect(id1).toBeDefined()
            expect(id2).toBeDefined()
            expect(id1).not.toBe(id2)
        })

        it('应该支持自定义前缀', () => {
            const id = generateId('test')
            expect(id).toMatch(/^test-/)
        })

        it('应该支持自定义长度', () => {
            const id = generateId('', 10)
            expect(id).toHaveLength(10)
        })
    })

    describe('formatBytes', () => {
        it('应该格式化字节数', () => {
            expect(formatBytes(0)).toBe('0 Bytes')
            expect(formatBytes(1024)).toBe('1 KB')
            expect(formatBytes(1024 * 1024)).toBe('1 MB')
            expect(formatBytes(1024 * 1024 * 1024)).toBe('1 GB')
        })

        it('应该支持自定义小数位数', () => {
            expect(formatBytes(1536, 2)).toBe('1.50 KB')
        })
    })

    describe('parseUrl', () => {
        it('应该解析URL', () => {
            const url = 'https://example.com:8080/path?query=value#hash'
            const parsed = parseUrl(url)

            expect(parsed.protocol).toBe('https:')
            expect(parsed.hostname).toBe('example.com')
            expect(parsed.port).toBe('8080')
            expect(parsed.pathname).toBe('/path')
            expect(parsed.search).toBe('?query=value')
            expect(parsed.hash).toBe('#hash')
        })

        it('应该处理相对URL', () => {
            const parsed = parseUrl('/path?query=value')

            expect(parsed.pathname).toBe('/path')
            expect(parsed.search).toBe('?query=value')
        })
    })

    describe('validateConfig', () => {
        it('应该验证配置对象', () => {
            const schema = {
                name: { type: 'string', required: true },
                age: { type: 'number', required: false }
            }

            const validConfig = { name: 'test', age: 25 }
            const invalidConfig = { age: 25 }

            expect(() => validateConfig(validConfig, schema)).not.toThrow()
            expect(() => validateConfig(invalidConfig, schema)).toThrow()
        })
    })

    describe('createEventEmitter', () => {
        it('应该创建事件发射器', () => {
            const emitter = createEventEmitter()
            const handler = vi.fn()

            emitter.on('test', handler)
            emitter.emit('test', 'data')

            expect(handler).toHaveBeenCalledWith('data')
        })

        it('应该支持一次性监听', () => {
            const emitter = createEventEmitter()
            const handler = vi.fn()

            emitter.once('test', handler)
            emitter.emit('test', 'data1')
            emitter.emit('test', 'data2')

            expect(handler).toHaveBeenCalledTimes(1)
            expect(handler).toHaveBeenCalledWith('data1')
        })

        it('应该支持取消监听', () => {
            const emitter = createEventEmitter()
            const handler = vi.fn()

            emitter.on('test', handler)
            emitter.off('test', handler)
            emitter.emit('test', 'data')

            expect(handler).not.toHaveBeenCalled()
        })
    })

    describe('createPromiseWithResolvers', () => {
        it('应该创建带解析器的Promise', () => {
            const { promise, resolve, reject } = createPromiseWithResolvers<string>()

            expect(promise).toBeInstanceOf(Promise)
            expect(typeof resolve).toBe('function')
            expect(typeof reject).toBe('function')
        })

        it('应该能够解析Promise', async () => {
            const { promise, resolve } = createPromiseWithResolvers<string>()

            resolve('success')

            await expect(promise).resolves.toBe('success')
        })

        it('应该能够拒绝Promise', async () => {
            const { promise, reject } = createPromiseWithResolvers<string>()

            reject(new Error('failed'))

            await expect(promise).rejects.toThrow('failed')
        })
    })

    describe('createRetryFunction', () => {
        it('应该创建重试函数', async () => {
            let attempts = 0
            const fn = vi.fn().mockImplementation(() => {
                attempts++
                if (attempts < 3) {
                    throw new Error('failed')
                }
                return 'success'
            })

            const retryFn = createRetryFunction(fn, { maxAttempts: 3, delay: 10 })
            const result = await retryFn()

            expect(result).toBe('success')
            expect(fn).toHaveBeenCalledTimes(3)
        })

        it('应该在超过最大重试次数后抛出错误', async () => {
            const fn = vi.fn().mockRejectedValue(new Error('failed'))
            const retryFn = createRetryFunction(fn, { maxAttempts: 2, delay: 10 })

            await expect(retryFn()).rejects.toThrow('failed')
            expect(fn).toHaveBeenCalledTimes(2)
        })
    })

    describe('createCacheManager', () => {
        it('应该创建缓存管理器', () => {
            const cache = createCacheManager()

            expect(cache.set).toBeDefined()
            expect(cache.get).toBeDefined()
            expect(cache.has).toBeDefined()
            expect(cache.delete).toBeDefined()
            expect(cache.clear).toBeDefined()
        })

        it('应该能够设置和获取缓存', () => {
            const cache = createCacheManager()

            cache.set('key', 'value')
            expect(cache.get('key')).toBe('value')
            expect(cache.has('key')).toBe(true)
        })

        it('应该支持TTL过期', async () => {
            const cache = createCacheManager()

            cache.set('key', 'value', 50)
            expect(cache.get('key')).toBe('value')

            await new Promise(resolve => setTimeout(resolve, 60))
            expect(cache.get('key')).toBeUndefined()
        })

        it('应该能够删除和清空缓存', () => {
            const cache = createCacheManager()

            cache.set('key1', 'value1')
            cache.set('key2', 'value2')

            cache.delete('key1')
            expect(cache.has('key1')).toBe(false)
            expect(cache.has('key2')).toBe(true)

            cache.clear()
            expect(cache.has('key2')).toBe(false)
        })
    })

    describe('createPerformanceMonitor', () => {
        it('应该创建性能监控器', () => {
            const monitor = createPerformanceMonitor()

            expect(monitor.start).toBeDefined()
            expect(monitor.end).toBeDefined()
            expect(monitor.measure).toBeDefined()
            expect(monitor.getMetrics).toBeDefined()
        })

        it('应该能够测量性能', () => {
            const monitor = createPerformanceMonitor()

            monitor.start('test')
            monitor.end('test')

            const metrics = monitor.getMetrics()
            expect(metrics.test).toBeDefined()
            expect(typeof metrics.test.duration).toBe('number')
        })

        it('应该能够直接测量函数执行时间', async () => {
            const monitor = createPerformanceMonitor()

            const result = await monitor.measure('async-test', async () => {
                await new Promise(resolve => setTimeout(resolve, 10))
                return 'result'
            })

            expect(result).toBe('result')
            const metrics = monitor.getMetrics()
            expect(metrics['async-test']).toBeDefined()
            expect(metrics['async-test'].duration).toBeGreaterThan(0)
        })
    })
})
