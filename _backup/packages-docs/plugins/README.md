# @micro-core/plugins

微前端核心插件系统 - 提供丰富的插件生态，扩展微前端框架功能。

## 📦 包含插件

### 🚀 核心插件

- **router** - 路由管理插件，提供应用路由同步和管理
- **communication** - 应用间通信插件，支持事件总线和状态共享
- **plugin-auth** - 认证授权插件，提供统一的身份认证和权限管理
- **plugin-sandbox-proxy** - 代理沙箱插件，基于Proxy提供应用隔离环境
- **plugin-sandbox-iframe** - iframe沙箱插件，基于iframe提供强隔离环境
- **plugin-sandbox-snapshot** - 快照沙箱插件，基于快照机制提供轻量级隔离
- **plugin-store** - 状态管理插件，提供跨应用状态共享
- **plugin-i18n** - 国际化插件，支持多语言切换
- **plugin-logger** - 日志管理插件，统一日志收集和管理
- **plugin-monitor** - 监控插件，提供性能监控和错误追踪

### 🔧 工具插件

- **plugin-devtools** - 开发工具插件，提供调试和开发辅助功能
- **plugin-mock** - 数据模拟插件，支持API数据模拟
- **plugin-cache** - 缓存管理插件，提供资源和数据缓存
- **plugin-prefetch** - 预加载插件，智能预加载应用资源

## 🚀 快速开始

### 安装

```bash
# 使用 pnpm
pnpm add @micro-core/plugins

# 使用 npm
npm install @micro-core/plugins

# 使用 yarn
yarn add @micro-core/plugins
```

### 基本使用

```typescript
import { MicroCoreKernel } from '@micro-core/core';
import { 
  createRouterPlugin,
  createCommunicationPlugin,
  createAuthPlugin,
  createProxySandboxPlugin
} from '@micro-core/plugins';

// 创建内核实例
const kernel = new MicroCoreKernel();

// 安装插件
await kernel.use(createRouterPlugin({
  mode: 'history',
  base: '/'
}));

await kernel.use(createCommunicationPlugin({
  enableEventBus: true,
  enableGlobalState: true
}));

await kernel.use(createAuthPlugin({
  apiUrl: '/api/auth',
  tokenKey: 'access_token'
}));

await kernel.use(createProxySandboxPlugin({
  enableStyleIsolation: true,
  enableScriptIsolation: true
}));

// 启动内核
await kernel.start();
```

## 📖 插件详细说明

### 路由插件 (Router Plugin)

提供微前端应用的路由管理和同步功能。

```typescript
import { createRouterPlugin, RouterMode } from '@micro-core/plugins';

const routerPlugin = createRouterPlugin({
  mode: RouterMode.HISTORY, // 路由模式：hash | history | memory
  base: '/',                // 基础路径
  strict: false,           // 严格模式
  sensitive: false         // 大小写敏感
});
```

**主要功能：**
- 支持 Hash、History、Memory 三种路由模式
- 自动同步应用路由状态
- 支持路由守卫和权限控制
- 提供编程式导航API

### 通信插件 (Communication Plugin)

提供应用间通信和状态共享功能。

```typescript
import { createCommunicationPlugin } from '@micro-core/plugins';

const communicationPlugin = createCommunicationPlugin({
  enableEventBus: true,     // 启用事件总线
  enableGlobalState: true,  // 启用全局状态
  timeout: 5000,           // 请求超时时间
  enableLogging: true      // 启用日志
});
```

**主要功能：**
- 事件总线：支持应用间事件通信
- 全局状态：跨应用状态共享和同步
- 请求响应：支持异步请求响应模式
- 消息队列：可靠的消息传递机制

### 认证插件 (Auth Plugin)

提供统一的身份认证和权限管理。

```typescript
import { createAuthPlugin } from '@micro-core/plugins';

const authPlugin = createAuthPlugin({
  apiUrl: '/api/auth',           // 认证API地址
  tokenKey: 'access_token',      // 令牌存储键
  refreshTokenKey: 'refresh_token', // 刷新令牌键
  autoRefresh: true,             // 自动刷新令牌
  enablePermissionCheck: true,   // 启用权限检查
  loginUrl: '/login',           // 登录页面URL
  unauthorizedUrl: '/403'       // 未授权页面URL
});
```

**主要功能：**
- JWT令牌管理：自动处理令牌存储和刷新
- 权限控制：基于角色和权限的访问控制
- 路由守卫：自动检查路由访问权限
- 用户状态：统一的用户认证状态管理

### 沙箱插件 (Sandbox Plugins)

提供多种应用隔离方案。

#### 代理沙箱 (Proxy Sandbox)

```typescript
import { createProxySandboxPlugin } from '@micro-core/plugins';

const proxySandboxPlugin = createProxySandboxPlugin({
  enableStyleIsolation: true,   // 样式隔离
  enableScriptIsolation: true,  // 脚本隔离
  enableEventIsolation: true,   // 事件隔离
  strictMode: false,           // 严格模式
  whiteList: [],              // 白名单
  blackList: []               // 黑名单
});
```

#### iframe沙箱 (Iframe Sandbox)

```typescript
import { createIframeSandboxPlugin } from '@micro-core/plugins';

const iframeSandboxPlugin = createIframeSandboxPlugin({
  enableCommunication: true,    // 启用通信
  sandbox: 'allow-scripts',     // 沙箱权限
  enableStyleIsolation: true    // 样式隔离
});
```

#### 快照沙箱 (Snapshot Sandbox)

```typescript
import { createSnapshotSandboxPlugin } from '@micro-core/plugins';

const snapshotSandboxPlugin = createSnapshotSandboxPlugin({
  enableWindowSnapshot: true,   // 窗口快照
  enableDocumentSnapshot: true, // 文档快照
  restoreOnUnmount: true       // 卸载时恢复
});
```

## 🔧 插件开发

### 创建自定义插件

```typescript
import type { Plugin, MicroCoreKernel } from '@micro-core/core';

export class CustomPlugin implements Plugin {
  public readonly name = 'custom';
  public readonly version = '1.0.0';

  private kernel?: MicroCoreKernel;

  constructor(private options: CustomPluginOptions = {}) {}

  async install(kernel: MicroCoreKernel): Promise<void> {
    this.kernel = kernel;
    
    // 注册生命周期钩子
    kernel.registerHook('beforeAppMount', this.beforeAppMount.bind(this));
    kernel.registerHook('afterAppMount', this.afterAppMount.bind(this));
    
    console.log('[CustomPlugin] 插件已安装');
  }

  async uninstall(): Promise<void> {
    this.kernel = undefined;
    console.log('[CustomPlugin] 插件已卸载');
  }

  private async beforeAppMount(appInfo: AppInfo): Promise<void> {
    // 应用挂载前的处理逻辑
  }

  private async afterAppMount(appInfo: AppInfo): Promise<void> {
    // 应用挂载后的处理逻辑
  }
}

export function createCustomPlugin(options?: CustomPluginOptions): CustomPlugin {
  return new CustomPlugin(options);
}
```

### 插件生命周期钩子

插件可以注册以下生命周期钩子：

- `beforeAppRegister` - 应用注册前
- `afterAppRegister` - 应用注册后
- `beforeAppLoad` - 应用加载前
- `afterAppLoad` - 应用加载后
- `beforeAppMount` - 应用挂载前
- `afterAppMount` - 应用挂载后
- `beforeAppUnmount` - 应用卸载前
- `afterAppUnmount` - 应用卸载后
- `beforeAppUnregister` - 应用注销前
- `afterAppUnregister` - 应用注销后

## 📚 API 参考

### 插件接口

```typescript
interface Plugin {
  readonly name: string;
  readonly version: string;
  install(kernel: MicroCoreKernel): Promise<void> | void;
  uninstall?(): Promise<void> | void;
}
```

### 内核扩展

插件可以通过以下方式扩展内核功能：

```typescript
// 注册生命周期钩子
kernel.registerHook(hookName, handler);

// 获取事件总线
const eventBus = kernel.getEventBus();

// 发射事件
eventBus.emit(eventName, data);

// 监听事件
eventBus.on(eventName, handler);

// 获取应用信息
const app = kernel.getApplication(appName);

// 获取所有应用
const apps = kernel.getApplications();
```

## 🤝 贡献指南

欢迎贡献新的插件或改进现有插件！

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-plugin`)
3. 提交更改 (`git commit -am 'Add amazing plugin'`)
4. 推送到分支 (`git push origin feature/amazing-plugin`)
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](../../LICENSE) 文件。

## 🔗 相关链接

- [微前端核心文档](../core/README.md)
- [适配器文档](../adapters/README.md)
- [构建工具文档](../builders/README.md)
- [示例应用](../../apps/examples/README.md)