# 适配器 API

Micro-Core 的适配器系统提供了跨框架支持，允许不同技术栈的应用在同一个微前端系统中协同工作。

## 核心接口

### Adapter 接口

所有适配器必须实现 `Adapter` 接口：

```typescript
interface Adapter {
  // 基本信息
  readonly name: string
  readonly version: string
  readonly framework: string
  
  // 生命周期方法
  mount(container: HTMLElement, props?: any): Promise<void>
  unmount(): Promise<void>
  update?(props: any): Promise<void>
  
  // 状态管理
  getState?(): any
  setState?(state: any): void
  
  // 事件处理
  on?(event: string, handler: Function): void
  off?(event: string, handler?: Function): void
  emit?(event: string, data?: any): void
}
```

### AdapterContext 接口

适配器上下文提供了访问系统功能的接口：

```typescript
interface AdapterContext {
  // 容器信息
  readonly container: HTMLElement
  readonly appName: string
  readonly props: any
  
  // 系统服务
  readonly eventBus: EventBus
  readonly globalState: GlobalState
  readonly router: Router
  
  // 工具方法
  createSandbox(): Sandbox
  destroySandbox(sandbox: Sandbox): void
  loadResource(url: string): Promise<any>
}
```

## 内置适配器

### React 适配器

```typescript
import { ReactAdapter } from '@micro-core/adapter-react'

class MyReactAdapter extends ReactAdapter {
  name = 'my-react-app'
  version = '1.0.0'
  framework = 'react'
  
  async mount(container: HTMLElement, props?: any) {
    const { createRoot } = await import('react-dom/client')
    const App = await import('./App')
    
    this.root = createRoot(container)
    this.root.render(<App.default {...props} />)
  }
  
  async unmount() {
    if (this.root) {
      this.root.unmount()
      this.root = null
    }
  }
  
  async update(props: any) {
    if (this.root) {
      const App = await import('./App')
      this.root.render(<App.default {...props} />)
    }
  }
}
```

### Vue 适配器

```typescript
import { VueAdapter } from '@micro-core/adapter-vue'

class MyVueAdapter extends VueAdapter {
  name = 'my-vue-app'
  version = '1.0.0'
  framework = 'vue'
  
  async mount(container: HTMLElement, props?: any) {
    const { createApp } = await import('vue')
    const App = await import('./App.vue')
    
    this.app = createApp(App.default, props)
    this.app.mount(container)
  }
  
  async unmount() {
    if (this.app) {
      this.app.unmount()
      this.app = null
    }
  }
  
  async update(props: any) {
    if (this.app) {
      // 更新 props
      Object.assign(this.app._props, props)
    }
  }
}
```

### Angular 适配器

```typescript
import { AngularAdapter } from '@micro-core/adapter-angular'

class MyAngularAdapter extends AngularAdapter {
  name = 'my-angular-app'
  version = '1.0.0'
  framework = 'angular'
  
  async mount(container: HTMLElement, props?: any) {
    const { platformBrowserDynamic } = await import('@angular/platform-browser-dynamic')
    const { AppModule } = await import('./app.module')
    
    this.platformRef = platformBrowserDynamic()
    this.moduleRef = await this.platformRef.bootstrapModule(AppModule)
    
    // 将组件渲染到容器
    const componentRef = this.moduleRef.injector.get(ApplicationRef)
    componentRef.bootstrap(AppComponent, container)
  }
  
  async unmount() {
    if (this.moduleRef) {
      this.moduleRef.destroy()
      this.moduleRef = null
    }
    if (this.platformRef) {
      this.platformRef.destroy()
      this.platformRef = null
    }
  }
}
```

## 自定义适配器

### 基础适配器类

```typescript
abstract class BaseAdapter implements Adapter {
  abstract readonly name: string
  abstract readonly version: string
  abstract readonly framework: string
  
  protected container: HTMLElement | null = null
  protected context: AdapterContext | null = null
  
  async mount(container: HTMLElement, props?: any): Promise<void> {
    this.container = container
    this.context = this.createContext(container, props)
    
    await this.beforeMount()
    await this.doMount(container, props)
    await this.afterMount()
  }
  
  async unmount(): Promise<void> {
    await this.beforeUnmount()
    await this.doUnmount()
    await this.afterUnmount()
    
    this.container = null
    this.context = null
  }
  
  // 抽象方法，子类必须实现
  protected abstract doMount(container: HTMLElement, props?: any): Promise<void>
  protected abstract doUnmount(): Promise<void>
  
  // 生命周期钩子，子类可选实现
  protected async beforeMount(): Promise<void> {}
  protected async afterMount(): Promise<void> {}
  protected async beforeUnmount(): Promise<void> {}
  protected async afterUnmount(): Promise<void> {}
  
  private createContext(container: HTMLElement, props?: any): AdapterContext {
    return {
      container,
      appName: this.name,
      props,
      eventBus: globalEventBus,
      globalState: globalState,
      router: globalRouter,
      createSandbox: () => new ProxySandbox(),
      destroySandbox: (sandbox) => sandbox.destroy(),
      loadResource: (url) => loadScript(url)
    }
  }
}
```

### jQuery 适配器示例

```typescript
class JQueryAdapter extends BaseAdapter {
  name = 'jquery-app'
  version = '1.0.0'
  framework = 'jquery'
  
  private $app: any
  
  protected async doMount(container: HTMLElement, props?: any): Promise<void> {
    // 加载 jQuery
    await this.context!.loadResource('https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js')
    
    // 初始化 jQuery 应用
    this.$app = (window as any).$(container)
    this.$app.html(`
      <div class="jquery-app">
        <h1>jQuery Application</h1>
        <div id="content"></div>
      </div>
    `)
    
    // 绑定事件
    this.$app.find('#content').on('click', this.handleClick.bind(this))
    
    // 监听全局事件
    this.context!.eventBus.on('global:update', this.handleGlobalUpdate.bind(this))
  }
  
  protected async doUnmount(): Promise<void> {
    if (this.$app) {
      // 清理事件监听器
      this.$app.off()
      this.$app.empty()
      this.$app = null
    }
    
    // 取消全局事件监听
    this.context!.eventBus.off('global:update', this.handleGlobalUpdate)
  }
  
  private handleClick(event: Event) {
    console.log('jQuery app clicked')
    this.context!.eventBus.emit('jquery:click', { target: event.target })
  }
  
  private handleGlobalUpdate(data: any) {
    if (this.$app) {
      this.$app.find('#content').text(`Updated: ${JSON.stringify(data)}`)
    }
  }
}
```

## 状态管理

### 状态同步

```typescript
class StatefulAdapter extends BaseAdapter {
  name = 'stateful-app'
  version = '1.0.0'
  framework = 'custom'
  
  private localState: any = {}
  
  protected async afterMount(): Promise<void> {
    // 同步全局状态到本地状态
    this.syncFromGlobalState()
    
    // 监听全局状态变化
    this.context!.globalState.subscribe(this.handleGlobalStateChange.bind(this))
  }
  
  getState(): any {
    return { ...this.localState }
  }
  
  setState(state: any): void {
    this.localState = { ...this.localState, ...state }
    
    // 同步到全局状态
    this.context!.globalState.update(this.name, state)
    
    // 触发重新渲染
    this.render()
  }
  
  private syncFromGlobalState(): void {
    const globalState = this.context!.globalState.get(this.name)
    if (globalState) {
      this.localState = { ...globalState }
    }
  }
  
  private handleGlobalStateChange(key: string, value: any): void {
    if (key === this.name) {
      this.localState = { ...value }
      this.render()
    }
  }
  
  private render(): void {
    // 重新渲染应用
    if (this.container) {
      this.container.innerHTML = this.generateHTML()
    }
  }
}
```

## 事件通信

### 事件处理

```typescript
class EventDrivenAdapter extends BaseAdapter {
  name = 'event-driven-app'
  version = '1.0.0'
  framework = 'custom'
  
  private eventHandlers = new Map<string, Function[]>()
  
  on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, [])
    }
    this.eventHandlers.get(event)!.push(handler)
  }
  
  off(event: string, handler?: Function): void {
    if (!this.eventHandlers.has(event)) return
    
    if (handler) {
      const handlers = this.eventHandlers.get(event)!
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    } else {
      this.eventHandlers.delete(event)
    }
  }
  
  emit(event: string, data?: any): void {
    // 触发本地事件
    if (this.eventHandlers.has(event)) {
      this.eventHandlers.get(event)!.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`Event handler error for ${event}:`, error)
        }
      })
    }
    
    // 触发全局事件
    this.context!.eventBus.emit(`${this.name}:${event}`, data)
  }
  
  protected async afterMount(): Promise<void> {
    // 监听全局事件
    this.context!.eventBus.on('global:broadcast', this.handleGlobalBroadcast.bind(this))
  }
  
  protected async beforeUnmount(): Promise<void> {
    // 清理所有事件监听器
    this.eventHandlers.clear()
    this.context!.eventBus.off('global:broadcast', this.handleGlobalBroadcast)
  }
  
  private handleGlobalBroadcast(data: any): void {
    this.emit('global-message', data)
  }
}
```

## 资源加载

### 动态资源加载

```typescript
class ResourceLoadingAdapter extends BaseAdapter {
  name = 'resource-loading-app'
  version = '1.0.0'
  framework = 'custom'
  
  private loadedResources = new Set<string>()
  
  protected async doMount(container: HTMLElement, props?: any): Promise<void> {
    // 加载必需的资源
    await this.loadRequiredResources()
    
    // 渲染应用
    await this.renderApp(container, props)
  }
  
  private async loadRequiredResources(): Promise<void> {
    const resources = [
      'https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js',
      'https://cdn.jsdelivr.net/npm/axios@0.24.0/dist/axios.min.js',
      '/assets/app-styles.css'
    ]
    
    for (const resource of resources) {
      if (!this.loadedResources.has(resource)) {
        await this.loadResource(resource)
        this.loadedResources.add(resource)
      }
    }
  }
  
  private async loadResource(url: string): Promise<void> {
    if (url.endsWith('.css')) {
      return this.loadStylesheet(url)
    } else if (url.endsWith('.js')) {
      return this.context!.loadResource(url)
    }
  }
  
  private loadStylesheet(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = url
      link.onload = () => resolve()
      link.onerror = reject
      document.head.appendChild(link)
    })
  }
  
  protected async doUnmount(): Promise<void> {
    // 清理动态加载的样式表
    const stylesheets = document.querySelectorAll(`link[href*="${this.name}"]`)
    stylesheets.forEach(link => link.remove())
  }
}
```

## 沙箱集成

### 沙箱适配器

```typescript
class SandboxedAdapter extends BaseAdapter {
  name = 'sandboxed-app'
  version = '1.0.0'
  framework = 'custom'
  
  private sandbox: Sandbox | null = null
  
  protected async beforeMount(): Promise<void> {
    // 创建沙箱
    this.sandbox = this.context!.createSandbox()
    
    // 配置沙箱
    this.sandbox.configure({
      isolation: {
        css: true,
        js: true,
        dom: false
      },
      globals: {
        // 允许访问的全局变量
        console: window.console,
        setTimeout: window.setTimeout,
        clearTimeout: window.clearTimeout
      }
    })
  }
  
  protected async doMount(container: HTMLElement, props?: any): Promise<void> {
    // 在沙箱中执行应用代码
    await this.sandbox!.execute(`
      // 应用代码
      const app = {
        init: function() {
          const div = document.createElement('div')
          div.innerHTML = '<h1>Sandboxed Application</h1>'
          container.appendChild(div)
        },
        
        destroy: function() {
          container.innerHTML = ''
        }
      }
      
      // 导出应用实例
      window.appInstance = app
      app.init()
    `, {
      container,
      props
    })
  }
  
  protected async doUnmount(): Promise<void> {
    if (this.sandbox) {
      // 在沙箱中执行清理代码
      await this.sandbox.execute(`
        if (window.appInstance && window.appInstance.destroy) {
          window.appInstance.destroy()
        }
      `)
      
      // 销毁沙箱
      this.context!.destroySandbox(this.sandbox)
      this.sandbox = null
    }
  }
}
```

## 错误处理

### 错误边界

```typescript
class ErrorHandlingAdapter extends BaseAdapter {
  name = 'error-handling-app'
  version = '1.0.0'
  framework = 'custom'
  
  private errorBoundary: ErrorBoundary | null = null
  
  protected async beforeMount(): Promise<void> {
    // 创建错误边界
    this.errorBoundary = new ErrorBoundary({
      onError: this.handleError.bind(this),
      fallback: this.renderErrorFallback.bind(this)
    })
  }
  
  protected async doMount(container: HTMLElement, props?: any): Promise<void> {
    try {
      // 在错误边界中渲染应用
      await this.errorBoundary!.render(container, () => {
        return this.renderApp(props)
      })
    } catch (error) {
      this.handleError(error)
    }
  }
  
  private handleError(error: Error): void {
    console.error(`Adapter ${this.name} error:`, error)
    
    // 上报错误
    this.context!.eventBus.emit('adapter:error', {
      adapter: this.name,
      error: error.message,
      stack: error.stack
    })
  }
  
  private renderErrorFallback(): string {
    return `
      <div class="error-fallback">
        <h2>应用加载失败</h2>
        <p>应用 ${this.name} 遇到错误，请刷新页面重试。</p>
        <button onclick="location.reload()">刷新页面</button>
      </div>
    `
  }
  
  protected async doUnmount(): Promise<void> {
    if (this.errorBoundary) {
      this.errorBoundary.destroy()
      this.errorBoundary = null
    }
  }
}
```

## 性能优化

### 懒加载适配器

```typescript
class LazyLoadAdapter extends BaseAdapter {
  name = 'lazy-load-app'
  version = '1.0.0'
  framework = 'custom'
  
  private isLoaded = false
  private loadPromise: Promise<void> | null = null
  
  protected async doMount(container: HTMLElement, props?: any): Promise<void> {
    // 显示加载状态
    this.showLoadingState(container)
    
    // 懒加载应用
    await this.lazyLoadApp()
    
    // 渲染应用
    await this.renderApp(container, props)
  }
  
  private async lazyLoadApp(): Promise<void> {
    if (this.isLoaded) return
    
    if (!this.loadPromise) {
      this.loadPromise = this.doLazyLoad()
    }
    
    await this.loadPromise
  }
  
  private async doLazyLoad(): Promise<void> {
    // 动态导入应用模块
    const appModule = await import(/* webpackChunkName: "lazy-app" */ './lazy-app')
    
    // 加载应用依赖
    await this.loadDependencies()
    
    // 初始化应用
    await appModule.initialize()
    
    this.isLoaded = true
  }
  
  private showLoadingState(container: HTMLElement): void {
    container.innerHTML = `
      <div class="loading-state">
        <div class="spinner"></div>
        <p>正在加载应用...</p>
      </div>
    `
  }
}
```

## 测试支持

### 适配器测试

```typescript
import { createAdapterTestContext } from '@micro-core/test-utils'

describe('MyAdapter', () => {
  let adapter: MyAdapter
  let container: HTMLElement
  let context: AdapterContext
  
  beforeEach(() => {
    adapter = new MyAdapter()
    container = document.createElement('div')
    context = createAdapterTestContext({
      container,
      appName: 'test-app',
      props: { test: true }
    })
  })
  
  afterEach(() => {
    if (adapter) {
      adapter.unmount()
    }
  })
  
  it('should mount correctly', async () => {
    await adapter.mount(container, { test: true })
    
    expect(container.children.length).toBeGreaterThan(0)
    expect(container.querySelector('.app-root')).toBeTruthy()
  })
  
  it('should unmount correctly', async () => {
    await adapter.mount(container, { test: true })
    await adapter.unmount()
    
    expect(container.innerHTML).toBe('')
  })
  
  it('should handle state updates', async () => {
    await adapter.mount(container, { test: true })
    
    adapter.setState({ count: 1 })
    
    expect(adapter.getState().count).toBe(1)
  })
})
```

## 最佳实践

### 1. 适配器设计原则

- **框架无关**：适配器应该封装框架特定的逻辑
- **生命周期管理**：正确处理挂载和卸载
- **错误处理**：提供完善的错误处理机制
- **性能优化**：支持懒加载和资源优化

### 2. 状态管理

- 使用全局状态管理器进行跨应用状态同步
- 本地状态与全局状态保持一致
- 避免状态泄漏和内存泄漏

### 3. 事件通信

- 使用标准化的事件接口
- 正确清理事件监听器
- 处理事件传播和冒泡

## 参考资料

- [适配器开发指南](/guide/adapters)
- [框架集成示例](/examples/framework-integration)
- [状态管理 API](/api/state-management)
- [事件系统 API](/api/event-system)
