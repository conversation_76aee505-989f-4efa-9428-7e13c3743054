/**
 * @fileoverview Sidecar 边车模式主入口
 * @description 提供一行代码接入微前端的边车模式支持
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import { type AppConfig, type SidecarConfig } from '@micro-core/shared';
/**
 * Sidecar 边车模式核心类
 * 提供应用隔离、资源代理、通信桥接等功能
 */
export declare class SidecarCore extends EventEmitter {
    private config;
    private isolationContainer;
    private resourceProxy;
    private messageBridge;
    private isInitialized;
    constructor(config: SidecarConfig);
    /**
     * 初始化 Sidecar
     */
    initialize(): Promise<void>;
    /**
     * 启动应用
     */
    start(appConfig: AppConfig): Promise<void>;
    /**
     * 停止应用
     */
    stop(appName: string): Promise<void>;
    /**
     * 销毁 Sidecar
     */
    destroy(): Promise<void>;
    /**
     * 获取应用状态
     */
    getAppStatus(appName: string): string | null;
    /**
     * 发送消息到应用
     */
    sendMessage(appName: string, message: any): Promise<void>;
    /**
     * 验证配置
     */
    private validateConfig;
}
/**
 * 一行代码接入函数
 * 提供最简单的 Sidecar 使用方式
 */
export declare function useSidecar(config: SidecarConfig): Promise<SidecarCore>;
/**
 * 创建 Sidecar 实例
 */
export declare function createSidecar(config: SidecarConfig): SidecarCore;
export type { BridgeConfig, IsolationConfig, ProxyConfig, SidecarConfig } from '@micro-core/shared';
declare const _default: {
    SidecarCore: typeof SidecarCore;
    useSidecar: typeof useSidecar;
    createSidecar: typeof createSidecar;
};
export default _default;
//# sourceMappingURL=index.d.ts.map