/**
 * @fileoverview Vue 2 应用测试环境设置
 * <AUTHOR> <<EMAIL>>
 */

import { afterEach, vi } from 'vitest'

// 每个测试后清理
afterEach(() => {
    // Vue 2 测试清理
})

// 模拟全局对象
Object.defineProperty(window, '__POWERED_BY_MICRO_CORE__', {
    writable: true,
    value: true
})

Object.defineProperty(window, '__MICRO_APP_NAME__', {
    writable: true,
    value: 'sub-app-vue2'
})

// 模拟 matchMedia
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
    })),
})

// 模拟 ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
}))

// 模拟 IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
}))

// 设置测试环境变量
process.env.NODE_ENV = 'test'