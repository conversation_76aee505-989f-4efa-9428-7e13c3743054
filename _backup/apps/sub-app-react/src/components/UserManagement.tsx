import React, { useEffect, useState } from 'react'

interface User {
    id: number
    name: string
    email: string
    role: string
    status: 'active' | 'inactive'
    createdAt: string
}

const UserManagement: React.FC = () => {
    const [users, setUsers] = useState<User[]>([])
    const [loading, setLoading] = useState(true)
    const [searchTerm, setSearchTerm] = useState('')
    const [selectedRole, setSelectedRole] = useState('all')

    useEffect(() => {
        // 模拟用户数据加载
        const loadUsers = async () => {
            setLoading(true)

            // 模拟API调用延迟
            await new Promise(resolve => setTimeout(resolve, 1000))

            const mockUsers: User[] = [
                {
                    id: 1,
                    name: '张三',
                    email: '<EMAIL>',
                    role: 'admin',
                    status: 'active',
                    createdAt: '2024-01-15'
                },
                {
                    id: 2,
                    name: '李四',
                    email: '<EMAIL>',
                    role: 'user',
                    status: 'active',
                    createdAt: '2024-01-20'
                },
                {
                    id: 3,
                    name: '王五',
                    email: '<EMAIL>',
                    role: 'editor',
                    status: 'inactive',
                    createdAt: '2024-01-25'
                },
                {
                    id: 4,
                    name: '赵六',
                    email: '<EMAIL>',
                    role: 'user',
                    status: 'active',
                    createdAt: '2024-02-01'
                }
            ]

            setUsers(mockUsers)
            setLoading(false)
        }

        loadUsers()
    }, [])

    const filteredUsers = users.filter(user => {
        const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.email.toLowerCase().includes(searchTerm.toLowerCase())
        const matchesRole = selectedRole === 'all' || user.role === selectedRole
        return matchesSearch && matchesRole
    })

    const handleStatusToggle = (userId: number) => {
        setUsers(prevUsers =>
            prevUsers.map(user =>
                user.id === userId
                    ? { ...user, status: user.status === 'active' ? 'inactive' : 'active' }
                    : user
            )
        )
    }

    const handleDeleteUser = (userId: number) => {
        if (window.confirm('确定要删除这个用户吗？')) {
            setUsers(prevUsers => prevUsers.filter(user => user.id !== userId))
        }
    }

    if (loading) {
        return (
            <div className="loading-container">
                <div className="loading-spinner"></div>
                <p>加载用户数据中...</p>
            </div>
        )
    }

    return (
        <div className="user-management">
            <div className="page-header">
                <h3>用户管理</h3>
                <button className="btn btn-primary">添加用户</button>
            </div>

            <div className="filters">
                <div className="search-box">
                    <input
                        type="text"
                        placeholder="搜索用户名或邮箱..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="search-input"
                    />
                </div>

                <div className="role-filter">
                    <select
                        value={selectedRole}
                        onChange={(e) => setSelectedRole(e.target.value)}
                        className="role-select"
                    >
                        <option value="all">所有角色</option>
                        <option value="admin">管理员</option>
                        <option value="editor">编辑者</option>
                        <option value="user">普通用户</option>
                    </select>
                </div>
            </div>

            <div className="user-table">
                <table>
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>角色</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredUsers.map(user => (
                            <tr key={user.id}>
                                <td>{user.name}</td>
                                <td>{user.email}</td>
                                <td>
                                    <span className={`role-badge role-${user.role}`}>
                                        {user.role === 'admin' ? '管理员' :
                                            user.role === 'editor' ? '编辑者' : '普通用户'}
                                    </span>
                                </td>
                                <td>
                                    <span className={`status-badge status-${user.status}`}>
                                        {user.status === 'active' ? '活跃' : '禁用'}
                                    </span>
                                </td>
                                <td>{user.createdAt}</td>
                                <td>
                                    <div className="action-buttons">
                                        <button
                                            onClick={() => handleStatusToggle(user.id)}
                                            className={`btn btn-sm ${user.status === 'active' ? 'btn-warning' : 'btn-success'}`}
                                        >
                                            {user.status === 'active' ? '禁用' : '启用'}
                                        </button>
                                        <button
                                            onClick={() => handleDeleteUser(user.id)}
                                            className="btn btn-sm btn-danger"
                                        >
                                            删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>

                {filteredUsers.length === 0 && (
                    <div className="no-data">
                        <p>没有找到匹配的用户</p>
                    </div>
                )}
            </div>
        </div>
    )
}

export default UserManagement