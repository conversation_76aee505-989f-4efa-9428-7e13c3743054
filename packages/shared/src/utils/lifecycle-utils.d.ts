/**
 * 共享工具包 - 公共基础设施层
 *
 * @description 提供微前端项目的公共工具、类型定义、常量和错误处理机制
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import type { AppInstance, LifecycleHooks } from '../types';
/**
 * 生命周期钩子执行器
 */
export declare class LifecycleExecutor {
    private hooks;
    /**
     * 注册生命周期钩子
     * @param hooks 钩子函数集合
     */
    registerHooks(hooks: LifecycleHooks): void;
    /**
     * 执行 beforeLoad 钩子
     * @param app 应用实例
     */
    executeBeforeLoad(app: AppInstance): Promise<void>;
    /**
     * 执行 afterLoad 钩子
     * @param app 应用实例
     */
    executeAfterLoad(app: AppInstance): Promise<void>;
    /**
     * 执行 beforeMount 钩子
     * @param app 应用实例
     */
    executeBeforeMount(app: AppInstance): Promise<void>;
    /**
     * 执行 afterMount 钩子
     * @param app 应用实例
     */
    executeAfterMount(app: AppInstance): Promise<void>;
    /**
     * 执行 beforeUnmount 钩子
     * @param app 应用实例
     */
    executeBeforeUnmount(app: AppInstance): Promise<void>;
    /**
     * 执行 afterUnmount 钩子
     * @param app 应用实例
     */
    executeAfterUnmount(app: AppInstance): Promise<void>;
    /**
     * 执行 onError 钩子
     * @param error 错误对象
     * @param app 应用实例
     */
    executeOnError(error: Error, app: AppInstance): Promise<void>;
}
/**
 * 创建生命周期执行器
 * @param hooks 初始钩子函数
 */
export declare function createLifecycleExecutor(hooks?: LifecycleHooks): LifecycleExecutor;
/**
 * 检查应用状态是否可以进行指定操作
 * @param currentStatus 当前状态
 * @param targetStatus 目标状态
 */
export declare function canTransitionTo(currentStatus: string, targetStatus: string): boolean;
/**
 * 获取状态转换路径
 * @param currentStatus 当前状态
 * @param targetStatus 目标状态
 */
export declare function getTransitionPath(currentStatus: string, targetStatus: string): string[];
/**
 * 状态转换验证器
 */
export declare class StateTransitionValidator {
    private validTransitions;
    constructor();
    /**
     * 初始化有效的状态转换
     */
    private initializeValidTransitions;
    /**
     * 验证状态转换是否有效
     * @param from 源状态
     * @param to 目标状态
     */
    isValidTransition(from: string, to: string): boolean;
    /**
     * 获取状态的所有有效转换目标
     * @param status 当前状态
     */
    getValidTransitions(status: string): string[];
    /**
     * 添加自定义状态转换
     * @param from 源状态
     * @param to 目标状态
     */
    addTransition(from: string, to: string): void;
    /**
     * 移除状态转换
     * @param from 源状态
     * @param to 目标状态
     */
    removeTransition(from: string, to: string): void;
}
/**
 * 创建状态转换验证器
 */
export declare function createStateTransitionValidator(): StateTransitionValidator;
/**
 * 生命周期状态管理器
 */
export declare class LifecycleStateManager {
    private currentStatus;
    private validator;
    private listeners;
    constructor();
    /**
     * 获取当前状态
     */
    getCurrentStatus(): string;
    /**
     * 设置状态
     * @param newStatus 新状态
     * @param force 是否强制设置（跳过验证）
     */
    setStatus(newStatus: string, force?: boolean): boolean;
    /**
     * 检查是否可以转换到指定状态
     * @param targetStatus 目标状态
     */
    canTransitionTo(targetStatus: string): boolean;
    /**
     * 获取可转换的状态列表
     */
    getValidTransitions(): string[];
    /**
     * 添加状态变化监听器
     * @param listener 监听器函数
     */
    onStatusChange(listener: (oldStatus: string, newStatus: string) => void): () => void;
    /**
     * 重置状态
     */
    reset(): void;
    /**
     * 获取状态验证器
     */
    getValidator(): StateTransitionValidator;
}
/**
 * 创建生命周期状态管理器
 */
export declare function createLifecycleStateManager(): LifecycleStateManager;
/**
 * 生命周期工具函数集合
 */
export declare const lifecycleUtils: {
    createExecutor: typeof createLifecycleExecutor;
    createValidator: typeof createStateTransitionValidator;
    createStateManager: typeof createLifecycleStateManager;
    canTransitionTo: typeof canTransitionTo;
    getTransitionPath: typeof getTransitionPath;
};
//# sourceMappingURL=lifecycle-utils.d.ts.map