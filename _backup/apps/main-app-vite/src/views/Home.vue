<template>
  <div class="home">
    <div class="hero">
      <h2>欢迎使用 Micro-Core 微前端架构</h2>
      <p>下一代微前端解决方案，高性能、高扩展性、高可靠性</p>
    </div>

    <div class="features">
      <div class="feature-card">
        <h3>🚀 高性能</h3>
        <p>微内核设计，核心库小于 15KB</p>
      </div>
      
      <div class="feature-card">
        <h3>🔧 插件化</h3>
        <p>100% 插件化设计，功能按需组合</p>
      </div>
      
      <div class="feature-card">
        <h3>🛡️ 多沙箱</h3>
        <p>6种沙箱策略，支持灵活组合</p>
      </div>
      
      <div class="feature-card">
        <h3>🌐 跨框架</h3>
        <p>全面支持主流前端框架</p>
      </div>
    </div>

    <div class="quick-start">
      <h3>快速开始</h3>
      <div class="code-block">
        <pre><code>npm install @micro-core/core
import { MicroCoreKernel } from '@micro-core/core'

const kernel = new MicroCoreKernel()
kernel.registerApplication({
  name: 'my-app',
  entry: 'http://localhost:3000',
  container: '#app'
})
kernel.start()</code></pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 首页逻辑
</script>

<style scoped>
.home {
  max-width: 1200px;
  margin: 0 auto;
}

.hero {
  text-align: center;
  padding: 4rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  margin-bottom: 3rem;
}

.hero h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.hero p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.feature-card h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.quick-start {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
}

.quick-start h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.code-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 4px;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  font-family: 'Courier New', monospace;
}
</style>