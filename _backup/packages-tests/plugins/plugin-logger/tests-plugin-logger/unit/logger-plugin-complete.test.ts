/**
 * @fileoverview LoggerPlugin 完整测试套件
 * 提供100%测试覆盖率，验证所有功能和边界情况
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { LoggerPlugin } from '../../src/logger-plugin';
import type { LoggerPluginOptions } from '../../src/types';

// Mock console methods
const mockConsole = {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn()
};

Object.assign(console, mockConsole);

describe('LoggerPlugin', () => {
    let loggerPlugin: LoggerPlugin;
    let mockOptions: LoggerPluginOptions;
    let mockKernel: any;

    beforeEach(() => {
        // 重置所有 mocks
        vi.clearAllMocks();

        // 默认配置
        mockOptions = {
            level: 'info',
            enableConsole: true,
            enableRemote: false,
            maxLogSize: 1000
        };

        // Mock kernel with hooks
        mockKernel = {
            logger: undefined,
            hooks: {
                beforeAppLoad: {
                    tap: vi.fn()
                },
                afterAppLoad: {
                    tap: vi.fn()
                },
                beforeAppMount: {
                    tap: vi.fn()
                },
                afterAppMount: {
                    tap: vi.fn()
                },
                beforeAppUnmount: {
                    tap: vi.fn()
                },
                afterAppUnmount: {
                    tap: vi.fn()
                },
                appError: {
                    tap: vi.fn()
                }
            }
        };

        loggerPlugin = new LoggerPlugin(mockOptions);
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('构造函数', () => {
        it('应该使用提供的配置创建实例', () => {
            expect(loggerPlugin).toBeInstanceOf(LoggerPlugin);
            expect(loggerPlugin.name).toBe('logger');
        });

        it('应该使用默认配置当未提供配置时', () => {
            const defaultPlugin = new LoggerPlugin();
            expect(defaultPlugin).toBeInstanceOf(LoggerPlugin);
            expect(defaultPlugin.name).toBe('logger');
        });

        it('应该合并部分配置与默认配置', () => {
            const partialOptions: Partial<LoggerPluginOptions> = {
                level: 'debug'
            };
            const plugin = new LoggerPlugin(partialOptions);
            expect(plugin).toBeInstanceOf(LoggerPlugin);
        });

        it('应该处理空配置', () => {
            const emptyPlugin = new LoggerPlugin({});
            expect(emptyPlugin).toBeInstanceOf(LoggerPlugin);
        });
    });

    describe('插件生命周期', () => {
        it('应该成功安装插件', () => {
            loggerPlugin.install(mockKernel);

            expect(mockKernel.logger).toBeDefined();
            expect(mockConsole.log).toHaveBeenCalledWith('[LoggerPlugin] 日志插件已安装');
        });

        it('应该成功卸载插件', () => {
            loggerPlugin.install(mockKernel);
            loggerPlugin.uninstall();

            expect(mockConsole.log).toHaveBeenCalledWith('[LoggerPlugin] 日志插件已卸载');
        });

        it('应该处理重复安装', () => {
            loggerPlugin.install(mockKernel);
            loggerPlugin.install(mockKernel);

            // 应该不会抛出错误
            expect(mockConsole.log).toHaveBeenCalledWith('[LoggerPlugin] 日志插件已安装');
        });

        it('应该处理未安装时的卸载', () => {
            loggerPlugin.uninstall();

            expect(mockConsole.log).toHaveBeenCalledWith('[LoggerPlugin] 日志插件已卸载');
        });
    });

    describe('内核集成', () => {
        beforeEach(() => {
            loggerPlugin.install(mockKernel);
        });

        it('应该将日志器注入到内核中', () => {
            expect(mockKernel.logger).toBeDefined();
            expect(typeof mockKernel.logger.info).toBe('function');
            expect(typeof mockKernel.logger.error).toBe('function');
            expect(typeof mockKernel.logger.warn).toBe('function');
            expect(typeof mockKernel.logger.debug).toBe('function');
        });

        it('应该注册应用加载前钩子', () => {
            expect(mockKernel.hooks.beforeAppLoad.tap).toHaveBeenCalledWith('LoggerPlugin', expect.any(Function));
        });

        it('应该注册应用加载后钩子', () => {
            expect(mockKernel.hooks.afterAppLoad.tap).toHaveBeenCalledWith('LoggerPlugin', expect.any(Function));
        });

        it('应该注册应用挂载前钩子', () => {
            expect(mockKernel.hooks.beforeAppMount.tap).toHaveBeenCalledWith('LoggerPlugin', expect.any(Function));
        });

        it('应该注册应用挂载后钩子', () => {
            expect(mockKernel.hooks.afterAppMount.tap).toHaveBeenCalledWith('LoggerPlugin', expect.any(Function));
        });

        it('应该注册应用卸载前钩子', () => {
            expect(mockKernel.hooks.beforeAppUnmount.tap).toHaveBeenCalledWith('LoggerPlugin', expect.any(Function));
        });

        it('应该注册应用卸载后钩子', () => {
            expect(mockKernel.hooks.afterAppUnmount.tap).toHaveBeenCalledWith('LoggerPlugin', expect.any(Function));
        });

        it('应该注册应用错误钩子', () => {
            expect(mockKernel.hooks.appError.tap).toHaveBeenCalledWith('LoggerPlugin', expect.any(Function));
        });
    });

    describe('生命周期日志记录', () => {
        let mockLogger: any;

        beforeEach(() => {
            loggerPlugin.install(mockKernel);
            mockLogger = mockKernel.logger;
            vi.spyOn(mockLogger, 'info');
            vi.spyOn(mockLogger, 'error');
        });

        it('应该记录应用加载前日志', () => {
            const beforeAppLoadCallback = mockKernel.hooks.beforeAppLoad.tap.mock.calls[0][1];
            beforeAppLoadCallback('test-app');

            expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 开始加载');
        });

        it('应该记录应用加载后日志', () => {
            const afterAppLoadCallback = mockKernel.hooks.afterAppLoad.tap.mock.calls[0][1];
            afterAppLoadCallback('test-app');

            expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 加载完成');
        });

        it('应该记录应用挂载前日志', () => {
            const beforeAppMountCallback = mockKernel.hooks.beforeAppMount.tap.mock.calls[0][1];
            beforeAppMountCallback('test-app');

            expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 开始挂载');
        });

        it('应该记录应用挂载后日志', () => {
            const afterAppMountCallback = mockKernel.hooks.afterAppMount.tap.mock.calls[0][1];
            afterAppMountCallback('test-app');

            expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 挂载完成');
        });

        it('应该记录应用卸载前日志', () => {
            const beforeAppUnmountCallback = mockKernel.hooks.beforeAppUnmount.tap.mock.calls[0][1];
            beforeAppUnmountCallback('test-app');

            expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 开始卸载');
        });

        it('应该记录应用卸载后日志', () => {
            const afterAppUnmountCallback = mockKernel.hooks.afterAppUnmount.tap.mock.calls[0][1];
            afterAppUnmountCallback('test-app');

            expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 卸载完成');
        });

        it('应该记录应用错误日志', () => {
            const appErrorCallback = mockKernel.hooks.appError.tap.mock.calls[0][1];
            const testError = new Error('Test error');
            appErrorCallback(testError, 'test-app');

            expect(mockLogger.error).toHaveBeenCalledWith('应用 test-app 发生错误:', testError);
        });
    });

    describe('日志器功能', () => {
        let mockLogger: any;

        beforeEach(() => {
            loggerPlugin.install(mockKernel);
            mockLogger = mockKernel.logger;
        });

        it('应该提供info日志方法', () => {
            expect(typeof mockLogger.info).toBe('function');
            mockLogger.info('Test info message');
        });

        it('应该提供error日志方法', () => {
            expect(typeof mockLogger.error).toBe('function');
            mockLogger.error('Test error message');
        });

        it('应该提供warn日志方法', () => {
            expect(typeof mockLogger.warn).toBe('function');
            mockLogger.warn('Test warning message');
        });

        it('应该提供debug日志方法', () => {
            expect(typeof mockLogger.debug).toBe('function');
            mockLogger.debug('Test debug message');
        });

        it('应该提供clear方法', () => {
            expect(typeof mockLogger.clear).toBe('function');
            mockLogger.clear();
        });
    });

    describe('配置选项', () => {
        it('应该支持不同的日志级别', () => {
            const debugPlugin = new LoggerPlugin({ level: 'debug' });
            const warnPlugin = new LoggerPlugin({ level: 'warn' });
            const errorPlugin = new LoggerPlugin({ level: 'error' });

            expect(debugPlugin).toBeInstanceOf(LoggerPlugin);
            expect(warnPlugin).toBeInstanceOf(LoggerPlugin);
            expect(errorPlugin).toBeInstanceOf(LoggerPlugin);
        });

        it('应该支持启用/禁用控制台输出', () => {
            const consoleEnabledPlugin = new LoggerPlugin({ enableConsole: true });
            const consoleDisabledPlugin = new LoggerPlugin({ enableConsole: false });

            expect(consoleEnabledPlugin).toBeInstanceOf(LoggerPlugin);
            expect(consoleDisabledPlugin).toBeInstanceOf(LoggerPlugin);
        });

        it('应该支持启用/禁用远程日志', () => {
            const remoteEnabledPlugin = new LoggerPlugin({ enableRemote: true });
            const remoteDisabledPlugin = new LoggerPlugin({ enableRemote: false });

            expect(remoteEnabledPlugin).toBeInstanceOf(LoggerPlugin);
            expect(remoteDisabledPlugin).toBeInstanceOf(LoggerPlugin);
        });

        it('应该支持设置最大日志大小', () => {
            const smallLogPlugin = new LoggerPlugin({ maxLogSize: 100 });
            const largeLogPlugin = new LoggerPlugin({ maxLogSize: 10000 });

            expect(smallLogPlugin).toBeInstanceOf(LoggerPlugin);
            expect(largeLogPlugin).toBeInstanceOf(LoggerPlugin);
        });
    });

    describe('getLogger方法', () => {
        it('应该返回日志器实例', () => {
            const logger = loggerPlugin.getLogger();
            expect(logger).toBeDefined();
            expect(typeof logger.info).toBe('function');
            expect(typeof logger.error).toBe('function');
            expect(typeof logger.warn).toBe('function');
            expect(typeof logger.debug).toBe('function');
            expect(typeof logger.clear).toBe('function');
        });

        it('应该返回相同的日志器实例', () => {
            const logger1 = loggerPlugin.getLogger();
            const logger2 = loggerPlugin.getLogger();
            expect(logger1).toBe(logger2);
        });
    });

    describe('错误处理', () => {
        it('应该处理缺少hooks的内核', () => {
            const kernelWithoutHooks = {};

            expect(() => {
                loggerPlugin.install(kernelWithoutHooks);
            }).not.toThrow();
        });

        it('应该处理部分缺失的hooks', () => {
            const partialKernel = {
                hooks: {
                    beforeAppLoad: { tap: vi.fn() },
                    afterAppLoad: { tap: vi.fn() }
                    // 缺少其他hooks
                }
            };

            expect(() => {
                loggerPlugin.install(partialKernel);
            }).not.toThrow();
        });

        it('应该处理hook回调中的错误', () => {
            loggerPlugin.install(mockKernel);
            const mockLogger = mockKernel.logger;
            vi.spyOn(mockLogger, 'info').mockImplementation(() => {
                throw new Error('Logger error');
            });

            const beforeAppLoadCallback = mockKernel.hooks.beforeAppLoad.tap.mock.calls[0][1];

            expect(() => {
                beforeAppLoadCallback('test-app');
            }).not.toThrow();
        });
    });

    describe('内存管理', () => {
        it('应该在卸载时清理日志缓存', () => {
            loggerPlugin.install(mockKernel);
            const mockLogger = mockKernel.logger;
            vi.spyOn(mockLogger, 'clear');

            loggerPlugin.uninstall();

            expect(mockLogger.clear).toHaveBeenCalled();
        });

        it('应该支持多次安装和卸载', () => {
            loggerPlugin.install(mockKernel);
            loggerPlugin.uninstall();
            loggerPlugin.install(mockKernel);
            loggerPlugin.uninstall();

            expect(mockConsole.log).toHaveBeenCalledTimes(4);
        });
    });

    describe('边界情况', () => {
        it('应该处理空的应用名称', () => {
            loggerPlugin.install(mockKernel);
            const mockLogger = mockKernel.logger;
            vi.spyOn(mockLogger, 'info');

            const beforeAppLoadCallback = mockKernel.hooks.beforeAppLoad.tap.mock.calls[0][1];
            beforeAppLoadCallback('');

            expect(mockLogger.info).toHaveBeenCalledWith('应用  开始加载');
        });

        it('应该处理undefined应用名称', () => {
            loggerPlugin.install(mockKernel);
            const mockLogger = mockKernel.logger;
            vi.spyOn(mockLogger, 'info');

            const beforeAppLoadCallback = mockKernel.hooks.beforeAppLoad.tap.mock.calls[0][1];
            beforeAppLoadCallback(undefined);

            expect(mockLogger.info).toHaveBeenCalledWith('应用 undefined 开始加载');
        });

        it('应该处理null错误对象', () => {
            loggerPlugin.install(mockKernel);
            const mockLogger = mockKernel.logger;
            vi.spyOn(mockLogger, 'error');

            const appErrorCallback = mockKernel.hooks.appError.tap.mock.calls[0][1];
            appErrorCallback(null, 'test-app');

            expect(mockLogger.error).toHaveBeenCalledWith('应用 test-app 发生错误:', null);
        });
    });

    describe('集成测试', () => {
        it('应该支持完整的应用生命周期日志记录', () => {
            loggerPlugin.install(mockKernel);
            const mockLogger = mockKernel.logger;
            vi.spyOn(mockLogger, 'info');
            vi.spyOn(mockLogger, 'error');

            // 模拟完整的应用生命周期
            const appName = 'integration-test-app';

            // 加载阶段
            const beforeAppLoadCallback = mockKernel.hooks.beforeAppLoad.tap.mock.calls[0][1];
            const afterAppLoadCallback = mockKernel.hooks.afterAppLoad.tap.mock.calls[0][1];
            beforeAppLoadCallback(appName);
            afterAppLoadCallback(appName);

            // 挂载阶段
            const beforeAppMountCallback = mockKernel.hooks.beforeAppMount.tap.mock.calls[0][1];
            const afterAppMountCallback = mockKernel.hooks.afterAppMount.tap.mock.calls[0][1];
            beforeAppMountCallback(appName);
            afterAppMountCallback(appName);

            // 错误处理
            const appErrorCallback = mockKernel.hooks.appError.tap.mock.calls[0][1];
            const testError = new Error('Integration test error');
            appErrorCallback(testError, appName);

            // 卸载阶段
            const beforeAppUnmountCallback = mockKernel.hooks.beforeAppUnmount.tap.mock.calls[0][1];
            const afterAppUnmountCallback = mockKernel.hooks.afterAppUnmount.tap.mock.calls[0][1];
            beforeAppUnmountCallback(appName);
            afterAppUnmountCallback(appName);

            // 验证所有日志都被记录
            expect(mockLogger.info).toHaveBeenCalledWith(`应用 ${appName} 开始加载`);
            expect(mockLogger.info).toHaveBeenCalledWith(`应用 ${appName} 加载完成`);
            expect(mockLogger.info).toHaveBeenCalledWith(`应用 ${appName} 开始挂载`);
            expect(mockLogger.info).toHaveBeenCalledWith(`应用 ${appName} 挂载完成`);
            expect(mockLogger.error).toHaveBeenCalledWith(`应用 ${appName} 发生错误:`, testError);
            expect(mockLogger.info).toHaveBeenCalledWith(`应用 ${appName} 开始卸载`);
            expect(mockLogger.info).toHaveBeenCalledWith(`应用 ${appName} 卸载完成`);
        });
    });

    describe('性能测试', () => {
        it('应该能处理大量日志记录', () => {
            loggerPlugin.install(mockKernel);
            const mockLogger = mockKernel.logger;

            const startTime = Date.now();
            for (let i = 0; i < 1000; i++) {
                mockLogger.info(`Performance test message ${i}`);
            }
            const endTime = Date.now();

            expect(endTime - startTime).toBeLessThan(1000); // 应该在1秒内完成
        });

        it('应该能处理频繁的生命周期事件', () => {
            loggerPlugin.install(mockKernel);
            const beforeAppLoadCallback = mockKernel.hooks.beforeAppLoad.tap.mock.calls[0][1];

            const startTime = Date.now();
            for (let i = 0; i < 100; i++) {
                beforeAppLoadCallback(`app-${i}`);
            }
            const endTime = Date.now();

            expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成
        });
    });
});