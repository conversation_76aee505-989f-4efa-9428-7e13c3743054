/**
 * HTML 适配器工具函数测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
    HtmlMicroAppIntegration,
    createDefaultHtmlConfig,
    createHtmlAdapter,
    createHtmlContainer,
    createHtmlErrorInfo,
    extractHtmlContent,
    formatHtmlError,
    isHtmlApp,
    isHtmlContent,
    isHtmlContentEnhanced,
    isHtmlEntry,
    mergeHtmlConfigs,
    validateHtmlConfig
} from '../src/utils';

// Mock shared utils
vi.mock('@micro-core/shared/utils', () => ({
    mergeConfigs: vi.fn((base, override) => ({ ...base, ...override })),
    createEnhancedContainer: vi.fn((name, framework, parent, options) => {
        const div = document.createElement('div');
        div.id = `micro-app-${name}`;
        div.className = options?.className || '';
        return div;
    }),
    cleanupContainer: vi.fn(),
    formatAdapterError: vi.fn((error, framework, operation, appName) =>
        new Error(`${framework} ${operation} error in ${appName}: ${error.message}`)
    ),
    createAdapterErrorInfo: vi.fn((error, framework, context) => ({
        framework,
        error: error.message,
        context
    })),
    isObject: vi.fn((value) => value !== null && typeof value === 'object'),
    isString: vi.fn((value) => typeof value === 'string')
}));

// Mock fetch
global.fetch = vi.fn();

describe('HTML 适配器工具函数', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        // 设置 DOM 环境
        Object.defineProperty(global, 'document', {
            value: {
                createElement: vi.fn((tag) => ({
                    tagName: tag.toUpperCase(),
                    innerHTML: '',
                    id: '',
                    className: '',
                    setAttribute: vi.fn(),
                    getAttribute: vi.fn(),
                    appendChild: vi.fn(),
                    removeChild: vi.fn(),
                    querySelectorAll: vi.fn(() => []),
                    parentNode: null
                })),
                querySelectorAll: vi.fn(() => [])
            },
            writable: true
        });
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('createHtmlAdapter', () => {
        it('应该创建 HTML 适配器', () => {
            const config = {
                name: 'test-app',
                html: '<div>Hello World</div>'
            };

            const adapter = createHtmlAdapter(config);

            expect(adapter).toHaveProperty('name', 'test-app');
            expect(adapter).toHaveProperty('config');
            expect(adapter).toHaveProperty('mount');
            expect(adapter).toHaveProperty('unmount');
            expect(typeof adapter.mount).toBe('function');
            expect(typeof adapter.unmount).toBe('function');
        });

        it('应该使用默认配置', () => {
            const config = { name: 'test-app', html: '<div>Test</div>' };
            const adapter = createHtmlAdapter(config);

            expect(adapter.config).toMatchObject({
                name: 'test-app',
                sandbox: false,
                allowScripts: true,
                allowStyles: true
            });
        });
    });

    describe('isHtmlApp', () => {
        it('应该识别 HTML 应用', () => {
            expect(isHtmlApp({ html: '<div>test</div>' })).toBe(true);
            expect(isHtmlApp({ template: '<div>test</div>' })).toBe(true);
            expect(isHtmlApp({ htmlFile: 'index.html' })).toBe(true);
            expect(isHtmlApp({ templateFile: 'template.html' })).toBe(true);
            expect(isHtmlApp({ htmlConfig: {} })).toBe(true);
        });

        it('应该拒绝非 HTML 应用', () => {
            expect(isHtmlApp(null)).toBe(false);
            expect(isHtmlApp(undefined)).toBe(false);
            expect(isHtmlApp('string')).toBe(false);
            expect(isHtmlApp({})).toBe(false);
            expect(isHtmlApp({ randomProp: 'value' })).toBe(false);
        });
    });

    describe('isHtmlEntry', () => {
        it('应该识别 HTML 入口', () => {
            expect(isHtmlEntry({ html: '<div>test</div>' })).toBe(true);
            expect(isHtmlEntry({ template: '<div>test</div>' })).toBe(true);
            expect(isHtmlEntry({ htmlFile: 'index.html' })).toBe(true);
            expect(isHtmlEntry({ templateFile: 'template.html' })).toBe(true);
            expect(isHtmlEntry({ htmlConfig: {} })).toBe(true);
        });

        it('应该拒绝非 HTML 入口', () => {
            expect(isHtmlEntry(null)).toBe(false);
            expect(isHtmlEntry({})).toBe(false);
            expect(isHtmlEntry({ randomExport: {} })).toBe(false);
        });
    });

    describe('validateHtmlConfig', () => {
        it('应该验证有效配置', () => {
            const config = {
                name: 'test-app',
                html: '<div>Hello World</div>',
                sandbox: false
            };

            const errors = validateHtmlConfig(config);
            expect(errors).toHaveLength(0);
        });

        it('应该检测配置错误', () => {
            const config = {
                name: '',
                sandbox: 'invalid' as any
            };

            const errors = validateHtmlConfig(config);
            expect(errors.length).toBeGreaterThan(0);
            expect(errors).toContain('配置中缺少有效的应用名称');
            expect(errors).toContain('配置中缺少 HTML 内容或文件路径');
        });

        it('应该检测冲突配置', () => {
            const config = {
                name: 'test-app',
                html: '<div>test</div>',
                htmlFile: 'index.html'
            };

            const errors = validateHtmlConfig(config);
            expect(errors).toContain('不能同时指定 HTML 内容和文件路径');
        });
    });

    describe('createDefaultHtmlConfig', () => {
        it('应该创建默认配置', () => {
            const config = createDefaultHtmlConfig({
                name: 'test-app',
                html: '<div>test</div>'
            });

            expect(config).toMatchObject({
                name: 'test-app',
                html: '<div>test</div>',
                sandbox: false,
                allowScripts: true,
                allowStyles: true,
                container: {
                    className: 'html-app-container'
                }
            });
        });

        it('应该合并用户配置', () => {
            const userConfig = {
                name: 'custom-app',
                html: '<div>custom</div>',
                sandbox: true
            };

            const config = createDefaultHtmlConfig(userConfig);
            expect(config.sandbox).toBe(true);
        });
    });

    describe('extractHtmlContent', () => {
        it('应该提取首选内容', () => {
            const moduleExports = {
                preferredTemplate: '<div>Preferred</div>',
                otherTemplate: '<div>Other</div>'
            };

            const content = extractHtmlContent(moduleExports, 'preferredTemplate');
            expect(content).toBe('<div>Preferred</div>');
        });

        it('应该提取默认导出', () => {
            const defaultContent = '<div>Default</div>';
            const moduleExports = {
                default: defaultContent,
                otherTemplate: '<div>Other</div>'
            };

            const content = extractHtmlContent(moduleExports);
            expect(content).toBe(defaultContent);
        });

        it('应该提取命名导出', () => {
            const moduleExports = {
                template1: '<div><span>Simple</span></div>',
                template2: '<div><p>Complex</p><span>More tags</span></div>'
            };

            const content = extractHtmlContent(moduleExports);
            expect(content).toBe('<div><p>Complex</p><span>More tags</span></div>'); // 选择标签更多的
        });

        it('应该抛出错误当没有找到内容时', () => {
            expect(() => extractHtmlContent({})).toThrow('未找到有效的 HTML 内容');
            expect(() => extractHtmlContent(null)).toThrow('模块导出必须是对象');
        });
    });

    describe('isHtmlContent', () => {
        it('应该识别 HTML 内容', () => {
            expect(isHtmlContent('<div>test</div>')).toBe(true);
            expect(isHtmlContent('<p>paragraph</p>')).toBe(true);
            expect(isHtmlContent('<span>span</span>')).toBe(true);
            expect(isHtmlContent('<!DOCTYPE html><html></html>')).toBe(true);
        });

        it('应该拒绝非 HTML 内容', () => {
            expect(isHtmlContent('plain text')).toBe(false);
            expect(isHtmlContent('')).toBe(false);
            expect(isHtmlContent(null)).toBe(false);
            expect(isHtmlContent(123)).toBe(false);
            expect(isHtmlContent({})).toBe(false);
        });
    });

    describe('isHtmlContentEnhanced', () => {
        it('应该识别增强的 HTML 内容', () => {
            const enhancedContent = '<div data-micro-app="test">Enhanced</div>';
            expect(isHtmlContentEnhanced(enhancedContent)).toBe(true);

            const enhancedContent2 = '<div class="micro-app-container">Enhanced</div>';
            expect(isHtmlContentEnhanced(enhancedContent2)).toBe(true);
        });

        it('应该拒绝普通 HTML 内容', () => {
            const normalContent = '<div>Normal</div>';
            expect(isHtmlContentEnhanced(normalContent)).toBe(false);
        });
    });

    describe('createHtmlContainer', () => {
        it('应该创建 HTML 容器', () => {
            const container = createHtmlContainer('test-app');

            expect(container).toBeDefined();
            expect(container.id).toBe('micro-app-test-app');
        });
    });

    describe('formatHtmlError', () => {
        it('应该格式化错误', () => {
            const error = new Error('Test error');
            const formattedError = formatHtmlError(error, 'mount', 'test-app');

            expect(formattedError.message).toContain('HTML');
            expect(formattedError.message).toContain('mount');
            expect(formattedError.message).toContain('test-app');
        });
    });

    describe('createHtmlErrorInfo', () => {
        it('应该创建错误信息', () => {
            const error = new Error('Test error');
            const context = { operation: 'mount' };
            const errorInfo = createHtmlErrorInfo(error, context);

            expect(errorInfo).toMatchObject({
                framework: 'HTML',
                error: 'Test error',
                context
            });
        });
    });

    describe('mergeHtmlConfigs', () => {
        it('应该合并配置', () => {
            const base = { name: 'base', sandbox: false };
            const override = { sandbox: true };

            const merged = mergeHtmlConfigs(base, override);

            expect(merged).toMatchObject({
                name: 'base',
                sandbox: true
            });
        });
    });

    describe('HtmlMicroAppIntegration', () => {
        let integration: HtmlMicroAppIntegration;
        let mockElement: HTMLElement;

        beforeEach(() => {
            const config = {
                name: 'test-app',
                html: '<div>Test Content</div>',
                lifecycle: {
                    mounted: vi.fn(),
                    beforeUnmount: vi.fn(),
                    unmounted: vi.fn(),
                    updated: vi.fn()
                }
            };
            integration = new HtmlMicroAppIntegration(config);
            mockElement = document.createElement('div') as any;
        });

        describe('mount', () => {
            it('应该挂载应用', async () => {
                await integration.mount(mockElement);

                expect(integration.getContainer()).toBeDefined();
            });

            it('应该处理挂载错误', async () => {
                // Mock 一个会抛出错误的情况
                vi.mocked(global.fetch).mockRejectedValue(new Error('Network error'));

                const configWithFile = {
                    name: 'test-app',
                    htmlFile: 'http://example.com/app.html'
                };
                const integrationWithFile = new HtmlMicroAppIntegration(configWithFile);

                await expect(integrationWithFile.mount(mockElement)).rejects.toThrow();
            });
        });

        describe('unmount', () => {
            it('应该卸载应用', async () => {
                await integration.mount(mockElement);
                await integration.unmount();

                expect(integration.getContainer()).toBeNull();
            });
        });

        describe('updateContent', () => {
            it('应该更新内容', async () => {
                await integration.mount(mockElement);
                await integration.updateContent('<div>Updated Content</div>');

                // 验证内容已更新（通过生命周期钩子调用）
                expect(integration.getContainer()).toBeDefined();
            });

            it('应该处理未挂载时的更新', async () => {
                await expect(integration.updateContent('<div>New</div>'))
                    .rejects.toThrow('应用未挂载，无法更新内容');
            });
        });
    });
});