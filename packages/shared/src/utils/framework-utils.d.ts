/**
 * 共享工具包 - 框架检测工具
 *
 * @description 提供前端框架检测、版本识别、兼容性检查等功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * 支持的框架类型
 */
export type FrameworkType = 'react' | 'vue' | 'angular' | 'svelte' | 'solid' | 'preact' | 'lit' | 'alpine' | 'jquery' | 'vanilla' | 'unknown';
/**
 * 框架检测结果
 */
export interface FrameworkDetectionResult {
    /** 框架类型 */
    type: FrameworkType;
    /** 框架版本 */
    version?: string;
    /** 检测置信度 (0-1) */
    confidence: number;
    /** 检测到的特征 */
    features: string[];
    /** 是否为开发模式 */
    isDevelopment?: boolean;
    /** 额外信息 */
    metadata?: Record<string, any>;
}
/**
 * 框架检测器类
 */
export declare class FrameworkDetector {
    private detectionCache;
    private cacheTimeout;
    /**
     * 检测当前页面使用的框架
     * @param forceRefresh 是否强制刷新检测结果
     * @returns 检测结果
     */
    detect(forceRefresh?: boolean): FrameworkDetectionResult;
    /**
     * 检测React
     */
    private detectReact;
    /**
     * 检测Vue
     */
    private detectVue;
    /**
     * 检测Angular
     */
    private detectAngular;
    /**
     * 检测Svelte
     */
    private detectSvelte;
    /**
     * 检测Solid
     */
    private detectSolid;
    /**
     * 检测Preact
     */
    private detectPreact;
    /**
     * 检测Lit
     */
    private detectLit;
    /**
     * 检测Alpine.js
     */
    private detectAlpine;
    /**
     * 检测jQuery
     */
    private detectJQuery;
    /**
     * 检查框架兼容性
     * @param targetFramework 目标框架
     * @param currentFramework 当前框架
     * @returns 是否兼容
     */
    checkCompatibility(targetFramework: FrameworkType, currentFramework?: FrameworkDetectionResult): boolean;
    /**
     * 获取框架建议
     * @param detectionResult 检测结果
     * @returns 建议信息
     */
    getRecommendations(detectionResult?: FrameworkDetectionResult): string[];
}
/**
 * 全局框架检测器实例
 */
export declare const frameworkDetector: FrameworkDetector;
/**
 * 快捷函数：检测当前框架
 * @param forceRefresh 是否强制刷新
 * @returns 检测结果
 */
export declare function detectFramework(forceRefresh?: boolean): FrameworkDetectionResult;
/**
 * 快捷函数：检查框架兼容性
 * @param targetFramework 目标框架
 * @returns 是否兼容
 */
export declare function isFrameworkCompatible(targetFramework: FrameworkType): boolean;
/**
 * 快捷函数：获取框架建议
 * @returns 建议列表
 */
export declare function getFrameworkRecommendations(): string[];
//# sourceMappingURL=framework-utils.d.ts.map