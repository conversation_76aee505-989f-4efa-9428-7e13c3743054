/**
 * 共享工具包 - 性能监控工具函数
 *
 * @description 提供性能监控和优化相关的工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
/**
 * 性能指标接口
 */
export interface PerformanceMetric {
    name: string;
    duration: number;
    timestamp: number;
    [key: string]: any;
}
/**
 * 性能监控器类
 */
export declare class PerformanceMonitor {
    private metrics;
    private observers;
    /**
     * 开始测量性能指标
     */
    startMeasure(name: string): () => PerformanceMetric;
    /**
     * 测量异步函数性能
     */
    measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T>;
    /**
     * 测量同步函数性能
     */
    measureSync<T>(name: string, fn: () => T): T;
    /**
     * 获取性能指标
     */
    getMetric(name: string): PerformanceMetric | undefined;
    /**
     * 获取所有性能指标
     */
    getAllMetrics(): PerformanceMetric[];
    /**
     * 清除指标
     */
    clearMetrics(): void;
    /**
     * 添加观察者
     */
    addObserver(observer: (metric: PerformanceMetric) => void): void;
    /**
     * 移除观察者
     */
    removeObserver(observer: (metric: PerformanceMetric) => void): void;
    /**
     * 通知观察者
     */
    private notifyObservers;
}
/**
 * 全局性能监控器实例
 */
export declare const globalPerformanceMonitor: PerformanceMonitor;
/**
 * 防抖函数
 */
export declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number, immediate?: boolean): (...args: Parameters<T>) => void;
/**
 * 节流函数
 */
export declare function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void;
/**
 * 延迟执行
 */
export declare function delay(ms: number): Promise<void>;
/**
 * 重试函数
 */
export declare function retry<T>(fn: () => Promise<T>, maxAttempts?: number, delayMs?: number): Promise<T>;
/**
 * 获取内存使用情况
 */
export declare function getMemoryUsage(): any;
/**
 * 获取页面加载性能
 */
export declare function getPageLoadPerformance(): Record<string, number> | null;
/**
 * 监控长任务
 */
export declare function observeLongTasks(callback: (entries: any[]) => void): void;
/**
 * 监控资源加载
 */
export declare function observeResourceTiming(callback: (entries: PerformanceResourceTiming[]) => void): void;
/**
 * 计算首次内容绘制时间 (FCP)
 */
export declare function getFCP(): Promise<number>;
/**
 * 计算最大内容绘制时间 (LCP)
 */
export declare function getLCP(): Promise<number>;
/**
 * 计算首次输入延迟 (FID)
 */
export declare function getFID(): Promise<number>;
/**
 * 性能装饰器
 */
export declare function performanceDecorator(name?: string): (target: any, propertyName: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
/**
 * 异步性能装饰器
 */
export declare function asyncPerformanceDecorator(name?: string): (target: any, propertyName: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
//# sourceMappingURL=performance-utils.d.ts.map