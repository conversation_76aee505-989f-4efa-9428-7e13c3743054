/**
 * HTML 适配器测试用例
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { HTMLAdapter } from '../src';

describe('HTMLAdapter', () => {
    let adapter: HTMLAdapter;
    let mockContainer: HTMLElement;
    let mockComponent: string | HTMLElement | (() => string | HTMLElement);
    let mockProps: Record<string, any>;

    beforeEach(() => {
        // 重置模拟
        vi.resetAllMocks();

        // 创建模拟 DOM 元素
        mockContainer = document.createElement('div');
        document.body.appendChild(mockContainer);

        // 创建模拟组件和属性
        mockComponent = '<div id="test-component">测试组件</div>';
        mockProps = { testProp: 'value' };

        // 创建适配器实例
        adapter = new HTMLAdapter();
    });

    afterEach(() => {
        // 清理 DOM
        document.body.removeChild(mockContainer);
    });

    describe('挂载', () => {
        it('应该能够挂载 HTML 字符串', async () => {
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            expect(mockContainer.innerHTML).toContain('测试组件');
        });

        it('应该能够挂载 HTML 元素', async () => {
            const element = document.createElement('div');
            element.id = 'test-element';
            element.textContent = '测试元素';

            await adapter.mount({
                component: element,
                container: mockContainer,
                props: mockProps
            });

            expect(mockContainer.innerHTML).toContain('测试元素');
        });

        it('应该能够挂载函数返回的 HTML', async () => {
            const htmlFn = () => '<div id="test-fn">函数返回的 HTML</div>';

            await adapter.mount({
                component: htmlFn,
                container: mockContainer,
                props: mockProps
            });

            expect(mockContainer.innerHTML).toContain('函数返回的 HTML');
        });

        it('应该能够处理带有事件的 HTML', async () => {
            const clickHandler = vi.fn();
            const htmlWithEvent = `<button id="test-button">点击我</button>`;

            await adapter.mount({
                component: htmlWithEvent,
                container: mockContainer,
                props: {
                    onClick: clickHandler
                }
            });

            const button = mockContainer.querySelector('#test-button');
            button?.dispatchEvent(new Event('click'));

            expect(clickHandler).toHaveBeenCalled();
        });

        it('应该在挂载失败时抛出错误', async () => {
            // 模拟挂载失败
            vi.spyOn(mockContainer, 'appendChild').mockImplementation(() => {
                throw new Error('挂载失败');
            });

            await expect(adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            })).rejects.toThrow('挂载失败');
        });
    });

    describe('卸载', () => {
        it('应该能够卸载 HTML 内容', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 然后卸载
            await adapter.unmount({ container: mockContainer });

            expect(mockContainer.innerHTML).toBe('');
        });

        it('应该在卸载失败时抛出错误', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 模拟卸载失败
            Object.defineProperty(mockContainer, 'innerHTML', {
                set: () => {
                    throw new Error('卸载失败');
                }
            });

            await expect(adapter.unmount({ container: mockContainer })).rejects.toThrow('卸载失败');
        });

        it('应该在容器不存在时不执行卸载', async () => {
            await adapter.unmount({ container: null as any });
            // 不应该抛出错误
            expect(true).toBe(true);
        });
    });

    describe('更新', () => {
        it('应该能够更新 HTML 内容', async () => {
            // 先挂载组件
            const htmlFn = (props: any) => `<div id="test-update">${props.content}</div>`;

            await adapter.mount({
                component: htmlFn,
                container: mockContainer,
                props: { content: '初始内容' }
            });

            expect(mockContainer.innerHTML).toContain('初始内容');

            // 然后更新属性
            await adapter.update({
                container: mockContainer,
                props: { content: '更新后的内容' },
                component: htmlFn
            });

            expect(mockContainer.innerHTML).toContain('更新后的内容');
        });

        it('应该在更新失败时抛出错误', async () => {
            // 先挂载组件
            const htmlFn = (props: any) => `<div id="test-update">${props.content}</div>`;

            await adapter.mount({
                component: htmlFn,
                container: mockContainer,
                props: { content: '初始内容' }
            });

            // 模拟更新失败
            vi.spyOn(adapter as any, 'renderHTML').mockImplementation(() => {
                throw new Error('更新失败');
            });

            // 然后更新属性
            await expect(adapter.update({
                container: mockContainer,
                props: { content: '更新后的内容' },
                component: htmlFn
            })).rejects.toThrow('更新失败');
        });
    });

    describe('工具函数', () => {
        it('应该能够检查是否是有效的 HTML 组件', () => {
            expect(adapter.isValidComponent('<div>测试</div>')).toBe(true);
            expect(adapter.isValidComponent(document.createElement('div'))).toBe(true);
            expect(adapter.isValidComponent(() => '<div>测试</div>')).toBe(true);
            expect(adapter.isValidComponent(null)).toBe(false);
            expect(adapter.isValidComponent(123)).toBe(false);
        });

        it('应该能够获取适配器名称', () => {
            expect(adapter.getName()).toBe('html');
        });
    });

    describe('事件处理', () => {
        it('应该能够绑定事件处理函数', async () => {
            const clickHandler = vi.fn();
            const htmlWithEvent = `<button id="test-button">点击我</button>`;

            await adapter.mount({
                component: htmlWithEvent,
                container: mockContainer,
                props: {
                    onClick: clickHandler
                }
            });

            const button = mockContainer.querySelector('#test-button');
            button?.dispatchEvent(new Event('click'));

            expect(clickHandler).toHaveBeenCalled();
        });

        it('应该能够解绑事件处理函数', async () => {
            const clickHandler = vi.fn();
            const htmlWithEvent = `<button id="test-button">点击我</button>`;

            await adapter.mount({
                component: htmlWithEvent,
                container: mockContainer,
                props: {
                    onClick: clickHandler
                }
            });

            // 卸载组件，应该解绑事件
            await adapter.unmount({ container: mockContainer });

            // 重新创建按钮并触发事件
            mockContainer.innerHTML = htmlWithEvent;
            const button = mockContainer.querySelector('#test-button');
            button?.dispatchEvent(new Event('click'));

            // 事件处理函数不应该被调用
            expect(clickHandler).not.toHaveBeenCalled();
        });
    });
});