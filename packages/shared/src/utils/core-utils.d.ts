/**
 * 微前端微内核便捷工具函数
 *
 * @description 提供简化的 API 接口，便于快速使用微内核功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */
import type { ApplicationConfig, MicroCoreConfig } from '../types';
/**
 * 微内核工具函数接口
 */
export interface MicroCoreUtils {
    /** 创建微内核实例 */
    createMicroCore(config?: MicroCoreConfig): any;
    /** 注册应用 */
    registerApplication(config: ApplicationConfig): Promise<void>;
    /** 加载应用 */
    loadApplication(name: string): Promise<void>;
    /** 挂载应用 */
    mountApplication(name: string, container?: HTMLElement): Promise<void>;
    /** 卸载应用 */
    unmountApplication(name: string): Promise<void>;
    /** 注销应用 */
    unregisterApplication(name: string): Promise<void>;
    /** 获取应用实例 */
    getApplication(name: string): any;
    /** 获取所有应用 */
    getAllApplications(): any[];
    /** 获取应用状态 */
    getApplicationStatus(name: string): any;
    /** 检查应用是否存在 */
    hasApplication(name: string): boolean;
    /** 启动系统 */
    start(): Promise<void>;
    /** 停止系统 */
    stop(): Promise<void>;
    /** 获取系统状态 */
    getStatus(): any;
}
/**
 * 创建微内核工具函数集合
 */
export declare function createMicroCoreUtils(microCoreInstance: any): MicroCoreUtils;
/**
 * 默认导出
 */
declare const _default: {
    createMicroCoreUtils: typeof createMicroCoreUtils;
};
export default _default;
//# sourceMappingURL=core-utils.d.ts.map