# 微前端 Monorepo 多包架构设计

基于架构特性.md文档的深度分析，设计完整的微前端monorepo多包架构。

## 架构概览

```
micro-core/
├── packages/                          # 核心包目录
│   ├── core/                         # 微内核包 (<15KB)
│   ├── shared/                       # 共享基础设施包
│   ├── sandbox/                      # 沙箱系统包
│   ├── plugins/                      # 插件系统包
│   ├── communication/                # 应用间通信包
│   ├── adapters/                     # 框架适配器包
│   ├── resource/                     # 资源管理包
│   ├── builders/                     # 构建工具适配包
│   ├── performance/                  # 性能优化包
│   ├── sidecar/                      # Sidecar模式包
│   ├── auth/                         # 权限管理包
│   ├── error/                        # 错误处理包
│   ├── devtools/                     # 开发工具包
│   └── testing/                      # 测试框架包
├── examples/                         # 演示项目
├── docs/                            # VitePress文档系统
├── tools/                           # 构建工具和脚本
└── 工程化配置文件
```

## 详细架构设计

### 1. 核心包 (packages/core/) - 微内核架构

**功能定位**: 微内核架构的核心，提供最小运行时功能，包体积严格控制在15KB以内

```
packages/core/
├── src/
│   ├── index.ts                      # 主入口文件，导出核心API
│   ├── kernel/                       # 内核核心模块
│   │   ├── index.ts                  # 内核模块导出
│   │   ├── lifecycle.ts              # 生命周期调度器
│   │   ├── registry.ts               # 应用注册表
│   │   └── scheduler.ts              # 任务调度器
│   ├── plugin/                       # 插件管理核心
│   │   ├── index.ts                  # 插件管理器导出
│   │   ├── manager.ts                # 插件管理器
│   │   ├── loader.ts                 # 插件加载器
│   │   └── registry.ts               # 插件注册表
│   ├── app/                          # 应用管理核心
│   │   ├── index.ts                  # 应用管理导出
│   │   ├── manager.ts                # 应用管理器
│   │   ├── loader.ts                 # 应用加载器
│   │   └── status.ts                 # 应用状态管理
│   ├── event/                        # 事件系统核心
│   │   ├── index.ts                  # 事件系统导出
│   │   ├── emitter.ts                # 事件发射器
│   │   └── bus.ts                    # 事件总线
│   └── types/                        # 核心类型定义
│       ├── index.ts                  # 类型导出
│       ├── app.ts                    # 应用相关类型
│       ├── plugin.ts                 # 插件相关类型
│       ├── lifecycle.ts              # 生命周期类型
│       └── event.ts                  # 事件相关类型
├── __tests__/                        # 测试文件
│   ├── kernel/
│   │   ├── lifecycle.test.ts         # 生命周期调度器测试
│   │   ├── registry.test.ts          # 应用注册表测试
│   │   └── scheduler.test.ts         # 任务调度器测试
│   ├── plugin/
│   │   ├── manager.test.ts           # 插件管理器测试
│   │   ├── loader.test.ts            # 插件加载器测试
│   │   └── registry.test.ts          # 插件注册表测试
│   ├── app/
│   │   ├── manager.test.ts           # 应用管理器测试
│   │   ├── loader.test.ts            # 应用加载器测试
│   │   └── status.test.ts            # 应用状态管理测试
│   └── event/
│       ├── emitter.test.ts           # 事件发射器测试
│       └── bus.test.ts               # 事件总线测试
├── package.json                      # 包配置文件
├── tsconfig.json                     # TypeScript配置
├── vite.config.ts                    # Vite构建配置
└── README.md                         # 包说明文档
```

**核心文件功能说明**:

- **src/index.ts**: 核心包主入口，导出微内核的所有公共API
- **src/kernel/lifecycle.ts**: 生命周期调度器，管理应用的启动、挂载、卸载等生命周期
- **src/kernel/registry.ts**: 应用注册表，维护所有注册应用的信息和状态
- **src/kernel/scheduler.ts**: 任务调度器，负责应用加载和执行的调度
- **src/plugin/manager.ts**: 插件管理器，提供插件注册、加载、卸载等核心功能
- **src/plugin/loader.ts**: 插件加载器，实现插件的动态加载和依赖解析
- **src/app/manager.ts**: 应用管理器，提供应用注册、启动、停止等管理功能
- **src/app/loader.ts**: 应用加载器，负责应用资源的加载和初始化
- **src/event/emitter.ts**: 事件发射器，提供基础的事件发布订阅功能
- **src/event/bus.ts**: 事件总线，实现跨应用的事件通信机制

### 2. 共享基础设施包 (packages/shared/) - 公共工具库

**功能定位**: 包含所有公共工具、函数、类型定义、枚举、常量等基础设施，为其他包提供统一的基础能力

```
packages/shared/
├── src/
│   ├── index.ts                      # 主入口文件，导出所有公共API
│   ├── utils/                        # 工具函数库 (9个核心工具模块)
│   │   ├── index.ts                  # 工具函数导出
│   │   ├── dom.ts                    # DOM操作工具
│   │   ├── url.ts                    # URL处理工具
│   │   ├── object.ts                 # 对象操作工具
│   │   ├── array.ts                  # 数组操作工具
│   │   ├── string.ts                 # 字符串处理工具
│   │   ├── function.ts               # 函数工具
│   │   ├── promise.ts                # Promise工具
│   │   ├── storage.ts                # 存储工具
│   │   └── validator.ts              # 验证工具
│   ├── types/                        # 类型定义系统
│   │   ├── index.ts                  # 类型导出
│   │   ├── common.ts                 # 通用类型定义
│   │   ├── app.ts                    # 应用相关类型
│   │   ├── plugin.ts                 # 插件相关类型
│   │   ├── sandbox.ts                # 沙箱相关类型
│   │   ├── communication.ts          # 通信相关类型
│   │   ├── resource.ts               # 资源相关类型
│   │   ├── performance.ts            # 性能相关类型
│   │   ├── auth.ts                   # 权限相关类型
│   │   └── error.ts                  # 错误相关类型
│   ├── constants/                    # 常量管理
│   │   ├── index.ts                  # 常量导出
│   │   ├── events.ts                 # 事件常量
│   │   ├── status.ts                 # 状态常量
│   │   ├── errors.ts                 # 错误码常量
│   │   ├── config.ts                 # 配置常量
│   │   └── lifecycle.ts              # 生命周期常量
│   ├── enums/                        # 枚举定义
│   │   ├── index.ts                  # 枚举导出
│   │   ├── app-status.ts             # 应用状态枚举
│   │   ├── plugin-status.ts          # 插件状态枚举
│   │   ├── sandbox-type.ts           # 沙箱类型枚举
│   │   ├── event-type.ts             # 事件类型枚举
│   │   ├── error-code.ts             # 错误码枚举
│   │   └── lifecycle-phase.ts        # 生命周期阶段枚举
│   ├── helpers/                      # 辅助函数
│   │   ├── index.ts                  # 辅助函数导出
│   │   ├── logger.ts                 # 日志辅助函数
│   │   ├── debug.ts                  # 调试辅助函数
│   │   ├── performance.ts            # 性能辅助函数
│   │   ├── security.ts               # 安全辅助函数
│   │   └── compatibility.ts          # 兼容性辅助函数
│   └── errors/                       # 错误处理基础
│       ├── index.ts                  # 错误类导出
│       ├── base.ts                   # 基础错误类
│       ├── app-error.ts              # 应用错误类
│       ├── plugin-error.ts           # 插件错误类
│       ├── sandbox-error.ts          # 沙箱错误类
│       └── communication-error.ts    # 通信错误类
├── __tests__/                        # 测试文件
│   ├── utils/
│   │   ├── dom.test.ts               # DOM工具测试
│   │   ├── url.test.ts               # URL工具测试
│   │   ├── object.test.ts            # 对象工具测试
│   │   ├── array.test.ts             # 数组工具测试
│   │   ├── string.test.ts            # 字符串工具测试
│   │   ├── function.test.ts          # 函数工具测试
│   │   ├── promise.test.ts           # Promise工具测试
│   │   ├── storage.test.ts           # 存储工具测试
│   │   └── validator.test.ts         # 验证工具测试
│   ├── helpers/
│   │   ├── logger.test.ts            # 日志辅助函数测试
│   │   ├── debug.test.ts             # 调试辅助函数测试
│   │   ├── performance.test.ts       # 性能辅助函数测试
│   │   ├── security.test.ts          # 安全辅助函数测试
│   │   └── compatibility.test.ts     # 兼容性辅助函数测试
│   └── errors/
│       ├── base.test.ts              # 基础错误类测试
│       ├── app-error.test.ts         # 应用错误类测试
│       ├── plugin-error.test.ts      # 插件错误类测试
│       ├── sandbox-error.test.ts     # 沙箱错误类测试
│       └── communication-error.test.ts # 通信错误类测试
├── package.json                      # 包配置文件
├── tsconfig.json                     # TypeScript配置
├── vite.config.ts                    # Vite构建配置
└── README.md                         # 包说明文档
```

**核心文件功能说明**:

- **src/utils/dom.ts**: DOM操作工具，提供元素查找、创建、操作等DOM相关工具函数
- **src/utils/url.ts**: URL处理工具，提供URL解析、构建、参数处理等功能
- **src/utils/object.ts**: 对象操作工具，提供深拷贝、合并、遍历等对象处理功能
- **src/utils/array.ts**: 数组操作工具，提供数组去重、分组、排序等数组处理功能
- **src/utils/string.ts**: 字符串处理工具，提供字符串格式化、转换、验证等功能
- **src/utils/function.ts**: 函数工具，提供防抖、节流、柯里化等函数式编程工具
- **src/utils/promise.ts**: Promise工具，提供Promise并发控制、超时处理等异步工具
- **src/utils/storage.ts**: 存储工具，提供localStorage、sessionStorage的封装和管理
- **src/utils/validator.ts**: 验证工具，提供各种数据验证和格式检查功能
- **src/types/**: 完整的TypeScript类型定义系统，为所有包提供类型支持
- **src/constants/**: 统一的常量定义，包括事件名、状态码、错误码等
- **src/enums/**: 枚举定义，提供类型安全的枚举值
- **src/helpers/**: 辅助函数，提供日志、调试、性能监控等辅助功能
- **src/errors/**: 标准化的错误处理机制，定义各种错误类型

### 3. 沙箱系统包 (packages/sandbox/) - 多层沙箱隔离

**功能定位**: 实现6种沙箱策略的完整实现，支持JavaScript、CSS、HTML三个维度的沙箱隔离

```
packages/sandbox/
├── src/
│   ├── index.ts                      # 主入口文件，导出沙箱系统API
│   ├── factory/                      # 沙箱工厂
│   │   ├── index.ts                  # 沙箱工厂导出
│   │   ├── sandbox-factory.ts        # 沙箱工厂实现
│   │   └── strategy-selector.ts      # 沙箱策略选择器
│   ├── manager/                      # 沙箱管理器
│   │   ├── index.ts                  # 沙箱管理器导出
│   │   ├── sandbox-manager.ts        # 沙箱管理器实现
│   │   ├── lifecycle-manager.ts      # 沙箱生命周期管理
│   │   └── resource-manager.ts       # 沙箱资源管理
│   ├── strategies/                   # 6种沙箱策略实现
│   │   ├── index.ts                  # 沙箱策略导出
│   │   ├── base/                     # 基础沙箱抽象
│   │   │   ├── index.ts              # 基础沙箱导出
│   │   │   ├── base-sandbox.ts       # 基础沙箱抽象类
│   │   │   └── sandbox-interface.ts  # 沙箱接口定义
│   │   ├── proxy/                    # Proxy沙箱策略
│   │   │   ├── index.ts              # Proxy沙箱导出
│   │   │   ├── proxy-sandbox.ts      # Proxy沙箱实现
│   │   │   ├── global-proxy.ts       # 全局对象代理
│   │   │   └── property-proxy.ts     # 属性代理
│   │   ├── define-property/          # DefineProperty沙箱策略
│   │   │   ├── index.ts              # DefineProperty沙箱导出
│   │   │   ├── define-property-sandbox.ts # DefineProperty沙箱实现
│   │   │   └── property-descriptor.ts # 属性描述符管理
│   │   ├── iframe/                   # iframe沙箱策略
│   │   │   ├── index.ts              # iframe沙箱导出
│   │   │   ├── iframe-sandbox.ts     # iframe沙箱实现
│   │   │   ├── iframe-manager.ts     # iframe管理器
│   │   │   └── message-bridge.ts     # iframe消息桥接
│   │   ├── web-component/            # WebComponent沙箱策略
│   │   │   ├── index.ts              # WebComponent沙箱导出
│   │   │   ├── web-component-sandbox.ts # WebComponent沙箱实现
│   │   │   ├── shadow-dom.ts         # Shadow DOM管理
│   │   │   └── custom-element.ts     # 自定义元素管理
│   │   ├── namespace/                # Namespace沙箱策略
│   │   │   ├── index.ts              # Namespace沙箱导出
│   │   │   ├── namespace-sandbox.ts  # Namespace沙箱实现
│   │   │   └── namespace-manager.ts  # 命名空间管理器
│   │   └── federation/               # Federation沙箱策略
│   │       ├── index.ts              # Federation沙箱导出
│   │       ├── federation-sandbox.ts # Federation沙箱实现
│   │       ├── module-federation.ts  # 模块联邦管理
│   │       └── remote-loader.ts      # 远程模块加载器
│   ├── isolation/                    # 隔离机制
│   │   ├── index.ts                  # 隔离机制导出
│   │   ├── js-isolation.ts           # JavaScript隔离
│   │   ├── css-isolation.ts          # CSS隔离
│   │   ├── html-isolation.ts         # HTML隔离
│   │   └── global-isolation.ts       # 全局变量隔离
│   ├── performance/                  # 性能优化
│   │   ├── index.ts                  # 性能优化导出
│   │   ├── sandbox-pool.ts           # 沙箱对象池
│   │   ├── resource-reuse.ts         # 资源复用
│   │   └── memory-optimization.ts    # 内存优化
│   └── types/                        # 沙箱相关类型
│       ├── index.ts                  # 类型导出
│       ├── sandbox.ts                # 沙箱类型定义
│       ├── strategy.ts               # 策略类型定义
│       ├── isolation.ts              # 隔离类型定义
│       └── performance.ts            # 性能类型定义
├── __tests__/                        # 测试文件
│   ├── factory/
│   │   ├── sandbox-factory.test.ts   # 沙箱工厂测试
│   │   └── strategy-selector.test.ts # 策略选择器测试
│   ├── manager/
│   │   ├── sandbox-manager.test.ts   # 沙箱管理器测试
│   │   ├── lifecycle-manager.test.ts # 生命周期管理测试
│   │   └── resource-manager.test.ts  # 资源管理测试
│   ├── strategies/
│   │   ├── proxy/
│   │   │   ├── proxy-sandbox.test.ts # Proxy沙箱测试
│   │   │   ├── global-proxy.test.ts  # 全局代理测试
│   │   │   └── property-proxy.test.ts # 属性代理测试
│   │   ├── define-property/
│   │   │   ├── define-property-sandbox.test.ts # DefineProperty沙箱测试
│   │   │   └── property-descriptor.test.ts # 属性描述符测试
│   │   ├── iframe/
│   │   │   ├── iframe-sandbox.test.ts # iframe沙箱测试
│   │   │   ├── iframe-manager.test.ts # iframe管理器测试
│   │   │   └── message-bridge.test.ts # 消息桥接测试
│   │   ├── web-component/
│   │   │   ├── web-component-sandbox.test.ts # WebComponent沙箱测试
│   │   │   ├── shadow-dom.test.ts    # Shadow DOM测试
│   │   │   └── custom-element.test.ts # 自定义元素测试
│   │   ├── namespace/
│   │   │   ├── namespace-sandbox.test.ts # Namespace沙箱测试
│   │   │   └── namespace-manager.test.ts # 命名空间管理器测试
│   │   └── federation/
│   │       ├── federation-sandbox.test.ts # Federation沙箱测试
│   │       ├── module-federation.test.ts # 模块联邦测试
│   │       └── remote-loader.test.ts # 远程加载器测试
│   ├── isolation/
│   │   ├── js-isolation.test.ts      # JavaScript隔离测试
│   │   ├── css-isolation.test.ts     # CSS隔离测试
│   │   ├── html-isolation.test.ts    # HTML隔离测试
│   │   └── global-isolation.test.ts  # 全局隔离测试
│   └── performance/
│       ├── sandbox-pool.test.ts      # 沙箱对象池测试
│       ├── resource-reuse.test.ts    # 资源复用测试
│       └── memory-optimization.test.ts # 内存优化测试
├── package.json                      # 包配置文件
├── tsconfig.json                     # TypeScript配置
├── vite.config.ts                    # Vite构建配置
└── README.md                         # 包说明文档
```

**核心文件功能说明**:

- **src/factory/sandbox-factory.ts**: 沙箱工厂，根据配置和环境创建合适的沙箱实例
- **src/factory/strategy-selector.ts**: 沙箱策略选择器，智能选择最优的沙箱策略
- **src/manager/sandbox-manager.ts**: 沙箱管理器，统一管理所有沙箱实例的生命周期
- **src/strategies/proxy/proxy-sandbox.ts**: Proxy沙箱实现，使用Proxy代理实现全局沙箱
- **src/strategies/define-property/define-property-sandbox.ts**: DefineProperty沙箱实现
- **src/strategies/iframe/iframe-sandbox.ts**: iframe沙箱实现，提供完全隔离的运行环境
- **src/strategies/web-component/web-component-sandbox.ts**: WebComponent沙箱实现
- **src/strategies/namespace/namespace-sandbox.ts**: Namespace沙箱实现
- **src/strategies/federation/federation-sandbox.ts**: Federation沙箱实现
- **src/isolation/**: 三个维度的隔离机制实现（JavaScript、CSS、HTML）
- **src/performance/**: 沙箱性能优化，包括对象池、资源复用、内存优化
