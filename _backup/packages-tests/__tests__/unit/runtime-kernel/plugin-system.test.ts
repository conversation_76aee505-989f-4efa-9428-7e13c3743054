/**
 * @fileoverview MicroCoreKernel 插件系统测试
 * @description 测试内核的插件安装、获取、管理等功能
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { MicroCoreError } from '../../../src/errors';
import { MicroCoreKernel } from '../../../src/runtime/kernel';

// Mock 依赖模块
vi.mock('../../../src/communication/event-bus');
vi.mock('../../../src/runtime/app-registry');
vi.mock('../../../src/runtime/lifecycle-manager');
vi.mock('../../../src/utils');

describe('MicroCoreKernel 插件系统', () => {
    let kernel: MicroCoreKernel;
    let mockEventBus: any;
    let mockAppRegistry: any;
    let mockLifecycleManager: any;
    let mockLogger: any;

    beforeEach(() => {
        // 创建模拟对象
        mockEventBus = {
            emit: vi.fn(),
            on: vi.fn(),
            off: vi.fn(),
            clear: vi.fn()
        };

        mockAppRegistry = {
            register: vi.fn(),
            unregister: vi.fn(),
            get: vi.fn(),
            getAll: vi.fn(),
            getByStatus: vi.fn(),
            updateStatus: vi.fn(),
            setError: vi.fn(),
            clear: vi.fn()
        };

        mockLifecycleManager = {
            bootstrap: vi.fn(),
            mount: vi.fn(),
            unmount: vi.fn(),
            clearHooks: vi.fn()
        };

        mockLogger = {
            debug: vi.fn(),
            info: vi.fn(),
            warn: vi.fn(),
            error: vi.fn()
        };

        // Mock 构造函数
        const { EventBus } = require('../../../src/communication/event-bus');
        const { AppRegistry } = require('../../../src/runtime/app-registry');
        const { LifecycleManager } = require('../../../src/runtime/lifecycle-manager');
        const { createLogger } = require('../../../src/utils');

        EventBus.mockImplementation(() => mockEventBus);
        AppRegistry.mockImplementation(() => mockAppRegistry);
        LifecycleManager.mockImplementation(() => mockLifecycleManager);
        createLogger.mockReturnValue(mockLogger);

        kernel = new MicroCoreKernel();
    });

    afterEach(() => {
        vi.clearAllMocks();
        vi.resetModules();
    });

    describe('use', () => {
        it('应该成功安装插件', () => {
            const mockPlugin: any = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn()
            };

            kernel.use(mockPlugin, { option: 'value' });

            expect(mockLogger.debug).toHaveBeenCalledWith('安装插件:', 'test-plugin');
            expect(mockPlugin.install).toHaveBeenCalledWith(kernel, { option: 'value' });
            expect(mockEventBus.emit).toHaveBeenCalledWith('plugin:installed', {
                plugin: mockPlugin,
                options: { option: 'value' }
            });
            expect(mockLogger.info).toHaveBeenCalledWith('插件 test-plugin 安装完成');
        });

        it('应该处理无install方法的插件', () => {
            const mockPlugin: any = {
                name: 'simple-plugin',
                version: '1.0.0'
            };

            kernel.use(mockPlugin);

            expect(mockLogger.info).toHaveBeenCalledWith('插件 simple-plugin 安装完成');
        });

        it('应该在插件已存在时抛出错误', () => {
            const mockPlugin: any = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn()
            };

            kernel.use(mockPlugin);

            expect(() => {
                kernel.use(mockPlugin);
            }).toThrow(MicroCoreError);
        });

        it('应该处理无效插件', () => {
            expect(() => {
                kernel.use(null as any);
            }).toThrow(MicroCoreError);

            expect(() => {
                kernel.use({ name: '' } as any);
            }).toThrow(MicroCoreError);
        });

        it('应该处理插件安装失败', () => {
            const error = new Error('安装失败');
            const mockPlugin: any = {
                name: 'failing-plugin',
                version: '1.0.0',
                install: vi.fn().mockImplementation(() => {
                    throw error;
                })
            };

            expect(() => {
                kernel.use(mockPlugin);
            }).toThrow(MicroCoreError);

            expect(mockLogger.error).toHaveBeenCalledWith('插件 failing-plugin 安装失败:', error);
        });

        it('应该在内核销毁后抛出错误', async () => {
            await kernel.destroy();

            const mockPlugin: any = {
                name: 'test-plugin',
                version: '1.0.0'
            };

            expect(() => {
                kernel.use(mockPlugin);
            }).toThrow(MicroCoreError);
        });

        it('应该处理插件安装时的字符串错误', () => {
            const mockPlugin: any = {
                name: 'failing-plugin',
                version: '1.0.0',
                install: vi.fn().mockImplementation(() => {
                    throw 'string error';
                })
            };

            expect(() => {
                kernel.use(mockPlugin);
            }).toThrow(MicroCoreError);
        });

        it('应该支持插件选项', () => {
            const mockPlugin: any = {
                name: 'configurable-plugin',
                version: '1.0.0',
                install: vi.fn()
            };

            const options = {
                enabled: true,
                config: { theme: 'dark' },
                callbacks: {
                    onInit: vi.fn(),
                    onDestroy: vi.fn()
                }
            };

            kernel.use(mockPlugin, options);

            expect(mockPlugin.install).toHaveBeenCalledWith(kernel, options);
        });

        it('应该处理复杂的插件对象', () => {
            const mockPlugin: any = {
                name: 'complex-plugin',
                version: '2.1.0',
                description: '复杂插件示例',
                author: 'Test Author',
                dependencies: ['other-plugin'],
                install: vi.fn(),
                uninstall: vi.fn(),
                configure: vi.fn()
            };

            expect(() => {
                kernel.use(mockPlugin);
            }).not.toThrow();

            expect(mockPlugin.install).toHaveBeenCalledWith(kernel, undefined);
        });
    });

    describe('getPlugin', () => {
        it('应该返回已安装的插件', () => {
            const mockPlugin: any = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn()
            };

            kernel.use(mockPlugin);

            const result = kernel.getPlugin('test-plugin');
            expect(result).toBe(mockPlugin);
        });

        it('应该在插件不存在时返回null', () => {
            const result = kernel.getPlugin('non-existent-plugin');
            expect(result).toBeNull();
        });

        it('应该在内核销毁后抛出错误', async () => {
            await kernel.destroy();

            expect(() => {
                kernel.getPlugin('test-plugin');
            }).toThrow(MicroCoreError);
        });

        it('应该处理空字符串插件名', () => {
            const result = kernel.getPlugin('');
            expect(result).toBeNull();
        });

        it('应该处理null插件名', () => {
            const result = kernel.getPlugin(null as any);
            expect(result).toBeNull();
        });
    });

    describe('getPlugins', () => {
        it('应该返回所有已安装的插件名称', () => {
            const plugin1: any = { name: 'plugin1', version: '1.0.0' };
            const plugin2: any = { name: 'plugin2', version: '1.0.0' };

            kernel.use(plugin1);
            kernel.use(plugin2);

            const result = kernel.getPlugins();
            expect(result).toContain('plugin1');
            expect(result).toContain('plugin2');
            expect(result.length).toBe(2);
        });

        it('应该在没有插件时返回空数组', () => {
            const result = kernel.getPlugins();
            expect(result).toEqual([]);
        });

        it('应该在内核销毁后抛出错误', async () => {
            await kernel.destroy();

            expect(() => {
                kernel.getPlugins();
            }).toThrow(MicroCoreError);
        });

        it('应该返回插件名称的副本', () => {
            const plugin: any = { name: 'test-plugin', version: '1.0.0' };
            kernel.use(plugin);

            const result1 = kernel.getPlugins();
            const result2 = kernel.getPlugins();

            expect(result1).not.toBe(result2); // 不是同一个数组引用
            expect(result1).toEqual(result2); // 但内容相同
        });
    });

    describe('hasPlugin', () => {
        it('应该正确检查插件是否存在', () => {
            const mockPlugin: any = {
                name: 'test-plugin',
                version: '1.0.0'
            };

            expect(kernel.hasPlugin('test-plugin')).toBe(false);

            kernel.use(mockPlugin);

            expect(kernel.hasPlugin('test-plugin')).toBe(true);
        });

        it('应该在内核销毁后抛出错误', async () => {
            await kernel.destroy();

            expect(() => {
                kernel.hasPlugin('test-plugin');
            }).toThrow(MicroCoreError);
        });

        it('应该处理空字符串插件名', () => {
            expect(kernel.hasPlugin('')).toBe(false);
        });

        it('应该处理null插件名', () => {
            expect(kernel.hasPlugin(null as any)).toBe(false);
        });

        it('应该区分大小写', () => {
            const mockPlugin: any = {
                name: 'TestPlugin',
                version: '1.0.0'
            };

            kernel.use(mockPlugin);

            expect(kernel.hasPlugin('TestPlugin')).toBe(true);
            expect(kernel.hasPlugin('testplugin')).toBe(false);
            expect(kernel.hasPlugin('TESTPLUGIN')).toBe(false);
        });
    });

    describe('插件生命周期', () => {
        it('应该在插件安装时触发事件', () => {
            const mockPlugin: any = {
                name: 'lifecycle-plugin',
                version: '1.0.0',
                install: vi.fn()
            };

            const options = { config: 'value' };

            kernel.use(mockPlugin, options);

            expect(mockEventBus.emit).toHaveBeenCalledWith('plugin:installed', {
                plugin: mockPlugin,
                options
            });
        });

        it('应该支持插件的异步安装', () => {
            const mockPlugin: any = {
                name: 'async-plugin',
                version: '1.0.0',
                install: vi.fn().mockResolvedValue(undefined)
            };

            expect(() => {
                kernel.use(mockPlugin);
            }).not.toThrow();

            expect(mockPlugin.install).toHaveBeenCalledWith(kernel, undefined);
        });

        it('应该处理插件安装过程中的异常', () => {
            const mockPlugin: any = {
                name: 'error-plugin',
                version: '1.0.0',
                install: vi.fn().mockImplementation(() => {
                    throw new Error('插件安装异常');
                })
            };

            expect(() => {
                kernel.use(mockPlugin);
            }).toThrow(MicroCoreError);

            expect(mockLogger.error).toHaveBeenCalledWith(
                '插件 error-plugin 安装失败:',
                expect.any(Error)
            );
        });
    });

    describe('插件依赖管理', () => {
        it('应该支持插件版本检查', () => {
            const plugin1: any = { name: 'plugin1', version: '1.0.0' };
            const plugin2: any = { name: 'plugin1', version: '2.0.0' };

            kernel.use(plugin1);

            // 尝试安装同名但不同版本的插件
            expect(() => {
                kernel.use(plugin2);
            }).toThrow(MicroCoreError);
        });

        it('应该处理插件名称冲突', () => {
            const plugin1: any = { name: 'conflict-plugin', version: '1.0.0' };
            const plugin2: any = { name: 'conflict-plugin', version: '1.0.0' };

            kernel.use(plugin1);

            expect(() => {
                kernel.use(plugin2);
            }).toThrow(MicroCoreError);

            expect(mockLogger.error).toHaveBeenCalledWith(
                '插件 conflict-plugin 安装失败:',
                expect.any(Error)
            );
        });
    });

    describe('性能测试', () => {
        it('应该高效处理大量插件安装', () => {
            const start = performance.now();

            for (let i = 0; i < 100; i++) {
                const plugin: any = {
                    name: `plugin-${i}`,
                    version: '1.0.0'
                };
                kernel.use(plugin);
            }

            const end = performance.now();
            expect(end - start).toBeLessThan(500); // 应该在500ms内完成
            expect(kernel.getPlugins().length).toBe(100);
        });

        it('应该高效处理大量插件查询', () => {
            // 先安装一些插件
            for (let i = 0; i < 50; i++) {
                const plugin: any = {
                    name: `plugin-${i}`,
                    version: '1.0.0'
                };
                kernel.use(plugin);
            }

            const start = performance.now();

            // 大量查询操作
            for (let i = 0; i < 1000; i++) {
                kernel.hasPlugin(`plugin-${i % 50}`);
                kernel.getPlugin(`plugin-${i % 50}`);
            }

            const end = performance.now();
            expect(end - start).toBeLessThan(100); // 应该在100ms内完成
        });
    });

    describe('边界情况', () => {
        it('应该处理特殊字符的插件名', () => {
            const specialNames = [
                'plugin-with-dash',
                'plugin_with_underscore',
                'plugin.with.dot',
                'plugin@with@at',
                'plugin#with#hash'
            ];

            specialNames.forEach(name => {
                const plugin: any = { name, version: '1.0.0' };

                expect(() => {
                    kernel.use(plugin);
                }).not.toThrow();

                expect(kernel.hasPlugin(name)).toBe(true);
                expect(kernel.getPlugin(name)).toBe(plugin);
            });
        });

        it('应该处理长插件名', () => {
            const longName = 'a'.repeat(1000);
            const plugin: any = { name: longName, version: '1.0.0' };

            expect(() => {
                kernel.use(plugin);
            }).not.toThrow();

            expect(kernel.hasPlugin(longName)).toBe(true);
        });

        it('应该处理Unicode插件名', () => {
            const unicodeNames = [
                '测试插件',
                'プラグイン',
                '플러그인',
                '🔌插件'
            ];

            unicodeNames.forEach(name => {
                const plugin: any = { name, version: '1.0.0' };

                expect(() => {
                    kernel.use(plugin);
                }).not.toThrow();

                expect(kernel.hasPlugin(name)).toBe(true);
            });
        });
    });

    describe('内存管理', () => {
        it('应该在内核销毁时清理插件引用', async () => {
            const plugins = Array.from({ length: 10 }, (_, i) => ({
                name: `plugin-${i}`,
                version: '1.0.0',
                install: vi.fn()
            }));

            plugins.forEach(plugin => kernel.use(plugin));

            expect(kernel.getPlugins().length).toBe(10);

            await kernel.destroy();

            // 销毁后应该无法访问插件
            expect(() => kernel.getPlugins()).toThrow(MicroCoreError);
            expect(() => kernel.hasPlugin('plugin-0')).toThrow(MicroCoreError);
            expect(() => kernel.getPlugin('plugin-0')).toThrow(MicroCoreError);
        });
    });
});