/**
 * Rollup 构建器测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { RollupBuilder } from '../builder-rollup/src';
import { BuilderOptions } from '../shared/types';
import * as testUtils from './test-utils';

// 模拟 rollup 依赖
vi.mock('rollup', () => ({
    rollup: vi.fn().mockResolvedValue({
        write: vi.fn().mockResolvedValue({
            output: [{ fileName: 'bundle.js' }]
        }),
        close: vi.fn().mockResolvedValue(undefined)
    }),
    watch: vi.fn().mockReturnValue({
        on: vi.fn(),
        close: vi.fn()
    })
}));

import * as rollup from 'rollup';

describe('RollupBuilder', () => {
    let builder: RollupBuilder;
    let mockOptions: BuilderOptions;

    beforeEach(() => {
        // 重置所有模拟
        vi.resetAllMocks();

        // 创建模拟选项
        mockOptions = {
            entry: './src/index.js',
            output: {
                path: './dist',
                filename: 'bundle.js',
                format: 'esm'
            },
            mode: 'development'
        };

        // 创建构建器实例
        builder = new RollupBuilder();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('构建配置', () => {
        it('应该能够创建有效的 Rollup 配置', () => {
            const config = builder.createConfig(mockOptions);
            expect(config).toBeDefined();
            expect(config.input).toBe(mockOptions.entry);
            expect(config.output).toBeDefined();
            expect(config.output.file).toContain(mockOptions.output.filename);
        });

        it('应该能够处理自定义配置', () => {
            const customOptions = {
                ...mockOptions,
                customConfig: {
                    external: ['react', 'react-dom']
                }
            };

            const config = builder.createConfig(customOptions);
            expect(config.external).toEqual(['react', 'react-dom']);
        });

        it('应该能够处理插件配置', () => {
            const pluginOptions = {
                ...mockOptions,
                plugins: [
                    { name: 'rollup-plugin-terser', options: {} }
                ]
            };

            const config = builder.createConfig(pluginOptions);
            expect(config.plugins).toBeDefined();
            expect(config.plugins.length).toBeGreaterThan(0);
        });
    });

    describe('构建过程', () => {
        it('应该能够执行构建', async () => {
            await builder.build(mockOptions);
            expect(rollup.rollup).toHaveBeenCalled();
        });

        it('应该在构建失败时抛出错误', async () => {
            (rollup.rollup as any).mockRejectedValue(new Error('构建失败'));
            await expect(builder.build(mockOptions)).rejects.toThrow('构建失败');
        });

        it('应该能够处理构建警告', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });

            const mockBundle = {
                write: vi.fn().mockResolvedValue({
                    output: [{ fileName: 'bundle.js' }],
                    warnings: [{ message: '构建警告' }]
                }),
                close: vi.fn().mockResolvedValue(undefined)
            };
            (rollup.rollup as any).mockResolvedValue(mockBundle);

            await builder.build(mockOptions);
            expect(consoleSpy).toHaveBeenCalled();
        });
    });

    describe('开发服务器', () => {
        it('应该能够启动开发服务器', async () => {
            await builder.serve(mockOptions);
            expect(rollup.watch).toHaveBeenCalled();
        });

        it('应该能够停止开发服务器', async () => {
            const mockWatcher = {
                close: vi.fn()
            };
            (builder as any).watcher = mockWatcher;

            await builder.stop();
            expect(mockWatcher.close).toHaveBeenCalled();
        });

        it('应该在开发服务器启动失败时抛出错误', async () => {
            (rollup.watch as any).mockImplementation(() => {
                throw new Error('服务器启动失败');
            });
            await expect(builder.serve(mockOptions)).rejects.toThrow('服务器启动失败');
        });
    });

    describe('构建验证', () => {
        it('应该能够验证构建产物', async () => {
            const validateSpy = vi.spyOn(testUtils, 'validateBuildOutput').mockResolvedValue(true);

            await builder.build(mockOptions);
            expect(validateSpy).toHaveBeenCalled();
        });

        it('应该能够检测构建产物完整性', async () => {
            const checkIntegritySpy = vi.spyOn(testUtils, 'checkBuildIntegrity').mockResolvedValue(true);

            await builder.build(mockOptions);
            expect(checkIntegritySpy).toHaveBeenCalled();
        });
    });

    describe('错误处理', () => {
        it('应该能够处理配置错误', () => {
            const invalidOptions = {
                ...mockOptions,
                entry: undefined
            } as any;

            expect(() => builder.createConfig(invalidOptions)).toThrow();
        });

        it('应该能够处理构建过程错误', async () => {
            (rollup.rollup as any).mockRejectedValue(new Error('构建过程错误'));
            await expect(builder.build(mockOptions)).rejects.toThrow('构建过程错误');
        });

        it('应该能够处理输出写入错误', async () => {
            const mockBundle = {
                write: vi.fn().mockRejectedValue(new Error('输出写入错误')),
                close: vi.fn().mockResolvedValue(undefined)
            };
            (rollup.rollup as any).mockResolvedValue(mockBundle);

            await expect(builder.build(mockOptions)).rejects.toThrow('输出写入错误');
        });
    });

    describe('构建优化', () => {
        it('应该能够配置构建优化选项', () => {
            const optimizeOptions = {
                ...mockOptions,
                optimize: {
                    minify: true,
                    treeshake: true
                }
            };

            const config = builder.createConfig(optimizeOptions);
            expect(config.treeshake).toBe(true);
        });

        it('应该能够配置代码分割', () => {
            const optimizeOptions = {
                ...mockOptions,
                output: {
                    ...mockOptions.output,
                    format: 'esm',
                    dir: './dist'
                },
                optimize: {
                    splitting: true
                }
            };

            const config = builder.createConfig(optimizeOptions);
            expect(config.output.dir).toBe('./dist');
            expect(config.output.format).toBe('esm');
        });
    });

    describe('多种输出格式', () => {
        it('应该能够配置多种输出格式', () => {
            const multiFormatOptions = {
                ...mockOptions,
                output: [
                    {
                        path: './dist',
                        filename: 'bundle.esm.js',
                        format: 'esm'
                    },
                    {
                        path: './dist',
                        filename: 'bundle.cjs.js',
                        format: 'cjs'
                    }
                ]
            };

            const config = builder.createConfig(multiFormatOptions);
            expect(Array.isArray(config.output)).toBe(true);
            expect(config.output.length).toBe(2);
            expect(config.output[0].format).toBe('esm');
            expect(config.output[1].format).toBe('cjs');
        });
    });
});