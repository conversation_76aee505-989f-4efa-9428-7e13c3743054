# @micro-core/adapter-angular

Micro-Core Angular 适配器 - 支持 Angular 框架的微前端集成

## 特性

- 🅰️ **Angular 支持**: 完整支持 Angular 12+ 版本
- 🔄 **生命周期管理**: 完整的应用生命周期管理
- 🏗️ **模块引导**: 支持 Angular 模块的动态引导
- 🎯 **容器管理**: 灵活的应用容器管理
- 🔧 **配置灵活**: 丰富的配置选项

## 安装

```bash
npm install @micro-core/adapter-angular
# 或
pnpm add @micro-core/adapter-angular
```

## 使用方法

### 基本使用

```typescript
// 子应用入口文件 (main.ts)
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { createAngularLifecycles } from '@micro-core/adapter-angular';
import { AppModule } from './app/app.module';

// 创建生命周期函数
const lifecycles = createAngularLifecycles(AppModule, {
  selector: 'app-root',
  enableProdMode: true
});

// 导出生命周期函数
export const bootstrap = lifecycles.bootstrap;
export const mount = lifecycles.mount;
export const unmount = lifecycles.unmount;
export const update = lifecycles.update;

// 独立运行时的启动逻辑
if (!(window as any).__POWERED_BY_MICRO_CORE__) {
  platformBrowserDynamic().bootstrapModule(AppModule)
    .catch(err => console.error(err));
}
```

### 插件模式使用

```typescript
import { MicroCoreKernel } from '@micro-core/core';
import { AngularAdapterPlugin } from '@micro-core/adapter-angular';

const kernel = new MicroCoreKernel();

// 注册 Angular 适配器插件
const angularAdapter = new AngularAdapterPlugin({
  selector: 'app-root',
  enableProdMode: true
});

kernel.use(angularAdapter);

// 使用适配器创建生命周期
const lifecycles = kernel.angularAdapter.createLifecycles(AppModule);
```

### 高级配置

```typescript
import { createAngularLifecycles } from '@micro-core/adapter-angular';
import { AppModule } from './app/app.module';

const lifecycles = createAngularLifecycles(AppModule, {
  selector: 'app-root',
  enableProdMode: process.env.NODE_ENV === 'production',
  bootstrapOptions: {
    ngZone: 'noop', // 禁用 Zone.js
    preserveWhitespaces: false
  }
});

export const bootstrap = lifecycles.bootstrap;
export const mount = lifecycles.mount;
export const unmount = lifecycles.unmount;
export const update = lifecycles.update;
```

## 配置选项

```typescript
interface AngularAdapterOptions {
  /** Angular 应用模块 */
  appModule?: any;
  /** 应用根选择器 */
  selector?: string; // 默认: 'app-root'
  /** 是否启用生产模式 */
  enableProdMode?: boolean; // 默认: false
  /** 自定义引导选项 */
  bootstrapOptions?: any;
}
```

## Angular 应用配置

### 1. 修改 Angular 应用模块

```typescript
// app.module.ts
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';

@NgModule({
  declarations: [
    AppComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
```

### 2. 配置路由

```typescript
// app-routing.module.ts
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  // 配置子应用路由
  { path: '', redirectTo: '/home', pathMatch: 'full' },
  { path: 'home', component: HomeComponent },
  // ... 其他路由
];

@NgModule({
  imports: [RouterModule.forRoot(routes, {
    // 微前端环境下的路由配置
    useHash: false,
    enableTracing: false
  })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
```

### 3. 处理基础路径

```typescript
// app.component.ts
import { Component, OnInit, Inject } from '@angular/core';
import { DOCUMENT } from '@angular/common';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {
  title = 'angular-micro-app';

  constructor(@Inject(DOCUMENT) private document: Document) {}

  ngOnInit() {
    // 处理微前端环境下的基础路径
    const base = this.document.querySelector('base');
    if (!base) {
      const baseElement = this.document.createElement('base');
      baseElement.href = '/angular/';
      this.document.head.appendChild(baseElement);
    }
  }
}
```

## 构建配置

### Angular CLI 配置

```json
// angular.json
{
  "projects": {
    "angular-micro-app": {
      "architect": {
        "build": {
          "builder": "@angular-devkit/build-angular:browser",
          "options": {
            "outputPath": "dist/angular-micro-app",
            "index": "src/index.html",
            "main": "src/main.ts",
            "polyfills": "src/polyfills.ts",
            "tsConfig": "tsconfig.app.json",
            "assets": ["src/favicon.ico", "src/assets"],
            "styles": ["src/styles.css"],
            "scripts": [],
            "deployUrl": "/angular/",
            "buildOptimizer": false,
            "optimization": false,
            "vendorChunk": false,
            "extractLicenses": false,
            "sourceMap": true,
            "namedChunks": true
          }
        }
      }
    }
  }
}
```

### Webpack 配置 (可选)

```javascript
// webpack.config.js
const ModuleFederationPlugin = require('@module-federation/webpack');

module.exports = {
  plugins: [
    new ModuleFederationPlugin({
      name: 'angularMicroApp',
      filename: 'remoteEntry.js',
      exposes: {
        './Module': './src/app/app.module.ts'
      },
      shared: {
        '@angular/core': { singleton: true },
        '@angular/common': { singleton: true },
        '@angular/platform-browser': { singleton: true }
      }
    })
  ]
};
```

## 最佳实践

### 1. 环境检测

```typescript
// 检测是否在微前端环境中运行
const isMicroFrontend = !!(window as any).__POWERED_BY_MICRO_CORE__;

if (!isMicroFrontend) {
  // 独立运行逻辑
  platformBrowserDynamic().bootstrapModule(AppModule);
}
```

### 2. 样式隔离

```css
/* 使用 CSS 命名空间避免样式冲突 */
.angular-micro-app {
  /* 应用样式 */
}

/* 或使用 Angular 的 ViewEncapsulation */
```

```typescript
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
  encapsulation: ViewEncapsulation.Emulated // 默认值，提供样式隔离
})
export class AppComponent { }
```

### 3. 状态管理

```typescript
// 使用 Angular 服务进行状态管理
@Injectable({
  providedIn: 'root'
})
export class MicroAppStateService {
  private state = new BehaviorSubject<any>({});

  updateState(newState: any) {
    this.state.next({ ...this.state.value, ...newState });
  }

  getState() {
    return this.state.asObservable();
  }
}
```

## 故障排除

### 常见问题

1. **Zone.js 冲突**
   ```typescript
   // 在 bootstrapOptions 中禁用 Zone.js
   bootstrapOptions: {
     ngZone: 'noop'
   }
   ```

2. **路由冲突**
   ```typescript
   // 使用 Hash 路由避免冲突
   RouterModule.forRoot(routes, { useHash: true })
   ```

3. **样式冲突**
   ```typescript
   // 使用 ViewEncapsulation 隔离样式
   encapsulation: ViewEncapsulation.Emulated
   ```

## 许可证

MIT License